#!/usr/bin/env python


# This file has been automatically generated.
# Instead of changing it, create a file called import_helper.py
# and put there a class called ImportHelper(object) in it.
#
# This class will be specially casted so that instead of extending object,
# it will actually extend the class BasicImportHelper()
#
# That means you just have to overload the methods you want to
# change, leaving the other ones intact.
#
# Something that you might want to do is use transactions, for example.
#
# Also, don't forget to add the necessary Django imports.
#
# This file was generated with the following command:
# manage.py dumpscript regionais
#
# to restore it, run
# manage.py runscript module_name.this_script_name
#
# example: if manage.py is at ./manage.py
# and the script is at ./some_folder/some_script.py
# you must make sure ./some_folder/__init__.py exists
# and run  ./manage.py runscript some_folder.some_script
import os, sys
from django.db import transaction

class BasicImportHelper:

    def pre_import(self):
        pass

    @transaction.atomic
    def run_import(self, import_data):
        import_data()

    def post_import(self):
        pass

    def locate_similar(self, current_object, search_data):
        # You will probably want to call this method from save_or_locate()
        # Example:
        #   new_obj = self.locate_similar(the_obj, {"national_id": the_obj.national_id } )

        the_obj = current_object.__class__.objects.get(**search_data)
        return the_obj

    def locate_object(self, original_class, original_pk_name, the_class, pk_name, pk_value, obj_content):
        # You may change this function to do specific lookup for specific objects
        #
        # original_class class of the django orm's object that needs to be located
        # original_pk_name the primary key of original_class
        # the_class      parent class of original_class which contains obj_content
        # pk_name        the primary key of original_class
        # pk_value       value of the primary_key
        # obj_content    content of the object which was not exported.
        #
        # You should use obj_content to locate the object on the target db
        #
        # An example where original_class and the_class are different is
        # when original_class is Farmer and the_class is Person. The table
        # may refer to a Farmer but you will actually need to locate Person
        # in order to instantiate that Farmer
        #
        # Example:
        #   if the_class == SurveyResultFormat or the_class == SurveyType or the_class == SurveyState:
        #       pk_name="name"
        #       pk_value=obj_content[pk_name]
        #   if the_class == StaffGroup:
        #       pk_value=8

        search_data = { pk_name: pk_value }
        the_obj = the_class.objects.get(**search_data)
        #print(the_obj)
        return the_obj


    def save_or_locate(self, the_obj):
        # Change this if you want to locate the object in the database
        try:
            the_obj.save()
        except:
            print("---------------")
            print("Error saving the following object:")
            print(the_obj.__class__)
            print(" ")
            print(the_obj.__dict__)
            print(" ")
            print(the_obj)
            print(" ")
            print("---------------")

            raise
        return the_obj


importer = None
try:
    import import_helper
    # We need this so ImportHelper can extend BasicImportHelper, although import_helper.py
    # has no knowlodge of this class
    importer = type("DynamicImportHelper", (import_helper.ImportHelper, BasicImportHelper ) , {} )()
except ImportError as e:
    # From Python 3.3 we can check e.name - string match is for backward compatibility.
    if 'import_helper' in str(e):
        importer = BasicImportHelper()
    else:
        raise

import datetime
from decimal import Decimal
from django.contrib.contenttypes.models import ContentType

try:
    import dateutil.parser
    from dateutil.tz import tzoffset
except ImportError:
    print("Please install python-dateutil")
    sys.exit(os.EX_USAGE)

def run():
    importer.pre_import()
    importer.run_import(import_data)
    importer.post_import()

def import_data():
    # Initial Imports

    # Processing model: regionais.models.Regional

    from regionais.models import Regional

    regionais_regional_1 = Regional()
    regionais_regional_1.nome = 'DR1'
    regionais_regional_1.tel1 = None
    regionais_regional_1.tel2 = None
    regionais_regional_1.logradouro = None
    regionais_regional_1.numero = None
    regionais_regional_1.bairro = None
    regionais_regional_1.cidade = 'Campinas'
    regionais_regional_1.cep = None
    regionais_regional_1.uf = 'SP'
    regionais_regional_1.email = None
    regionais_regional_1.diretor = None
    regionais_regional_1.email_diretor = None
    regionais_regional_1 = importer.save_or_locate(regionais_regional_1)

    regionais_regional_2 = Regional()
    regionais_regional_2.nome = 'DR10'
    regionais_regional_2.tel1 = None
    regionais_regional_2.tel2 = None
    regionais_regional_2.logradouro = None
    regionais_regional_2.numero = None
    regionais_regional_2.bairro = None
    regionais_regional_2.cidade = 'São Paulo'
    regionais_regional_2.cep = None
    regionais_regional_2.uf = 'SP'
    regionais_regional_2.email = None
    regionais_regional_2.diretor = None
    regionais_regional_2.email_diretor = None
    regionais_regional_2 = importer.save_or_locate(regionais_regional_2)

    regionais_regional_3 = Regional()
    regionais_regional_3.nome = 'DR11'
    regionais_regional_3.tel1 = None
    regionais_regional_3.tel2 = None
    regionais_regional_3.logradouro = None
    regionais_regional_3.numero = None
    regionais_regional_3.bairro = None
    regionais_regional_3.cidade = 'Araçatuba'
    regionais_regional_3.cep = None
    regionais_regional_3.uf = 'SP'
    regionais_regional_3.email = None
    regionais_regional_3.diretor = None
    regionais_regional_3.email_diretor = None
    regionais_regional_3 = importer.save_or_locate(regionais_regional_3)

    regionais_regional_4 = Regional()
    regionais_regional_4.nome = 'DR12'
    regionais_regional_4.tel1 = None
    regionais_regional_4.tel2 = None
    regionais_regional_4.logradouro = None
    regionais_regional_4.numero = None
    regionais_regional_4.bairro = None
    regionais_regional_4.cidade = 'Presidente Prudente'
    regionais_regional_4.cep = None
    regionais_regional_4.uf = 'SP'
    regionais_regional_4.email = None
    regionais_regional_4.diretor = None
    regionais_regional_4.email_diretor = None
    regionais_regional_4 = importer.save_or_locate(regionais_regional_4)

    regionais_regional_5 = Regional()
    regionais_regional_5.nome = 'DR13'
    regionais_regional_5.tel1 = None
    regionais_regional_5.tel2 = None
    regionais_regional_5.logradouro = None
    regionais_regional_5.numero = None
    regionais_regional_5.bairro = None
    regionais_regional_5.cidade = 'Rio Claro'
    regionais_regional_5.cep = None
    regionais_regional_5.uf = 'SP'
    regionais_regional_5.email = None
    regionais_regional_5.diretor = None
    regionais_regional_5.email_diretor = None
    regionais_regional_5 = importer.save_or_locate(regionais_regional_5)

    regionais_regional_6 = Regional()
    regionais_regional_6.nome = 'DR14'
    regionais_regional_6.tel1 = None
    regionais_regional_6.tel2 = None
    regionais_regional_6.logradouro = None
    regionais_regional_6.numero = None
    regionais_regional_6.bairro = None
    regionais_regional_6.cidade = 'Barretos'
    regionais_regional_6.cep = None
    regionais_regional_6.uf = 'SP'
    regionais_regional_6.email = None
    regionais_regional_6.diretor = None
    regionais_regional_6.email_diretor = None
    regionais_regional_6 = importer.save_or_locate(regionais_regional_6)

    regionais_regional_7 = Regional()
    regionais_regional_7.nome = 'DR2'
    regionais_regional_7.tel1 = None
    regionais_regional_7.tel2 = None
    regionais_regional_7.logradouro = None
    regionais_regional_7.numero = None
    regionais_regional_7.bairro = None
    regionais_regional_7.cidade = 'Itapetininga'
    regionais_regional_7.cep = None
    regionais_regional_7.uf = 'SP'
    regionais_regional_7.email = None
    regionais_regional_7.diretor = None
    regionais_regional_7.email_diretor = None
    regionais_regional_7 = importer.save_or_locate(regionais_regional_7)

    regionais_regional_8 = Regional()
    regionais_regional_8.nome = 'DR3'
    regionais_regional_8.tel1 = None
    regionais_regional_8.tel2 = None
    regionais_regional_8.logradouro = None
    regionais_regional_8.numero = None
    regionais_regional_8.bairro = None
    regionais_regional_8.cidade = 'Bauru'
    regionais_regional_8.cep = None
    regionais_regional_8.uf = 'SP'
    regionais_regional_8.email = None
    regionais_regional_8.diretor = None
    regionais_regional_8.email_diretor = None
    regionais_regional_8 = importer.save_or_locate(regionais_regional_8)

    regionais_regional_9 = Regional()
    regionais_regional_9.nome = 'DR4'
    regionais_regional_9.tel1 = None
    regionais_regional_9.tel2 = None
    regionais_regional_9.logradouro = None
    regionais_regional_9.numero = None
    regionais_regional_9.bairro = None
    regionais_regional_9.cidade = 'Araraquara'
    regionais_regional_9.cep = None
    regionais_regional_9.uf = 'SP'
    regionais_regional_9.email = None
    regionais_regional_9.diretor = None
    regionais_regional_9.email_diretor = None
    regionais_regional_9 = importer.save_or_locate(regionais_regional_9)

    regionais_regional_10 = Regional()
    regionais_regional_10.nome = 'DR5'
    regionais_regional_10.tel1 = None
    regionais_regional_10.tel2 = None
    regionais_regional_10.logradouro = None
    regionais_regional_10.numero = None
    regionais_regional_10.bairro = None
    regionais_regional_10.cidade = 'Cubatão'
    regionais_regional_10.cep = None
    regionais_regional_10.uf = 'SP'
    regionais_regional_10.email = None
    regionais_regional_10.diretor = None
    regionais_regional_10.email_diretor = None
    regionais_regional_10 = importer.save_or_locate(regionais_regional_10)

    regionais_regional_11 = Regional()
    regionais_regional_11.nome = 'DR6'
    regionais_regional_11.tel1 = None
    regionais_regional_11.tel2 = None
    regionais_regional_11.logradouro = None
    regionais_regional_11.numero = None
    regionais_regional_11.bairro = None
    regionais_regional_11.cidade = 'Taubaté'
    regionais_regional_11.cep = None
    regionais_regional_11.uf = 'SP'
    regionais_regional_11.email = None
    regionais_regional_11.diretor = None
    regionais_regional_11.email_diretor = None
    regionais_regional_11 = importer.save_or_locate(regionais_regional_11)

    regionais_regional_12 = Regional()
    regionais_regional_12.nome = 'DR7'
    regionais_regional_12.tel1 = None
    regionais_regional_12.tel2 = None
    regionais_regional_12.logradouro = None
    regionais_regional_12.numero = None
    regionais_regional_12.bairro = None
    regionais_regional_12.cidade = 'Assis'
    regionais_regional_12.cep = None
    regionais_regional_12.uf = 'SP'
    regionais_regional_12.email = None
    regionais_regional_12.diretor = None
    regionais_regional_12.email_diretor = None
    regionais_regional_12 = importer.save_or_locate(regionais_regional_12)

    regionais_regional_13 = Regional()
    regionais_regional_13.nome = 'DR8'
    regionais_regional_13.tel1 = None
    regionais_regional_13.tel2 = None
    regionais_regional_13.logradouro = None
    regionais_regional_13.numero = None
    regionais_regional_13.bairro = None
    regionais_regional_13.cidade = 'Ribeirão Preto'
    regionais_regional_13.cep = None
    regionais_regional_13.uf = 'SP'
    regionais_regional_13.email = None
    regionais_regional_13.diretor = None
    regionais_regional_13.email_diretor = None
    regionais_regional_13 = importer.save_or_locate(regionais_regional_13)

    regionais_regional_14 = Regional()
    regionais_regional_14.nome = 'DR9'
    regionais_regional_14.tel1 = None
    regionais_regional_14.tel2 = None
    regionais_regional_14.logradouro = None
    regionais_regional_14.numero = None
    regionais_regional_14.bairro = None
    regionais_regional_14.cidade = 'São José do Rio Preto'
    regionais_regional_14.cep = None
    regionais_regional_14.uf = 'SP'
    regionais_regional_14.email = None
    regionais_regional_14.diretor = None
    regionais_regional_14.email_diretor = None
    regionais_regional_14 = importer.save_or_locate(regionais_regional_14)

