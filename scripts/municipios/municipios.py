#!/usr/bin/env python


# This file has been automatically generated.
# Instead of changing it, create a file called import_helper.py
# and put there a class called ImportHelper(object) in it.
#
# This class will be specially casted so that instead of extending object,
# it will actually extend the class BasicImportHelper()
#
# That means you just have to overload the methods you want to
# change, leaving the other ones intact.
#
# Something that you might want to do is use transactions, for example.
#
# Also, don't forget to add the necessary Django imports.
#
# This file was generated with the following command:
# manage.py dumpscript municipios
#
# to restore it, run
# manage.py runscript module_name.this_script_name
#
# example: if manage.py is at ./manage.py
# and the script is at ./some_folder/some_script.py
# you must make sure ./some_folder/__init__.py exists
# and run  ./manage.py runscript some_folder.some_script
import os, sys
from django.db import transaction

class BasicImportHelper:

    def pre_import(self):
        pass

    @transaction.atomic
    def run_import(self, import_data):
        import_data()

    def post_import(self):
        pass

    def locate_similar(self, current_object, search_data):
        # You will probably want to call this method from save_or_locate()
        # Example:
        #   new_obj = self.locate_similar(the_obj, {"national_id": the_obj.national_id } )

        the_obj = current_object.__class__.objects.get(**search_data)
        return the_obj

    def locate_object(self, original_class, original_pk_name, the_class, pk_name, pk_value, obj_content):
        # You may change this function to do specific lookup for specific objects
        #
        # original_class class of the django orm's object that needs to be located
        # original_pk_name the primary key of original_class
        # the_class      parent class of original_class which contains obj_content
        # pk_name        the primary key of original_class
        # pk_value       value of the primary_key
        # obj_content    content of the object which was not exported.
        #
        # You should use obj_content to locate the object on the target db
        #
        # An example where original_class and the_class are different is
        # when original_class is Farmer and the_class is Person. The table
        # may refer to a Farmer but you will actually need to locate Person
        # in order to instantiate that Farmer
        #
        # Example:
        #   if the_class == SurveyResultFormat or the_class == SurveyType or the_class == SurveyState:
        #       pk_name="name"
        #       pk_value=obj_content[pk_name]
        #   if the_class == StaffGroup:
        #       pk_value=8

        search_data = { pk_name: pk_value }
        the_obj = the_class.objects.get(**search_data)
        #print(the_obj)
        return the_obj


    def save_or_locate(self, the_obj):
        # Change this if you want to locate the object in the database
        try:
            the_obj.save()
        except:
            print("---------------")
            print("Error saving the following object:")
            print(the_obj.__class__)
            print(" ")
            print(the_obj.__dict__)
            print(" ")
            print(the_obj)
            print(" ")
            print("---------------")

            raise
        return the_obj


importer = None
try:
    import import_helper
    # We need this so ImportHelper can extend BasicImportHelper, although import_helper.py
    # has no knowlodge of this class
    importer = type("DynamicImportHelper", (import_helper.ImportHelper, BasicImportHelper ) , {} )()
except ImportError as e:
    # From Python 3.3 we can check e.name - string match is for backward compatibility.
    if 'import_helper' in str(e):
        importer = BasicImportHelper()
    else:
        raise

import datetime
from decimal import Decimal
from django.contrib.contenttypes.models import ContentType

try:
    import dateutil.parser
    from dateutil.tz import tzoffset
except ImportError:
    print("Please install python-dateutil")
    sys.exit(os.EX_USAGE)

def run():
    importer.pre_import()
    importer.run_import(import_data)
    importer.post_import()

def import_data():
    # Initial Imports

    # Processing model: municipios.models.Municipio

    from municipios.models import Municipio

    municipios_municipio_1 = Municipio()
    municipios_municipio_1.cod_ibge = '3500105'
    municipios_municipio_1.nome = 'Adamantina'
    municipios_municipio_1.capital = False
    municipios_municipio_1.uf = 'SP'
    municipios_municipio_1.lat = Decimal('-21.6820000')
    municipios_municipio_1.lng = Decimal('-51.0737000')
    municipios_municipio_1 = importer.save_or_locate(municipios_municipio_1)

    municipios_municipio_2 = Municipio()
    municipios_municipio_2.cod_ibge = '3500204'
    municipios_municipio_2.nome = 'Adolfo'
    municipios_municipio_2.capital = False
    municipios_municipio_2.uf = 'SP'
    municipios_municipio_2.lat = Decimal('-21.2325000')
    municipios_municipio_2.lng = Decimal('-49.6451000')
    municipios_municipio_2 = importer.save_or_locate(municipios_municipio_2)

    municipios_municipio_3 = Municipio()
    municipios_municipio_3.cod_ibge = '3500303'
    municipios_municipio_3.nome = 'Aguaí'
    municipios_municipio_3.capital = False
    municipios_municipio_3.uf = 'SP'
    municipios_municipio_3.lat = Decimal('-22.0572000')
    municipios_municipio_3.lng = Decimal('-46.9735000')
    municipios_municipio_3 = importer.save_or_locate(municipios_municipio_3)

    municipios_municipio_4 = Municipio()
    municipios_municipio_4.cod_ibge = '3500709'
    municipios_municipio_4.nome = 'Agudos'
    municipios_municipio_4.capital = False
    municipios_municipio_4.uf = 'SP'
    municipios_municipio_4.lat = Decimal('-22.4694000')
    municipios_municipio_4.lng = Decimal('-48.9863000')
    municipios_municipio_4 = importer.save_or_locate(municipios_municipio_4)

    municipios_municipio_5 = Municipio()
    municipios_municipio_5.cod_ibge = '3500758'
    municipios_municipio_5.nome = 'Alambari'
    municipios_municipio_5.capital = False
    municipios_municipio_5.uf = 'SP'
    municipios_municipio_5.lat = Decimal('-23.5503000')
    municipios_municipio_5.lng = Decimal('-47.8980000')
    municipios_municipio_5 = importer.save_or_locate(municipios_municipio_5)

    municipios_municipio_6 = Municipio()
    municipios_municipio_6.cod_ibge = '3500808'
    municipios_municipio_6.nome = 'Alfredo Marcondes'
    municipios_municipio_6.capital = False
    municipios_municipio_6.uf = 'SP'
    municipios_municipio_6.lat = Decimal('-21.9527000')
    municipios_municipio_6.lng = Decimal('-51.4140000')
    municipios_municipio_6 = importer.save_or_locate(municipios_municipio_6)

    municipios_municipio_7 = Municipio()
    municipios_municipio_7.cod_ibge = '3500907'
    municipios_municipio_7.nome = 'Altair'
    municipios_municipio_7.capital = False
    municipios_municipio_7.uf = 'SP'
    municipios_municipio_7.lat = Decimal('-20.5242000')
    municipios_municipio_7.lng = Decimal('-49.0571000')
    municipios_municipio_7 = importer.save_or_locate(municipios_municipio_7)

    municipios_municipio_8 = Municipio()
    municipios_municipio_8.cod_ibge = '3501004'
    municipios_municipio_8.nome = 'Altinópolis'
    municipios_municipio_8.capital = False
    municipios_municipio_8.uf = 'SP'
    municipios_municipio_8.lat = Decimal('-21.0214000')
    municipios_municipio_8.lng = Decimal('-47.3712000')
    municipios_municipio_8 = importer.save_or_locate(municipios_municipio_8)

    municipios_municipio_9 = Municipio()
    municipios_municipio_9.cod_ibge = '3501103'
    municipios_municipio_9.nome = 'Alto Alegre'
    municipios_municipio_9.capital = False
    municipios_municipio_9.uf = 'SP'
    municipios_municipio_9.lat = Decimal('-21.5811000')
    municipios_municipio_9.lng = Decimal('-50.1680000')
    municipios_municipio_9 = importer.save_or_locate(municipios_municipio_9)

    municipios_municipio_10 = Municipio()
    municipios_municipio_10.cod_ibge = '3501152'
    municipios_municipio_10.nome = 'Alumínio'
    municipios_municipio_10.capital = False
    municipios_municipio_10.uf = 'SP'
    municipios_municipio_10.lat = Decimal('-23.5306000')
    municipios_municipio_10.lng = Decimal('-47.2546000')
    municipios_municipio_10 = importer.save_or_locate(municipios_municipio_10)

    municipios_municipio_11 = Municipio()
    municipios_municipio_11.cod_ibge = '3501509'
    municipios_municipio_11.nome = 'Alvinlândia'
    municipios_municipio_11.capital = False
    municipios_municipio_11.uf = 'SP'
    municipios_municipio_11.lat = Decimal('-22.4435000')
    municipios_municipio_11.lng = Decimal('-49.7623000')
    municipios_municipio_11 = importer.save_or_locate(municipios_municipio_11)

    municipios_municipio_12 = Municipio()
    municipios_municipio_12.cod_ibge = '3501608'
    municipios_municipio_12.nome = 'Americana'
    municipios_municipio_12.capital = False
    municipios_municipio_12.uf = 'SP'
    municipios_municipio_12.lat = Decimal('-22.7374000')
    municipios_municipio_12.lng = Decimal('-47.3331000')
    municipios_municipio_12 = importer.save_or_locate(municipios_municipio_12)

    municipios_municipio_13 = Municipio()
    municipios_municipio_13.cod_ibge = '3501905'
    municipios_municipio_13.nome = 'Amparo'
    municipios_municipio_13.capital = False
    municipios_municipio_13.uf = 'SP'
    municipios_municipio_13.lat = Decimal('-22.7088000')
    municipios_municipio_13.lng = Decimal('-46.7720000')
    municipios_municipio_13 = importer.save_or_locate(municipios_municipio_13)

    municipios_municipio_14 = Municipio()
    municipios_municipio_14.cod_ibge = '3501707'
    municipios_municipio_14.nome = 'Américo Brasiliense'
    municipios_municipio_14.capital = False
    municipios_municipio_14.uf = 'SP'
    municipios_municipio_14.lat = Decimal('-21.7288000')
    municipios_municipio_14.lng = Decimal('-48.1147000')
    municipios_municipio_14 = importer.save_or_locate(municipios_municipio_14)

    municipios_municipio_15 = Municipio()
    municipios_municipio_15.cod_ibge = '3501806'
    municipios_municipio_15.nome = 'Américo de Campos'
    municipios_municipio_15.capital = False
    municipios_municipio_15.uf = 'SP'
    municipios_municipio_15.lat = Decimal('-20.2985000')
    municipios_municipio_15.lng = Decimal('-49.7359000')
    municipios_municipio_15 = importer.save_or_locate(municipios_municipio_15)

    municipios_municipio_16 = Municipio()
    municipios_municipio_16.cod_ibge = '3502002'
    municipios_municipio_16.nome = 'Analândia'
    municipios_municipio_16.capital = False
    municipios_municipio_16.uf = 'SP'
    municipios_municipio_16.lat = Decimal('-22.1289000')
    municipios_municipio_16.lng = Decimal('-47.6619000')
    municipios_municipio_16 = importer.save_or_locate(municipios_municipio_16)

    municipios_municipio_17 = Municipio()
    municipios_municipio_17.cod_ibge = '3502101'
    municipios_municipio_17.nome = 'Andradina'
    municipios_municipio_17.capital = False
    municipios_municipio_17.uf = 'SP'
    municipios_municipio_17.lat = Decimal('-20.8948000')
    municipios_municipio_17.lng = Decimal('-51.3786000')
    municipios_municipio_17 = importer.save_or_locate(municipios_municipio_17)

    municipios_municipio_18 = Municipio()
    municipios_municipio_18.cod_ibge = '3502200'
    municipios_municipio_18.nome = 'Angatuba'
    municipios_municipio_18.capital = False
    municipios_municipio_18.uf = 'SP'
    municipios_municipio_18.lat = Decimal('-23.4917000')
    municipios_municipio_18.lng = Decimal('-48.4139000')
    municipios_municipio_18 = importer.save_or_locate(municipios_municipio_18)

    municipios_municipio_19 = Municipio()
    municipios_municipio_19.cod_ibge = '3502309'
    municipios_municipio_19.nome = 'Anhembi'
    municipios_municipio_19.capital = False
    municipios_municipio_19.uf = 'SP'
    municipios_municipio_19.lat = Decimal('-22.7930000')
    municipios_municipio_19.lng = Decimal('-48.1336000')
    municipios_municipio_19 = importer.save_or_locate(municipios_municipio_19)

    municipios_municipio_20 = Municipio()
    municipios_municipio_20.cod_ibge = '3502408'
    municipios_municipio_20.nome = 'Anhumas'
    municipios_municipio_20.capital = False
    municipios_municipio_20.uf = 'SP'
    municipios_municipio_20.lat = Decimal('-22.2934000')
    municipios_municipio_20.lng = Decimal('-51.3895000')
    municipios_municipio_20 = importer.save_or_locate(municipios_municipio_20)

    municipios_municipio_21 = Municipio()
    municipios_municipio_21.cod_ibge = '3502507'
    municipios_municipio_21.nome = 'Aparecida'
    municipios_municipio_21.capital = False
    municipios_municipio_21.uf = 'SP'
    municipios_municipio_21.lat = Decimal('-22.8495000')
    municipios_municipio_21.lng = Decimal('-45.2325000')
    municipios_municipio_21 = importer.save_or_locate(municipios_municipio_21)

    municipios_municipio_22 = Municipio()
    municipios_municipio_22.cod_ibge = '3502606'
    municipios_municipio_22.nome = "Aparecida d'Oeste"
    municipios_municipio_22.capital = False
    municipios_municipio_22.uf = 'SP'
    municipios_municipio_22.lat = Decimal('-20.4487000')
    municipios_municipio_22.lng = Decimal('-50.8835000')
    municipios_municipio_22 = importer.save_or_locate(municipios_municipio_22)

    municipios_municipio_23 = Municipio()
    municipios_municipio_23.cod_ibge = '3502705'
    municipios_municipio_23.nome = 'Apiaí'
    municipios_municipio_23.capital = False
    municipios_municipio_23.uf = 'SP'
    municipios_municipio_23.lat = Decimal('-24.5108000')
    municipios_municipio_23.lng = Decimal('-48.8443000')
    municipios_municipio_23 = importer.save_or_locate(municipios_municipio_23)

    municipios_municipio_24 = Municipio()
    municipios_municipio_24.cod_ibge = '3503000'
    municipios_municipio_24.nome = 'Aramina'
    municipios_municipio_24.capital = False
    municipios_municipio_24.uf = 'SP'
    municipios_municipio_24.lat = Decimal('-20.0882000')
    municipios_municipio_24.lng = Decimal('-47.7873000')
    municipios_municipio_24 = importer.save_or_locate(municipios_municipio_24)

    municipios_municipio_25 = Municipio()
    municipios_municipio_25.cod_ibge = '3503109'
    municipios_municipio_25.nome = 'Arandu'
    municipios_municipio_25.capital = False
    municipios_municipio_25.uf = 'SP'
    municipios_municipio_25.lat = Decimal('-23.1386000')
    municipios_municipio_25.lng = Decimal('-49.0487000')
    municipios_municipio_25 = importer.save_or_locate(municipios_municipio_25)

    municipios_municipio_26 = Municipio()
    municipios_municipio_26.cod_ibge = '3503158'
    municipios_municipio_26.nome = 'Arapeí'
    municipios_municipio_26.capital = False
    municipios_municipio_26.uf = 'SP'
    municipios_municipio_26.lat = Decimal('-22.6717000')
    municipios_municipio_26.lng = Decimal('-44.4441000')
    municipios_municipio_26 = importer.save_or_locate(municipios_municipio_26)

    municipios_municipio_27 = Municipio()
    municipios_municipio_27.cod_ibge = '3503208'
    municipios_municipio_27.nome = 'Araraquara'
    municipios_municipio_27.capital = False
    municipios_municipio_27.uf = 'SP'
    municipios_municipio_27.lat = Decimal('-21.7845000')
    municipios_municipio_27.lng = Decimal('-48.1780000')
    municipios_municipio_27 = importer.save_or_locate(municipios_municipio_27)

    municipios_municipio_28 = Municipio()
    municipios_municipio_28.cod_ibge = '3503307'
    municipios_municipio_28.nome = 'Araras'
    municipios_municipio_28.capital = False
    municipios_municipio_28.uf = 'SP'
    municipios_municipio_28.lat = Decimal('-22.3572000')
    municipios_municipio_28.lng = Decimal('-47.3842000')
    municipios_municipio_28 = importer.save_or_locate(municipios_municipio_28)

    municipios_municipio_29 = Municipio()
    municipios_municipio_29.cod_ibge = '3502754'
    municipios_municipio_29.nome = 'Araçariguama'
    municipios_municipio_29.capital = False
    municipios_municipio_29.uf = 'SP'
    municipios_municipio_29.lat = Decimal('-23.4366000')
    municipios_municipio_29.lng = Decimal('-47.0608000')
    municipios_municipio_29 = importer.save_or_locate(municipios_municipio_29)

    municipios_municipio_30 = Municipio()
    municipios_municipio_30.cod_ibge = '3502804'
    municipios_municipio_30.nome = 'Araçatuba'
    municipios_municipio_30.capital = False
    municipios_municipio_30.uf = 'SP'
    municipios_municipio_30.lat = Decimal('-21.2076000')
    municipios_municipio_30.lng = Decimal('-50.4401000')
    municipios_municipio_30 = importer.save_or_locate(municipios_municipio_30)

    municipios_municipio_31 = Municipio()
    municipios_municipio_31.cod_ibge = '3502903'
    municipios_municipio_31.nome = 'Araçoiaba da Serra'
    municipios_municipio_31.capital = False
    municipios_municipio_31.uf = 'SP'
    municipios_municipio_31.lat = Decimal('-23.5029000')
    municipios_municipio_31.lng = Decimal('-47.6166000')
    municipios_municipio_31 = importer.save_or_locate(municipios_municipio_31)

    municipios_municipio_32 = Municipio()
    municipios_municipio_32.cod_ibge = '3503356'
    municipios_municipio_32.nome = 'Arco-Íris'
    municipios_municipio_32.capital = False
    municipios_municipio_32.uf = 'SP'
    municipios_municipio_32.lat = Decimal('-21.7728000')
    municipios_municipio_32.lng = Decimal('-50.4660000')
    municipios_municipio_32 = importer.save_or_locate(municipios_municipio_32)

    municipios_municipio_33 = Municipio()
    municipios_municipio_33.cod_ibge = '3503406'
    municipios_municipio_33.nome = 'Arealva'
    municipios_municipio_33.capital = False
    municipios_municipio_33.uf = 'SP'
    municipios_municipio_33.lat = Decimal('-22.0310000')
    municipios_municipio_33.lng = Decimal('-48.9135000')
    municipios_municipio_33 = importer.save_or_locate(municipios_municipio_33)

    municipios_municipio_34 = Municipio()
    municipios_municipio_34.cod_ibge = '3503505'
    municipios_municipio_34.nome = 'Areias'
    municipios_municipio_34.capital = False
    municipios_municipio_34.uf = 'SP'
    municipios_municipio_34.lat = Decimal('-22.5786000')
    municipios_municipio_34.lng = Decimal('-44.6992000')
    municipios_municipio_34 = importer.save_or_locate(municipios_municipio_34)

    municipios_municipio_35 = Municipio()
    municipios_municipio_35.cod_ibge = '3503604'
    municipios_municipio_35.nome = 'Areiópolis'
    municipios_municipio_35.capital = False
    municipios_municipio_35.uf = 'SP'
    municipios_municipio_35.lat = Decimal('-22.6672000')
    municipios_municipio_35.lng = Decimal('-48.6681000')
    municipios_municipio_35 = importer.save_or_locate(municipios_municipio_35)

    municipios_municipio_36 = Municipio()
    municipios_municipio_36.cod_ibge = '3503703'
    municipios_municipio_36.nome = 'Ariranha'
    municipios_municipio_36.capital = False
    municipios_municipio_36.uf = 'SP'
    municipios_municipio_36.lat = Decimal('-21.1872000')
    municipios_municipio_36.lng = Decimal('-48.7904000')
    municipios_municipio_36 = importer.save_or_locate(municipios_municipio_36)

    municipios_municipio_37 = Municipio()
    municipios_municipio_37.cod_ibge = '3503802'
    municipios_municipio_37.nome = 'Artur Nogueira'
    municipios_municipio_37.capital = False
    municipios_municipio_37.uf = 'SP'
    municipios_municipio_37.lat = Decimal('-22.5727000')
    municipios_municipio_37.lng = Decimal('-47.1727000')
    municipios_municipio_37 = importer.save_or_locate(municipios_municipio_37)

    municipios_municipio_38 = Municipio()
    municipios_municipio_38.cod_ibge = '3503901'
    municipios_municipio_38.nome = 'Arujá'
    municipios_municipio_38.capital = False
    municipios_municipio_38.uf = 'SP'
    municipios_municipio_38.lat = Decimal('-23.3965000')
    municipios_municipio_38.lng = Decimal('-46.3200000')
    municipios_municipio_38 = importer.save_or_locate(municipios_municipio_38)

    municipios_municipio_39 = Municipio()
    municipios_municipio_39.cod_ibge = '3503950'
    municipios_municipio_39.nome = 'Aspásia'
    municipios_municipio_39.capital = False
    municipios_municipio_39.uf = 'SP'
    municipios_municipio_39.lat = Decimal('-20.1600000')
    municipios_municipio_39.lng = Decimal('-50.7280000')
    municipios_municipio_39 = importer.save_or_locate(municipios_municipio_39)

    municipios_municipio_40 = Municipio()
    municipios_municipio_40.cod_ibge = '3504008'
    municipios_municipio_40.nome = 'Assis'
    municipios_municipio_40.capital = False
    municipios_municipio_40.uf = 'SP'
    municipios_municipio_40.lat = Decimal('-22.6600000')
    municipios_municipio_40.lng = Decimal('-50.4183000')
    municipios_municipio_40 = importer.save_or_locate(municipios_municipio_40)

    municipios_municipio_41 = Municipio()
    municipios_municipio_41.cod_ibge = '3504107'
    municipios_municipio_41.nome = 'Atibaia'
    municipios_municipio_41.capital = False
    municipios_municipio_41.uf = 'SP'
    municipios_municipio_41.lat = Decimal('-23.1171000')
    municipios_municipio_41.lng = Decimal('-46.5563000')
    municipios_municipio_41 = importer.save_or_locate(municipios_municipio_41)

    municipios_municipio_42 = Municipio()
    municipios_municipio_42.cod_ibge = '3504206'
    municipios_municipio_42.nome = 'Auriflama'
    municipios_municipio_42.capital = False
    municipios_municipio_42.uf = 'SP'
    municipios_municipio_42.lat = Decimal('-20.6836000')
    municipios_municipio_42.lng = Decimal('-50.5572000')
    municipios_municipio_42 = importer.save_or_locate(municipios_municipio_42)

    municipios_municipio_43 = Municipio()
    municipios_municipio_43.cod_ibge = '3504404'
    municipios_municipio_43.nome = 'Avanhandava'
    municipios_municipio_43.capital = False
    municipios_municipio_43.uf = 'SP'
    municipios_municipio_43.lat = Decimal('-21.4584000')
    municipios_municipio_43.lng = Decimal('-49.9509000')
    municipios_municipio_43 = importer.save_or_locate(municipios_municipio_43)

    municipios_municipio_44 = Municipio()
    municipios_municipio_44.cod_ibge = '3504503'
    municipios_municipio_44.nome = 'Avaré'
    municipios_municipio_44.capital = False
    municipios_municipio_44.uf = 'SP'
    municipios_municipio_44.lat = Decimal('-23.1067000')
    municipios_municipio_44.lng = Decimal('-48.9251000')
    municipios_municipio_44 = importer.save_or_locate(municipios_municipio_44)

    municipios_municipio_45 = Municipio()
    municipios_municipio_45.cod_ibge = '3504305'
    municipios_municipio_45.nome = 'Avaí'
    municipios_municipio_45.capital = False
    municipios_municipio_45.uf = 'SP'
    municipios_municipio_45.lat = Decimal('-22.1514000')
    municipios_municipio_45.lng = Decimal('-49.3356000')
    municipios_municipio_45 = importer.save_or_locate(municipios_municipio_45)

    municipios_municipio_46 = Municipio()
    municipios_municipio_46.cod_ibge = '3504602'
    municipios_municipio_46.nome = 'Bady Bassitt'
    municipios_municipio_46.capital = False
    municipios_municipio_46.uf = 'SP'
    municipios_municipio_46.lat = Decimal('-20.9197000')
    municipios_municipio_46.lng = Decimal('-49.4385000')
    municipios_municipio_46 = importer.save_or_locate(municipios_municipio_46)

    municipios_municipio_47 = Municipio()
    municipios_municipio_47.cod_ibge = '3504701'
    municipios_municipio_47.nome = 'Balbinos'
    municipios_municipio_47.capital = False
    municipios_municipio_47.uf = 'SP'
    municipios_municipio_47.lat = Decimal('-21.8963000')
    municipios_municipio_47.lng = Decimal('-49.3619000')
    municipios_municipio_47 = importer.save_or_locate(municipios_municipio_47)

    municipios_municipio_48 = Municipio()
    municipios_municipio_48.cod_ibge = '3504909'
    municipios_municipio_48.nome = 'Bananal'
    municipios_municipio_48.capital = False
    municipios_municipio_48.uf = 'SP'
    municipios_municipio_48.lat = Decimal('-22.6819000')
    municipios_municipio_48.lng = Decimal('-44.3281000')
    municipios_municipio_48 = importer.save_or_locate(municipios_municipio_48)

    municipios_municipio_49 = Municipio()
    municipios_municipio_49.cod_ibge = '3505104'
    municipios_municipio_49.nome = 'Barbosa'
    municipios_municipio_49.capital = False
    municipios_municipio_49.uf = 'SP'
    municipios_municipio_49.lat = Decimal('-21.2657000')
    municipios_municipio_49.lng = Decimal('-49.9518000')
    municipios_municipio_49 = importer.save_or_locate(municipios_municipio_49)

    municipios_municipio_50 = Municipio()
    municipios_municipio_50.cod_ibge = '3505203'
    municipios_municipio_50.nome = 'Bariri'
    municipios_municipio_50.capital = False
    municipios_municipio_50.uf = 'SP'
    municipios_municipio_50.lat = Decimal('-22.0730000')
    municipios_municipio_50.lng = Decimal('-48.7438000')
    municipios_municipio_50 = importer.save_or_locate(municipios_municipio_50)

    municipios_municipio_51 = Municipio()
    municipios_municipio_51.cod_ibge = '3505302'
    municipios_municipio_51.nome = 'Barra Bonita'
    municipios_municipio_51.capital = False
    municipios_municipio_51.uf = 'SP'
    municipios_municipio_51.lat = Decimal('-22.4909000')
    municipios_municipio_51.lng = Decimal('-48.5583000')
    municipios_municipio_51 = importer.save_or_locate(municipios_municipio_51)

    municipios_municipio_52 = Municipio()
    municipios_municipio_52.cod_ibge = '3505351'
    municipios_municipio_52.nome = 'Barra do Chapéu'
    municipios_municipio_52.capital = False
    municipios_municipio_52.uf = 'SP'
    municipios_municipio_52.lat = Decimal('-24.4722000')
    municipios_municipio_52.lng = Decimal('-49.0238000')
    municipios_municipio_52 = importer.save_or_locate(municipios_municipio_52)

    municipios_municipio_53 = Municipio()
    municipios_municipio_53.cod_ibge = '3505401'
    municipios_municipio_53.nome = 'Barra do Turvo'
    municipios_municipio_53.capital = False
    municipios_municipio_53.uf = 'SP'
    municipios_municipio_53.lat = Decimal('-24.7590000')
    municipios_municipio_53.lng = Decimal('-48.5013000')
    municipios_municipio_53 = importer.save_or_locate(municipios_municipio_53)

    municipios_municipio_54 = Municipio()
    municipios_municipio_54.cod_ibge = '3505500'
    municipios_municipio_54.nome = 'Barretos'
    municipios_municipio_54.capital = False
    municipios_municipio_54.uf = 'SP'
    municipios_municipio_54.lat = Decimal('-20.5531000')
    municipios_municipio_54.lng = Decimal('-48.5698000')
    municipios_municipio_54 = importer.save_or_locate(municipios_municipio_54)

    municipios_municipio_55 = Municipio()
    municipios_municipio_55.cod_ibge = '3505609'
    municipios_municipio_55.nome = 'Barrinha'
    municipios_municipio_55.capital = False
    municipios_municipio_55.uf = 'SP'
    municipios_municipio_55.lat = Decimal('-21.1864000')
    municipios_municipio_55.lng = Decimal('-48.1636000')
    municipios_municipio_55 = importer.save_or_locate(municipios_municipio_55)

    municipios_municipio_56 = Municipio()
    municipios_municipio_56.cod_ibge = '3505708'
    municipios_municipio_56.nome = 'Barueri'
    municipios_municipio_56.capital = False
    municipios_municipio_56.uf = 'SP'
    municipios_municipio_56.lat = Decimal('-23.5057000')
    municipios_municipio_56.lng = Decimal('-46.8790000')
    municipios_municipio_56 = importer.save_or_locate(municipios_municipio_56)

    municipios_municipio_57 = Municipio()
    municipios_municipio_57.cod_ibge = '3505005'
    municipios_municipio_57.nome = 'Barão de Antonina'
    municipios_municipio_57.capital = False
    municipios_municipio_57.uf = 'SP'
    municipios_municipio_57.lat = Decimal('-23.6284000')
    municipios_municipio_57.lng = Decimal('-49.5634000')
    municipios_municipio_57 = importer.save_or_locate(municipios_municipio_57)

    municipios_municipio_58 = Municipio()
    municipios_municipio_58.cod_ibge = '3505807'
    municipios_municipio_58.nome = 'Bastos'
    municipios_municipio_58.capital = False
    municipios_municipio_58.uf = 'SP'
    municipios_municipio_58.lat = Decimal('-21.9210000')
    municipios_municipio_58.lng = Decimal('-50.7357000')
    municipios_municipio_58 = importer.save_or_locate(municipios_municipio_58)

    municipios_municipio_59 = Municipio()
    municipios_municipio_59.cod_ibge = '3505906'
    municipios_municipio_59.nome = 'Batatais'
    municipios_municipio_59.capital = False
    municipios_municipio_59.uf = 'SP'
    municipios_municipio_59.lat = Decimal('-20.8929000')
    municipios_municipio_59.lng = Decimal('-47.5921000')
    municipios_municipio_59 = importer.save_or_locate(municipios_municipio_59)

    municipios_municipio_60 = Municipio()
    municipios_municipio_60.cod_ibge = '3506003'
    municipios_municipio_60.nome = 'Bauru'
    municipios_municipio_60.capital = False
    municipios_municipio_60.uf = 'SP'
    municipios_municipio_60.lat = Decimal('-22.3246000')
    municipios_municipio_60.lng = Decimal('-49.0871000')
    municipios_municipio_60 = importer.save_or_locate(municipios_municipio_60)

    municipios_municipio_61 = Municipio()
    municipios_municipio_61.cod_ibge = '3506102'
    municipios_municipio_61.nome = 'Bebedouro'
    municipios_municipio_61.capital = False
    municipios_municipio_61.uf = 'SP'
    municipios_municipio_61.lat = Decimal('-20.9491000')
    municipios_municipio_61.lng = Decimal('-48.4791000')
    municipios_municipio_61 = importer.save_or_locate(municipios_municipio_61)

    municipios_municipio_62 = Municipio()
    municipios_municipio_62.cod_ibge = '3506201'
    municipios_municipio_62.nome = 'Bento de Abreu'
    municipios_municipio_62.capital = False
    municipios_municipio_62.uf = 'SP'
    municipios_municipio_62.lat = Decimal('-21.2686000')
    municipios_municipio_62.lng = Decimal('-50.8140000')
    municipios_municipio_62 = importer.save_or_locate(municipios_municipio_62)

    municipios_municipio_63 = Municipio()
    municipios_municipio_63.cod_ibge = '3506300'
    municipios_municipio_63.nome = 'Bernardino de Campos'
    municipios_municipio_63.capital = False
    municipios_municipio_63.uf = 'SP'
    municipios_municipio_63.lat = Decimal('-23.0164000')
    municipios_municipio_63.lng = Decimal('-49.4679000')
    municipios_municipio_63 = importer.save_or_locate(municipios_municipio_63)

    municipios_municipio_64 = Municipio()
    municipios_municipio_64.cod_ibge = '3506359'
    municipios_municipio_64.nome = 'Bertioga'
    municipios_municipio_64.capital = False
    municipios_municipio_64.uf = 'SP'
    municipios_municipio_64.lat = Decimal('-23.8486000')
    municipios_municipio_64.lng = Decimal('-46.1396000')
    municipios_municipio_64 = importer.save_or_locate(municipios_municipio_64)

    municipios_municipio_65 = Municipio()
    municipios_municipio_65.cod_ibge = '3506409'
    municipios_municipio_65.nome = 'Bilac'
    municipios_municipio_65.capital = False
    municipios_municipio_65.uf = 'SP'
    municipios_municipio_65.lat = Decimal('-21.4040000')
    municipios_municipio_65.lng = Decimal('-50.4746000')
    municipios_municipio_65 = importer.save_or_locate(municipios_municipio_65)

    municipios_municipio_66 = Municipio()
    municipios_municipio_66.cod_ibge = '3506508'
    municipios_municipio_66.nome = 'Birigui'
    municipios_municipio_66.capital = False
    municipios_municipio_66.uf = 'SP'
    municipios_municipio_66.lat = Decimal('-21.2910000')
    municipios_municipio_66.lng = Decimal('-50.3432000')
    municipios_municipio_66 = importer.save_or_locate(municipios_municipio_66)

    municipios_municipio_67 = Municipio()
    municipios_municipio_67.cod_ibge = '3506607'
    municipios_municipio_67.nome = 'Biritiba-Mirim'
    municipios_municipio_67.capital = False
    municipios_municipio_67.uf = 'SP'
    municipios_municipio_67.lat = Decimal('-23.5698000')
    municipios_municipio_67.lng = Decimal('-46.0407000')
    municipios_municipio_67 = importer.save_or_locate(municipios_municipio_67)

    municipios_municipio_68 = Municipio()
    municipios_municipio_68.cod_ibge = '3506706'
    municipios_municipio_68.nome = 'Boa Esperança do Sul'
    municipios_municipio_68.capital = False
    municipios_municipio_68.uf = 'SP'
    municipios_municipio_68.lat = Decimal('-21.9918000')
    municipios_municipio_68.lng = Decimal('-48.3906000')
    municipios_municipio_68 = importer.save_or_locate(municipios_municipio_68)

    municipios_municipio_69 = Municipio()
    municipios_municipio_69.cod_ibge = '3506805'
    municipios_municipio_69.nome = 'Bocaina'
    municipios_municipio_69.capital = False
    municipios_municipio_69.uf = 'SP'
    municipios_municipio_69.lat = Decimal('-22.1365000')
    municipios_municipio_69.lng = Decimal('-48.5230000')
    municipios_municipio_69 = importer.save_or_locate(municipios_municipio_69)

    municipios_municipio_70 = Municipio()
    municipios_municipio_70.cod_ibge = '3506904'
    municipios_municipio_70.nome = 'Bofete'
    municipios_municipio_70.capital = False
    municipios_municipio_70.uf = 'SP'
    municipios_municipio_70.lat = Decimal('-23.1055000')
    municipios_municipio_70.lng = Decimal('-48.2582000')
    municipios_municipio_70 = importer.save_or_locate(municipios_municipio_70)

    municipios_municipio_71 = Municipio()
    municipios_municipio_71.cod_ibge = '3507001'
    municipios_municipio_71.nome = 'Boituva'
    municipios_municipio_71.capital = False
    municipios_municipio_71.uf = 'SP'
    municipios_municipio_71.lat = Decimal('-23.2855000')
    municipios_municipio_71.lng = Decimal('-47.6786000')
    municipios_municipio_71 = importer.save_or_locate(municipios_municipio_71)

    municipios_municipio_72 = Municipio()
    municipios_municipio_72.cod_ibge = '3507100'
    municipios_municipio_72.nome = 'Bom Jesus dos Perdões'
    municipios_municipio_72.capital = False
    municipios_municipio_72.uf = 'SP'
    municipios_municipio_72.lat = Decimal('-23.1356000')
    municipios_municipio_72.lng = Decimal('-46.4675000')
    municipios_municipio_72 = importer.save_or_locate(municipios_municipio_72)

    municipios_municipio_73 = Municipio()
    municipios_municipio_73.cod_ibge = '3507159'
    municipios_municipio_73.nome = 'Bom Sucesso de Itararé'
    municipios_municipio_73.capital = False
    municipios_municipio_73.uf = 'SP'
    municipios_municipio_73.lat = Decimal('-24.3155000')
    municipios_municipio_73.lng = Decimal('-49.1451000')
    municipios_municipio_73 = importer.save_or_locate(municipios_municipio_73)

    municipios_municipio_74 = Municipio()
    municipios_municipio_74.cod_ibge = '3507308'
    municipios_municipio_74.nome = 'Boracéia'
    municipios_municipio_74.capital = False
    municipios_municipio_74.uf = 'SP'
    municipios_municipio_74.lat = Decimal('-22.1926000')
    municipios_municipio_74.lng = Decimal('-48.7808000')
    municipios_municipio_74 = importer.save_or_locate(municipios_municipio_74)

    municipios_municipio_75 = Municipio()
    municipios_municipio_75.cod_ibge = '3507407'
    municipios_municipio_75.nome = 'Borborema'
    municipios_municipio_75.capital = False
    municipios_municipio_75.uf = 'SP'
    municipios_municipio_75.lat = Decimal('-21.6214000')
    municipios_municipio_75.lng = Decimal('-49.0741000')
    municipios_municipio_75 = importer.save_or_locate(municipios_municipio_75)

    municipios_municipio_76 = Municipio()
    municipios_municipio_76.cod_ibge = '3507456'
    municipios_municipio_76.nome = 'Borebi'
    municipios_municipio_76.capital = False
    municipios_municipio_76.uf = 'SP'
    municipios_municipio_76.lat = Decimal('-22.5728000')
    municipios_municipio_76.lng = Decimal('-48.9707000')
    municipios_municipio_76 = importer.save_or_locate(municipios_municipio_76)

    municipios_municipio_77 = Municipio()
    municipios_municipio_77.cod_ibge = '3507209'
    municipios_municipio_77.nome = 'Borá'
    municipios_municipio_77.capital = False
    municipios_municipio_77.uf = 'SP'
    municipios_municipio_77.lat = Decimal('-22.2696000')
    municipios_municipio_77.lng = Decimal('-50.5409000')
    municipios_municipio_77 = importer.save_or_locate(municipios_municipio_77)

    municipios_municipio_78 = Municipio()
    municipios_municipio_78.cod_ibge = '3507506'
    municipios_municipio_78.nome = 'Botucatu'
    municipios_municipio_78.capital = False
    municipios_municipio_78.uf = 'SP'
    municipios_municipio_78.lat = Decimal('-22.8837000')
    municipios_municipio_78.lng = Decimal('-48.4437000')
    municipios_municipio_78 = importer.save_or_locate(municipios_municipio_78)

    municipios_municipio_79 = Municipio()
    municipios_municipio_79.cod_ibge = '3507605'
    municipios_municipio_79.nome = 'Bragança Paulista'
    municipios_municipio_79.capital = False
    municipios_municipio_79.uf = 'SP'
    municipios_municipio_79.lat = Decimal('-22.9527000')
    municipios_municipio_79.lng = Decimal('-46.5419000')
    municipios_municipio_79 = importer.save_or_locate(municipios_municipio_79)

    municipios_municipio_80 = Municipio()
    municipios_municipio_80.cod_ibge = '3507704'
    municipios_municipio_80.nome = 'Braúna'
    municipios_municipio_80.capital = False
    municipios_municipio_80.uf = 'SP'
    municipios_municipio_80.lat = Decimal('-21.4990000')
    municipios_municipio_80.lng = Decimal('-50.3175000')
    municipios_municipio_80 = importer.save_or_locate(municipios_municipio_80)

    municipios_municipio_81 = Municipio()
    municipios_municipio_81.cod_ibge = '3507753'
    municipios_municipio_81.nome = 'Brejo Alegre'
    municipios_municipio_81.capital = False
    municipios_municipio_81.uf = 'SP'
    municipios_municipio_81.lat = Decimal('-21.1651000')
    municipios_municipio_81.lng = Decimal('-50.1861000')
    municipios_municipio_81 = importer.save_or_locate(municipios_municipio_81)

    municipios_municipio_82 = Municipio()
    municipios_municipio_82.cod_ibge = '3507803'
    municipios_municipio_82.nome = 'Brodowski'
    municipios_municipio_82.capital = False
    municipios_municipio_82.uf = 'SP'
    municipios_municipio_82.lat = Decimal('-20.9845000')
    municipios_municipio_82.lng = Decimal('-47.6572000')
    municipios_municipio_82 = importer.save_or_locate(municipios_municipio_82)

    municipios_municipio_83 = Municipio()
    municipios_municipio_83.cod_ibge = '3507902'
    municipios_municipio_83.nome = 'Brotas'
    municipios_municipio_83.capital = False
    municipios_municipio_83.uf = 'SP'
    municipios_municipio_83.lat = Decimal('-22.2795000')
    municipios_municipio_83.lng = Decimal('-48.1251000')
    municipios_municipio_83 = importer.save_or_locate(municipios_municipio_83)

    municipios_municipio_84 = Municipio()
    municipios_municipio_84.cod_ibge = '3508009'
    municipios_municipio_84.nome = 'Buri'
    municipios_municipio_84.capital = False
    municipios_municipio_84.uf = 'SP'
    municipios_municipio_84.lat = Decimal('-23.7977000')
    municipios_municipio_84.lng = Decimal('-48.5958000')
    municipios_municipio_84 = importer.save_or_locate(municipios_municipio_84)

    municipios_municipio_85 = Municipio()
    municipios_municipio_85.cod_ibge = '3508108'
    municipios_municipio_85.nome = 'Buritama'
    municipios_municipio_85.capital = False
    municipios_municipio_85.uf = 'SP'
    municipios_municipio_85.lat = Decimal('-21.0661000')
    municipios_municipio_85.lng = Decimal('-50.1475000')
    municipios_municipio_85 = importer.save_or_locate(municipios_municipio_85)

    municipios_municipio_86 = Municipio()
    municipios_municipio_86.cod_ibge = '3508207'
    municipios_municipio_86.nome = 'Buritizal'
    municipios_municipio_86.capital = False
    municipios_municipio_86.uf = 'SP'
    municipios_municipio_86.lat = Decimal('-20.1911000')
    municipios_municipio_86.lng = Decimal('-47.7096000')
    municipios_municipio_86 = importer.save_or_locate(municipios_municipio_86)

    municipios_municipio_87 = Municipio()
    municipios_municipio_87.cod_ibge = '3504800'
    municipios_municipio_87.nome = 'Bálsamo'
    municipios_municipio_87.capital = False
    municipios_municipio_87.uf = 'SP'
    municipios_municipio_87.lat = Decimal('-20.7348000')
    municipios_municipio_87.lng = Decimal('-49.5865000')
    municipios_municipio_87 = importer.save_or_locate(municipios_municipio_87)

    municipios_municipio_88 = Municipio()
    municipios_municipio_88.cod_ibge = '3508405'
    municipios_municipio_88.nome = 'Cabreúva'
    municipios_municipio_88.capital = False
    municipios_municipio_88.uf = 'SP'
    municipios_municipio_88.lat = Decimal('-23.3053000')
    municipios_municipio_88.lng = Decimal('-47.1362000')
    municipios_municipio_88 = importer.save_or_locate(municipios_municipio_88)

    municipios_municipio_89 = Municipio()
    municipios_municipio_89.cod_ibge = '3508306'
    municipios_municipio_89.nome = 'Cabrália Paulista'
    municipios_municipio_89.capital = False
    municipios_municipio_89.uf = 'SP'
    municipios_municipio_89.lat = Decimal('-22.4576000')
    municipios_municipio_89.lng = Decimal('-49.3393000')
    municipios_municipio_89 = importer.save_or_locate(municipios_municipio_89)

    municipios_municipio_90 = Municipio()
    municipios_municipio_90.cod_ibge = '3508603'
    municipios_municipio_90.nome = 'Cachoeira Paulista'
    municipios_municipio_90.capital = False
    municipios_municipio_90.uf = 'SP'
    municipios_municipio_90.lat = Decimal('-22.6665000')
    municipios_municipio_90.lng = Decimal('-45.0154000')
    municipios_municipio_90 = importer.save_or_locate(municipios_municipio_90)

    municipios_municipio_91 = Municipio()
    municipios_municipio_91.cod_ibge = '3508702'
    municipios_municipio_91.nome = 'Caconde'
    municipios_municipio_91.capital = False
    municipios_municipio_91.uf = 'SP'
    municipios_municipio_91.lat = Decimal('-21.5280000')
    municipios_municipio_91.lng = Decimal('-46.6437000')
    municipios_municipio_91 = importer.save_or_locate(municipios_municipio_91)

    municipios_municipio_92 = Municipio()
    municipios_municipio_92.cod_ibge = '3508801'
    municipios_municipio_92.nome = 'Cafelândia'
    municipios_municipio_92.capital = False
    municipios_municipio_92.uf = 'SP'
    municipios_municipio_92.lat = Decimal('-21.8031000')
    municipios_municipio_92.lng = Decimal('-49.6092000')
    municipios_municipio_92 = importer.save_or_locate(municipios_municipio_92)

    municipios_municipio_93 = Municipio()
    municipios_municipio_93.cod_ibge = '3508900'
    municipios_municipio_93.nome = 'Caiabu'
    municipios_municipio_93.capital = False
    municipios_municipio_93.uf = 'SP'
    municipios_municipio_93.lat = Decimal('-22.0127000')
    municipios_municipio_93.lng = Decimal('-51.2394000')
    municipios_municipio_93 = importer.save_or_locate(municipios_municipio_93)

    municipios_municipio_94 = Municipio()
    municipios_municipio_94.cod_ibge = '3509007'
    municipios_municipio_94.nome = 'Caieiras'
    municipios_municipio_94.capital = False
    municipios_municipio_94.uf = 'SP'
    municipios_municipio_94.lat = Decimal('-23.3607000')
    municipios_municipio_94.lng = Decimal('-46.7397000')
    municipios_municipio_94 = importer.save_or_locate(municipios_municipio_94)

    municipios_municipio_95 = Municipio()
    municipios_municipio_95.cod_ibge = '3509106'
    municipios_municipio_95.nome = 'Caiuá'
    municipios_municipio_95.capital = False
    municipios_municipio_95.uf = 'SP'
    municipios_municipio_95.lat = Decimal('-21.8322000')
    municipios_municipio_95.lng = Decimal('-51.9969000')
    municipios_municipio_95 = importer.save_or_locate(municipios_municipio_95)

    municipios_municipio_96 = Municipio()
    municipios_municipio_96.cod_ibge = '3509205'
    municipios_municipio_96.nome = 'Cajamar'
    municipios_municipio_96.capital = False
    municipios_municipio_96.uf = 'SP'
    municipios_municipio_96.lat = Decimal('-23.3550000')
    municipios_municipio_96.lng = Decimal('-46.8781000')
    municipios_municipio_96 = importer.save_or_locate(municipios_municipio_96)

    municipios_municipio_97 = Municipio()
    municipios_municipio_97.cod_ibge = '3509254'
    municipios_municipio_97.nome = 'Cajati'
    municipios_municipio_97.capital = False
    municipios_municipio_97.uf = 'SP'
    municipios_municipio_97.lat = Decimal('-24.7324000')
    municipios_municipio_97.lng = Decimal('-48.1223000')
    municipios_municipio_97 = importer.save_or_locate(municipios_municipio_97)

    municipios_municipio_98 = Municipio()
    municipios_municipio_98.cod_ibge = '3509304'
    municipios_municipio_98.nome = 'Cajobi'
    municipios_municipio_98.capital = False
    municipios_municipio_98.uf = 'SP'
    municipios_municipio_98.lat = Decimal('-20.8773000')
    municipios_municipio_98.lng = Decimal('-48.8063000')
    municipios_municipio_98 = importer.save_or_locate(municipios_municipio_98)

    municipios_municipio_99 = Municipio()
    municipios_municipio_99.cod_ibge = '3509403'
    municipios_municipio_99.nome = 'Cajuru'
    municipios_municipio_99.capital = False
    municipios_municipio_99.uf = 'SP'
    municipios_municipio_99.lat = Decimal('-21.2749000')
    municipios_municipio_99.lng = Decimal('-47.3030000')
    municipios_municipio_99 = importer.save_or_locate(municipios_municipio_99)

    municipios_municipio_100 = Municipio()
    municipios_municipio_100.cod_ibge = '3509452'
    municipios_municipio_100.nome = 'Campina do Monte Alegre'
    municipios_municipio_100.capital = False
    municipios_municipio_100.uf = 'SP'
    municipios_municipio_100.lat = Decimal('-23.5895000')
    municipios_municipio_100.lng = Decimal('-48.4758000')
    municipios_municipio_100 = importer.save_or_locate(municipios_municipio_100)

    municipios_municipio_101 = Municipio()
    municipios_municipio_101.cod_ibge = '3509502'
    municipios_municipio_101.nome = 'Campinas'
    municipios_municipio_101.capital = False
    municipios_municipio_101.uf = 'SP'
    municipios_municipio_101.lat = Decimal('-22.9053000')
    municipios_municipio_101.lng = Decimal('-47.0659000')
    municipios_municipio_101 = importer.save_or_locate(municipios_municipio_101)

    municipios_municipio_102 = Municipio()
    municipios_municipio_102.cod_ibge = '3509601'
    municipios_municipio_102.nome = 'Campo Limpo Paulista'
    municipios_municipio_102.capital = False
    municipios_municipio_102.uf = 'SP'
    municipios_municipio_102.lat = Decimal('-23.2078000')
    municipios_municipio_102.lng = Decimal('-46.7889000')
    municipios_municipio_102 = importer.save_or_locate(municipios_municipio_102)

    municipios_municipio_103 = Municipio()
    municipios_municipio_103.cod_ibge = '3509809'
    municipios_municipio_103.nome = 'Campos Novos Paulista'
    municipios_municipio_103.capital = False
    municipios_municipio_103.uf = 'SP'
    municipios_municipio_103.lat = Decimal('-22.6020000')
    municipios_municipio_103.lng = Decimal('-49.9987000')
    municipios_municipio_103 = importer.save_or_locate(municipios_municipio_103)

    municipios_municipio_104 = Municipio()
    municipios_municipio_104.cod_ibge = '3509700'
    municipios_municipio_104.nome = 'Campos do Jordão'
    municipios_municipio_104.capital = False
    municipios_municipio_104.uf = 'SP'
    municipios_municipio_104.lat = Decimal('-22.7296000')
    municipios_municipio_104.lng = Decimal('-45.5833000')
    municipios_municipio_104 = importer.save_or_locate(municipios_municipio_104)

    municipios_municipio_105 = Municipio()
    municipios_municipio_105.cod_ibge = '3509908'
    municipios_municipio_105.nome = 'Cananéia'
    municipios_municipio_105.capital = False
    municipios_municipio_105.uf = 'SP'
    municipios_municipio_105.lat = Decimal('-25.0144000')
    municipios_municipio_105.lng = Decimal('-47.9341000')
    municipios_municipio_105 = importer.save_or_locate(municipios_municipio_105)

    municipios_municipio_106 = Municipio()
    municipios_municipio_106.cod_ibge = '3509957'
    municipios_municipio_106.nome = 'Canas'
    municipios_municipio_106.capital = False
    municipios_municipio_106.uf = 'SP'
    municipios_municipio_106.lat = Decimal('-22.7003000')
    municipios_municipio_106.lng = Decimal('-45.0521000')
    municipios_municipio_106 = importer.save_or_locate(municipios_municipio_106)

    municipios_municipio_107 = Municipio()
    municipios_municipio_107.cod_ibge = '3510153'
    municipios_municipio_107.nome = 'Canitar'
    municipios_municipio_107.capital = False
    municipios_municipio_107.uf = 'SP'
    municipios_municipio_107.lat = Decimal('-23.0040000')
    municipios_municipio_107.lng = Decimal('-49.7839000')
    municipios_municipio_107 = importer.save_or_locate(municipios_municipio_107)

    municipios_municipio_108 = Municipio()
    municipios_municipio_108.cod_ibge = '3510302'
    municipios_municipio_108.nome = 'Capela do Alto'
    municipios_municipio_108.capital = False
    municipios_municipio_108.uf = 'SP'
    municipios_municipio_108.lat = Decimal('-23.4685000')
    municipios_municipio_108.lng = Decimal('-47.7388000')
    municipios_municipio_108 = importer.save_or_locate(municipios_municipio_108)

    municipios_municipio_109 = Municipio()
    municipios_municipio_109.cod_ibge = '3510401'
    municipios_municipio_109.nome = 'Capivari'
    municipios_municipio_109.capital = False
    municipios_municipio_109.uf = 'SP'
    municipios_municipio_109.lat = Decimal('-22.9951000')
    municipios_municipio_109.lng = Decimal('-47.5071000')
    municipios_municipio_109 = importer.save_or_locate(municipios_municipio_109)

    municipios_municipio_110 = Municipio()
    municipios_municipio_110.cod_ibge = '3510203'
    municipios_municipio_110.nome = 'Capão Bonito'
    municipios_municipio_110.capital = False
    municipios_municipio_110.uf = 'SP'
    municipios_municipio_110.lat = Decimal('-24.0113000')
    municipios_municipio_110.lng = Decimal('-48.3482000')
    municipios_municipio_110 = importer.save_or_locate(municipios_municipio_110)

    municipios_municipio_111 = Municipio()
    municipios_municipio_111.cod_ibge = '3510500'
    municipios_municipio_111.nome = 'Caraguatatuba'
    municipios_municipio_111.capital = False
    municipios_municipio_111.uf = 'SP'
    municipios_municipio_111.lat = Decimal('-23.6125000')
    municipios_municipio_111.lng = Decimal('-45.4125000')
    municipios_municipio_111 = importer.save_or_locate(municipios_municipio_111)

    municipios_municipio_112 = Municipio()
    municipios_municipio_112.cod_ibge = '3510609'
    municipios_municipio_112.nome = 'Carapicuíba'
    municipios_municipio_112.capital = False
    municipios_municipio_112.uf = 'SP'
    municipios_municipio_112.lat = Decimal('-23.5235000')
    municipios_municipio_112.lng = Decimal('-46.8407000')
    municipios_municipio_112 = importer.save_or_locate(municipios_municipio_112)

    municipios_municipio_113 = Municipio()
    municipios_municipio_113.cod_ibge = '3510708'
    municipios_municipio_113.nome = 'Cardoso'
    municipios_municipio_113.capital = False
    municipios_municipio_113.uf = 'SP'
    municipios_municipio_113.lat = Decimal('-20.0800000')
    municipios_municipio_113.lng = Decimal('-49.9183000')
    municipios_municipio_113 = importer.save_or_locate(municipios_municipio_113)

    municipios_municipio_114 = Municipio()
    municipios_municipio_114.cod_ibge = '3510807'
    municipios_municipio_114.nome = 'Casa Branca'
    municipios_municipio_114.capital = False
    municipios_municipio_114.uf = 'SP'
    municipios_municipio_114.lat = Decimal('-21.7708000')
    municipios_municipio_114.lng = Decimal('-47.0852000')
    municipios_municipio_114 = importer.save_or_locate(municipios_municipio_114)

    municipios_municipio_115 = Municipio()
    municipios_municipio_115.cod_ibge = '3511003'
    municipios_municipio_115.nome = 'Castilho'
    municipios_municipio_115.capital = False
    municipios_municipio_115.uf = 'SP'
    municipios_municipio_115.lat = Decimal('-20.8689000')
    municipios_municipio_115.lng = Decimal('-51.4884000')
    municipios_municipio_115 = importer.save_or_locate(municipios_municipio_115)

    municipios_municipio_116 = Municipio()
    municipios_municipio_116.cod_ibge = '3511102'
    municipios_municipio_116.nome = 'Catanduva'
    municipios_municipio_116.capital = False
    municipios_municipio_116.uf = 'SP'
    municipios_municipio_116.lat = Decimal('-21.1314000')
    municipios_municipio_116.lng = Decimal('-48.9770000')
    municipios_municipio_116 = importer.save_or_locate(municipios_municipio_116)

    municipios_municipio_117 = Municipio()
    municipios_municipio_117.cod_ibge = '3511201'
    municipios_municipio_117.nome = 'Catiguá'
    municipios_municipio_117.capital = False
    municipios_municipio_117.uf = 'SP'
    municipios_municipio_117.lat = Decimal('-21.0519000')
    municipios_municipio_117.lng = Decimal('-49.0616000')
    municipios_municipio_117 = importer.save_or_locate(municipios_municipio_117)

    municipios_municipio_118 = Municipio()
    municipios_municipio_118.cod_ibge = '3508504'
    municipios_municipio_118.nome = 'Caçapava'
    municipios_municipio_118.capital = False
    municipios_municipio_118.uf = 'SP'
    municipios_municipio_118.lat = Decimal('-23.0992000')
    municipios_municipio_118.lng = Decimal('-45.7076000')
    municipios_municipio_118 = importer.save_or_locate(municipios_municipio_118)

    municipios_municipio_119 = Municipio()
    municipios_municipio_119.cod_ibge = '3511300'
    municipios_municipio_119.nome = 'Cedral'
    municipios_municipio_119.capital = False
    municipios_municipio_119.uf = 'SP'
    municipios_municipio_119.lat = Decimal('-20.9009000')
    municipios_municipio_119.lng = Decimal('-49.2664000')
    municipios_municipio_119 = importer.save_or_locate(municipios_municipio_119)

    municipios_municipio_120 = Municipio()
    municipios_municipio_120.cod_ibge = '3511409'
    municipios_municipio_120.nome = 'Cerqueira César'
    municipios_municipio_120.capital = False
    municipios_municipio_120.uf = 'SP'
    municipios_municipio_120.lat = Decimal('-23.0380000')
    municipios_municipio_120.lng = Decimal('-49.1655000')
    municipios_municipio_120 = importer.save_or_locate(municipios_municipio_120)

    municipios_municipio_121 = Municipio()
    municipios_municipio_121.cod_ibge = '3511508'
    municipios_municipio_121.nome = 'Cerquilho'
    municipios_municipio_121.capital = False
    municipios_municipio_121.uf = 'SP'
    municipios_municipio_121.lat = Decimal('-23.1665000')
    municipios_municipio_121.lng = Decimal('-47.7459000')
    municipios_municipio_121 = importer.save_or_locate(municipios_municipio_121)

    municipios_municipio_122 = Municipio()
    municipios_municipio_122.cod_ibge = '3511607'
    municipios_municipio_122.nome = 'Cesário Lange'
    municipios_municipio_122.capital = False
    municipios_municipio_122.uf = 'SP'
    municipios_municipio_122.lat = Decimal('-23.2260000')
    municipios_municipio_122.lng = Decimal('-47.9545000')
    municipios_municipio_122 = importer.save_or_locate(municipios_municipio_122)

    municipios_municipio_123 = Municipio()
    municipios_municipio_123.cod_ibge = '3511706'
    municipios_municipio_123.nome = 'Charqueada'
    municipios_municipio_123.capital = False
    municipios_municipio_123.uf = 'SP'
    municipios_municipio_123.lat = Decimal('-22.5096000')
    municipios_municipio_123.lng = Decimal('-47.7755000')
    municipios_municipio_123 = importer.save_or_locate(municipios_municipio_123)

    municipios_municipio_124 = Municipio()
    municipios_municipio_124.cod_ibge = '3557204'
    municipios_municipio_124.nome = 'Chavantes'
    municipios_municipio_124.capital = False
    municipios_municipio_124.uf = 'SP'
    municipios_municipio_124.lat = Decimal('-23.0366000')
    municipios_municipio_124.lng = Decimal('-49.7096000')
    municipios_municipio_124 = importer.save_or_locate(municipios_municipio_124)

    municipios_municipio_125 = Municipio()
    municipios_municipio_125.cod_ibge = '3511904'
    municipios_municipio_125.nome = 'Clementina'
    municipios_municipio_125.capital = False
    municipios_municipio_125.uf = 'SP'
    municipios_municipio_125.lat = Decimal('-21.5604000')
    municipios_municipio_125.lng = Decimal('-50.4525000')
    municipios_municipio_125 = importer.save_or_locate(municipios_municipio_125)

    municipios_municipio_126 = Municipio()
    municipios_municipio_126.cod_ibge = '3512001'
    municipios_municipio_126.nome = 'Colina'
    municipios_municipio_126.capital = False
    municipios_municipio_126.uf = 'SP'
    municipios_municipio_126.lat = Decimal('-20.7114000')
    municipios_municipio_126.lng = Decimal('-48.5387000')
    municipios_municipio_126 = importer.save_or_locate(municipios_municipio_126)

    municipios_municipio_127 = Municipio()
    municipios_municipio_127.cod_ibge = '3512100'
    municipios_municipio_127.nome = 'Colômbia'
    municipios_municipio_127.capital = False
    municipios_municipio_127.uf = 'SP'
    municipios_municipio_127.lat = Decimal('-20.1768000')
    municipios_municipio_127.lng = Decimal('-48.6865000')
    municipios_municipio_127 = importer.save_or_locate(municipios_municipio_127)

    municipios_municipio_128 = Municipio()
    municipios_municipio_128.cod_ibge = '3512209'
    municipios_municipio_128.nome = 'Conchal'
    municipios_municipio_128.capital = False
    municipios_municipio_128.uf = 'SP'
    municipios_municipio_128.lat = Decimal('-22.3375000')
    municipios_municipio_128.lng = Decimal('-47.1729000')
    municipios_municipio_128 = importer.save_or_locate(municipios_municipio_128)

    municipios_municipio_129 = Municipio()
    municipios_municipio_129.cod_ibge = '3512308'
    municipios_municipio_129.nome = 'Conchas'
    municipios_municipio_129.capital = False
    municipios_municipio_129.uf = 'SP'
    municipios_municipio_129.lat = Decimal('-23.0154000')
    municipios_municipio_129.lng = Decimal('-48.0134000')
    municipios_municipio_129 = importer.save_or_locate(municipios_municipio_129)

    municipios_municipio_130 = Municipio()
    municipios_municipio_130.cod_ibge = '3512407'
    municipios_municipio_130.nome = 'Cordeirópolis'
    municipios_municipio_130.capital = False
    municipios_municipio_130.uf = 'SP'
    municipios_municipio_130.lat = Decimal('-22.4778000')
    municipios_municipio_130.lng = Decimal('-47.4519000')
    municipios_municipio_130 = importer.save_or_locate(municipios_municipio_130)

    municipios_municipio_131 = Municipio()
    municipios_municipio_131.cod_ibge = '3512506'
    municipios_municipio_131.nome = 'Coroados'
    municipios_municipio_131.capital = False
    municipios_municipio_131.uf = 'SP'
    municipios_municipio_131.lat = Decimal('-21.3521000')
    municipios_municipio_131.lng = Decimal('-50.2859000')
    municipios_municipio_131 = importer.save_or_locate(municipios_municipio_131)

    municipios_municipio_132 = Municipio()
    municipios_municipio_132.cod_ibge = '3512605'
    municipios_municipio_132.nome = 'Coronel Macedo'
    municipios_municipio_132.capital = False
    municipios_municipio_132.uf = 'SP'
    municipios_municipio_132.lat = Decimal('-23.6261000')
    municipios_municipio_132.lng = Decimal('-49.3100000')
    municipios_municipio_132 = importer.save_or_locate(municipios_municipio_132)

    municipios_municipio_133 = Municipio()
    municipios_municipio_133.cod_ibge = '3512704'
    municipios_municipio_133.nome = 'Corumbataí'
    municipios_municipio_133.capital = False
    municipios_municipio_133.uf = 'SP'
    municipios_municipio_133.lat = Decimal('-22.2213000')
    municipios_municipio_133.lng = Decimal('-47.6215000')
    municipios_municipio_133 = importer.save_or_locate(municipios_municipio_133)

    municipios_municipio_134 = Municipio()
    municipios_municipio_134.cod_ibge = '3512902'
    municipios_municipio_134.nome = 'Cosmorama'
    municipios_municipio_134.capital = False
    municipios_municipio_134.uf = 'SP'
    municipios_municipio_134.lat = Decimal('-20.4755000')
    municipios_municipio_134.lng = Decimal('-49.7827000')
    municipios_municipio_134 = importer.save_or_locate(municipios_municipio_134)

    municipios_municipio_135 = Municipio()
    municipios_municipio_135.cod_ibge = '3512803'
    municipios_municipio_135.nome = 'Cosmópolis'
    municipios_municipio_135.capital = False
    municipios_municipio_135.uf = 'SP'
    municipios_municipio_135.lat = Decimal('-22.6419000')
    municipios_municipio_135.lng = Decimal('-47.1926000')
    municipios_municipio_135 = importer.save_or_locate(municipios_municipio_135)

    municipios_municipio_136 = Municipio()
    municipios_municipio_136.cod_ibge = '3513009'
    municipios_municipio_136.nome = 'Cotia'
    municipios_municipio_136.capital = False
    municipios_municipio_136.uf = 'SP'
    municipios_municipio_136.lat = Decimal('-23.6022000')
    municipios_municipio_136.lng = Decimal('-46.9190000')
    municipios_municipio_136 = importer.save_or_locate(municipios_municipio_136)

    municipios_municipio_137 = Municipio()
    municipios_municipio_137.cod_ibge = '3513108'
    municipios_municipio_137.nome = 'Cravinhos'
    municipios_municipio_137.capital = False
    municipios_municipio_137.uf = 'SP'
    municipios_municipio_137.lat = Decimal('-21.3380000')
    municipios_municipio_137.lng = Decimal('-47.7324000')
    municipios_municipio_137 = importer.save_or_locate(municipios_municipio_137)

    municipios_municipio_138 = Municipio()
    municipios_municipio_138.cod_ibge = '3513207'
    municipios_municipio_138.nome = 'Cristais Paulista'
    municipios_municipio_138.capital = False
    municipios_municipio_138.uf = 'SP'
    municipios_municipio_138.lat = Decimal('-20.4036000')
    municipios_municipio_138.lng = Decimal('-47.4209000')
    municipios_municipio_138 = importer.save_or_locate(municipios_municipio_138)

    municipios_municipio_139 = Municipio()
    municipios_municipio_139.cod_ibge = '3513405'
    municipios_municipio_139.nome = 'Cruzeiro'
    municipios_municipio_139.capital = False
    municipios_municipio_139.uf = 'SP'
    municipios_municipio_139.lat = Decimal('-22.5728000')
    municipios_municipio_139.lng = Decimal('-44.9690000')
    municipios_municipio_139 = importer.save_or_locate(municipios_municipio_139)

    municipios_municipio_140 = Municipio()
    municipios_municipio_140.cod_ibge = '3513306'
    municipios_municipio_140.nome = 'Cruzália'
    municipios_municipio_140.capital = False
    municipios_municipio_140.uf = 'SP'
    municipios_municipio_140.lat = Decimal('-22.7373000')
    municipios_municipio_140.lng = Decimal('-50.7909000')
    municipios_municipio_140 = importer.save_or_locate(municipios_municipio_140)

    municipios_municipio_141 = Municipio()
    municipios_municipio_141.cod_ibge = '3513504'
    municipios_municipio_141.nome = 'Cubatão'
    municipios_municipio_141.capital = False
    municipios_municipio_141.uf = 'SP'
    municipios_municipio_141.lat = Decimal('-23.8911000')
    municipios_municipio_141.lng = Decimal('-46.4240000')
    municipios_municipio_141 = importer.save_or_locate(municipios_municipio_141)

    municipios_municipio_142 = Municipio()
    municipios_municipio_142.cod_ibge = '3513603'
    municipios_municipio_142.nome = 'Cunha'
    municipios_municipio_142.capital = False
    municipios_municipio_142.uf = 'SP'
    municipios_municipio_142.lat = Decimal('-23.0731000')
    municipios_municipio_142.lng = Decimal('-44.9576000')
    municipios_municipio_142 = importer.save_or_locate(municipios_municipio_142)

    municipios_municipio_143 = Municipio()
    municipios_municipio_143.cod_ibge = '3510906'
    municipios_municipio_143.nome = 'Cássia dos Coqueiros'
    municipios_municipio_143.capital = False
    municipios_municipio_143.uf = 'SP'
    municipios_municipio_143.lat = Decimal('-21.2801000')
    municipios_municipio_143.lng = Decimal('-47.1643000')
    municipios_municipio_143 = importer.save_or_locate(municipios_municipio_143)

    municipios_municipio_144 = Municipio()
    municipios_municipio_144.cod_ibge = '3510005'
    municipios_municipio_144.nome = 'Cândido Mota'
    municipios_municipio_144.capital = False
    municipios_municipio_144.uf = 'SP'
    municipios_municipio_144.lat = Decimal('-22.7471000')
    municipios_municipio_144.lng = Decimal('-50.3873000')
    municipios_municipio_144 = importer.save_or_locate(municipios_municipio_144)

    municipios_municipio_145 = Municipio()
    municipios_municipio_145.cod_ibge = '3510104'
    municipios_municipio_145.nome = 'Cândido Rodrigues'
    municipios_municipio_145.capital = False
    municipios_municipio_145.uf = 'SP'
    municipios_municipio_145.lat = Decimal('-21.3275000')
    municipios_municipio_145.lng = Decimal('-48.6327000')
    municipios_municipio_145 = importer.save_or_locate(municipios_municipio_145)

    municipios_municipio_146 = Municipio()
    municipios_municipio_146.cod_ibge = '3513702'
    municipios_municipio_146.nome = 'Descalvado'
    municipios_municipio_146.capital = False
    municipios_municipio_146.uf = 'SP'
    municipios_municipio_146.lat = Decimal('-21.9002000')
    municipios_municipio_146.lng = Decimal('-47.6181000')
    municipios_municipio_146 = importer.save_or_locate(municipios_municipio_146)

    municipios_municipio_147 = Municipio()
    municipios_municipio_147.cod_ibge = '3513801'
    municipios_municipio_147.nome = 'Diadema'
    municipios_municipio_147.capital = False
    municipios_municipio_147.uf = 'SP'
    municipios_municipio_147.lat = Decimal('-23.6813000')
    municipios_municipio_147.lng = Decimal('-46.6205000')
    municipios_municipio_147 = importer.save_or_locate(municipios_municipio_147)

    municipios_municipio_148 = Municipio()
    municipios_municipio_148.cod_ibge = '3513850'
    municipios_municipio_148.nome = 'Dirce Reis'
    municipios_municipio_148.capital = False
    municipios_municipio_148.uf = 'SP'
    municipios_municipio_148.lat = Decimal('-20.4642000')
    municipios_municipio_148.lng = Decimal('-50.6073000')
    municipios_municipio_148 = importer.save_or_locate(municipios_municipio_148)

    municipios_municipio_149 = Municipio()
    municipios_municipio_149.cod_ibge = '3513900'
    municipios_municipio_149.nome = 'Divinolândia'
    municipios_municipio_149.capital = False
    municipios_municipio_149.uf = 'SP'
    municipios_municipio_149.lat = Decimal('-21.6637000')
    municipios_municipio_149.lng = Decimal('-46.7361000')
    municipios_municipio_149 = importer.save_or_locate(municipios_municipio_149)

    municipios_municipio_150 = Municipio()
    municipios_municipio_150.cod_ibge = '3514007'
    municipios_municipio_150.nome = 'Dobrada'
    municipios_municipio_150.capital = False
    municipios_municipio_150.uf = 'SP'
    municipios_municipio_150.lat = Decimal('-21.5155000')
    municipios_municipio_150.lng = Decimal('-48.3935000')
    municipios_municipio_150 = importer.save_or_locate(municipios_municipio_150)

    municipios_municipio_151 = Municipio()
    municipios_municipio_151.cod_ibge = '3514106'
    municipios_municipio_151.nome = 'Dois Córregos'
    municipios_municipio_151.capital = False
    municipios_municipio_151.uf = 'SP'
    municipios_municipio_151.lat = Decimal('-22.3673000')
    municipios_municipio_151.lng = Decimal('-48.3819000')
    municipios_municipio_151 = importer.save_or_locate(municipios_municipio_151)

    municipios_municipio_152 = Municipio()
    municipios_municipio_152.cod_ibge = '3514205'
    municipios_municipio_152.nome = 'Dolcinópolis'
    municipios_municipio_152.capital = False
    municipios_municipio_152.uf = 'SP'
    municipios_municipio_152.lat = Decimal('-20.1240000')
    municipios_municipio_152.lng = Decimal('-50.5149000')
    municipios_municipio_152 = importer.save_or_locate(municipios_municipio_152)

    municipios_municipio_153 = Municipio()
    municipios_municipio_153.cod_ibge = '3514304'
    municipios_municipio_153.nome = 'Dourado'
    municipios_municipio_153.capital = False
    municipios_municipio_153.uf = 'SP'
    municipios_municipio_153.lat = Decimal('-22.1044000')
    municipios_municipio_153.lng = Decimal('-48.3178000')
    municipios_municipio_153 = importer.save_or_locate(municipios_municipio_153)

    municipios_municipio_154 = Municipio()
    municipios_municipio_154.cod_ibge = '3514403'
    municipios_municipio_154.nome = 'Dracena'
    municipios_municipio_154.capital = False
    municipios_municipio_154.uf = 'SP'
    municipios_municipio_154.lat = Decimal('-21.4843000')
    municipios_municipio_154.lng = Decimal('-51.5350000')
    municipios_municipio_154 = importer.save_or_locate(municipios_municipio_154)

    municipios_municipio_155 = Municipio()
    municipios_municipio_155.cod_ibge = '3514502'
    municipios_municipio_155.nome = 'Duartina'
    municipios_municipio_155.capital = False
    municipios_municipio_155.uf = 'SP'
    municipios_municipio_155.lat = Decimal('-22.4146000')
    municipios_municipio_155.lng = Decimal('-49.4084000')
    municipios_municipio_155 = importer.save_or_locate(municipios_municipio_155)

    municipios_municipio_156 = Municipio()
    municipios_municipio_156.cod_ibge = '3514601'
    municipios_municipio_156.nome = 'Dumont'
    municipios_municipio_156.capital = False
    municipios_municipio_156.uf = 'SP'
    municipios_municipio_156.lat = Decimal('-21.2324000')
    municipios_municipio_156.lng = Decimal('-47.9756000')
    municipios_municipio_156 = importer.save_or_locate(municipios_municipio_156)

    municipios_municipio_157 = Municipio()
    municipios_municipio_157.cod_ibge = '3514700'
    municipios_municipio_157.nome = 'Echaporã'
    municipios_municipio_157.capital = False
    municipios_municipio_157.uf = 'SP'
    municipios_municipio_157.lat = Decimal('-22.4326000')
    municipios_municipio_157.lng = Decimal('-50.2038000')
    municipios_municipio_157 = importer.save_or_locate(municipios_municipio_157)

    municipios_municipio_158 = Municipio()
    municipios_municipio_158.cod_ibge = '3514809'
    municipios_municipio_158.nome = 'Eldorado'
    municipios_municipio_158.capital = False
    municipios_municipio_158.uf = 'SP'
    municipios_municipio_158.lat = Decimal('-24.5281000')
    municipios_municipio_158.lng = Decimal('-48.1141000')
    municipios_municipio_158 = importer.save_or_locate(municipios_municipio_158)

    municipios_municipio_159 = Municipio()
    municipios_municipio_159.cod_ibge = '3514908'
    municipios_municipio_159.nome = 'Elias Fausto'
    municipios_municipio_159.capital = False
    municipios_municipio_159.uf = 'SP'
    municipios_municipio_159.lat = Decimal('-23.0428000')
    municipios_municipio_159.lng = Decimal('-47.3682000')
    municipios_municipio_159 = importer.save_or_locate(municipios_municipio_159)

    municipios_municipio_160 = Municipio()
    municipios_municipio_160.cod_ibge = '3514924'
    municipios_municipio_160.nome = 'Elisiário'
    municipios_municipio_160.capital = False
    municipios_municipio_160.uf = 'SP'
    municipios_municipio_160.lat = Decimal('-21.1678000')
    municipios_municipio_160.lng = Decimal('-49.1146000')
    municipios_municipio_160 = importer.save_or_locate(municipios_municipio_160)

    municipios_municipio_161 = Municipio()
    municipios_municipio_161.cod_ibge = '3514957'
    municipios_municipio_161.nome = 'Embaúba'
    municipios_municipio_161.capital = False
    municipios_municipio_161.uf = 'SP'
    municipios_municipio_161.lat = Decimal('-20.9796000')
    municipios_municipio_161.lng = Decimal('-48.8325000')
    municipios_municipio_161 = importer.save_or_locate(municipios_municipio_161)

    municipios_municipio_162 = Municipio()
    municipios_municipio_162.cod_ibge = '3515004'
    municipios_municipio_162.nome = 'Embu das Artes'
    municipios_municipio_162.capital = False
    municipios_municipio_162.uf = 'SP'
    municipios_municipio_162.lat = Decimal('-23.6437000')
    municipios_municipio_162.lng = Decimal('-46.8579000')
    municipios_municipio_162 = importer.save_or_locate(municipios_municipio_162)

    municipios_municipio_163 = Municipio()
    municipios_municipio_163.cod_ibge = '3515103'
    municipios_municipio_163.nome = 'Embu-Guaçu'
    municipios_municipio_163.capital = False
    municipios_municipio_163.uf = 'SP'
    municipios_municipio_163.lat = Decimal('-23.8297000')
    municipios_municipio_163.lng = Decimal('-46.8136000')
    municipios_municipio_163 = importer.save_or_locate(municipios_municipio_163)

    municipios_municipio_164 = Municipio()
    municipios_municipio_164.cod_ibge = '3515129'
    municipios_municipio_164.nome = 'Emilianópolis'
    municipios_municipio_164.capital = False
    municipios_municipio_164.uf = 'SP'
    municipios_municipio_164.lat = Decimal('-21.8314000')
    municipios_municipio_164.lng = Decimal('-51.4832000')
    municipios_municipio_164 = importer.save_or_locate(municipios_municipio_164)

    municipios_municipio_165 = Municipio()
    municipios_municipio_165.cod_ibge = '3515152'
    municipios_municipio_165.nome = 'Engenheiro Coelho'
    municipios_municipio_165.capital = False
    municipios_municipio_165.uf = 'SP'
    municipios_municipio_165.lat = Decimal('-22.4836000')
    municipios_municipio_165.lng = Decimal('-47.2110000')
    municipios_municipio_165 = importer.save_or_locate(municipios_municipio_165)

    municipios_municipio_166 = Municipio()
    municipios_municipio_166.cod_ibge = '3515186'
    municipios_municipio_166.nome = 'Espírito Santo do Pinhal'
    municipios_municipio_166.capital = False
    municipios_municipio_166.uf = 'SP'
    municipios_municipio_166.lat = Decimal('-22.1909000')
    municipios_municipio_166.lng = Decimal('-46.7477000')
    municipios_municipio_166 = importer.save_or_locate(municipios_municipio_166)

    municipios_municipio_167 = Municipio()
    municipios_municipio_167.cod_ibge = '3515194'
    municipios_municipio_167.nome = 'Espírito Santo do Turvo'
    municipios_municipio_167.capital = False
    municipios_municipio_167.uf = 'SP'
    municipios_municipio_167.lat = Decimal('-22.6925000')
    municipios_municipio_167.lng = Decimal('-49.4341000')
    municipios_municipio_167 = importer.save_or_locate(municipios_municipio_167)

    municipios_municipio_168 = Municipio()
    municipios_municipio_168.cod_ibge = '3557303'
    municipios_municipio_168.nome = 'Estiva Gerbi'
    municipios_municipio_168.capital = False
    municipios_municipio_168.uf = 'SP'
    municipios_municipio_168.lat = Decimal('-22.2713000')
    municipios_municipio_168.lng = Decimal('-46.9481000')
    municipios_municipio_168 = importer.save_or_locate(municipios_municipio_168)

    municipios_municipio_169 = Municipio()
    municipios_municipio_169.cod_ibge = '3515202'
    municipios_municipio_169.nome = "Estrela d'Oeste"
    municipios_municipio_169.capital = False
    municipios_municipio_169.uf = 'SP'
    municipios_municipio_169.lat = Decimal('-20.2875000')
    municipios_municipio_169.lng = Decimal('-50.4049000')
    municipios_municipio_169 = importer.save_or_locate(municipios_municipio_169)

    municipios_municipio_170 = Municipio()
    municipios_municipio_170.cod_ibge = '3515301'
    municipios_municipio_170.nome = 'Estrela do Norte'
    municipios_municipio_170.capital = False
    municipios_municipio_170.uf = 'SP'
    municipios_municipio_170.lat = Decimal('-22.4859000')
    municipios_municipio_170.lng = Decimal('-51.6632000')
    municipios_municipio_170 = importer.save_or_locate(municipios_municipio_170)

    municipios_municipio_171 = Municipio()
    municipios_municipio_171.cod_ibge = '3515350'
    municipios_municipio_171.nome = 'Euclides da Cunha Paulista'
    municipios_municipio_171.capital = False
    municipios_municipio_171.uf = 'SP'
    municipios_municipio_171.lat = Decimal('-22.5545000')
    municipios_municipio_171.lng = Decimal('-52.5928000')
    municipios_municipio_171 = importer.save_or_locate(municipios_municipio_171)

    municipios_municipio_172 = Municipio()
    municipios_municipio_172.cod_ibge = '3515400'
    municipios_municipio_172.nome = 'Fartura'
    municipios_municipio_172.capital = False
    municipios_municipio_172.uf = 'SP'
    municipios_municipio_172.lat = Decimal('-23.3916000')
    municipios_municipio_172.lng = Decimal('-49.5124000')
    municipios_municipio_172 = importer.save_or_locate(municipios_municipio_172)

    municipios_municipio_173 = Municipio()
    municipios_municipio_173.cod_ibge = '3515608'
    municipios_municipio_173.nome = 'Fernando Prestes'
    municipios_municipio_173.capital = False
    municipios_municipio_173.uf = 'SP'
    municipios_municipio_173.lat = Decimal('-21.2661000')
    municipios_municipio_173.lng = Decimal('-48.6874000')
    municipios_municipio_173 = importer.save_or_locate(municipios_municipio_173)

    municipios_municipio_174 = Municipio()
    municipios_municipio_174.cod_ibge = '3515509'
    municipios_municipio_174.nome = 'Fernandópolis'
    municipios_municipio_174.capital = False
    municipios_municipio_174.uf = 'SP'
    municipios_municipio_174.lat = Decimal('-20.2806000')
    municipios_municipio_174.lng = Decimal('-50.2471000')
    municipios_municipio_174 = importer.save_or_locate(municipios_municipio_174)

    municipios_municipio_175 = Municipio()
    municipios_municipio_175.cod_ibge = '3515657'
    municipios_municipio_175.nome = 'Fernão'
    municipios_municipio_175.capital = False
    municipios_municipio_175.uf = 'SP'
    municipios_municipio_175.lat = Decimal('-22.3607000')
    municipios_municipio_175.lng = Decimal('-49.5187000')
    municipios_municipio_175 = importer.save_or_locate(municipios_municipio_175)

    municipios_municipio_176 = Municipio()
    municipios_municipio_176.cod_ibge = '3515707'
    municipios_municipio_176.nome = 'Ferraz de Vasconcelos'
    municipios_municipio_176.capital = False
    municipios_municipio_176.uf = 'SP'
    municipios_municipio_176.lat = Decimal('-23.5411000')
    municipios_municipio_176.lng = Decimal('-46.3710000')
    municipios_municipio_176 = importer.save_or_locate(municipios_municipio_176)

    municipios_municipio_177 = Municipio()
    municipios_municipio_177.cod_ibge = '3515806'
    municipios_municipio_177.nome = 'Flora Rica'
    municipios_municipio_177.capital = False
    municipios_municipio_177.uf = 'SP'
    municipios_municipio_177.lat = Decimal('-21.6727000')
    municipios_municipio_177.lng = Decimal('-51.3821000')
    municipios_municipio_177 = importer.save_or_locate(municipios_municipio_177)

    municipios_municipio_178 = Municipio()
    municipios_municipio_178.cod_ibge = '3515905'
    municipios_municipio_178.nome = 'Floreal'
    municipios_municipio_178.capital = False
    municipios_municipio_178.uf = 'SP'
    municipios_municipio_178.lat = Decimal('-20.6752000')
    municipios_municipio_178.lng = Decimal('-50.1513000')
    municipios_municipio_178 = importer.save_or_locate(municipios_municipio_178)

    municipios_municipio_179 = Municipio()
    municipios_municipio_179.cod_ibge = '3516101'
    municipios_municipio_179.nome = 'Florínia'
    municipios_municipio_179.capital = False
    municipios_municipio_179.uf = 'SP'
    municipios_municipio_179.lat = Decimal('-22.8680000')
    municipios_municipio_179.lng = Decimal('-50.6814000')
    municipios_municipio_179 = importer.save_or_locate(municipios_municipio_179)

    municipios_municipio_180 = Municipio()
    municipios_municipio_180.cod_ibge = '3516002'
    municipios_municipio_180.nome = 'Flórida Paulista'
    municipios_municipio_180.capital = False
    municipios_municipio_180.uf = 'SP'
    municipios_municipio_180.lat = Decimal('-21.6127000')
    municipios_municipio_180.lng = Decimal('-51.1724000')
    municipios_municipio_180 = importer.save_or_locate(municipios_municipio_180)

    municipios_municipio_181 = Municipio()
    municipios_municipio_181.cod_ibge = '3516200'
    municipios_municipio_181.nome = 'Franca'
    municipios_municipio_181.capital = False
    municipios_municipio_181.uf = 'SP'
    municipios_municipio_181.lat = Decimal('-20.5352000')
    municipios_municipio_181.lng = Decimal('-47.4039000')
    municipios_municipio_181 = importer.save_or_locate(municipios_municipio_181)

    municipios_municipio_182 = Municipio()
    municipios_municipio_182.cod_ibge = '3516309'
    municipios_municipio_182.nome = 'Francisco Morato'
    municipios_municipio_182.capital = False
    municipios_municipio_182.uf = 'SP'
    municipios_municipio_182.lat = Decimal('-23.2792000')
    municipios_municipio_182.lng = Decimal('-46.7448000')
    municipios_municipio_182 = importer.save_or_locate(municipios_municipio_182)

    municipios_municipio_183 = Municipio()
    municipios_municipio_183.cod_ibge = '3516408'
    municipios_municipio_183.nome = 'Franco da Rocha'
    municipios_municipio_183.capital = False
    municipios_municipio_183.uf = 'SP'
    municipios_municipio_183.lat = Decimal('-23.3229000')
    municipios_municipio_183.lng = Decimal('-46.7290000')
    municipios_municipio_183 = importer.save_or_locate(municipios_municipio_183)

    municipios_municipio_184 = Municipio()
    municipios_municipio_184.cod_ibge = '3516507'
    municipios_municipio_184.nome = 'Gabriel Monteiro'
    municipios_municipio_184.capital = False
    municipios_municipio_184.uf = 'SP'
    municipios_municipio_184.lat = Decimal('-21.5294000')
    municipios_municipio_184.lng = Decimal('-50.5573000')
    municipios_municipio_184 = importer.save_or_locate(municipios_municipio_184)

    municipios_municipio_185 = Municipio()
    municipios_municipio_185.cod_ibge = '3516705'
    municipios_municipio_185.nome = 'Garça'
    municipios_municipio_185.capital = False
    municipios_municipio_185.uf = 'SP'
    municipios_municipio_185.lat = Decimal('-22.2125000')
    municipios_municipio_185.lng = Decimal('-49.6546000')
    municipios_municipio_185 = importer.save_or_locate(municipios_municipio_185)

    municipios_municipio_186 = Municipio()
    municipios_municipio_186.cod_ibge = '3516804'
    municipios_municipio_186.nome = 'Gastão Vidigal'
    municipios_municipio_186.capital = False
    municipios_municipio_186.uf = 'SP'
    municipios_municipio_186.lat = Decimal('-20.7948000')
    municipios_municipio_186.lng = Decimal('-50.1912000')
    municipios_municipio_186 = importer.save_or_locate(municipios_municipio_186)

    municipios_municipio_187 = Municipio()
    municipios_municipio_187.cod_ibge = '3516853'
    municipios_municipio_187.nome = 'Gavião Peixoto'
    municipios_municipio_187.capital = False
    municipios_municipio_187.uf = 'SP'
    municipios_municipio_187.lat = Decimal('-21.8367000')
    municipios_municipio_187.lng = Decimal('-48.4957000')
    municipios_municipio_187 = importer.save_or_locate(municipios_municipio_187)

    municipios_municipio_188 = Municipio()
    municipios_municipio_188.cod_ibge = '3516903'
    municipios_municipio_188.nome = 'General Salgado'
    municipios_municipio_188.capital = False
    municipios_municipio_188.uf = 'SP'
    municipios_municipio_188.lat = Decimal('-20.6485000')
    municipios_municipio_188.lng = Decimal('-50.3640000')
    municipios_municipio_188 = importer.save_or_locate(municipios_municipio_188)

    municipios_municipio_189 = Municipio()
    municipios_municipio_189.cod_ibge = '3517000'
    municipios_municipio_189.nome = 'Getulina'
    municipios_municipio_189.capital = False
    municipios_municipio_189.uf = 'SP'
    municipios_municipio_189.lat = Decimal('-21.7961000')
    municipios_municipio_189.lng = Decimal('-49.9312000')
    municipios_municipio_189 = importer.save_or_locate(municipios_municipio_189)

    municipios_municipio_190 = Municipio()
    municipios_municipio_190.cod_ibge = '3517109'
    municipios_municipio_190.nome = 'Glicério'
    municipios_municipio_190.capital = False
    municipios_municipio_190.uf = 'SP'
    municipios_municipio_190.lat = Decimal('-21.3812000')
    municipios_municipio_190.lng = Decimal('-50.2123000')
    municipios_municipio_190 = importer.save_or_locate(municipios_municipio_190)

    municipios_municipio_191 = Municipio()
    municipios_municipio_191.cod_ibge = '3517307'
    municipios_municipio_191.nome = 'Guaimbê'
    municipios_municipio_191.capital = False
    municipios_municipio_191.uf = 'SP'
    municipios_municipio_191.lat = Decimal('-21.9091000')
    municipios_municipio_191.lng = Decimal('-49.8986000')
    municipios_municipio_191 = importer.save_or_locate(municipios_municipio_191)

    municipios_municipio_192 = Municipio()
    municipios_municipio_192.cod_ibge = '3517208'
    municipios_municipio_192.nome = 'Guaiçara'
    municipios_municipio_192.capital = False
    municipios_municipio_192.uf = 'SP'
    municipios_municipio_192.lat = Decimal('-21.6195000')
    municipios_municipio_192.lng = Decimal('-49.8013000')
    municipios_municipio_192 = importer.save_or_locate(municipios_municipio_192)

    municipios_municipio_193 = Municipio()
    municipios_municipio_193.cod_ibge = '3517604'
    municipios_municipio_193.nome = 'Guapiara'
    municipios_municipio_193.capital = False
    municipios_municipio_193.uf = 'SP'
    municipios_municipio_193.lat = Decimal('-24.1892000')
    municipios_municipio_193.lng = Decimal('-48.5295000')
    municipios_municipio_193 = importer.save_or_locate(municipios_municipio_193)

    municipios_municipio_194 = Municipio()
    municipios_municipio_194.cod_ibge = '3517505'
    municipios_municipio_194.nome = 'Guapiaçu'
    municipios_municipio_194.capital = False
    municipios_municipio_194.uf = 'SP'
    municipios_municipio_194.lat = Decimal('-20.7959000')
    municipios_municipio_194.lng = Decimal('-49.2172000')
    municipios_municipio_194 = importer.save_or_locate(municipios_municipio_194)

    municipios_municipio_195 = Municipio()
    municipios_municipio_195.cod_ibge = '3517901'
    municipios_municipio_195.nome = 'Guaraci'
    municipios_municipio_195.capital = False
    municipios_municipio_195.uf = 'SP'
    municipios_municipio_195.lat = Decimal('-20.4977000')
    municipios_municipio_195.lng = Decimal('-48.9391000')
    municipios_municipio_195 = importer.save_or_locate(municipios_municipio_195)

    municipios_municipio_196 = Municipio()
    municipios_municipio_196.cod_ibge = '3518008'
    municipios_municipio_196.nome = "Guarani d'Oeste"
    municipios_municipio_196.capital = False
    municipios_municipio_196.uf = 'SP'
    municipios_municipio_196.lat = Decimal('-20.0746000')
    municipios_municipio_196.lng = Decimal('-50.3411000')
    municipios_municipio_196 = importer.save_or_locate(municipios_municipio_196)

    municipios_municipio_197 = Municipio()
    municipios_municipio_197.cod_ibge = '3518107'
    municipios_municipio_197.nome = 'Guarantã'
    municipios_municipio_197.capital = False
    municipios_municipio_197.uf = 'SP'
    municipios_municipio_197.lat = Decimal('-21.8942000')
    municipios_municipio_197.lng = Decimal('-49.5914000')
    municipios_municipio_197 = importer.save_or_locate(municipios_municipio_197)

    municipios_municipio_198 = Municipio()
    municipios_municipio_198.cod_ibge = '3518206'
    municipios_municipio_198.nome = 'Guararapes'
    municipios_municipio_198.capital = False
    municipios_municipio_198.uf = 'SP'
    municipios_municipio_198.lat = Decimal('-21.2544000')
    municipios_municipio_198.lng = Decimal('-50.6453000')
    municipios_municipio_198 = importer.save_or_locate(municipios_municipio_198)

    municipios_municipio_199 = Municipio()
    municipios_municipio_199.cod_ibge = '3518305'
    municipios_municipio_199.nome = 'Guararema'
    municipios_municipio_199.capital = False
    municipios_municipio_199.uf = 'SP'
    municipios_municipio_199.lat = Decimal('-23.4112000')
    municipios_municipio_199.lng = Decimal('-46.0369000')
    municipios_municipio_199 = importer.save_or_locate(municipios_municipio_199)

    municipios_municipio_200 = Municipio()
    municipios_municipio_200.cod_ibge = '3518404'
    municipios_municipio_200.nome = 'Guaratinguetá'
    municipios_municipio_200.capital = False
    municipios_municipio_200.uf = 'SP'
    municipios_municipio_200.lat = Decimal('-22.8075000')
    municipios_municipio_200.lng = Decimal('-45.1938000')
    municipios_municipio_200 = importer.save_or_locate(municipios_municipio_200)

    municipios_municipio_201 = Municipio()
    municipios_municipio_201.cod_ibge = '3517802'
    municipios_municipio_201.nome = 'Guaraçaí'
    municipios_municipio_201.capital = False
    municipios_municipio_201.uf = 'SP'
    municipios_municipio_201.lat = Decimal('-21.0292000')
    municipios_municipio_201.lng = Decimal('-51.2119000')
    municipios_municipio_201 = importer.save_or_locate(municipios_municipio_201)

    municipios_municipio_202 = Municipio()
    municipios_municipio_202.cod_ibge = '3518503'
    municipios_municipio_202.nome = 'Guareí'
    municipios_municipio_202.capital = False
    municipios_municipio_202.uf = 'SP'
    municipios_municipio_202.lat = Decimal('-23.3714000')
    municipios_municipio_202.lng = Decimal('-48.1837000')
    municipios_municipio_202 = importer.save_or_locate(municipios_municipio_202)

    municipios_municipio_203 = Municipio()
    municipios_municipio_203.cod_ibge = '3518602'
    municipios_municipio_203.nome = 'Guariba'
    municipios_municipio_203.capital = False
    municipios_municipio_203.uf = 'SP'
    municipios_municipio_203.lat = Decimal('-21.3594000')
    municipios_municipio_203.lng = Decimal('-48.2316000')
    municipios_municipio_203 = importer.save_or_locate(municipios_municipio_203)

    municipios_municipio_204 = Municipio()
    municipios_municipio_204.cod_ibge = '3518701'
    municipios_municipio_204.nome = 'Guarujá'
    municipios_municipio_204.capital = False
    municipios_municipio_204.uf = 'SP'
    municipios_municipio_204.lat = Decimal('-23.9888000')
    municipios_municipio_204.lng = Decimal('-46.2580000')
    municipios_municipio_204 = importer.save_or_locate(municipios_municipio_204)

    municipios_municipio_205 = Municipio()
    municipios_municipio_205.cod_ibge = '3518800'
    municipios_municipio_205.nome = 'Guarulhos'
    municipios_municipio_205.capital = False
    municipios_municipio_205.uf = 'SP'
    municipios_municipio_205.lat = Decimal('-23.4538000')
    municipios_municipio_205.lng = Decimal('-46.5333000')
    municipios_municipio_205 = importer.save_or_locate(municipios_municipio_205)

    municipios_municipio_206 = Municipio()
    municipios_municipio_206.cod_ibge = '3517703'
    municipios_municipio_206.nome = 'Guará'
    municipios_municipio_206.capital = False
    municipios_municipio_206.uf = 'SP'
    municipios_municipio_206.lat = Decimal('-20.4302000')
    municipios_municipio_206.lng = Decimal('-47.8236000')
    municipios_municipio_206 = importer.save_or_locate(municipios_municipio_206)

    municipios_municipio_207 = Municipio()
    municipios_municipio_207.cod_ibge = '3518859'
    municipios_municipio_207.nome = 'Guatapará'
    municipios_municipio_207.capital = False
    municipios_municipio_207.uf = 'SP'
    municipios_municipio_207.lat = Decimal('-21.4944000')
    municipios_municipio_207.lng = Decimal('-48.0356000')
    municipios_municipio_207 = importer.save_or_locate(municipios_municipio_207)

    municipios_municipio_208 = Municipio()
    municipios_municipio_208.cod_ibge = '3517406'
    municipios_municipio_208.nome = 'Guaíra'
    municipios_municipio_208.capital = False
    municipios_municipio_208.uf = 'SP'
    municipios_municipio_208.lat = Decimal('-20.3196000')
    municipios_municipio_208.lng = Decimal('-48.3120000')
    municipios_municipio_208 = importer.save_or_locate(municipios_municipio_208)

    municipios_municipio_209 = Municipio()
    municipios_municipio_209.cod_ibge = '3518909'
    municipios_municipio_209.nome = 'Guzolândia'
    municipios_municipio_209.capital = False
    municipios_municipio_209.uf = 'SP'
    municipios_municipio_209.lat = Decimal('-20.6467000')
    municipios_municipio_209.lng = Decimal('-50.6645000')
    municipios_municipio_209 = importer.save_or_locate(municipios_municipio_209)

    municipios_municipio_210 = Municipio()
    municipios_municipio_210.cod_ibge = '3516606'
    municipios_municipio_210.nome = 'Gália'
    municipios_municipio_210.capital = False
    municipios_municipio_210.uf = 'SP'
    municipios_municipio_210.lat = Decimal('-22.2918000')
    municipios_municipio_210.lng = Decimal('-49.5504000')
    municipios_municipio_210 = importer.save_or_locate(municipios_municipio_210)

    municipios_municipio_211 = Municipio()
    municipios_municipio_211.cod_ibge = '3519006'
    municipios_municipio_211.nome = 'Herculândia'
    municipios_municipio_211.capital = False
    municipios_municipio_211.uf = 'SP'
    municipios_municipio_211.lat = Decimal('-22.0038000')
    municipios_municipio_211.lng = Decimal('-50.3907000')
    municipios_municipio_211 = importer.save_or_locate(municipios_municipio_211)

    municipios_municipio_212 = Municipio()
    municipios_municipio_212.cod_ibge = '3519055'
    municipios_municipio_212.nome = 'Holambra'
    municipios_municipio_212.capital = False
    municipios_municipio_212.uf = 'SP'
    municipios_municipio_212.lat = Decimal('-22.6405000')
    municipios_municipio_212.lng = Decimal('-47.0487000')
    municipios_municipio_212 = importer.save_or_locate(municipios_municipio_212)

    municipios_municipio_213 = Municipio()
    municipios_municipio_213.cod_ibge = '3519071'
    municipios_municipio_213.nome = 'Hortolândia'
    municipios_municipio_213.capital = False
    municipios_municipio_213.uf = 'SP'
    municipios_municipio_213.lat = Decimal('-22.8529000')
    municipios_municipio_213.lng = Decimal('-47.2143000')
    municipios_municipio_213 = importer.save_or_locate(municipios_municipio_213)

    municipios_municipio_214 = Municipio()
    municipios_municipio_214.cod_ibge = '3519105'
    municipios_municipio_214.nome = 'Iacanga'
    municipios_municipio_214.capital = False
    municipios_municipio_214.uf = 'SP'
    municipios_municipio_214.lat = Decimal('-21.8896000')
    municipios_municipio_214.lng = Decimal('-49.0310000')
    municipios_municipio_214 = importer.save_or_locate(municipios_municipio_214)

    municipios_municipio_215 = Municipio()
    municipios_municipio_215.cod_ibge = '3519204'
    municipios_municipio_215.nome = 'Iacri'
    municipios_municipio_215.capital = False
    municipios_municipio_215.uf = 'SP'
    municipios_municipio_215.lat = Decimal('-21.8572000')
    municipios_municipio_215.lng = Decimal('-50.6932000')
    municipios_municipio_215 = importer.save_or_locate(municipios_municipio_215)

    municipios_municipio_216 = Municipio()
    municipios_municipio_216.cod_ibge = '3519253'
    municipios_municipio_216.nome = 'Iaras'
    municipios_municipio_216.capital = False
    municipios_municipio_216.uf = 'SP'
    municipios_municipio_216.lat = Decimal('-22.8682000')
    municipios_municipio_216.lng = Decimal('-49.1634000')
    municipios_municipio_216 = importer.save_or_locate(municipios_municipio_216)

    municipios_municipio_217 = Municipio()
    municipios_municipio_217.cod_ibge = '3519303'
    municipios_municipio_217.nome = 'Ibaté'
    municipios_municipio_217.capital = False
    municipios_municipio_217.uf = 'SP'
    municipios_municipio_217.lat = Decimal('-21.9584000')
    municipios_municipio_217.lng = Decimal('-47.9882000')
    municipios_municipio_217 = importer.save_or_locate(municipios_municipio_217)

    municipios_municipio_218 = Municipio()
    municipios_municipio_218.cod_ibge = '3519501'
    municipios_municipio_218.nome = 'Ibirarema'
    municipios_municipio_218.capital = False
    municipios_municipio_218.uf = 'SP'
    municipios_municipio_218.lat = Decimal('-22.8185000')
    municipios_municipio_218.lng = Decimal('-50.0739000')
    municipios_municipio_218 = importer.save_or_locate(municipios_municipio_218)

    municipios_municipio_219 = Municipio()
    municipios_municipio_219.cod_ibge = '3519402'
    municipios_municipio_219.nome = 'Ibirá'
    municipios_municipio_219.capital = False
    municipios_municipio_219.uf = 'SP'
    municipios_municipio_219.lat = Decimal('-21.0830000')
    municipios_municipio_219.lng = Decimal('-49.2448000')
    municipios_municipio_219 = importer.save_or_locate(municipios_municipio_219)

    municipios_municipio_220 = Municipio()
    municipios_municipio_220.cod_ibge = '3519600'
    municipios_municipio_220.nome = 'Ibitinga'
    municipios_municipio_220.capital = False
    municipios_municipio_220.uf = 'SP'
    municipios_municipio_220.lat = Decimal('-21.7562000')
    municipios_municipio_220.lng = Decimal('-48.8319000')
    municipios_municipio_220 = importer.save_or_locate(municipios_municipio_220)

    municipios_municipio_221 = Municipio()
    municipios_municipio_221.cod_ibge = '3519709'
    municipios_municipio_221.nome = 'Ibiúna'
    municipios_municipio_221.capital = False
    municipios_municipio_221.uf = 'SP'
    municipios_municipio_221.lat = Decimal('-23.6596000')
    municipios_municipio_221.lng = Decimal('-47.2230000')
    municipios_municipio_221 = importer.save_or_locate(municipios_municipio_221)

    municipios_municipio_222 = Municipio()
    municipios_municipio_222.cod_ibge = '3519808'
    municipios_municipio_222.nome = 'Icém'
    municipios_municipio_222.capital = False
    municipios_municipio_222.uf = 'SP'
    municipios_municipio_222.lat = Decimal('-20.3391000')
    municipios_municipio_222.lng = Decimal('-49.1915000')
    municipios_municipio_222 = importer.save_or_locate(municipios_municipio_222)

    municipios_municipio_223 = Municipio()
    municipios_municipio_223.cod_ibge = '3519907'
    municipios_municipio_223.nome = 'Iepê'
    municipios_municipio_223.capital = False
    municipios_municipio_223.uf = 'SP'
    municipios_municipio_223.lat = Decimal('-22.6602000')
    municipios_municipio_223.lng = Decimal('-51.0779000')
    municipios_municipio_223 = importer.save_or_locate(municipios_municipio_223)

    municipios_municipio_224 = Municipio()
    municipios_municipio_224.cod_ibge = '3520103'
    municipios_municipio_224.nome = 'Igarapava'
    municipios_municipio_224.capital = False
    municipios_municipio_224.uf = 'SP'
    municipios_municipio_224.lat = Decimal('-20.0407000')
    municipios_municipio_224.lng = Decimal('-47.7466000')
    municipios_municipio_224 = importer.save_or_locate(municipios_municipio_224)

    municipios_municipio_225 = Municipio()
    municipios_municipio_225.cod_ibge = '3520202'
    municipios_municipio_225.nome = 'Igaratá'
    municipios_municipio_225.capital = False
    municipios_municipio_225.uf = 'SP'
    municipios_municipio_225.lat = Decimal('-23.2037000')
    municipios_municipio_225.lng = Decimal('-46.1570000')
    municipios_municipio_225 = importer.save_or_locate(municipios_municipio_225)

    municipios_municipio_226 = Municipio()
    municipios_municipio_226.cod_ibge = '3520004'
    municipios_municipio_226.nome = 'Igaraçu do Tietê'
    municipios_municipio_226.capital = False
    municipios_municipio_226.uf = 'SP'
    municipios_municipio_226.lat = Decimal('-22.5090000')
    municipios_municipio_226.lng = Decimal('-48.5597000')
    municipios_municipio_226 = importer.save_or_locate(municipios_municipio_226)

    municipios_municipio_227 = Municipio()
    municipios_municipio_227.cod_ibge = '3520301'
    municipios_municipio_227.nome = 'Iguape'
    municipios_municipio_227.capital = False
    municipios_municipio_227.uf = 'SP'
    municipios_municipio_227.lat = Decimal('-24.6990000')
    municipios_municipio_227.lng = Decimal('-47.5537000')
    municipios_municipio_227 = importer.save_or_locate(municipios_municipio_227)

    municipios_municipio_228 = Municipio()
    municipios_municipio_228.cod_ibge = '3520426'
    municipios_municipio_228.nome = 'Ilha Comprida'
    municipios_municipio_228.capital = False
    municipios_municipio_228.uf = 'SP'
    municipios_municipio_228.lat = Decimal('-24.7307000')
    municipios_municipio_228.lng = Decimal('-47.5383000')
    municipios_municipio_228 = importer.save_or_locate(municipios_municipio_228)

    municipios_municipio_229 = Municipio()
    municipios_municipio_229.cod_ibge = '3520442'
    municipios_municipio_229.nome = 'Ilha Solteira'
    municipios_municipio_229.capital = False
    municipios_municipio_229.uf = 'SP'
    municipios_municipio_229.lat = Decimal('-20.4326000')
    municipios_municipio_229.lng = Decimal('-51.3426000')
    municipios_municipio_229 = importer.save_or_locate(municipios_municipio_229)

    municipios_municipio_230 = Municipio()
    municipios_municipio_230.cod_ibge = '3520400'
    municipios_municipio_230.nome = 'Ilhabela'
    municipios_municipio_230.capital = False
    municipios_municipio_230.uf = 'SP'
    municipios_municipio_230.lat = Decimal('-23.7785000')
    municipios_municipio_230.lng = Decimal('-45.3552000')
    municipios_municipio_230 = importer.save_or_locate(municipios_municipio_230)

    municipios_municipio_231 = Municipio()
    municipios_municipio_231.cod_ibge = '3520509'
    municipios_municipio_231.nome = 'Indaiatuba'
    municipios_municipio_231.capital = False
    municipios_municipio_231.uf = 'SP'
    municipios_municipio_231.lat = Decimal('-23.0816000')
    municipios_municipio_231.lng = Decimal('-47.2101000')
    municipios_municipio_231 = importer.save_or_locate(municipios_municipio_231)

    municipios_municipio_232 = Municipio()
    municipios_municipio_232.cod_ibge = '3520608'
    municipios_municipio_232.nome = 'Indiana'
    municipios_municipio_232.capital = False
    municipios_municipio_232.uf = 'SP'
    municipios_municipio_232.lat = Decimal('-22.1738000')
    municipios_municipio_232.lng = Decimal('-51.2555000')
    municipios_municipio_232 = importer.save_or_locate(municipios_municipio_232)

    municipios_municipio_233 = Municipio()
    municipios_municipio_233.cod_ibge = '3520707'
    municipios_municipio_233.nome = 'Indiaporã'
    municipios_municipio_233.capital = False
    municipios_municipio_233.uf = 'SP'
    municipios_municipio_233.lat = Decimal('-19.9790000')
    municipios_municipio_233.lng = Decimal('-50.2909000')
    municipios_municipio_233 = importer.save_or_locate(municipios_municipio_233)

    municipios_municipio_234 = Municipio()
    municipios_municipio_234.cod_ibge = '3520806'
    municipios_municipio_234.nome = 'Inúbia Paulista'
    municipios_municipio_234.capital = False
    municipios_municipio_234.uf = 'SP'
    municipios_municipio_234.lat = Decimal('-21.7695000')
    municipios_municipio_234.lng = Decimal('-50.9633000')
    municipios_municipio_234 = importer.save_or_locate(municipios_municipio_234)

    municipios_municipio_235 = Municipio()
    municipios_municipio_235.cod_ibge = '3520905'
    municipios_municipio_235.nome = 'Ipaussu'
    municipios_municipio_235.capital = False
    municipios_municipio_235.uf = 'SP'
    municipios_municipio_235.lat = Decimal('-23.0575000')
    municipios_municipio_235.lng = Decimal('-49.6279000')
    municipios_municipio_235 = importer.save_or_locate(municipios_municipio_235)

    municipios_municipio_236 = Municipio()
    municipios_municipio_236.cod_ibge = '3521002'
    municipios_municipio_236.nome = 'Iperó'
    municipios_municipio_236.capital = False
    municipios_municipio_236.uf = 'SP'
    municipios_municipio_236.lat = Decimal('-23.3513000')
    municipios_municipio_236.lng = Decimal('-47.6927000')
    municipios_municipio_236 = importer.save_or_locate(municipios_municipio_236)

    municipios_municipio_237 = Municipio()
    municipios_municipio_237.cod_ibge = '3521101'
    municipios_municipio_237.nome = 'Ipeúna'
    municipios_municipio_237.capital = False
    municipios_municipio_237.uf = 'SP'
    municipios_municipio_237.lat = Decimal('-22.4355000')
    municipios_municipio_237.lng = Decimal('-47.7151000')
    municipios_municipio_237 = importer.save_or_locate(municipios_municipio_237)

    municipios_municipio_238 = Municipio()
    municipios_municipio_238.cod_ibge = '3521150'
    municipios_municipio_238.nome = 'Ipiguá'
    municipios_municipio_238.capital = False
    municipios_municipio_238.uf = 'SP'
    municipios_municipio_238.lat = Decimal('-20.6557000')
    municipios_municipio_238.lng = Decimal('-49.3842000')
    municipios_municipio_238 = importer.save_or_locate(municipios_municipio_238)

    municipios_municipio_239 = Municipio()
    municipios_municipio_239.cod_ibge = '3521200'
    municipios_municipio_239.nome = 'Iporanga'
    municipios_municipio_239.capital = False
    municipios_municipio_239.uf = 'SP'
    municipios_municipio_239.lat = Decimal('-24.5847000')
    municipios_municipio_239.lng = Decimal('-48.5971000')
    municipios_municipio_239 = importer.save_or_locate(municipios_municipio_239)

    municipios_municipio_240 = Municipio()
    municipios_municipio_240.cod_ibge = '3521309'
    municipios_municipio_240.nome = 'Ipuã'
    municipios_municipio_240.capital = False
    municipios_municipio_240.uf = 'SP'
    municipios_municipio_240.lat = Decimal('-20.4438000')
    municipios_municipio_240.lng = Decimal('-48.0129000')
    municipios_municipio_240 = importer.save_or_locate(municipios_municipio_240)

    municipios_municipio_241 = Municipio()
    municipios_municipio_241.cod_ibge = '3521408'
    municipios_municipio_241.nome = 'Iracemápolis'
    municipios_municipio_241.capital = False
    municipios_municipio_241.uf = 'SP'
    municipios_municipio_241.lat = Decimal('-22.5832000')
    municipios_municipio_241.lng = Decimal('-47.5230000')
    municipios_municipio_241 = importer.save_or_locate(municipios_municipio_241)

    municipios_municipio_242 = Municipio()
    municipios_municipio_242.cod_ibge = '3521606'
    municipios_municipio_242.nome = 'Irapuru'
    municipios_municipio_242.capital = False
    municipios_municipio_242.uf = 'SP'
    municipios_municipio_242.lat = Decimal('-21.5684000')
    municipios_municipio_242.lng = Decimal('-51.3472000')
    municipios_municipio_242 = importer.save_or_locate(municipios_municipio_242)

    municipios_municipio_243 = Municipio()
    municipios_municipio_243.cod_ibge = '3521507'
    municipios_municipio_243.nome = 'Irapuã'
    municipios_municipio_243.capital = False
    municipios_municipio_243.uf = 'SP'
    municipios_municipio_243.lat = Decimal('-21.2768000')
    municipios_municipio_243.lng = Decimal('-49.4164000')
    municipios_municipio_243 = importer.save_or_locate(municipios_municipio_243)

    municipios_municipio_244 = Municipio()
    municipios_municipio_244.cod_ibge = '3521705'
    municipios_municipio_244.nome = 'Itaberá'
    municipios_municipio_244.capital = False
    municipios_municipio_244.uf = 'SP'
    municipios_municipio_244.lat = Decimal('-23.8638000')
    municipios_municipio_244.lng = Decimal('-49.1400000')
    municipios_municipio_244 = importer.save_or_locate(municipios_municipio_244)

    municipios_municipio_245 = Municipio()
    municipios_municipio_245.cod_ibge = '3521903'
    municipios_municipio_245.nome = 'Itajobi'
    municipios_municipio_245.capital = False
    municipios_municipio_245.uf = 'SP'
    municipios_municipio_245.lat = Decimal('-21.3123000')
    municipios_municipio_245.lng = Decimal('-49.0629000')
    municipios_municipio_245 = importer.save_or_locate(municipios_municipio_245)

    municipios_municipio_246 = Municipio()
    municipios_municipio_246.cod_ibge = '3522000'
    municipios_municipio_246.nome = 'Itaju'
    municipios_municipio_246.capital = False
    municipios_municipio_246.uf = 'SP'
    municipios_municipio_246.lat = Decimal('-21.9857000')
    municipios_municipio_246.lng = Decimal('-48.8116000')
    municipios_municipio_246 = importer.save_or_locate(municipios_municipio_246)

    municipios_municipio_247 = Municipio()
    municipios_municipio_247.cod_ibge = '3522109'
    municipios_municipio_247.nome = 'Itanhaém'
    municipios_municipio_247.capital = False
    municipios_municipio_247.uf = 'SP'
    municipios_municipio_247.lat = Decimal('-24.1736000')
    municipios_municipio_247.lng = Decimal('-46.7880000')
    municipios_municipio_247 = importer.save_or_locate(municipios_municipio_247)

    municipios_municipio_248 = Municipio()
    municipios_municipio_248.cod_ibge = '3522208'
    municipios_municipio_248.nome = 'Itapecerica da Serra'
    municipios_municipio_248.capital = False
    municipios_municipio_248.uf = 'SP'
    municipios_municipio_248.lat = Decimal('-23.7161000')
    municipios_municipio_248.lng = Decimal('-46.8572000')
    municipios_municipio_248 = importer.save_or_locate(municipios_municipio_248)

    municipios_municipio_249 = Municipio()
    municipios_municipio_249.cod_ibge = '3522307'
    municipios_municipio_249.nome = 'Itapetininga'
    municipios_municipio_249.capital = False
    municipios_municipio_249.uf = 'SP'
    municipios_municipio_249.lat = Decimal('-23.5886000')
    municipios_municipio_249.lng = Decimal('-48.0483000')
    municipios_municipio_249 = importer.save_or_locate(municipios_municipio_249)

    municipios_municipio_250 = Municipio()
    municipios_municipio_250.cod_ibge = '3522406'
    municipios_municipio_250.nome = 'Itapeva'
    municipios_municipio_250.capital = False
    municipios_municipio_250.uf = 'SP'
    municipios_municipio_250.lat = Decimal('-23.9788000')
    municipios_municipio_250.lng = Decimal('-48.8764000')
    municipios_municipio_250 = importer.save_or_locate(municipios_municipio_250)

    municipios_municipio_251 = Municipio()
    municipios_municipio_251.cod_ibge = '3522505'
    municipios_municipio_251.nome = 'Itapevi'
    municipios_municipio_251.capital = False
    municipios_municipio_251.uf = 'SP'
    municipios_municipio_251.lat = Decimal('-23.5488000')
    municipios_municipio_251.lng = Decimal('-46.9327000')
    municipios_municipio_251 = importer.save_or_locate(municipios_municipio_251)

    municipios_municipio_252 = Municipio()
    municipios_municipio_252.cod_ibge = '3522604'
    municipios_municipio_252.nome = 'Itapira'
    municipios_municipio_252.capital = False
    municipios_municipio_252.uf = 'SP'
    municipios_municipio_252.lat = Decimal('-22.4357000')
    municipios_municipio_252.lng = Decimal('-46.8224000')
    municipios_municipio_252 = importer.save_or_locate(municipios_municipio_252)

    municipios_municipio_253 = Municipio()
    municipios_municipio_253.cod_ibge = '3522653'
    municipios_municipio_253.nome = 'Itapirapuã Paulista'
    municipios_municipio_253.capital = False
    municipios_municipio_253.uf = 'SP'
    municipios_municipio_253.lat = Decimal('-24.5720000')
    municipios_municipio_253.lng = Decimal('-49.1661000')
    municipios_municipio_253 = importer.save_or_locate(municipios_municipio_253)

    municipios_municipio_254 = Municipio()
    municipios_municipio_254.cod_ibge = '3522802'
    municipios_municipio_254.nome = 'Itaporanga'
    municipios_municipio_254.capital = False
    municipios_municipio_254.uf = 'SP'
    municipios_municipio_254.lat = Decimal('-23.7043000')
    municipios_municipio_254.lng = Decimal('-49.4819000')
    municipios_municipio_254 = importer.save_or_locate(municipios_municipio_254)

    municipios_municipio_255 = Municipio()
    municipios_municipio_255.cod_ibge = '3523008'
    municipios_municipio_255.nome = 'Itapura'
    municipios_municipio_255.capital = False
    municipios_municipio_255.uf = 'SP'
    municipios_municipio_255.lat = Decimal('-20.6419000')
    municipios_municipio_255.lng = Decimal('-51.5063000')
    municipios_municipio_255 = importer.save_or_locate(municipios_municipio_255)

    municipios_municipio_256 = Municipio()
    municipios_municipio_256.cod_ibge = '3522901'
    municipios_municipio_256.nome = 'Itapuí'
    municipios_municipio_256.capital = False
    municipios_municipio_256.uf = 'SP'
    municipios_municipio_256.lat = Decimal('-22.2324000')
    municipios_municipio_256.lng = Decimal('-48.7197000')
    municipios_municipio_256 = importer.save_or_locate(municipios_municipio_256)

    municipios_municipio_257 = Municipio()
    municipios_municipio_257.cod_ibge = '3523107'
    municipios_municipio_257.nome = 'Itaquaquecetuba'
    municipios_municipio_257.capital = False
    municipios_municipio_257.uf = 'SP'
    municipios_municipio_257.lat = Decimal('-23.4835000')
    municipios_municipio_257.lng = Decimal('-46.3457000')
    municipios_municipio_257 = importer.save_or_locate(municipios_municipio_257)

    municipios_municipio_258 = Municipio()
    municipios_municipio_258.cod_ibge = '3523206'
    municipios_municipio_258.nome = 'Itararé'
    municipios_municipio_258.capital = False
    municipios_municipio_258.uf = 'SP'
    municipios_municipio_258.lat = Decimal('-24.1085000')
    municipios_municipio_258.lng = Decimal('-49.3352000')
    municipios_municipio_258 = importer.save_or_locate(municipios_municipio_258)

    municipios_municipio_259 = Municipio()
    municipios_municipio_259.cod_ibge = '3523305'
    municipios_municipio_259.nome = 'Itariri'
    municipios_municipio_259.capital = False
    municipios_municipio_259.uf = 'SP'
    municipios_municipio_259.lat = Decimal('-24.2834000')
    municipios_municipio_259.lng = Decimal('-47.1736000')
    municipios_municipio_259 = importer.save_or_locate(municipios_municipio_259)

    municipios_municipio_260 = Municipio()
    municipios_municipio_260.cod_ibge = '3523404'
    municipios_municipio_260.nome = 'Itatiba'
    municipios_municipio_260.capital = False
    municipios_municipio_260.uf = 'SP'
    municipios_municipio_260.lat = Decimal('-23.0035000')
    municipios_municipio_260.lng = Decimal('-46.8464000')
    municipios_municipio_260 = importer.save_or_locate(municipios_municipio_260)

    municipios_municipio_261 = Municipio()
    municipios_municipio_261.cod_ibge = '3523503'
    municipios_municipio_261.nome = 'Itatinga'
    municipios_municipio_261.capital = False
    municipios_municipio_261.uf = 'SP'
    municipios_municipio_261.lat = Decimal('-23.1047000')
    municipios_municipio_261.lng = Decimal('-48.6157000')
    municipios_municipio_261 = importer.save_or_locate(municipios_municipio_261)

    municipios_municipio_262 = Municipio()
    municipios_municipio_262.cod_ibge = '3521804'
    municipios_municipio_262.nome = 'Itaí'
    municipios_municipio_262.capital = False
    municipios_municipio_262.uf = 'SP'
    municipios_municipio_262.lat = Decimal('-23.4213000')
    municipios_municipio_262.lng = Decimal('-49.0920000')
    municipios_municipio_262 = importer.save_or_locate(municipios_municipio_262)

    municipios_municipio_263 = Municipio()
    municipios_municipio_263.cod_ibge = '3522158'
    municipios_municipio_263.nome = 'Itaóca'
    municipios_municipio_263.capital = False
    municipios_municipio_263.uf = 'SP'
    municipios_municipio_263.lat = Decimal('-24.6393000')
    municipios_municipio_263.lng = Decimal('-48.8413000')
    municipios_municipio_263 = importer.save_or_locate(municipios_municipio_263)

    municipios_municipio_264 = Municipio()
    municipios_municipio_264.cod_ibge = '3523602'
    municipios_municipio_264.nome = 'Itirapina'
    municipios_municipio_264.capital = False
    municipios_municipio_264.uf = 'SP'
    municipios_municipio_264.lat = Decimal('-22.2562000')
    municipios_municipio_264.lng = Decimal('-47.8166000')
    municipios_municipio_264 = importer.save_or_locate(municipios_municipio_264)

    municipios_municipio_265 = Municipio()
    municipios_municipio_265.cod_ibge = '3523701'
    municipios_municipio_265.nome = 'Itirapuã'
    municipios_municipio_265.capital = False
    municipios_municipio_265.uf = 'SP'
    municipios_municipio_265.lat = Decimal('-20.6416000')
    municipios_municipio_265.lng = Decimal('-47.2194000')
    municipios_municipio_265 = importer.save_or_locate(municipios_municipio_265)

    municipios_municipio_266 = Municipio()
    municipios_municipio_266.cod_ibge = '3523800'
    municipios_municipio_266.nome = 'Itobi'
    municipios_municipio_266.capital = False
    municipios_municipio_266.uf = 'SP'
    municipios_municipio_266.lat = Decimal('-21.7309000')
    municipios_municipio_266.lng = Decimal('-46.9743000')
    municipios_municipio_266 = importer.save_or_locate(municipios_municipio_266)

    municipios_municipio_267 = Municipio()
    municipios_municipio_267.cod_ibge = '3523909'
    municipios_municipio_267.nome = 'Itu'
    municipios_municipio_267.capital = False
    municipios_municipio_267.uf = 'SP'
    municipios_municipio_267.lat = Decimal('-23.2544000')
    municipios_municipio_267.lng = Decimal('-47.2927000')
    municipios_municipio_267 = importer.save_or_locate(municipios_municipio_267)

    municipios_municipio_268 = Municipio()
    municipios_municipio_268.cod_ibge = '3524006'
    municipios_municipio_268.nome = 'Itupeva'
    municipios_municipio_268.capital = False
    municipios_municipio_268.uf = 'SP'
    municipios_municipio_268.lat = Decimal('-23.1526000')
    municipios_municipio_268.lng = Decimal('-47.0593000')
    municipios_municipio_268 = importer.save_or_locate(municipios_municipio_268)

    municipios_municipio_269 = Municipio()
    municipios_municipio_269.cod_ibge = '3524105'
    municipios_municipio_269.nome = 'Ituverava'
    municipios_municipio_269.capital = False
    municipios_municipio_269.uf = 'SP'
    municipios_municipio_269.lat = Decimal('-20.3355000')
    municipios_municipio_269.lng = Decimal('-47.7902000')
    municipios_municipio_269 = importer.save_or_locate(municipios_municipio_269)

    municipios_municipio_270 = Municipio()
    municipios_municipio_270.cod_ibge = '3522703'
    municipios_municipio_270.nome = 'Itápolis'
    municipios_municipio_270.capital = False
    municipios_municipio_270.uf = 'SP'
    municipios_municipio_270.lat = Decimal('-21.5942000')
    municipios_municipio_270.lng = Decimal('-48.8149000')
    municipios_municipio_270 = importer.save_or_locate(municipios_municipio_270)

    municipios_municipio_271 = Municipio()
    municipios_municipio_271.cod_ibge = '3524204'
    municipios_municipio_271.nome = 'Jaborandi'
    municipios_municipio_271.capital = False
    municipios_municipio_271.uf = 'SP'
    municipios_municipio_271.lat = Decimal('-20.6884000')
    municipios_municipio_271.lng = Decimal('-48.4112000')
    municipios_municipio_271 = importer.save_or_locate(municipios_municipio_271)

    municipios_municipio_272 = Municipio()
    municipios_municipio_272.cod_ibge = '3524303'
    municipios_municipio_272.nome = 'Jaboticabal'
    municipios_municipio_272.capital = False
    municipios_municipio_272.uf = 'SP'
    municipios_municipio_272.lat = Decimal('-21.2520000')
    municipios_municipio_272.lng = Decimal('-48.3252000')
    municipios_municipio_272 = importer.save_or_locate(municipios_municipio_272)

    municipios_municipio_273 = Municipio()
    municipios_municipio_273.cod_ibge = '3524402'
    municipios_municipio_273.nome = 'Jacareí'
    municipios_municipio_273.capital = False
    municipios_municipio_273.uf = 'SP'
    municipios_municipio_273.lat = Decimal('-23.2983000')
    municipios_municipio_273.lng = Decimal('-45.9658000')
    municipios_municipio_273 = importer.save_or_locate(municipios_municipio_273)

    municipios_municipio_274 = Municipio()
    municipios_municipio_274.cod_ibge = '3524501'
    municipios_municipio_274.nome = 'Jaci'
    municipios_municipio_274.capital = False
    municipios_municipio_274.uf = 'SP'
    municipios_municipio_274.lat = Decimal('-20.8805000')
    municipios_municipio_274.lng = Decimal('-49.5797000')
    municipios_municipio_274 = importer.save_or_locate(municipios_municipio_274)

    municipios_municipio_275 = Municipio()
    municipios_municipio_275.cod_ibge = '3524600'
    municipios_municipio_275.nome = 'Jacupiranga'
    municipios_municipio_275.capital = False
    municipios_municipio_275.uf = 'SP'
    municipios_municipio_275.lat = Decimal('-24.6963000')
    municipios_municipio_275.lng = Decimal('-48.0064000')
    municipios_municipio_275 = importer.save_or_locate(municipios_municipio_275)

    municipios_municipio_276 = Municipio()
    municipios_municipio_276.cod_ibge = '3524709'
    municipios_municipio_276.nome = 'Jaguariúna'
    municipios_municipio_276.capital = False
    municipios_municipio_276.uf = 'SP'
    municipios_municipio_276.lat = Decimal('-22.7037000')
    municipios_municipio_276.lng = Decimal('-46.9851000')
    municipios_municipio_276 = importer.save_or_locate(municipios_municipio_276)

    municipios_municipio_277 = Municipio()
    municipios_municipio_277.cod_ibge = '3524808'
    municipios_municipio_277.nome = 'Jales'
    municipios_municipio_277.capital = False
    municipios_municipio_277.uf = 'SP'
    municipios_municipio_277.lat = Decimal('-20.2672000')
    municipios_municipio_277.lng = Decimal('-50.5494000')
    municipios_municipio_277 = importer.save_or_locate(municipios_municipio_277)

    municipios_municipio_278 = Municipio()
    municipios_municipio_278.cod_ibge = '3524907'
    municipios_municipio_278.nome = 'Jambeiro'
    municipios_municipio_278.capital = False
    municipios_municipio_278.uf = 'SP'
    municipios_municipio_278.lat = Decimal('-23.2522000')
    municipios_municipio_278.lng = Decimal('-45.6942000')
    municipios_municipio_278 = importer.save_or_locate(municipios_municipio_278)

    municipios_municipio_279 = Municipio()
    municipios_municipio_279.cod_ibge = '3525003'
    municipios_municipio_279.nome = 'Jandira'
    municipios_municipio_279.capital = False
    municipios_municipio_279.uf = 'SP'
    municipios_municipio_279.lat = Decimal('-23.5275000')
    municipios_municipio_279.lng = Decimal('-46.9023000')
    municipios_municipio_279 = importer.save_or_locate(municipios_municipio_279)

    municipios_municipio_280 = Municipio()
    municipios_municipio_280.cod_ibge = '3525102'
    municipios_municipio_280.nome = 'Jardinópolis'
    municipios_municipio_280.capital = False
    municipios_municipio_280.uf = 'SP'
    municipios_municipio_280.lat = Decimal('-21.0176000')
    municipios_municipio_280.lng = Decimal('-47.7606000')
    municipios_municipio_280 = importer.save_or_locate(municipios_municipio_280)

    municipios_municipio_281 = Municipio()
    municipios_municipio_281.cod_ibge = '3525201'
    municipios_municipio_281.nome = 'Jarinu'
    municipios_municipio_281.capital = False
    municipios_municipio_281.uf = 'SP'
    municipios_municipio_281.lat = Decimal('-23.1039000')
    municipios_municipio_281.lng = Decimal('-46.7280000')
    municipios_municipio_281 = importer.save_or_locate(municipios_municipio_281)

    municipios_municipio_282 = Municipio()
    municipios_municipio_282.cod_ibge = '3525300'
    municipios_municipio_282.nome = 'Jaú'
    municipios_municipio_282.capital = False
    municipios_municipio_282.uf = 'SP'
    municipios_municipio_282.lat = Decimal('-22.2936000')
    municipios_municipio_282.lng = Decimal('-48.5592000')
    municipios_municipio_282 = importer.save_or_locate(municipios_municipio_282)

    municipios_municipio_283 = Municipio()
    municipios_municipio_283.cod_ibge = '3525409'
    municipios_municipio_283.nome = 'Jeriquara'
    municipios_municipio_283.capital = False
    municipios_municipio_283.uf = 'SP'
    municipios_municipio_283.lat = Decimal('-20.3116000')
    municipios_municipio_283.lng = Decimal('-47.5918000')
    municipios_municipio_283 = importer.save_or_locate(municipios_municipio_283)

    municipios_municipio_284 = Municipio()
    municipios_municipio_284.cod_ibge = '3525508'
    municipios_municipio_284.nome = 'Joanópolis'
    municipios_municipio_284.capital = False
    municipios_municipio_284.uf = 'SP'
    municipios_municipio_284.lat = Decimal('-22.9270000')
    municipios_municipio_284.lng = Decimal('-46.2741000')
    municipios_municipio_284 = importer.save_or_locate(municipios_municipio_284)

    municipios_municipio_285 = Municipio()
    municipios_municipio_285.cod_ibge = '3525706'
    municipios_municipio_285.nome = 'José Bonifácio'
    municipios_municipio_285.capital = False
    municipios_municipio_285.uf = 'SP'
    municipios_municipio_285.lat = Decimal('-21.0551000')
    municipios_municipio_285.lng = Decimal('-49.6892000')
    municipios_municipio_285 = importer.save_or_locate(municipios_municipio_285)

    municipios_municipio_286 = Municipio()
    municipios_municipio_286.cod_ibge = '3525607'
    municipios_municipio_286.nome = 'João Ramalho'
    municipios_municipio_286.capital = False
    municipios_municipio_286.uf = 'SP'
    municipios_municipio_286.lat = Decimal('-22.2473000')
    municipios_municipio_286.lng = Decimal('-50.7694000')
    municipios_municipio_286 = importer.save_or_locate(municipios_municipio_286)

    municipios_municipio_287 = Municipio()
    municipios_municipio_287.cod_ibge = '3525854'
    municipios_municipio_287.nome = 'Jumirim'
    municipios_municipio_287.capital = False
    municipios_municipio_287.uf = 'SP'
    municipios_municipio_287.lat = Decimal('-23.0884000')
    municipios_municipio_287.lng = Decimal('-47.7868000')
    municipios_municipio_287 = importer.save_or_locate(municipios_municipio_287)

    municipios_municipio_288 = Municipio()
    municipios_municipio_288.cod_ibge = '3525904'
    municipios_municipio_288.nome = 'Jundiaí'
    municipios_municipio_288.capital = False
    municipios_municipio_288.uf = 'SP'
    municipios_municipio_288.lat = Decimal('-23.1852000')
    municipios_municipio_288.lng = Decimal('-46.8974000')
    municipios_municipio_288 = importer.save_or_locate(municipios_municipio_288)

    municipios_municipio_289 = Municipio()
    municipios_municipio_289.cod_ibge = '3526001'
    municipios_municipio_289.nome = 'Junqueirópolis'
    municipios_municipio_289.capital = False
    municipios_municipio_289.uf = 'SP'
    municipios_municipio_289.lat = Decimal('-21.5103000')
    municipios_municipio_289.lng = Decimal('-51.4342000')
    municipios_municipio_289 = importer.save_or_locate(municipios_municipio_289)

    municipios_municipio_290 = Municipio()
    municipios_municipio_290.cod_ibge = '3526209'
    municipios_municipio_290.nome = 'Juquitiba'
    municipios_municipio_290.capital = False
    municipios_municipio_290.uf = 'SP'
    municipios_municipio_290.lat = Decimal('-23.9244000')
    municipios_municipio_290.lng = Decimal('-47.0653000')
    municipios_municipio_290 = importer.save_or_locate(municipios_municipio_290)

    municipios_municipio_291 = Municipio()
    municipios_municipio_291.cod_ibge = '3526100'
    municipios_municipio_291.nome = 'Juquiá'
    municipios_municipio_291.capital = False
    municipios_municipio_291.uf = 'SP'
    municipios_municipio_291.lat = Decimal('-24.3101000')
    municipios_municipio_291.lng = Decimal('-47.6426000')
    municipios_municipio_291 = importer.save_or_locate(municipios_municipio_291)

    municipios_municipio_292 = Municipio()
    municipios_municipio_292.cod_ibge = '3525805'
    municipios_municipio_292.nome = 'Júlio Mesquita'
    municipios_municipio_292.capital = False
    municipios_municipio_292.uf = 'SP'
    municipios_municipio_292.lat = Decimal('-22.0112000')
    municipios_municipio_292.lng = Decimal('-49.7873000')
    municipios_municipio_292 = importer.save_or_locate(municipios_municipio_292)

    municipios_municipio_293 = Municipio()
    municipios_municipio_293.cod_ibge = '3526308'
    municipios_municipio_293.nome = 'Lagoinha'
    municipios_municipio_293.capital = False
    municipios_municipio_293.uf = 'SP'
    municipios_municipio_293.lat = Decimal('-23.0846000')
    municipios_municipio_293.lng = Decimal('-45.1944000')
    municipios_municipio_293 = importer.save_or_locate(municipios_municipio_293)

    municipios_municipio_294 = Municipio()
    municipios_municipio_294.cod_ibge = '3526407'
    municipios_municipio_294.nome = 'Laranjal Paulista'
    municipios_municipio_294.capital = False
    municipios_municipio_294.uf = 'SP'
    municipios_municipio_294.lat = Decimal('-23.0506000')
    municipios_municipio_294.lng = Decimal('-47.8375000')
    municipios_municipio_294 = importer.save_or_locate(municipios_municipio_294)

    municipios_municipio_295 = Municipio()
    municipios_municipio_295.cod_ibge = '3526605'
    municipios_municipio_295.nome = 'Lavrinhas'
    municipios_municipio_295.capital = False
    municipios_municipio_295.uf = 'SP'
    municipios_municipio_295.lat = Decimal('-22.5700000')
    municipios_municipio_295.lng = Decimal('-44.9024000')
    municipios_municipio_295 = importer.save_or_locate(municipios_municipio_295)

    municipios_municipio_296 = Municipio()
    municipios_municipio_296.cod_ibge = '3526506'
    municipios_municipio_296.nome = 'Lavínia'
    municipios_municipio_296.capital = False
    municipios_municipio_296.uf = 'SP'
    municipios_municipio_296.lat = Decimal('-21.1639000')
    municipios_municipio_296.lng = Decimal('-51.0412000')
    municipios_municipio_296 = importer.save_or_locate(municipios_municipio_296)

    municipios_municipio_297 = Municipio()
    municipios_municipio_297.cod_ibge = '3526704'
    municipios_municipio_297.nome = 'Leme'
    municipios_municipio_297.capital = False
    municipios_municipio_297.uf = 'SP'
    municipios_municipio_297.lat = Decimal('-22.1809000')
    municipios_municipio_297.lng = Decimal('-47.3841000')
    municipios_municipio_297 = importer.save_or_locate(municipios_municipio_297)

    municipios_municipio_298 = Municipio()
    municipios_municipio_298.cod_ibge = '3526803'
    municipios_municipio_298.nome = 'Lençóis Paulista'
    municipios_municipio_298.capital = False
    municipios_municipio_298.uf = 'SP'
    municipios_municipio_298.lat = Decimal('-22.6027000')
    municipios_municipio_298.lng = Decimal('-48.8037000')
    municipios_municipio_298 = importer.save_or_locate(municipios_municipio_298)

    municipios_municipio_299 = Municipio()
    municipios_municipio_299.cod_ibge = '3526902'
    municipios_municipio_299.nome = 'Limeira'
    municipios_municipio_299.capital = False
    municipios_municipio_299.uf = 'SP'
    municipios_municipio_299.lat = Decimal('-22.5660000')
    municipios_municipio_299.lng = Decimal('-47.3970000')
    municipios_municipio_299 = importer.save_or_locate(municipios_municipio_299)

    municipios_municipio_300 = Municipio()
    municipios_municipio_300.cod_ibge = '3527009'
    municipios_municipio_300.nome = 'Lindóia'
    municipios_municipio_300.capital = False
    municipios_municipio_300.uf = 'SP'
    municipios_municipio_300.lat = Decimal('-22.5226000')
    municipios_municipio_300.lng = Decimal('-46.6500000')
    municipios_municipio_300 = importer.save_or_locate(municipios_municipio_300)

    municipios_municipio_301 = Municipio()
    municipios_municipio_301.cod_ibge = '3527108'
    municipios_municipio_301.nome = 'Lins'
    municipios_municipio_301.capital = False
    municipios_municipio_301.uf = 'SP'
    municipios_municipio_301.lat = Decimal('-21.6718000')
    municipios_municipio_301.lng = Decimal('-49.7526000')
    municipios_municipio_301 = importer.save_or_locate(municipios_municipio_301)

    municipios_municipio_302 = Municipio()
    municipios_municipio_302.cod_ibge = '3527207'
    municipios_municipio_302.nome = 'Lorena'
    municipios_municipio_302.capital = False
    municipios_municipio_302.uf = 'SP'
    municipios_municipio_302.lat = Decimal('-22.7334000')
    municipios_municipio_302.lng = Decimal('-45.1197000')
    municipios_municipio_302 = importer.save_or_locate(municipios_municipio_302)

    municipios_municipio_303 = Municipio()
    municipios_municipio_303.cod_ibge = '3527256'
    municipios_municipio_303.nome = 'Lourdes'
    municipios_municipio_303.capital = False
    municipios_municipio_303.uf = 'SP'
    municipios_municipio_303.lat = Decimal('-20.9660000')
    municipios_municipio_303.lng = Decimal('-50.2263000')
    municipios_municipio_303 = importer.save_or_locate(municipios_municipio_303)

    municipios_municipio_304 = Municipio()
    municipios_municipio_304.cod_ibge = '3527306'
    municipios_municipio_304.nome = 'Louveira'
    municipios_municipio_304.capital = False
    municipios_municipio_304.uf = 'SP'
    municipios_municipio_304.lat = Decimal('-23.0856000')
    municipios_municipio_304.lng = Decimal('-46.9484000')
    municipios_municipio_304 = importer.save_or_locate(municipios_municipio_304)

    municipios_municipio_305 = Municipio()
    municipios_municipio_305.cod_ibge = '3527504'
    municipios_municipio_305.nome = 'Lucianópolis'
    municipios_municipio_305.capital = False
    municipios_municipio_305.uf = 'SP'
    municipios_municipio_305.lat = Decimal('-22.4294000')
    municipios_municipio_305.lng = Decimal('-49.5220000')
    municipios_municipio_305 = importer.save_or_locate(municipios_municipio_305)

    municipios_municipio_306 = Municipio()
    municipios_municipio_306.cod_ibge = '3527405'
    municipios_municipio_306.nome = 'Lucélia'
    municipios_municipio_306.capital = False
    municipios_municipio_306.uf = 'SP'
    municipios_municipio_306.lat = Decimal('-21.7182000')
    municipios_municipio_306.lng = Decimal('-51.0215000')
    municipios_municipio_306 = importer.save_or_locate(municipios_municipio_306)

    municipios_municipio_307 = Municipio()
    municipios_municipio_307.cod_ibge = '3527702'
    municipios_municipio_307.nome = 'Luiziânia'
    municipios_municipio_307.capital = False
    municipios_municipio_307.uf = 'SP'
    municipios_municipio_307.lat = Decimal('-21.6737000')
    municipios_municipio_307.lng = Decimal('-50.3294000')
    municipios_municipio_307 = importer.save_or_locate(municipios_municipio_307)

    municipios_municipio_308 = Municipio()
    municipios_municipio_308.cod_ibge = '3527801'
    municipios_municipio_308.nome = 'Lupércio'
    municipios_municipio_308.capital = False
    municipios_municipio_308.uf = 'SP'
    municipios_municipio_308.lat = Decimal('-22.4146000')
    municipios_municipio_308.lng = Decimal('-49.8180000')
    municipios_municipio_308 = importer.save_or_locate(municipios_municipio_308)

    municipios_municipio_309 = Municipio()
    municipios_municipio_309.cod_ibge = '3527900'
    municipios_municipio_309.nome = 'Lutécia'
    municipios_municipio_309.capital = False
    municipios_municipio_309.uf = 'SP'
    municipios_municipio_309.lat = Decimal('-22.3384000')
    municipios_municipio_309.lng = Decimal('-50.3940000')
    municipios_municipio_309 = importer.save_or_locate(municipios_municipio_309)

    municipios_municipio_310 = Municipio()
    municipios_municipio_310.cod_ibge = '3527603'
    municipios_municipio_310.nome = 'Luís Antônio'
    municipios_municipio_310.capital = False
    municipios_municipio_310.uf = 'SP'
    municipios_municipio_310.lat = Decimal('-21.5500000')
    municipios_municipio_310.lng = Decimal('-47.7801000')
    municipios_municipio_310 = importer.save_or_locate(municipios_municipio_310)

    municipios_municipio_311 = Municipio()
    municipios_municipio_311.cod_ibge = '3528007'
    municipios_municipio_311.nome = 'Macatuba'
    municipios_municipio_311.capital = False
    municipios_municipio_311.uf = 'SP'
    municipios_municipio_311.lat = Decimal('-22.5002000')
    municipios_municipio_311.lng = Decimal('-48.7102000')
    municipios_municipio_311 = importer.save_or_locate(municipios_municipio_311)

    municipios_municipio_312 = Municipio()
    municipios_municipio_312.cod_ibge = '3528106'
    municipios_municipio_312.nome = 'Macaubal'
    municipios_municipio_312.capital = False
    municipios_municipio_312.uf = 'SP'
    municipios_municipio_312.lat = Decimal('-20.8022000')
    municipios_municipio_312.lng = Decimal('-49.9687000')
    municipios_municipio_312 = importer.save_or_locate(municipios_municipio_312)

    municipios_municipio_313 = Municipio()
    municipios_municipio_313.cod_ibge = '3528205'
    municipios_municipio_313.nome = 'Macedônia'
    municipios_municipio_313.capital = False
    municipios_municipio_313.uf = 'SP'
    municipios_municipio_313.lat = Decimal('-20.1444000')
    municipios_municipio_313.lng = Decimal('-50.1973000')
    municipios_municipio_313 = importer.save_or_locate(municipios_municipio_313)

    municipios_municipio_314 = Municipio()
    municipios_municipio_314.cod_ibge = '3528304'
    municipios_municipio_314.nome = 'Magda'
    municipios_municipio_314.capital = False
    municipios_municipio_314.uf = 'SP'
    municipios_municipio_314.lat = Decimal('-20.6445000')
    municipios_municipio_314.lng = Decimal('-50.2305000')
    municipios_municipio_314 = importer.save_or_locate(municipios_municipio_314)

    municipios_municipio_315 = Municipio()
    municipios_municipio_315.cod_ibge = '3528403'
    municipios_municipio_315.nome = 'Mairinque'
    municipios_municipio_315.capital = False
    municipios_municipio_315.uf = 'SP'
    municipios_municipio_315.lat = Decimal('-23.5398000')
    municipios_municipio_315.lng = Decimal('-47.1850000')
    municipios_municipio_315 = importer.save_or_locate(municipios_municipio_315)

    municipios_municipio_316 = Municipio()
    municipios_municipio_316.cod_ibge = '3528502'
    municipios_municipio_316.nome = 'Mairiporã'
    municipios_municipio_316.capital = False
    municipios_municipio_316.uf = 'SP'
    municipios_municipio_316.lat = Decimal('-23.3171000')
    municipios_municipio_316.lng = Decimal('-46.5897000')
    municipios_municipio_316 = importer.save_or_locate(municipios_municipio_316)

    municipios_municipio_317 = Municipio()
    municipios_municipio_317.cod_ibge = '3528601'
    municipios_municipio_317.nome = 'Manduri'
    municipios_municipio_317.capital = False
    municipios_municipio_317.uf = 'SP'
    municipios_municipio_317.lat = Decimal('-23.0056000')
    municipios_municipio_317.lng = Decimal('-49.3202000')
    municipios_municipio_317 = importer.save_or_locate(municipios_municipio_317)

    municipios_municipio_318 = Municipio()
    municipios_municipio_318.cod_ibge = '3528700'
    municipios_municipio_318.nome = 'Marabá Paulista'
    municipios_municipio_318.capital = False
    municipios_municipio_318.uf = 'SP'
    municipios_municipio_318.lat = Decimal('-22.1068000')
    municipios_municipio_318.lng = Decimal('-51.9617000')
    municipios_municipio_318 = importer.save_or_locate(municipios_municipio_318)

    municipios_municipio_319 = Municipio()
    municipios_municipio_319.cod_ibge = '3528809'
    municipios_municipio_319.nome = 'Maracaí'
    municipios_municipio_319.capital = False
    municipios_municipio_319.uf = 'SP'
    municipios_municipio_319.lat = Decimal('-22.6149000')
    municipios_municipio_319.lng = Decimal('-50.6713000')
    municipios_municipio_319 = importer.save_or_locate(municipios_municipio_319)

    municipios_municipio_320 = Municipio()
    municipios_municipio_320.cod_ibge = '3528858'
    municipios_municipio_320.nome = 'Marapoama'
    municipios_municipio_320.capital = False
    municipios_municipio_320.uf = 'SP'
    municipios_municipio_320.lat = Decimal('-21.2587000')
    municipios_municipio_320.lng = Decimal('-49.1300000')
    municipios_municipio_320 = importer.save_or_locate(municipios_municipio_320)

    municipios_municipio_321 = Municipio()
    municipios_municipio_321.cod_ibge = '3529104'
    municipios_municipio_321.nome = 'Marinópolis'
    municipios_municipio_321.capital = False
    municipios_municipio_321.uf = 'SP'
    municipios_municipio_321.lat = Decimal('-20.4389000')
    municipios_municipio_321.lng = Decimal('-50.8254000')
    municipios_municipio_321 = importer.save_or_locate(municipios_municipio_321)

    municipios_municipio_322 = Municipio()
    municipios_municipio_322.cod_ibge = '3528908'
    municipios_municipio_322.nome = 'Mariápolis'
    municipios_municipio_322.capital = False
    municipios_municipio_322.uf = 'SP'
    municipios_municipio_322.lat = Decimal('-21.7959000')
    municipios_municipio_322.lng = Decimal('-51.1824000')
    municipios_municipio_322 = importer.save_or_locate(municipios_municipio_322)

    municipios_municipio_323 = Municipio()
    municipios_municipio_323.cod_ibge = '3529203'
    municipios_municipio_323.nome = 'Martinópolis'
    municipios_municipio_323.capital = False
    municipios_municipio_323.uf = 'SP'
    municipios_municipio_323.lat = Decimal('-22.1462000')
    municipios_municipio_323.lng = Decimal('-51.1709000')
    municipios_municipio_323 = importer.save_or_locate(municipios_municipio_323)

    municipios_municipio_324 = Municipio()
    municipios_municipio_324.cod_ibge = '3529005'
    municipios_municipio_324.nome = 'Marília'
    municipios_municipio_324.capital = False
    municipios_municipio_324.uf = 'SP'
    municipios_municipio_324.lat = Decimal('-22.2171000')
    municipios_municipio_324.lng = Decimal('-49.9501000')
    municipios_municipio_324 = importer.save_or_locate(municipios_municipio_324)

    municipios_municipio_325 = Municipio()
    municipios_municipio_325.cod_ibge = '3529302'
    municipios_municipio_325.nome = 'Matão'
    municipios_municipio_325.capital = False
    municipios_municipio_325.uf = 'SP'
    municipios_municipio_325.lat = Decimal('-21.6025000')
    municipios_municipio_325.lng = Decimal('-48.3640000')
    municipios_municipio_325 = importer.save_or_locate(municipios_municipio_325)

    municipios_municipio_326 = Municipio()
    municipios_municipio_326.cod_ibge = '3529401'
    municipios_municipio_326.nome = 'Mauá'
    municipios_municipio_326.capital = False
    municipios_municipio_326.uf = 'SP'
    municipios_municipio_326.lat = Decimal('-23.6677000')
    municipios_municipio_326.lng = Decimal('-46.4613000')
    municipios_municipio_326 = importer.save_or_locate(municipios_municipio_326)

    municipios_municipio_327 = Municipio()
    municipios_municipio_327.cod_ibge = '3529500'
    municipios_municipio_327.nome = 'Mendonça'
    municipios_municipio_327.capital = False
    municipios_municipio_327.uf = 'SP'
    municipios_municipio_327.lat = Decimal('-21.1757000')
    municipios_municipio_327.lng = Decimal('-49.5791000')
    municipios_municipio_327 = importer.save_or_locate(municipios_municipio_327)

    municipios_municipio_328 = Municipio()
    municipios_municipio_328.cod_ibge = '3529609'
    municipios_municipio_328.nome = 'Meridiano'
    municipios_municipio_328.capital = False
    municipios_municipio_328.uf = 'SP'
    municipios_municipio_328.lat = Decimal('-20.3579000')
    municipios_municipio_328.lng = Decimal('-50.1811000')
    municipios_municipio_328 = importer.save_or_locate(municipios_municipio_328)

    municipios_municipio_329 = Municipio()
    municipios_municipio_329.cod_ibge = '3529658'
    municipios_municipio_329.nome = 'Mesópolis'
    municipios_municipio_329.capital = False
    municipios_municipio_329.uf = 'SP'
    municipios_municipio_329.lat = Decimal('-19.9684000')
    municipios_municipio_329.lng = Decimal('-50.6326000')
    municipios_municipio_329 = importer.save_or_locate(municipios_municipio_329)

    municipios_municipio_330 = Municipio()
    municipios_municipio_330.cod_ibge = '3529708'
    municipios_municipio_330.nome = 'Miguelópolis'
    municipios_municipio_330.capital = False
    municipios_municipio_330.uf = 'SP'
    municipios_municipio_330.lat = Decimal('-20.1796000')
    municipios_municipio_330.lng = Decimal('-48.0310000')
    municipios_municipio_330 = importer.save_or_locate(municipios_municipio_330)

    municipios_municipio_331 = Municipio()
    municipios_municipio_331.cod_ibge = '3529807'
    municipios_municipio_331.nome = 'Mineiros do Tietê'
    municipios_municipio_331.capital = False
    municipios_municipio_331.uf = 'SP'
    municipios_municipio_331.lat = Decimal('-22.4120000')
    municipios_municipio_331.lng = Decimal('-48.4510000')
    municipios_municipio_331 = importer.save_or_locate(municipios_municipio_331)

    municipios_municipio_332 = Municipio()
    municipios_municipio_332.cod_ibge = '3530003'
    municipios_municipio_332.nome = 'Mira Estrela'
    municipios_municipio_332.capital = False
    municipios_municipio_332.uf = 'SP'
    municipios_municipio_332.lat = Decimal('-19.9789000')
    municipios_municipio_332.lng = Decimal('-50.1390000')
    municipios_municipio_332 = importer.save_or_locate(municipios_municipio_332)

    municipios_municipio_333 = Municipio()
    municipios_municipio_333.cod_ibge = '3529906'
    municipios_municipio_333.nome = 'Miracatu'
    municipios_municipio_333.capital = False
    municipios_municipio_333.uf = 'SP'
    municipios_municipio_333.lat = Decimal('-24.2766000')
    municipios_municipio_333.lng = Decimal('-47.4625000')
    municipios_municipio_333 = importer.save_or_locate(municipios_municipio_333)

    municipios_municipio_334 = Municipio()
    municipios_municipio_334.cod_ibge = '3530102'
    municipios_municipio_334.nome = 'Mirandópolis'
    municipios_municipio_334.capital = False
    municipios_municipio_334.uf = 'SP'
    municipios_municipio_334.lat = Decimal('-21.1313000')
    municipios_municipio_334.lng = Decimal('-51.1035000')
    municipios_municipio_334 = importer.save_or_locate(municipios_municipio_334)

    municipios_municipio_335 = Municipio()
    municipios_municipio_335.cod_ibge = '3530201'
    municipios_municipio_335.nome = 'Mirante do Paranapanema'
    municipios_municipio_335.capital = False
    municipios_municipio_335.uf = 'SP'
    municipios_municipio_335.lat = Decimal('-22.2904000')
    municipios_municipio_335.lng = Decimal('-51.9084000')
    municipios_municipio_335 = importer.save_or_locate(municipios_municipio_335)

    municipios_municipio_336 = Municipio()
    municipios_municipio_336.cod_ibge = '3530300'
    municipios_municipio_336.nome = 'Mirassol'
    municipios_municipio_336.capital = False
    municipios_municipio_336.uf = 'SP'
    municipios_municipio_336.lat = Decimal('-20.8169000')
    municipios_municipio_336.lng = Decimal('-49.5206000')
    municipios_municipio_336 = importer.save_or_locate(municipios_municipio_336)

    municipios_municipio_337 = Municipio()
    municipios_municipio_337.cod_ibge = '3530409'
    municipios_municipio_337.nome = 'Mirassolândia'
    municipios_municipio_337.capital = False
    municipios_municipio_337.uf = 'SP'
    municipios_municipio_337.lat = Decimal('-20.6179000')
    municipios_municipio_337.lng = Decimal('-49.4617000')
    municipios_municipio_337 = importer.save_or_locate(municipios_municipio_337)

    municipios_municipio_338 = Municipio()
    municipios_municipio_338.cod_ibge = '3530508'
    municipios_municipio_338.nome = 'Mococa'
    municipios_municipio_338.capital = False
    municipios_municipio_338.uf = 'SP'
    municipios_municipio_338.lat = Decimal('-21.4647000')
    municipios_municipio_338.lng = Decimal('-47.0024000')
    municipios_municipio_338 = importer.save_or_locate(municipios_municipio_338)

    municipios_municipio_339 = Municipio()
    municipios_municipio_339.cod_ibge = '3530706'
    municipios_municipio_339.nome = 'Mogi Guaçu'
    municipios_municipio_339.capital = False
    municipios_municipio_339.uf = 'SP'
    municipios_municipio_339.lat = Decimal('-22.3675000')
    municipios_municipio_339.lng = Decimal('-46.9428000')
    municipios_municipio_339 = importer.save_or_locate(municipios_municipio_339)

    municipios_municipio_340 = Municipio()
    municipios_municipio_340.cod_ibge = '3530805'
    municipios_municipio_340.nome = 'Mogi Mirim'
    municipios_municipio_340.capital = False
    municipios_municipio_340.uf = 'SP'
    municipios_municipio_340.lat = Decimal('-22.4332000')
    municipios_municipio_340.lng = Decimal('-46.9532000')
    municipios_municipio_340 = importer.save_or_locate(municipios_municipio_340)

    municipios_municipio_341 = Municipio()
    municipios_municipio_341.cod_ibge = '3530607'
    municipios_municipio_341.nome = 'Mogi das Cruzes'
    municipios_municipio_341.capital = False
    municipios_municipio_341.uf = 'SP'
    municipios_municipio_341.lat = Decimal('-23.5208000')
    municipios_municipio_341.lng = Decimal('-46.1854000')
    municipios_municipio_341 = importer.save_or_locate(municipios_municipio_341)

    municipios_municipio_342 = Municipio()
    municipios_municipio_342.cod_ibge = '3530904'
    municipios_municipio_342.nome = 'Mombuca'
    municipios_municipio_342.capital = False
    municipios_municipio_342.uf = 'SP'
    municipios_municipio_342.lat = Decimal('-22.9285000')
    municipios_municipio_342.lng = Decimal('-47.5590000')
    municipios_municipio_342 = importer.save_or_locate(municipios_municipio_342)

    municipios_municipio_343 = Municipio()
    municipios_municipio_343.cod_ibge = '3531100'
    municipios_municipio_343.nome = 'Mongaguá'
    municipios_municipio_343.capital = False
    municipios_municipio_343.uf = 'SP'
    municipios_municipio_343.lat = Decimal('-24.0809000')
    municipios_municipio_343.lng = Decimal('-46.6265000')
    municipios_municipio_343 = importer.save_or_locate(municipios_municipio_343)

    municipios_municipio_344 = Municipio()
    municipios_municipio_344.cod_ibge = '3531209'
    municipios_municipio_344.nome = 'Monte Alegre do Sul'
    municipios_municipio_344.capital = False
    municipios_municipio_344.uf = 'SP'
    municipios_municipio_344.lat = Decimal('-22.6817000')
    municipios_municipio_344.lng = Decimal('-46.6810000')
    municipios_municipio_344 = importer.save_or_locate(municipios_municipio_344)

    municipios_municipio_345 = Municipio()
    municipios_municipio_345.cod_ibge = '3531308'
    municipios_municipio_345.nome = 'Monte Alto'
    municipios_municipio_345.capital = False
    municipios_municipio_345.uf = 'SP'
    municipios_municipio_345.lat = Decimal('-21.2655000')
    municipios_municipio_345.lng = Decimal('-48.4971000')
    municipios_municipio_345 = importer.save_or_locate(municipios_municipio_345)

    municipios_municipio_346 = Municipio()
    municipios_municipio_346.cod_ibge = '3531407'
    municipios_municipio_346.nome = 'Monte Aprazível'
    municipios_municipio_346.capital = False
    municipios_municipio_346.uf = 'SP'
    municipios_municipio_346.lat = Decimal('-20.7680000')
    municipios_municipio_346.lng = Decimal('-49.7184000')
    municipios_municipio_346 = importer.save_or_locate(municipios_municipio_346)

    municipios_municipio_347 = Municipio()
    municipios_municipio_347.cod_ibge = '3531506'
    municipios_municipio_347.nome = 'Monte Azul Paulista'
    municipios_municipio_347.capital = False
    municipios_municipio_347.uf = 'SP'
    municipios_municipio_347.lat = Decimal('-20.9065000')
    municipios_municipio_347.lng = Decimal('-48.6387000')
    municipios_municipio_347 = importer.save_or_locate(municipios_municipio_347)

    municipios_municipio_348 = Municipio()
    municipios_municipio_348.cod_ibge = '3531605'
    municipios_municipio_348.nome = 'Monte Castelo'
    municipios_municipio_348.capital = False
    municipios_municipio_348.uf = 'SP'
    municipios_municipio_348.lat = Decimal('-21.2981000')
    municipios_municipio_348.lng = Decimal('-51.5679000')
    municipios_municipio_348 = importer.save_or_locate(municipios_municipio_348)

    municipios_municipio_349 = Municipio()
    municipios_municipio_349.cod_ibge = '3531803'
    municipios_municipio_349.nome = 'Monte Mor'
    municipios_municipio_349.capital = False
    municipios_municipio_349.uf = 'SP'
    municipios_municipio_349.lat = Decimal('-22.9450000')
    municipios_municipio_349.lng = Decimal('-47.3122000')
    municipios_municipio_349 = importer.save_or_locate(municipios_municipio_349)

    municipios_municipio_350 = Municipio()
    municipios_municipio_350.cod_ibge = '3531704'
    municipios_municipio_350.nome = 'Monteiro Lobato'
    municipios_municipio_350.capital = False
    municipios_municipio_350.uf = 'SP'
    municipios_municipio_350.lat = Decimal('-22.9544000')
    municipios_municipio_350.lng = Decimal('-45.8407000')
    municipios_municipio_350 = importer.save_or_locate(municipios_municipio_350)

    municipios_municipio_351 = Municipio()
    municipios_municipio_351.cod_ibge = '3531001'
    municipios_municipio_351.nome = 'Monções'
    municipios_municipio_351.capital = False
    municipios_municipio_351.uf = 'SP'
    municipios_municipio_351.lat = Decimal('-20.8509000')
    municipios_municipio_351.lng = Decimal('-50.0975000')
    municipios_municipio_351 = importer.save_or_locate(municipios_municipio_351)

    municipios_municipio_352 = Municipio()
    municipios_municipio_352.cod_ibge = '3531902'
    municipios_municipio_352.nome = 'Morro Agudo'
    municipios_municipio_352.capital = False
    municipios_municipio_352.uf = 'SP'
    municipios_municipio_352.lat = Decimal('-20.7288000')
    municipios_municipio_352.lng = Decimal('-48.0581000')
    municipios_municipio_352 = importer.save_or_locate(municipios_municipio_352)

    municipios_municipio_353 = Municipio()
    municipios_municipio_353.cod_ibge = '3532009'
    municipios_municipio_353.nome = 'Morungaba'
    municipios_municipio_353.capital = False
    municipios_municipio_353.uf = 'SP'
    municipios_municipio_353.lat = Decimal('-22.8811000')
    municipios_municipio_353.lng = Decimal('-46.7896000')
    municipios_municipio_353 = importer.save_or_locate(municipios_municipio_353)

    municipios_municipio_354 = Municipio()
    municipios_municipio_354.cod_ibge = '3532058'
    municipios_municipio_354.nome = 'Motuca'
    municipios_municipio_354.capital = False
    municipios_municipio_354.uf = 'SP'
    municipios_municipio_354.lat = Decimal('-21.5103000')
    municipios_municipio_354.lng = Decimal('-48.1538000')
    municipios_municipio_354 = importer.save_or_locate(municipios_municipio_354)

    municipios_municipio_355 = Municipio()
    municipios_municipio_355.cod_ibge = '3532108'
    municipios_municipio_355.nome = 'Murutinga do Sul'
    municipios_municipio_355.capital = False
    municipios_municipio_355.uf = 'SP'
    municipios_municipio_355.lat = Decimal('-20.9908000')
    municipios_municipio_355.lng = Decimal('-51.2774000')
    municipios_municipio_355 = importer.save_or_locate(municipios_municipio_355)

    municipios_municipio_356 = Municipio()
    municipios_municipio_356.cod_ibge = '3532157'
    municipios_municipio_356.nome = 'Nantes'
    municipios_municipio_356.capital = False
    municipios_municipio_356.uf = 'SP'
    municipios_municipio_356.lat = Decimal('-22.6156000')
    municipios_municipio_356.lng = Decimal('-51.2400000')
    municipios_municipio_356 = importer.save_or_locate(municipios_municipio_356)

    municipios_municipio_357 = Municipio()
    municipios_municipio_357.cod_ibge = '3532207'
    municipios_municipio_357.nome = 'Narandiba'
    municipios_municipio_357.capital = False
    municipios_municipio_357.uf = 'SP'
    municipios_municipio_357.lat = Decimal('-22.4057000')
    municipios_municipio_357.lng = Decimal('-51.5274000')
    municipios_municipio_357 = importer.save_or_locate(municipios_municipio_357)

    municipios_municipio_358 = Municipio()
    municipios_municipio_358.cod_ibge = '3532306'
    municipios_municipio_358.nome = 'Natividade da Serra'
    municipios_municipio_358.capital = False
    municipios_municipio_358.uf = 'SP'
    municipios_municipio_358.lat = Decimal('-23.3707000')
    municipios_municipio_358.lng = Decimal('-45.4468000')
    municipios_municipio_358 = importer.save_or_locate(municipios_municipio_358)

    municipios_municipio_359 = Municipio()
    municipios_municipio_359.cod_ibge = '3532405'
    municipios_municipio_359.nome = 'Nazaré Paulista'
    municipios_municipio_359.capital = False
    municipios_municipio_359.uf = 'SP'
    municipios_municipio_359.lat = Decimal('-23.1747000')
    municipios_municipio_359.lng = Decimal('-46.3983000')
    municipios_municipio_359 = importer.save_or_locate(municipios_municipio_359)

    municipios_municipio_360 = Municipio()
    municipios_municipio_360.cod_ibge = '3532504'
    municipios_municipio_360.nome = 'Neves Paulista'
    municipios_municipio_360.capital = False
    municipios_municipio_360.uf = 'SP'
    municipios_municipio_360.lat = Decimal('-20.8430000')
    municipios_municipio_360.lng = Decimal('-49.6358000')
    municipios_municipio_360 = importer.save_or_locate(municipios_municipio_360)

    municipios_municipio_361 = Municipio()
    municipios_municipio_361.cod_ibge = '3532603'
    municipios_municipio_361.nome = 'Nhandeara'
    municipios_municipio_361.capital = False
    municipios_municipio_361.uf = 'SP'
    municipios_municipio_361.lat = Decimal('-20.6945000')
    municipios_municipio_361.lng = Decimal('-50.0436000')
    municipios_municipio_361 = importer.save_or_locate(municipios_municipio_361)

    municipios_municipio_362 = Municipio()
    municipios_municipio_362.cod_ibge = '3532702'
    municipios_municipio_362.nome = 'Nipoã'
    municipios_municipio_362.capital = False
    municipios_municipio_362.uf = 'SP'
    municipios_municipio_362.lat = Decimal('-20.9114000')
    municipios_municipio_362.lng = Decimal('-49.7833000')
    municipios_municipio_362 = importer.save_or_locate(municipios_municipio_362)

    municipios_municipio_363 = Municipio()
    municipios_municipio_363.cod_ibge = '3532801'
    municipios_municipio_363.nome = 'Nova Aliança'
    municipios_municipio_363.capital = False
    municipios_municipio_363.uf = 'SP'
    municipios_municipio_363.lat = Decimal('-21.0156000')
    municipios_municipio_363.lng = Decimal('-49.4986000')
    municipios_municipio_363 = importer.save_or_locate(municipios_municipio_363)

    municipios_municipio_364 = Municipio()
    municipios_municipio_364.cod_ibge = '3532827'
    municipios_municipio_364.nome = 'Nova Campina'
    municipios_municipio_364.capital = False
    municipios_municipio_364.uf = 'SP'
    municipios_municipio_364.lat = Decimal('-24.1224000')
    municipios_municipio_364.lng = Decimal('-48.9022000')
    municipios_municipio_364 = importer.save_or_locate(municipios_municipio_364)

    municipios_municipio_365 = Municipio()
    municipios_municipio_365.cod_ibge = '3532843'
    municipios_municipio_365.nome = 'Nova Canaã Paulista'
    municipios_municipio_365.capital = False
    municipios_municipio_365.uf = 'SP'
    municipios_municipio_365.lat = Decimal('-20.3836000')
    municipios_municipio_365.lng = Decimal('-50.9483000')
    municipios_municipio_365 = importer.save_or_locate(municipios_municipio_365)

    municipios_municipio_366 = Municipio()
    municipios_municipio_366.cod_ibge = '3532868'
    municipios_municipio_366.nome = 'Nova Castilho'
    municipios_municipio_366.capital = False
    municipios_municipio_366.uf = 'SP'
    municipios_municipio_366.lat = Decimal('-20.7615000')
    municipios_municipio_366.lng = Decimal('-50.3477000')
    municipios_municipio_366 = importer.save_or_locate(municipios_municipio_366)

    municipios_municipio_367 = Municipio()
    municipios_municipio_367.cod_ibge = '3532900'
    municipios_municipio_367.nome = 'Nova Europa'
    municipios_municipio_367.capital = False
    municipios_municipio_367.uf = 'SP'
    municipios_municipio_367.lat = Decimal('-21.7765000')
    municipios_municipio_367.lng = Decimal('-48.5705000')
    municipios_municipio_367 = importer.save_or_locate(municipios_municipio_367)

    municipios_municipio_368 = Municipio()
    municipios_municipio_368.cod_ibge = '3533007'
    municipios_municipio_368.nome = 'Nova Granada'
    municipios_municipio_368.capital = False
    municipios_municipio_368.uf = 'SP'
    municipios_municipio_368.lat = Decimal('-20.5321000')
    municipios_municipio_368.lng = Decimal('-49.3123000')
    municipios_municipio_368 = importer.save_or_locate(municipios_municipio_368)

    municipios_municipio_369 = Municipio()
    municipios_municipio_369.cod_ibge = '3533106'
    municipios_municipio_369.nome = 'Nova Guataporanga'
    municipios_municipio_369.capital = False
    municipios_municipio_369.uf = 'SP'
    municipios_municipio_369.lat = Decimal('-21.3320000')
    municipios_municipio_369.lng = Decimal('-51.6447000')
    municipios_municipio_369 = importer.save_or_locate(municipios_municipio_369)

    municipios_municipio_370 = Municipio()
    municipios_municipio_370.cod_ibge = '3533205'
    municipios_municipio_370.nome = 'Nova Independência'
    municipios_municipio_370.capital = False
    municipios_municipio_370.uf = 'SP'
    municipios_municipio_370.lat = Decimal('-21.1026000')
    municipios_municipio_370.lng = Decimal('-51.4905000')
    municipios_municipio_370 = importer.save_or_locate(municipios_municipio_370)

    municipios_municipio_371 = Municipio()
    municipios_municipio_371.cod_ibge = '3533304'
    municipios_municipio_371.nome = 'Nova Luzitânia'
    municipios_municipio_371.capital = False
    municipios_municipio_371.uf = 'SP'
    municipios_municipio_371.lat = Decimal('-20.8560000')
    municipios_municipio_371.lng = Decimal('-50.2617000')
    municipios_municipio_371 = importer.save_or_locate(municipios_municipio_371)

    municipios_municipio_372 = Municipio()
    municipios_municipio_372.cod_ibge = '3533403'
    municipios_municipio_372.nome = 'Nova Odessa'
    municipios_municipio_372.capital = False
    municipios_municipio_372.uf = 'SP'
    municipios_municipio_372.lat = Decimal('-22.7832000')
    municipios_municipio_372.lng = Decimal('-47.2941000')
    municipios_municipio_372 = importer.save_or_locate(municipios_municipio_372)

    municipios_municipio_373 = Municipio()
    municipios_municipio_373.cod_ibge = '3533254'
    municipios_municipio_373.nome = 'Novais'
    municipios_municipio_373.capital = False
    municipios_municipio_373.uf = 'SP'
    municipios_municipio_373.lat = Decimal('-20.9893000')
    municipios_municipio_373.lng = Decimal('-48.9141000')
    municipios_municipio_373 = importer.save_or_locate(municipios_municipio_373)

    municipios_municipio_374 = Municipio()
    municipios_municipio_374.cod_ibge = '3533502'
    municipios_municipio_374.nome = 'Novo Horizonte'
    municipios_municipio_374.capital = False
    municipios_municipio_374.uf = 'SP'
    municipios_municipio_374.lat = Decimal('-21.4651000')
    municipios_municipio_374.lng = Decimal('-49.2234000')
    municipios_municipio_374 = importer.save_or_locate(municipios_municipio_374)

    municipios_municipio_375 = Municipio()
    municipios_municipio_375.cod_ibge = '3533601'
    municipios_municipio_375.nome = 'Nuporanga'
    municipios_municipio_375.capital = False
    municipios_municipio_375.uf = 'SP'
    municipios_municipio_375.lat = Decimal('-20.7296000')
    municipios_municipio_375.lng = Decimal('-47.7429000')
    municipios_municipio_375 = importer.save_or_locate(municipios_municipio_375)

    municipios_municipio_376 = Municipio()
    municipios_municipio_376.cod_ibge = '3533700'
    municipios_municipio_376.nome = 'Ocauçu'
    municipios_municipio_376.capital = False
    municipios_municipio_376.uf = 'SP'
    municipios_municipio_376.lat = Decimal('-22.4380000')
    municipios_municipio_376.lng = Decimal('-49.9220000')
    municipios_municipio_376 = importer.save_or_locate(municipios_municipio_376)

    municipios_municipio_377 = Municipio()
    municipios_municipio_377.cod_ibge = '3533908'
    municipios_municipio_377.nome = 'Olímpia'
    municipios_municipio_377.capital = False
    municipios_municipio_377.uf = 'SP'
    municipios_municipio_377.lat = Decimal('-20.7366000')
    municipios_municipio_377.lng = Decimal('-48.9106000')
    municipios_municipio_377 = importer.save_or_locate(municipios_municipio_377)

    municipios_municipio_378 = Municipio()
    municipios_municipio_378.cod_ibge = '3534005'
    municipios_municipio_378.nome = 'Onda Verde'
    municipios_municipio_378.capital = False
    municipios_municipio_378.uf = 'SP'
    municipios_municipio_378.lat = Decimal('-20.6042000')
    municipios_municipio_378.lng = Decimal('-49.2929000')
    municipios_municipio_378 = importer.save_or_locate(municipios_municipio_378)

    municipios_municipio_379 = Municipio()
    municipios_municipio_379.cod_ibge = '3534104'
    municipios_municipio_379.nome = 'Oriente'
    municipios_municipio_379.capital = False
    municipios_municipio_379.uf = 'SP'
    municipios_municipio_379.lat = Decimal('-22.1549000')
    municipios_municipio_379.lng = Decimal('-50.0971000')
    municipios_municipio_379 = importer.save_or_locate(municipios_municipio_379)

    municipios_municipio_380 = Municipio()
    municipios_municipio_380.cod_ibge = '3534203'
    municipios_municipio_380.nome = 'Orindiúva'
    municipios_municipio_380.capital = False
    municipios_municipio_380.uf = 'SP'
    municipios_municipio_380.lat = Decimal('-20.1861000')
    municipios_municipio_380.lng = Decimal('-49.3464000')
    municipios_municipio_380 = importer.save_or_locate(municipios_municipio_380)

    municipios_municipio_381 = Municipio()
    municipios_municipio_381.cod_ibge = '3534302'
    municipios_municipio_381.nome = 'Orlândia'
    municipios_municipio_381.capital = False
    municipios_municipio_381.uf = 'SP'
    municipios_municipio_381.lat = Decimal('-20.7169000')
    municipios_municipio_381.lng = Decimal('-47.8852000')
    municipios_municipio_381 = importer.save_or_locate(municipios_municipio_381)

    municipios_municipio_382 = Municipio()
    municipios_municipio_382.cod_ibge = '3534401'
    municipios_municipio_382.nome = 'Osasco'
    municipios_municipio_382.capital = False
    municipios_municipio_382.uf = 'SP'
    municipios_municipio_382.lat = Decimal('-23.5324000')
    municipios_municipio_382.lng = Decimal('-46.7916000')
    municipios_municipio_382 = importer.save_or_locate(municipios_municipio_382)

    municipios_municipio_383 = Municipio()
    municipios_municipio_383.cod_ibge = '3534500'
    municipios_municipio_383.nome = 'Oscar Bressane'
    municipios_municipio_383.capital = False
    municipios_municipio_383.uf = 'SP'
    municipios_municipio_383.lat = Decimal('-22.3149000')
    municipios_municipio_383.lng = Decimal('-50.2811000')
    municipios_municipio_383 = importer.save_or_locate(municipios_municipio_383)

    municipios_municipio_384 = Municipio()
    municipios_municipio_384.cod_ibge = '3534609'
    municipios_municipio_384.nome = 'Osvaldo Cruz'
    municipios_municipio_384.capital = False
    municipios_municipio_384.uf = 'SP'
    municipios_municipio_384.lat = Decimal('-21.7968000')
    municipios_municipio_384.lng = Decimal('-50.8793000')
    municipios_municipio_384 = importer.save_or_locate(municipios_municipio_384)

    municipios_municipio_385 = Municipio()
    municipios_municipio_385.cod_ibge = '3534708'
    municipios_municipio_385.nome = 'Ourinhos'
    municipios_municipio_385.capital = False
    municipios_municipio_385.uf = 'SP'
    municipios_municipio_385.lat = Decimal('-22.9797000')
    municipios_municipio_385.lng = Decimal('-49.8697000')
    municipios_municipio_385 = importer.save_or_locate(municipios_municipio_385)

    municipios_municipio_386 = Municipio()
    municipios_municipio_386.cod_ibge = '3534807'
    municipios_municipio_386.nome = 'Ouro Verde'
    municipios_municipio_386.capital = False
    municipios_municipio_386.uf = 'SP'
    municipios_municipio_386.lat = Decimal('-21.4872000')
    municipios_municipio_386.lng = Decimal('-51.7024000')
    municipios_municipio_386 = importer.save_or_locate(municipios_municipio_386)

    municipios_municipio_387 = Municipio()
    municipios_municipio_387.cod_ibge = '3534757'
    municipios_municipio_387.nome = 'Ouroeste'
    municipios_municipio_387.capital = False
    municipios_municipio_387.uf = 'SP'
    municipios_municipio_387.lat = Decimal('-20.0061000')
    municipios_municipio_387.lng = Decimal('-50.3768000')
    municipios_municipio_387 = importer.save_or_locate(municipios_municipio_387)

    municipios_municipio_388 = Municipio()
    municipios_municipio_388.cod_ibge = '3534906'
    municipios_municipio_388.nome = 'Pacaembu'
    municipios_municipio_388.capital = False
    municipios_municipio_388.uf = 'SP'
    municipios_municipio_388.lat = Decimal('-21.5627000')
    municipios_municipio_388.lng = Decimal('-51.2654000')
    municipios_municipio_388 = importer.save_or_locate(municipios_municipio_388)

    municipios_municipio_389 = Municipio()
    municipios_municipio_389.cod_ibge = '3535002'
    municipios_municipio_389.nome = 'Palestina'
    municipios_municipio_389.capital = False
    municipios_municipio_389.uf = 'SP'
    municipios_municipio_389.lat = Decimal('-20.3900000')
    municipios_municipio_389.lng = Decimal('-49.4309000')
    municipios_municipio_389 = importer.save_or_locate(municipios_municipio_389)

    municipios_municipio_390 = Municipio()
    municipios_municipio_390.cod_ibge = '3535101'
    municipios_municipio_390.nome = 'Palmares Paulista'
    municipios_municipio_390.capital = False
    municipios_municipio_390.uf = 'SP'
    municipios_municipio_390.lat = Decimal('-21.0854000')
    municipios_municipio_390.lng = Decimal('-48.8037000')
    municipios_municipio_390 = importer.save_or_locate(municipios_municipio_390)

    municipios_municipio_391 = Municipio()
    municipios_municipio_391.cod_ibge = '3535200'
    municipios_municipio_391.nome = "Palmeira d'Oeste"
    municipios_municipio_391.capital = False
    municipios_municipio_391.uf = 'SP'
    municipios_municipio_391.lat = Decimal('-20.4148000')
    municipios_municipio_391.lng = Decimal('-50.7632000')
    municipios_municipio_391 = importer.save_or_locate(municipios_municipio_391)

    municipios_municipio_392 = Municipio()
    municipios_municipio_392.cod_ibge = '3535309'
    municipios_municipio_392.nome = 'Palmital'
    municipios_municipio_392.capital = False
    municipios_municipio_392.uf = 'SP'
    municipios_municipio_392.lat = Decimal('-22.7858000')
    municipios_municipio_392.lng = Decimal('-50.2180000')
    municipios_municipio_392 = importer.save_or_locate(municipios_municipio_392)

    municipios_municipio_393 = Municipio()
    municipios_municipio_393.cod_ibge = '3535408'
    municipios_municipio_393.nome = 'Panorama'
    municipios_municipio_393.capital = False
    municipios_municipio_393.uf = 'SP'
    municipios_municipio_393.lat = Decimal('-21.3540000')
    municipios_municipio_393.lng = Decimal('-51.8562000')
    municipios_municipio_393 = importer.save_or_locate(municipios_municipio_393)

    municipios_municipio_394 = Municipio()
    municipios_municipio_394.cod_ibge = '3535507'
    municipios_municipio_394.nome = 'Paraguaçu Paulista'
    municipios_municipio_394.capital = False
    municipios_municipio_394.uf = 'SP'
    municipios_municipio_394.lat = Decimal('-22.4114000')
    municipios_municipio_394.lng = Decimal('-50.5732000')
    municipios_municipio_394 = importer.save_or_locate(municipios_municipio_394)

    municipios_municipio_395 = Municipio()
    municipios_municipio_395.cod_ibge = '3535606'
    municipios_municipio_395.nome = 'Paraibuna'
    municipios_municipio_395.capital = False
    municipios_municipio_395.uf = 'SP'
    municipios_municipio_395.lat = Decimal('-23.3872000')
    municipios_municipio_395.lng = Decimal('-45.6639000')
    municipios_municipio_395 = importer.save_or_locate(municipios_municipio_395)

    municipios_municipio_396 = Municipio()
    municipios_municipio_396.cod_ibge = '3535804'
    municipios_municipio_396.nome = 'Paranapanema'
    municipios_municipio_396.capital = False
    municipios_municipio_396.uf = 'SP'
    municipios_municipio_396.lat = Decimal('-23.3862000')
    municipios_municipio_396.lng = Decimal('-48.7214000')
    municipios_municipio_396 = importer.save_or_locate(municipios_municipio_396)

    municipios_municipio_397 = Municipio()
    municipios_municipio_397.cod_ibge = '3535903'
    municipios_municipio_397.nome = 'Paranapuã'
    municipios_municipio_397.capital = False
    municipios_municipio_397.uf = 'SP'
    municipios_municipio_397.lat = Decimal('-20.1048000')
    municipios_municipio_397.lng = Decimal('-50.5886000')
    municipios_municipio_397 = importer.save_or_locate(municipios_municipio_397)

    municipios_municipio_398 = Municipio()
    municipios_municipio_398.cod_ibge = '3536000'
    municipios_municipio_398.nome = 'Parapuã'
    municipios_municipio_398.capital = False
    municipios_municipio_398.uf = 'SP'
    municipios_municipio_398.lat = Decimal('-21.7792000')
    municipios_municipio_398.lng = Decimal('-50.7949000')
    municipios_municipio_398 = importer.save_or_locate(municipios_municipio_398)

    municipios_municipio_399 = Municipio()
    municipios_municipio_399.cod_ibge = '3535705'
    municipios_municipio_399.nome = 'Paraíso'
    municipios_municipio_399.capital = False
    municipios_municipio_399.uf = 'SP'
    municipios_municipio_399.lat = Decimal('-21.0159000')
    municipios_municipio_399.lng = Decimal('-48.7761000')
    municipios_municipio_399 = importer.save_or_locate(municipios_municipio_399)

    municipios_municipio_400 = Municipio()
    municipios_municipio_400.cod_ibge = '3536109'
    municipios_municipio_400.nome = 'Pardinho'
    municipios_municipio_400.capital = False
    municipios_municipio_400.uf = 'SP'
    municipios_municipio_400.lat = Decimal('-23.0841000')
    municipios_municipio_400.lng = Decimal('-48.3679000')
    municipios_municipio_400 = importer.save_or_locate(municipios_municipio_400)

    municipios_municipio_401 = Municipio()
    municipios_municipio_401.cod_ibge = '3536208'
    municipios_municipio_401.nome = 'Pariquera-Açu'
    municipios_municipio_401.capital = False
    municipios_municipio_401.uf = 'SP'
    municipios_municipio_401.lat = Decimal('-24.7147000')
    municipios_municipio_401.lng = Decimal('-47.8742000')
    municipios_municipio_401 = importer.save_or_locate(municipios_municipio_401)

    municipios_municipio_402 = Municipio()
    municipios_municipio_402.cod_ibge = '3536257'
    municipios_municipio_402.nome = 'Parisi'
    municipios_municipio_402.capital = False
    municipios_municipio_402.uf = 'SP'
    municipios_municipio_402.lat = Decimal('-20.3034000')
    municipios_municipio_402.lng = Decimal('-50.0163000')
    municipios_municipio_402 = importer.save_or_locate(municipios_municipio_402)

    municipios_municipio_403 = Municipio()
    municipios_municipio_403.cod_ibge = '3536307'
    municipios_municipio_403.nome = 'Patrocínio Paulista'
    municipios_municipio_403.capital = False
    municipios_municipio_403.uf = 'SP'
    municipios_municipio_403.lat = Decimal('-20.6384000')
    municipios_municipio_403.lng = Decimal('-47.2801000')
    municipios_municipio_403 = importer.save_or_locate(municipios_municipio_403)

    municipios_municipio_404 = Municipio()
    municipios_municipio_404.cod_ibge = '3536406'
    municipios_municipio_404.nome = 'Paulicéia'
    municipios_municipio_404.capital = False
    municipios_municipio_404.uf = 'SP'
    municipios_municipio_404.lat = Decimal('-21.3153000')
    municipios_municipio_404.lng = Decimal('-51.8321000')
    municipios_municipio_404 = importer.save_or_locate(municipios_municipio_404)

    municipios_municipio_405 = Municipio()
    municipios_municipio_405.cod_ibge = '3536570'
    municipios_municipio_405.nome = 'Paulistânia'
    municipios_municipio_405.capital = False
    municipios_municipio_405.uf = 'SP'
    municipios_municipio_405.lat = Decimal('-22.5768000')
    municipios_municipio_405.lng = Decimal('-49.4008000')
    municipios_municipio_405 = importer.save_or_locate(municipios_municipio_405)

    municipios_municipio_406 = Municipio()
    municipios_municipio_406.cod_ibge = '3536604'
    municipios_municipio_406.nome = 'Paulo de Faria'
    municipios_municipio_406.capital = False
    municipios_municipio_406.uf = 'SP'
    municipios_municipio_406.lat = Decimal('-20.0296000')
    municipios_municipio_406.lng = Decimal('-49.4000000')
    municipios_municipio_406 = importer.save_or_locate(municipios_municipio_406)

    municipios_municipio_407 = Municipio()
    municipios_municipio_407.cod_ibge = '3536505'
    municipios_municipio_407.nome = 'Paulínia'
    municipios_municipio_407.capital = False
    municipios_municipio_407.uf = 'SP'
    municipios_municipio_407.lat = Decimal('-22.7542000')
    municipios_municipio_407.lng = Decimal('-47.1488000')
    municipios_municipio_407 = importer.save_or_locate(municipios_municipio_407)

    municipios_municipio_408 = Municipio()
    municipios_municipio_408.cod_ibge = '3536703'
    municipios_municipio_408.nome = 'Pederneiras'
    municipios_municipio_408.capital = False
    municipios_municipio_408.uf = 'SP'
    municipios_municipio_408.lat = Decimal('-22.3511000')
    municipios_municipio_408.lng = Decimal('-48.7781000')
    municipios_municipio_408 = importer.save_or_locate(municipios_municipio_408)

    municipios_municipio_409 = Municipio()
    municipios_municipio_409.cod_ibge = '3536802'
    municipios_municipio_409.nome = 'Pedra Bela'
    municipios_municipio_409.capital = False
    municipios_municipio_409.uf = 'SP'
    municipios_municipio_409.lat = Decimal('-22.7902000')
    municipios_municipio_409.lng = Decimal('-46.4455000')
    municipios_municipio_409 = importer.save_or_locate(municipios_municipio_409)

    municipios_municipio_410 = Municipio()
    municipios_municipio_410.cod_ibge = '3536901'
    municipios_municipio_410.nome = 'Pedranópolis'
    municipios_municipio_410.capital = False
    municipios_municipio_410.uf = 'SP'
    municipios_municipio_410.lat = Decimal('-20.2474000')
    municipios_municipio_410.lng = Decimal('-50.1129000')
    municipios_municipio_410 = importer.save_or_locate(municipios_municipio_410)

    municipios_municipio_411 = Municipio()
    municipios_municipio_411.cod_ibge = '3537008'
    municipios_municipio_411.nome = 'Pedregulho'
    municipios_municipio_411.capital = False
    municipios_municipio_411.uf = 'SP'
    municipios_municipio_411.lat = Decimal('-20.2535000')
    municipios_municipio_411.lng = Decimal('-47.4775000')
    municipios_municipio_411 = importer.save_or_locate(municipios_municipio_411)

    municipios_municipio_412 = Municipio()
    municipios_municipio_412.cod_ibge = '3537107'
    municipios_municipio_412.nome = 'Pedreira'
    municipios_municipio_412.capital = False
    municipios_municipio_412.uf = 'SP'
    municipios_municipio_412.lat = Decimal('-22.7413000')
    municipios_municipio_412.lng = Decimal('-46.8948000')
    municipios_municipio_412 = importer.save_or_locate(municipios_municipio_412)

    municipios_municipio_413 = Municipio()
    municipios_municipio_413.cod_ibge = '3537156'
    municipios_municipio_413.nome = 'Pedrinhas Paulista'
    municipios_municipio_413.capital = False
    municipios_municipio_413.uf = 'SP'
    municipios_municipio_413.lat = Decimal('-22.8174000')
    municipios_municipio_413.lng = Decimal('-50.7933000')
    municipios_municipio_413 = importer.save_or_locate(municipios_municipio_413)

    municipios_municipio_414 = Municipio()
    municipios_municipio_414.cod_ibge = '3537206'
    municipios_municipio_414.nome = 'Pedro de Toledo'
    municipios_municipio_414.capital = False
    municipios_municipio_414.uf = 'SP'
    municipios_municipio_414.lat = Decimal('-24.2764000')
    municipios_municipio_414.lng = Decimal('-47.2354000')
    municipios_municipio_414 = importer.save_or_locate(municipios_municipio_414)

    municipios_municipio_415 = Municipio()
    municipios_municipio_415.cod_ibge = '3537305'
    municipios_municipio_415.nome = 'Penápolis'
    municipios_municipio_415.capital = False
    municipios_municipio_415.uf = 'SP'
    municipios_municipio_415.lat = Decimal('-21.4148000')
    municipios_municipio_415.lng = Decimal('-50.0769000')
    municipios_municipio_415 = importer.save_or_locate(municipios_municipio_415)

    municipios_municipio_416 = Municipio()
    municipios_municipio_416.cod_ibge = '3537404'
    municipios_municipio_416.nome = 'Pereira Barreto'
    municipios_municipio_416.capital = False
    municipios_municipio_416.uf = 'SP'
    municipios_municipio_416.lat = Decimal('-20.6368000')
    municipios_municipio_416.lng = Decimal('-51.1123000')
    municipios_municipio_416 = importer.save_or_locate(municipios_municipio_416)

    municipios_municipio_417 = Municipio()
    municipios_municipio_417.cod_ibge = '3537503'
    municipios_municipio_417.nome = 'Pereiras'
    municipios_municipio_417.capital = False
    municipios_municipio_417.uf = 'SP'
    municipios_municipio_417.lat = Decimal('-23.0804000')
    municipios_municipio_417.lng = Decimal('-47.9720000')
    municipios_municipio_417 = importer.save_or_locate(municipios_municipio_417)

    municipios_municipio_418 = Municipio()
    municipios_municipio_418.cod_ibge = '3537602'
    municipios_municipio_418.nome = 'Peruíbe'
    municipios_municipio_418.capital = False
    municipios_municipio_418.uf = 'SP'
    municipios_municipio_418.lat = Decimal('-24.3120000')
    municipios_municipio_418.lng = Decimal('-47.0012000')
    municipios_municipio_418 = importer.save_or_locate(municipios_municipio_418)

    municipios_municipio_419 = Municipio()
    municipios_municipio_419.cod_ibge = '3537701'
    municipios_municipio_419.nome = 'Piacatu'
    municipios_municipio_419.capital = False
    municipios_municipio_419.uf = 'SP'
    municipios_municipio_419.lat = Decimal('-21.5921000')
    municipios_municipio_419.lng = Decimal('-50.6003000')
    municipios_municipio_419 = importer.save_or_locate(municipios_municipio_419)

    municipios_municipio_420 = Municipio()
    municipios_municipio_420.cod_ibge = '3537800'
    municipios_municipio_420.nome = 'Piedade'
    municipios_municipio_420.capital = False
    municipios_municipio_420.uf = 'SP'
    municipios_municipio_420.lat = Decimal('-23.7139000')
    municipios_municipio_420.lng = Decimal('-47.4256000')
    municipios_municipio_420 = importer.save_or_locate(municipios_municipio_420)

    municipios_municipio_421 = Municipio()
    municipios_municipio_421.cod_ibge = '3537909'
    municipios_municipio_421.nome = 'Pilar do Sul'
    municipios_municipio_421.capital = False
    municipios_municipio_421.uf = 'SP'
    municipios_municipio_421.lat = Decimal('-23.8077000')
    municipios_municipio_421.lng = Decimal('-47.7222000')
    municipios_municipio_421 = importer.save_or_locate(municipios_municipio_421)

    municipios_municipio_422 = Municipio()
    municipios_municipio_422.cod_ibge = '3538006'
    municipios_municipio_422.nome = 'Pindamonhangaba'
    municipios_municipio_422.capital = False
    municipios_municipio_422.uf = 'SP'
    municipios_municipio_422.lat = Decimal('-22.9246000')
    municipios_municipio_422.lng = Decimal('-45.4613000')
    municipios_municipio_422 = importer.save_or_locate(municipios_municipio_422)

    municipios_municipio_423 = Municipio()
    municipios_municipio_423.cod_ibge = '3538105'
    municipios_municipio_423.nome = 'Pindorama'
    municipios_municipio_423.capital = False
    municipios_municipio_423.uf = 'SP'
    municipios_municipio_423.lat = Decimal('-21.1853000')
    municipios_municipio_423.lng = Decimal('-48.9086000')
    municipios_municipio_423 = importer.save_or_locate(municipios_municipio_423)

    municipios_municipio_424 = Municipio()
    municipios_municipio_424.cod_ibge = '3538204'
    municipios_municipio_424.nome = 'Pinhalzinho'
    municipios_municipio_424.capital = False
    municipios_municipio_424.uf = 'SP'
    municipios_municipio_424.lat = Decimal('-22.7811000')
    municipios_municipio_424.lng = Decimal('-46.5897000')
    municipios_municipio_424 = importer.save_or_locate(municipios_municipio_424)

    municipios_municipio_425 = Municipio()
    municipios_municipio_425.cod_ibge = '3538303'
    municipios_municipio_425.nome = 'Piquerobi'
    municipios_municipio_425.capital = False
    municipios_municipio_425.uf = 'SP'
    municipios_municipio_425.lat = Decimal('-21.8747000')
    municipios_municipio_425.lng = Decimal('-51.7282000')
    municipios_municipio_425 = importer.save_or_locate(municipios_municipio_425)

    municipios_municipio_426 = Municipio()
    municipios_municipio_426.cod_ibge = '3538501'
    municipios_municipio_426.nome = 'Piquete'
    municipios_municipio_426.capital = False
    municipios_municipio_426.uf = 'SP'
    municipios_municipio_426.lat = Decimal('-22.6069000')
    municipios_municipio_426.lng = Decimal('-45.1869000')
    municipios_municipio_426 = importer.save_or_locate(municipios_municipio_426)

    municipios_municipio_427 = Municipio()
    municipios_municipio_427.cod_ibge = '3538600'
    municipios_municipio_427.nome = 'Piracaia'
    municipios_municipio_427.capital = False
    municipios_municipio_427.uf = 'SP'
    municipios_municipio_427.lat = Decimal('-23.0525000')
    municipios_municipio_427.lng = Decimal('-46.3594000')
    municipios_municipio_427 = importer.save_or_locate(municipios_municipio_427)

    municipios_municipio_428 = Municipio()
    municipios_municipio_428.cod_ibge = '3538709'
    municipios_municipio_428.nome = 'Piracicaba'
    municipios_municipio_428.capital = False
    municipios_municipio_428.uf = 'SP'
    municipios_municipio_428.lat = Decimal('-22.7338000')
    municipios_municipio_428.lng = Decimal('-47.6476000')
    municipios_municipio_428 = importer.save_or_locate(municipios_municipio_428)

    municipios_municipio_429 = Municipio()
    municipios_municipio_429.cod_ibge = '3538808'
    municipios_municipio_429.nome = 'Piraju'
    municipios_municipio_429.capital = False
    municipios_municipio_429.uf = 'SP'
    municipios_municipio_429.lat = Decimal('-23.1981000')
    municipios_municipio_429.lng = Decimal('-49.3803000')
    municipios_municipio_429 = importer.save_or_locate(municipios_municipio_429)

    municipios_municipio_430 = Municipio()
    municipios_municipio_430.cod_ibge = '3538907'
    municipios_municipio_430.nome = 'Pirajuí'
    municipios_municipio_430.capital = False
    municipios_municipio_430.uf = 'SP'
    municipios_municipio_430.lat = Decimal('-21.9990000')
    municipios_municipio_430.lng = Decimal('-49.4608000')
    municipios_municipio_430 = importer.save_or_locate(municipios_municipio_430)

    municipios_municipio_431 = Municipio()
    municipios_municipio_431.cod_ibge = '3539004'
    municipios_municipio_431.nome = 'Pirangi'
    municipios_municipio_431.capital = False
    municipios_municipio_431.uf = 'SP'
    municipios_municipio_431.lat = Decimal('-21.0886000')
    municipios_municipio_431.lng = Decimal('-48.6607000')
    municipios_municipio_431 = importer.save_or_locate(municipios_municipio_431)

    municipios_municipio_432 = Municipio()
    municipios_municipio_432.cod_ibge = '3539103'
    municipios_municipio_432.nome = 'Pirapora do Bom Jesus'
    municipios_municipio_432.capital = False
    municipios_municipio_432.uf = 'SP'
    municipios_municipio_432.lat = Decimal('-23.3965000')
    municipios_municipio_432.lng = Decimal('-46.9991000')
    municipios_municipio_432 = importer.save_or_locate(municipios_municipio_432)

    municipios_municipio_433 = Municipio()
    municipios_municipio_433.cod_ibge = '3539202'
    municipios_municipio_433.nome = 'Pirapozinho'
    municipios_municipio_433.capital = False
    municipios_municipio_433.uf = 'SP'
    municipios_municipio_433.lat = Decimal('-22.2711000')
    municipios_municipio_433.lng = Decimal('-51.4976000')
    municipios_municipio_433 = importer.save_or_locate(municipios_municipio_433)

    municipios_municipio_434 = Municipio()
    municipios_municipio_434.cod_ibge = '3539301'
    municipios_municipio_434.nome = 'Pirassununga'
    municipios_municipio_434.capital = False
    municipios_municipio_434.uf = 'SP'
    municipios_municipio_434.lat = Decimal('-21.9960000')
    municipios_municipio_434.lng = Decimal('-47.4257000')
    municipios_municipio_434 = importer.save_or_locate(municipios_municipio_434)

    municipios_municipio_435 = Municipio()
    municipios_municipio_435.cod_ibge = '3539400'
    municipios_municipio_435.nome = 'Piratininga'
    municipios_municipio_435.capital = False
    municipios_municipio_435.uf = 'SP'
    municipios_municipio_435.lat = Decimal('-22.4142000')
    municipios_municipio_435.lng = Decimal('-49.1339000')
    municipios_municipio_435 = importer.save_or_locate(municipios_municipio_435)

    municipios_municipio_436 = Municipio()
    municipios_municipio_436.cod_ibge = '3539509'
    municipios_municipio_436.nome = 'Pitangueiras'
    municipios_municipio_436.capital = False
    municipios_municipio_436.uf = 'SP'
    municipios_municipio_436.lat = Decimal('-21.0132000')
    municipios_municipio_436.lng = Decimal('-48.2210000')
    municipios_municipio_436 = importer.save_or_locate(municipios_municipio_436)

    municipios_municipio_437 = Municipio()
    municipios_municipio_437.cod_ibge = '3539608'
    municipios_municipio_437.nome = 'Planalto'
    municipios_municipio_437.capital = False
    municipios_municipio_437.uf = 'SP'
    municipios_municipio_437.lat = Decimal('-21.0342000')
    municipios_municipio_437.lng = Decimal('-49.9330000')
    municipios_municipio_437 = importer.save_or_locate(municipios_municipio_437)

    municipios_municipio_438 = Municipio()
    municipios_municipio_438.cod_ibge = '3539707'
    municipios_municipio_438.nome = 'Platina'
    municipios_municipio_438.capital = False
    municipios_municipio_438.uf = 'SP'
    municipios_municipio_438.lat = Decimal('-22.6371000')
    municipios_municipio_438.lng = Decimal('-50.2104000')
    municipios_municipio_438 = importer.save_or_locate(municipios_municipio_438)

    municipios_municipio_439 = Municipio()
    municipios_municipio_439.cod_ibge = '3539905'
    municipios_municipio_439.nome = 'Poloni'
    municipios_municipio_439.capital = False
    municipios_municipio_439.uf = 'SP'
    municipios_municipio_439.lat = Decimal('-20.7829000')
    municipios_municipio_439.lng = Decimal('-49.8258000')
    municipios_municipio_439 = importer.save_or_locate(municipios_municipio_439)

    municipios_municipio_440 = Municipio()
    municipios_municipio_440.cod_ibge = '3540002'
    municipios_municipio_440.nome = 'Pompéia'
    municipios_municipio_440.capital = False
    municipios_municipio_440.uf = 'SP'
    municipios_municipio_440.lat = Decimal('-22.1070000')
    municipios_municipio_440.lng = Decimal('-50.1760000')
    municipios_municipio_440 = importer.save_or_locate(municipios_municipio_440)

    municipios_municipio_441 = Municipio()
    municipios_municipio_441.cod_ibge = '3540101'
    municipios_municipio_441.nome = 'Pongaí'
    municipios_municipio_441.capital = False
    municipios_municipio_441.uf = 'SP'
    municipios_municipio_441.lat = Decimal('-21.7396000')
    municipios_municipio_441.lng = Decimal('-49.3604000')
    municipios_municipio_441 = importer.save_or_locate(municipios_municipio_441)

    municipios_municipio_442 = Municipio()
    municipios_municipio_442.cod_ibge = '3540200'
    municipios_municipio_442.nome = 'Pontal'
    municipios_municipio_442.capital = False
    municipios_municipio_442.uf = 'SP'
    municipios_municipio_442.lat = Decimal('-21.0216000')
    municipios_municipio_442.lng = Decimal('-48.0423000')
    municipios_municipio_442 = importer.save_or_locate(municipios_municipio_442)

    municipios_municipio_443 = Municipio()
    municipios_municipio_443.cod_ibge = '3540259'
    municipios_municipio_443.nome = 'Pontalinda'
    municipios_municipio_443.capital = False
    municipios_municipio_443.uf = 'SP'
    municipios_municipio_443.lat = Decimal('-20.4396000')
    municipios_municipio_443.lng = Decimal('-50.5258000')
    municipios_municipio_443 = importer.save_or_locate(municipios_municipio_443)

    municipios_municipio_444 = Municipio()
    municipios_municipio_444.cod_ibge = '3540309'
    municipios_municipio_444.nome = 'Pontes Gestal'
    municipios_municipio_444.capital = False
    municipios_municipio_444.uf = 'SP'
    municipios_municipio_444.lat = Decimal('-20.1727000')
    municipios_municipio_444.lng = Decimal('-49.7064000')
    municipios_municipio_444 = importer.save_or_locate(municipios_municipio_444)

    municipios_municipio_445 = Municipio()
    municipios_municipio_445.cod_ibge = '3540408'
    municipios_municipio_445.nome = 'Populina'
    municipios_municipio_445.capital = False
    municipios_municipio_445.uf = 'SP'
    municipios_municipio_445.lat = Decimal('-19.9453000')
    municipios_municipio_445.lng = Decimal('-50.5380000')
    municipios_municipio_445 = importer.save_or_locate(municipios_municipio_445)

    municipios_municipio_446 = Municipio()
    municipios_municipio_446.cod_ibge = '3540507'
    municipios_municipio_446.nome = 'Porangaba'
    municipios_municipio_446.capital = False
    municipios_municipio_446.uf = 'SP'
    municipios_municipio_446.lat = Decimal('-23.1761000')
    municipios_municipio_446.lng = Decimal('-48.1195000')
    municipios_municipio_446 = importer.save_or_locate(municipios_municipio_446)

    municipios_municipio_447 = Municipio()
    municipios_municipio_447.cod_ibge = '3540606'
    municipios_municipio_447.nome = 'Porto Feliz'
    municipios_municipio_447.capital = False
    municipios_municipio_447.uf = 'SP'
    municipios_municipio_447.lat = Decimal('-23.2093000')
    municipios_municipio_447.lng = Decimal('-47.5251000')
    municipios_municipio_447 = importer.save_or_locate(municipios_municipio_447)

    municipios_municipio_448 = Municipio()
    municipios_municipio_448.cod_ibge = '3540705'
    municipios_municipio_448.nome = 'Porto Ferreira'
    municipios_municipio_448.capital = False
    municipios_municipio_448.uf = 'SP'
    municipios_municipio_448.lat = Decimal('-21.8498000')
    municipios_municipio_448.lng = Decimal('-47.4870000')
    municipios_municipio_448 = importer.save_or_locate(municipios_municipio_448)

    municipios_municipio_449 = Municipio()
    municipios_municipio_449.cod_ibge = '3540754'
    municipios_municipio_449.nome = 'Potim'
    municipios_municipio_449.capital = False
    municipios_municipio_449.uf = 'SP'
    municipios_municipio_449.lat = Decimal('-22.8343000')
    municipios_municipio_449.lng = Decimal('-45.2552000')
    municipios_municipio_449 = importer.save_or_locate(municipios_municipio_449)

    municipios_municipio_450 = Municipio()
    municipios_municipio_450.cod_ibge = '3540804'
    municipios_municipio_450.nome = 'Potirendaba'
    municipios_municipio_450.capital = False
    municipios_municipio_450.uf = 'SP'
    municipios_municipio_450.lat = Decimal('-21.0428000')
    municipios_municipio_450.lng = Decimal('-49.3815000')
    municipios_municipio_450 = importer.save_or_locate(municipios_municipio_450)

    municipios_municipio_451 = Municipio()
    municipios_municipio_451.cod_ibge = '3539806'
    municipios_municipio_451.nome = 'Poá'
    municipios_municipio_451.capital = False
    municipios_municipio_451.uf = 'SP'
    municipios_municipio_451.lat = Decimal('-23.5333000')
    municipios_municipio_451.lng = Decimal('-46.3473000')
    municipios_municipio_451 = importer.save_or_locate(municipios_municipio_451)

    municipios_municipio_452 = Municipio()
    municipios_municipio_452.cod_ibge = '3540853'
    municipios_municipio_452.nome = 'Pracinha'
    municipios_municipio_452.capital = False
    municipios_municipio_452.uf = 'SP'
    municipios_municipio_452.lat = Decimal('-21.8496000')
    municipios_municipio_452.lng = Decimal('-51.0868000')
    municipios_municipio_452 = importer.save_or_locate(municipios_municipio_452)

    municipios_municipio_453 = Municipio()
    municipios_municipio_453.cod_ibge = '3540903'
    municipios_municipio_453.nome = 'Pradópolis'
    municipios_municipio_453.capital = False
    municipios_municipio_453.uf = 'SP'
    municipios_municipio_453.lat = Decimal('-21.3626000')
    municipios_municipio_453.lng = Decimal('-48.0679000')
    municipios_municipio_453 = importer.save_or_locate(municipios_municipio_453)

    municipios_municipio_454 = Municipio()
    municipios_municipio_454.cod_ibge = '3541000'
    municipios_municipio_454.nome = 'Praia Grande'
    municipios_municipio_454.capital = False
    municipios_municipio_454.uf = 'SP'
    municipios_municipio_454.lat = Decimal('-24.0084000')
    municipios_municipio_454.lng = Decimal('-46.4121000')
    municipios_municipio_454 = importer.save_or_locate(municipios_municipio_454)

    municipios_municipio_455 = Municipio()
    municipios_municipio_455.cod_ibge = '3541059'
    municipios_municipio_455.nome = 'Pratânia'
    municipios_municipio_455.capital = False
    municipios_municipio_455.uf = 'SP'
    municipios_municipio_455.lat = Decimal('-22.8112000')
    municipios_municipio_455.lng = Decimal('-48.6636000')
    municipios_municipio_455 = importer.save_or_locate(municipios_municipio_455)

    municipios_municipio_456 = Municipio()
    municipios_municipio_456.cod_ibge = '3541109'
    municipios_municipio_456.nome = 'Presidente Alves'
    municipios_municipio_456.capital = False
    municipios_municipio_456.uf = 'SP'
    municipios_municipio_456.lat = Decimal('-22.0999000')
    municipios_municipio_456.lng = Decimal('-49.4381000')
    municipios_municipio_456 = importer.save_or_locate(municipios_municipio_456)

    municipios_municipio_457 = Municipio()
    municipios_municipio_457.cod_ibge = '3541208'
    municipios_municipio_457.nome = 'Presidente Bernardes'
    municipios_municipio_457.capital = False
    municipios_municipio_457.uf = 'SP'
    municipios_municipio_457.lat = Decimal('-22.0082000')
    municipios_municipio_457.lng = Decimal('-51.5565000')
    municipios_municipio_457 = importer.save_or_locate(municipios_municipio_457)

    municipios_municipio_458 = Municipio()
    municipios_municipio_458.cod_ibge = '3541307'
    municipios_municipio_458.nome = 'Presidente Epitácio'
    municipios_municipio_458.capital = False
    municipios_municipio_458.uf = 'SP'
    municipios_municipio_458.lat = Decimal('-21.7651000')
    municipios_municipio_458.lng = Decimal('-52.1111000')
    municipios_municipio_458 = importer.save_or_locate(municipios_municipio_458)

    municipios_municipio_459 = Municipio()
    municipios_municipio_459.cod_ibge = '3541406'
    municipios_municipio_459.nome = 'Presidente Prudente'
    municipios_municipio_459.capital = False
    municipios_municipio_459.uf = 'SP'
    municipios_municipio_459.lat = Decimal('-22.1207000')
    municipios_municipio_459.lng = Decimal('-51.3925000')
    municipios_municipio_459 = importer.save_or_locate(municipios_municipio_459)

    municipios_municipio_460 = Municipio()
    municipios_municipio_460.cod_ibge = '3541505'
    municipios_municipio_460.nome = 'Presidente Venceslau'
    municipios_municipio_460.capital = False
    municipios_municipio_460.uf = 'SP'
    municipios_municipio_460.lat = Decimal('-21.8732000')
    municipios_municipio_460.lng = Decimal('-51.8447000')
    municipios_municipio_460 = importer.save_or_locate(municipios_municipio_460)

    municipios_municipio_461 = Municipio()
    municipios_municipio_461.cod_ibge = '3541604'
    municipios_municipio_461.nome = 'Promissão'
    municipios_municipio_461.capital = False
    municipios_municipio_461.uf = 'SP'
    municipios_municipio_461.lat = Decimal('-21.5356000')
    municipios_municipio_461.lng = Decimal('-49.8599000')
    municipios_municipio_461 = importer.save_or_locate(municipios_municipio_461)

    municipios_municipio_462 = Municipio()
    municipios_municipio_462.cod_ibge = '3541653'
    municipios_municipio_462.nome = 'Quadra'
    municipios_municipio_462.capital = False
    municipios_municipio_462.uf = 'SP'
    municipios_municipio_462.lat = Decimal('-23.2993000')
    municipios_municipio_462.lng = Decimal('-48.0547000')
    municipios_municipio_462 = importer.save_or_locate(municipios_municipio_462)

    municipios_municipio_463 = Municipio()
    municipios_municipio_463.cod_ibge = '3541703'
    municipios_municipio_463.nome = 'Quatá'
    municipios_municipio_463.capital = False
    municipios_municipio_463.uf = 'SP'
    municipios_municipio_463.lat = Decimal('-22.2456000')
    municipios_municipio_463.lng = Decimal('-50.6966000')
    municipios_municipio_463 = importer.save_or_locate(municipios_municipio_463)

    municipios_municipio_464 = Municipio()
    municipios_municipio_464.cod_ibge = '3541802'
    municipios_municipio_464.nome = 'Queiroz'
    municipios_municipio_464.capital = False
    municipios_municipio_464.uf = 'SP'
    municipios_municipio_464.lat = Decimal('-21.7969000')
    municipios_municipio_464.lng = Decimal('-50.2415000')
    municipios_municipio_464 = importer.save_or_locate(municipios_municipio_464)

    municipios_municipio_465 = Municipio()
    municipios_municipio_465.cod_ibge = '3541901'
    municipios_municipio_465.nome = 'Queluz'
    municipios_municipio_465.capital = False
    municipios_municipio_465.uf = 'SP'
    municipios_municipio_465.lat = Decimal('-22.5312000')
    municipios_municipio_465.lng = Decimal('-44.7781000')
    municipios_municipio_465 = importer.save_or_locate(municipios_municipio_465)

    municipios_municipio_466 = Municipio()
    municipios_municipio_466.cod_ibge = '3542008'
    municipios_municipio_466.nome = 'Quintana'
    municipios_municipio_466.capital = False
    municipios_municipio_466.uf = 'SP'
    municipios_municipio_466.lat = Decimal('-22.0692000')
    municipios_municipio_466.lng = Decimal('-50.3070000')
    municipios_municipio_466 = importer.save_or_locate(municipios_municipio_466)

    municipios_municipio_467 = Municipio()
    municipios_municipio_467.cod_ibge = '3542107'
    municipios_municipio_467.nome = 'Rafard'
    municipios_municipio_467.capital = False
    municipios_municipio_467.uf = 'SP'
    municipios_municipio_467.lat = Decimal('-23.0105000')
    municipios_municipio_467.lng = Decimal('-47.5318000')
    municipios_municipio_467 = importer.save_or_locate(municipios_municipio_467)

    municipios_municipio_468 = Municipio()
    municipios_municipio_468.cod_ibge = '3542206'
    municipios_municipio_468.nome = 'Rancharia'
    municipios_municipio_468.capital = False
    municipios_municipio_468.uf = 'SP'
    municipios_municipio_468.lat = Decimal('-22.2269000')
    municipios_municipio_468.lng = Decimal('-50.8930000')
    municipios_municipio_468 = importer.save_or_locate(municipios_municipio_468)

    municipios_municipio_469 = Municipio()
    municipios_municipio_469.cod_ibge = '3542305'
    municipios_municipio_469.nome = 'Redenção da Serra'
    municipios_municipio_469.capital = False
    municipios_municipio_469.uf = 'SP'
    municipios_municipio_469.lat = Decimal('-23.2638000')
    municipios_municipio_469.lng = Decimal('-45.5422000')
    municipios_municipio_469 = importer.save_or_locate(municipios_municipio_469)

    municipios_municipio_470 = Municipio()
    municipios_municipio_470.cod_ibge = '3542404'
    municipios_municipio_470.nome = 'Regente Feijó'
    municipios_municipio_470.capital = False
    municipios_municipio_470.uf = 'SP'
    municipios_municipio_470.lat = Decimal('-22.2181000')
    municipios_municipio_470.lng = Decimal('-51.3055000')
    municipios_municipio_470 = importer.save_or_locate(municipios_municipio_470)

    municipios_municipio_471 = Municipio()
    municipios_municipio_471.cod_ibge = '3542503'
    municipios_municipio_471.nome = 'Reginópolis'
    municipios_municipio_471.capital = False
    municipios_municipio_471.uf = 'SP'
    municipios_municipio_471.lat = Decimal('-21.8914000')
    municipios_municipio_471.lng = Decimal('-49.2268000')
    municipios_municipio_471 = importer.save_or_locate(municipios_municipio_471)

    municipios_municipio_472 = Municipio()
    municipios_municipio_472.cod_ibge = '3542602'
    municipios_municipio_472.nome = 'Registro'
    municipios_municipio_472.capital = False
    municipios_municipio_472.uf = 'SP'
    municipios_municipio_472.lat = Decimal('-24.4979000')
    municipios_municipio_472.lng = Decimal('-47.8449000')
    municipios_municipio_472 = importer.save_or_locate(municipios_municipio_472)

    municipios_municipio_473 = Municipio()
    municipios_municipio_473.cod_ibge = '3542701'
    municipios_municipio_473.nome = 'Restinga'
    municipios_municipio_473.capital = False
    municipios_municipio_473.uf = 'SP'
    municipios_municipio_473.lat = Decimal('-20.6056000')
    municipios_municipio_473.lng = Decimal('-47.4833000')
    municipios_municipio_473 = importer.save_or_locate(municipios_municipio_473)

    municipios_municipio_474 = Municipio()
    municipios_municipio_474.cod_ibge = '3542800'
    municipios_municipio_474.nome = 'Ribeira'
    municipios_municipio_474.capital = False
    municipios_municipio_474.uf = 'SP'
    municipios_municipio_474.lat = Decimal('-24.6517000')
    municipios_municipio_474.lng = Decimal('-49.0044000')
    municipios_municipio_474 = importer.save_or_locate(municipios_municipio_474)

    municipios_municipio_475 = Municipio()
    municipios_municipio_475.cod_ibge = '3542909'
    municipios_municipio_475.nome = 'Ribeirão Bonito'
    municipios_municipio_475.capital = False
    municipios_municipio_475.uf = 'SP'
    municipios_municipio_475.lat = Decimal('-22.0685000')
    municipios_municipio_475.lng = Decimal('-48.1820000')
    municipios_municipio_475 = importer.save_or_locate(municipios_municipio_475)

    municipios_municipio_476 = Municipio()
    municipios_municipio_476.cod_ibge = '3543006'
    municipios_municipio_476.nome = 'Ribeirão Branco'
    municipios_municipio_476.capital = False
    municipios_municipio_476.uf = 'SP'
    municipios_municipio_476.lat = Decimal('-24.2206000')
    municipios_municipio_476.lng = Decimal('-48.7635000')
    municipios_municipio_476 = importer.save_or_locate(municipios_municipio_476)

    municipios_municipio_477 = Municipio()
    municipios_municipio_477.cod_ibge = '3543105'
    municipios_municipio_477.nome = 'Ribeirão Corrente'
    municipios_municipio_477.capital = False
    municipios_municipio_477.uf = 'SP'
    municipios_municipio_477.lat = Decimal('-20.4579000')
    municipios_municipio_477.lng = Decimal('-47.5904000')
    municipios_municipio_477 = importer.save_or_locate(municipios_municipio_477)

    municipios_municipio_478 = Municipio()
    municipios_municipio_478.cod_ibge = '3543253'
    municipios_municipio_478.nome = 'Ribeirão Grande'
    municipios_municipio_478.capital = False
    municipios_municipio_478.uf = 'SP'
    municipios_municipio_478.lat = Decimal('-24.1011000')
    municipios_municipio_478.lng = Decimal('-48.3679000')
    municipios_municipio_478 = importer.save_or_locate(municipios_municipio_478)

    municipios_municipio_479 = Municipio()
    municipios_municipio_479.cod_ibge = '3543303'
    municipios_municipio_479.nome = 'Ribeirão Pires'
    municipios_municipio_479.capital = False
    municipios_municipio_479.uf = 'SP'
    municipios_municipio_479.lat = Decimal('-23.7067000')
    municipios_municipio_479.lng = Decimal('-46.4058000')
    municipios_municipio_479 = importer.save_or_locate(municipios_municipio_479)

    municipios_municipio_480 = Municipio()
    municipios_municipio_480.cod_ibge = '3543402'
    municipios_municipio_480.nome = 'Ribeirão Preto'
    municipios_municipio_480.capital = False
    municipios_municipio_480.uf = 'SP'
    municipios_municipio_480.lat = Decimal('-21.1699000')
    municipios_municipio_480.lng = Decimal('-47.8099000')
    municipios_municipio_480 = importer.save_or_locate(municipios_municipio_480)

    municipios_municipio_481 = Municipio()
    municipios_municipio_481.cod_ibge = '3543204'
    municipios_municipio_481.nome = 'Ribeirão do Sul'
    municipios_municipio_481.capital = False
    municipios_municipio_481.uf = 'SP'
    municipios_municipio_481.lat = Decimal('-22.7890000')
    municipios_municipio_481.lng = Decimal('-49.9330000')
    municipios_municipio_481 = importer.save_or_locate(municipios_municipio_481)

    municipios_municipio_482 = Municipio()
    municipios_municipio_482.cod_ibge = '3543238'
    municipios_municipio_482.nome = 'Ribeirão dos Índios'
    municipios_municipio_482.capital = False
    municipios_municipio_482.uf = 'SP'
    municipios_municipio_482.lat = Decimal('-21.8382000')
    municipios_municipio_482.lng = Decimal('-51.6103000')
    municipios_municipio_482 = importer.save_or_locate(municipios_municipio_482)

    municipios_municipio_483 = Municipio()
    municipios_municipio_483.cod_ibge = '3543600'
    municipios_municipio_483.nome = 'Rifaina'
    municipios_municipio_483.capital = False
    municipios_municipio_483.uf = 'SP'
    municipios_municipio_483.lat = Decimal('-20.0803000')
    municipios_municipio_483.lng = Decimal('-47.4291000')
    municipios_municipio_483 = importer.save_or_locate(municipios_municipio_483)

    municipios_municipio_484 = Municipio()
    municipios_municipio_484.cod_ibge = '3543709'
    municipios_municipio_484.nome = 'Rincão'
    municipios_municipio_484.capital = False
    municipios_municipio_484.uf = 'SP'
    municipios_municipio_484.lat = Decimal('-21.5894000')
    municipios_municipio_484.lng = Decimal('-48.0728000')
    municipios_municipio_484 = importer.save_or_locate(municipios_municipio_484)

    municipios_municipio_485 = Municipio()
    municipios_municipio_485.cod_ibge = '3543808'
    municipios_municipio_485.nome = 'Rinópolis'
    municipios_municipio_485.capital = False
    municipios_municipio_485.uf = 'SP'
    municipios_municipio_485.lat = Decimal('-21.7284000')
    municipios_municipio_485.lng = Decimal('-50.7239000')
    municipios_municipio_485 = importer.save_or_locate(municipios_municipio_485)

    municipios_municipio_486 = Municipio()
    municipios_municipio_486.cod_ibge = '3543907'
    municipios_municipio_486.nome = 'Rio Claro'
    municipios_municipio_486.capital = False
    municipios_municipio_486.uf = 'SP'
    municipios_municipio_486.lat = Decimal('-22.3984000')
    municipios_municipio_486.lng = Decimal('-47.5546000')
    municipios_municipio_486 = importer.save_or_locate(municipios_municipio_486)

    municipios_municipio_487 = Municipio()
    municipios_municipio_487.cod_ibge = '3544103'
    municipios_municipio_487.nome = 'Rio Grande da Serra'
    municipios_municipio_487.capital = False
    municipios_municipio_487.uf = 'SP'
    municipios_municipio_487.lat = Decimal('-23.7437000')
    municipios_municipio_487.lng = Decimal('-46.3971000')
    municipios_municipio_487 = importer.save_or_locate(municipios_municipio_487)

    municipios_municipio_488 = Municipio()
    municipios_municipio_488.cod_ibge = '3544004'
    municipios_municipio_488.nome = 'Rio das Pedras'
    municipios_municipio_488.capital = False
    municipios_municipio_488.uf = 'SP'
    municipios_municipio_488.lat = Decimal('-22.8417000')
    municipios_municipio_488.lng = Decimal('-47.6047000')
    municipios_municipio_488 = importer.save_or_locate(municipios_municipio_488)

    municipios_municipio_489 = Municipio()
    municipios_municipio_489.cod_ibge = '3544202'
    municipios_municipio_489.nome = 'Riolândia'
    municipios_municipio_489.capital = False
    municipios_municipio_489.uf = 'SP'
    municipios_municipio_489.lat = Decimal('-19.9868000')
    municipios_municipio_489.lng = Decimal('-49.6836000')
    municipios_municipio_489 = importer.save_or_locate(municipios_municipio_489)

    municipios_municipio_490 = Municipio()
    municipios_municipio_490.cod_ibge = '3543501'
    municipios_municipio_490.nome = 'Riversul'
    municipios_municipio_490.capital = False
    municipios_municipio_490.uf = 'SP'
    municipios_municipio_490.lat = Decimal('-23.8290000')
    municipios_municipio_490.lng = Decimal('-49.4290000')
    municipios_municipio_490 = importer.save_or_locate(municipios_municipio_490)

    municipios_municipio_491 = Municipio()
    municipios_municipio_491.cod_ibge = '3544251'
    municipios_municipio_491.nome = 'Rosana'
    municipios_municipio_491.capital = False
    municipios_municipio_491.uf = 'SP'
    municipios_municipio_491.lat = Decimal('-22.5782000')
    municipios_municipio_491.lng = Decimal('-53.0603000')
    municipios_municipio_491 = importer.save_or_locate(municipios_municipio_491)

    municipios_municipio_492 = Municipio()
    municipios_municipio_492.cod_ibge = '3544301'
    municipios_municipio_492.nome = 'Roseira'
    municipios_municipio_492.capital = False
    municipios_municipio_492.uf = 'SP'
    municipios_municipio_492.lat = Decimal('-22.8938000')
    municipios_municipio_492.lng = Decimal('-45.3070000')
    municipios_municipio_492 = importer.save_or_locate(municipios_municipio_492)

    municipios_municipio_493 = Municipio()
    municipios_municipio_493.cod_ibge = '3544509'
    municipios_municipio_493.nome = 'Rubinéia'
    municipios_municipio_493.capital = False
    municipios_municipio_493.uf = 'SP'
    municipios_municipio_493.lat = Decimal('-20.1759000')
    municipios_municipio_493.lng = Decimal('-51.0070000')
    municipios_municipio_493 = importer.save_or_locate(municipios_municipio_493)

    municipios_municipio_494 = Municipio()
    municipios_municipio_494.cod_ibge = '3544400'
    municipios_municipio_494.nome = 'Rubiácea'
    municipios_municipio_494.capital = False
    municipios_municipio_494.uf = 'SP'
    municipios_municipio_494.lat = Decimal('-21.3006000')
    municipios_municipio_494.lng = Decimal('-50.7296000')
    municipios_municipio_494 = importer.save_or_locate(municipios_municipio_494)

    municipios_municipio_495 = Municipio()
    municipios_municipio_495.cod_ibge = '3544608'
    municipios_municipio_495.nome = 'Sabino'
    municipios_municipio_495.capital = False
    municipios_municipio_495.uf = 'SP'
    municipios_municipio_495.lat = Decimal('-21.4593000')
    municipios_municipio_495.lng = Decimal('-49.5755000')
    municipios_municipio_495 = importer.save_or_locate(municipios_municipio_495)

    municipios_municipio_496 = Municipio()
    municipios_municipio_496.cod_ibge = '3544707'
    municipios_municipio_496.nome = 'Sagres'
    municipios_municipio_496.capital = False
    municipios_municipio_496.uf = 'SP'
    municipios_municipio_496.lat = Decimal('-21.8823000')
    municipios_municipio_496.lng = Decimal('-50.9594000')
    municipios_municipio_496 = importer.save_or_locate(municipios_municipio_496)

    municipios_municipio_497 = Municipio()
    municipios_municipio_497.cod_ibge = '3544806'
    municipios_municipio_497.nome = 'Sales'
    municipios_municipio_497.capital = False
    municipios_municipio_497.uf = 'SP'
    municipios_municipio_497.lat = Decimal('-21.3427000')
    municipios_municipio_497.lng = Decimal('-49.4897000')
    municipios_municipio_497 = importer.save_or_locate(municipios_municipio_497)

    municipios_municipio_498 = Municipio()
    municipios_municipio_498.cod_ibge = '3544905'
    municipios_municipio_498.nome = 'Sales Oliveira'
    municipios_municipio_498.capital = False
    municipios_municipio_498.uf = 'SP'
    municipios_municipio_498.lat = Decimal('-20.7696000')
    municipios_municipio_498.lng = Decimal('-47.8369000')
    municipios_municipio_498 = importer.save_or_locate(municipios_municipio_498)

    municipios_municipio_499 = Municipio()
    municipios_municipio_499.cod_ibge = '3545001'
    municipios_municipio_499.nome = 'Salesópolis'
    municipios_municipio_499.capital = False
    municipios_municipio_499.uf = 'SP'
    municipios_municipio_499.lat = Decimal('-23.5288000')
    municipios_municipio_499.lng = Decimal('-45.8465000')
    municipios_municipio_499 = importer.save_or_locate(municipios_municipio_499)

    municipios_municipio_500 = Municipio()
    municipios_municipio_500.cod_ibge = '3545100'
    municipios_municipio_500.nome = 'Salmourão'
    municipios_municipio_500.capital = False
    municipios_municipio_500.uf = 'SP'
    municipios_municipio_500.lat = Decimal('-21.6267000')
    municipios_municipio_500.lng = Decimal('-50.8614000')
    municipios_municipio_500 = importer.save_or_locate(municipios_municipio_500)

    municipios_municipio_501 = Municipio()
    municipios_municipio_501.cod_ibge = '3545159'
    municipios_municipio_501.nome = 'Saltinho'
    municipios_municipio_501.capital = False
    municipios_municipio_501.uf = 'SP'
    municipios_municipio_501.lat = Decimal('-22.8442000')
    municipios_municipio_501.lng = Decimal('-47.6754000')
    municipios_municipio_501 = importer.save_or_locate(municipios_municipio_501)

    municipios_municipio_502 = Municipio()
    municipios_municipio_502.cod_ibge = '3545209'
    municipios_municipio_502.nome = 'Salto'
    municipios_municipio_502.capital = False
    municipios_municipio_502.uf = 'SP'
    municipios_municipio_502.lat = Decimal('-23.1996000')
    municipios_municipio_502.lng = Decimal('-47.2931000')
    municipios_municipio_502 = importer.save_or_locate(municipios_municipio_502)

    municipios_municipio_503 = Municipio()
    municipios_municipio_503.cod_ibge = '3545407'
    municipios_municipio_503.nome = 'Salto Grande'
    municipios_municipio_503.capital = False
    municipios_municipio_503.uf = 'SP'
    municipios_municipio_503.lat = Decimal('-22.8894000')
    municipios_municipio_503.lng = Decimal('-49.9831000')
    municipios_municipio_503 = importer.save_or_locate(municipios_municipio_503)

    municipios_municipio_504 = Municipio()
    municipios_municipio_504.cod_ibge = '3545308'
    municipios_municipio_504.nome = 'Salto de Pirapora'
    municipios_municipio_504.capital = False
    municipios_municipio_504.uf = 'SP'
    municipios_municipio_504.lat = Decimal('-23.6474000')
    municipios_municipio_504.lng = Decimal('-47.5743000')
    municipios_municipio_504 = importer.save_or_locate(municipios_municipio_504)

    municipios_municipio_505 = Municipio()
    municipios_municipio_505.cod_ibge = '3545506'
    municipios_municipio_505.nome = 'Sandovalina'
    municipios_municipio_505.capital = False
    municipios_municipio_505.uf = 'SP'
    municipios_municipio_505.lat = Decimal('-22.4551000')
    municipios_municipio_505.lng = Decimal('-51.7648000')
    municipios_municipio_505 = importer.save_or_locate(municipios_municipio_505)

    municipios_municipio_506 = Municipio()
    municipios_municipio_506.cod_ibge = '3545605'
    municipios_municipio_506.nome = 'Santa Adélia'
    municipios_municipio_506.capital = False
    municipios_municipio_506.uf = 'SP'
    municipios_municipio_506.lat = Decimal('-21.2427000')
    municipios_municipio_506.lng = Decimal('-48.8063000')
    municipios_municipio_506 = importer.save_or_locate(municipios_municipio_506)

    municipios_municipio_507 = Municipio()
    municipios_municipio_507.cod_ibge = '3545704'
    municipios_municipio_507.nome = 'Santa Albertina'
    municipios_municipio_507.capital = False
    municipios_municipio_507.uf = 'SP'
    municipios_municipio_507.lat = Decimal('-20.0311000')
    municipios_municipio_507.lng = Decimal('-50.7297000')
    municipios_municipio_507 = importer.save_or_locate(municipios_municipio_507)

    municipios_municipio_508 = Municipio()
    municipios_municipio_508.cod_ibge = '3546009'
    municipios_municipio_508.nome = 'Santa Branca'
    municipios_municipio_508.capital = False
    municipios_municipio_508.uf = 'SP'
    municipios_municipio_508.lat = Decimal('-23.3933000')
    municipios_municipio_508.lng = Decimal('-45.8875000')
    municipios_municipio_508 = importer.save_or_locate(municipios_municipio_508)

    municipios_municipio_509 = Municipio()
    municipios_municipio_509.cod_ibge = '3545803'
    municipios_municipio_509.nome = "Santa Bárbara d'Oeste"
    municipios_municipio_509.capital = False
    municipios_municipio_509.uf = 'SP'
    municipios_municipio_509.lat = Decimal('-22.7553000')
    municipios_municipio_509.lng = Decimal('-47.4143000')
    municipios_municipio_509 = importer.save_or_locate(municipios_municipio_509)

    municipios_municipio_510 = Municipio()
    municipios_municipio_510.cod_ibge = '3546108'
    municipios_municipio_510.nome = "Santa Clara d'Oeste"
    municipios_municipio_510.capital = False
    municipios_municipio_510.uf = 'SP'
    municipios_municipio_510.lat = Decimal('-20.0900000')
    municipios_municipio_510.lng = Decimal('-50.9491000')
    municipios_municipio_510 = importer.save_or_locate(municipios_municipio_510)

    municipios_municipio_511 = Municipio()
    municipios_municipio_511.cod_ibge = '3546207'
    municipios_municipio_511.nome = 'Santa Cruz da Conceição'
    municipios_municipio_511.capital = False
    municipios_municipio_511.uf = 'SP'
    municipios_municipio_511.lat = Decimal('-22.1405000')
    municipios_municipio_511.lng = Decimal('-47.4512000')
    municipios_municipio_511 = importer.save_or_locate(municipios_municipio_511)

    municipios_municipio_512 = Municipio()
    municipios_municipio_512.cod_ibge = '3546256'
    municipios_municipio_512.nome = 'Santa Cruz da Esperança'
    municipios_municipio_512.capital = False
    municipios_municipio_512.uf = 'SP'
    municipios_municipio_512.lat = Decimal('-21.2951000')
    municipios_municipio_512.lng = Decimal('-47.4304000')
    municipios_municipio_512 = importer.save_or_locate(municipios_municipio_512)

    municipios_municipio_513 = Municipio()
    municipios_municipio_513.cod_ibge = '3546306'
    municipios_municipio_513.nome = 'Santa Cruz das Palmeiras'
    municipios_municipio_513.capital = False
    municipios_municipio_513.uf = 'SP'
    municipios_municipio_513.lat = Decimal('-21.8235000')
    municipios_municipio_513.lng = Decimal('-47.2480000')
    municipios_municipio_513 = importer.save_or_locate(municipios_municipio_513)

    municipios_municipio_514 = Municipio()
    municipios_municipio_514.cod_ibge = '3546405'
    municipios_municipio_514.nome = 'Santa Cruz do Rio Pardo'
    municipios_municipio_514.capital = False
    municipios_municipio_514.uf = 'SP'
    municipios_municipio_514.lat = Decimal('-22.8988000')
    municipios_municipio_514.lng = Decimal('-49.6354000')
    municipios_municipio_514 = importer.save_or_locate(municipios_municipio_514)

    municipios_municipio_515 = Municipio()
    municipios_municipio_515.cod_ibge = '3546504'
    municipios_municipio_515.nome = 'Santa Ernestina'
    municipios_municipio_515.capital = False
    municipios_municipio_515.uf = 'SP'
    municipios_municipio_515.lat = Decimal('-21.4618000')
    municipios_municipio_515.lng = Decimal('-48.3953000')
    municipios_municipio_515 = importer.save_or_locate(municipios_municipio_515)

    municipios_municipio_516 = Municipio()
    municipios_municipio_516.cod_ibge = '3546603'
    municipios_municipio_516.nome = 'Santa Fé do Sul'
    municipios_municipio_516.capital = False
    municipios_municipio_516.uf = 'SP'
    municipios_municipio_516.lat = Decimal('-20.2083000')
    municipios_municipio_516.lng = Decimal('-50.9320000')
    municipios_municipio_516 = importer.save_or_locate(municipios_municipio_516)

    municipios_municipio_517 = Municipio()
    municipios_municipio_517.cod_ibge = '3546702'
    municipios_municipio_517.nome = 'Santa Gertrudes'
    municipios_municipio_517.capital = False
    municipios_municipio_517.uf = 'SP'
    municipios_municipio_517.lat = Decimal('-22.4572000')
    municipios_municipio_517.lng = Decimal('-47.5272000')
    municipios_municipio_517 = importer.save_or_locate(municipios_municipio_517)

    municipios_municipio_518 = Municipio()
    municipios_municipio_518.cod_ibge = '3546801'
    municipios_municipio_518.nome = 'Santa Isabel'
    municipios_municipio_518.capital = False
    municipios_municipio_518.uf = 'SP'
    municipios_municipio_518.lat = Decimal('-23.3172000')
    municipios_municipio_518.lng = Decimal('-46.2237000')
    municipios_municipio_518 = importer.save_or_locate(municipios_municipio_518)

    municipios_municipio_519 = Municipio()
    municipios_municipio_519.cod_ibge = '3546900'
    municipios_municipio_519.nome = 'Santa Lúcia'
    municipios_municipio_519.capital = False
    municipios_municipio_519.uf = 'SP'
    municipios_municipio_519.lat = Decimal('-21.6850000')
    municipios_municipio_519.lng = Decimal('-48.0885000')
    municipios_municipio_519 = importer.save_or_locate(municipios_municipio_519)

    municipios_municipio_520 = Municipio()
    municipios_municipio_520.cod_ibge = '3547007'
    municipios_municipio_520.nome = 'Santa Maria da Serra'
    municipios_municipio_520.capital = False
    municipios_municipio_520.uf = 'SP'
    municipios_municipio_520.lat = Decimal('-22.5661000')
    municipios_municipio_520.lng = Decimal('-48.1593000')
    municipios_municipio_520 = importer.save_or_locate(municipios_municipio_520)

    municipios_municipio_521 = Municipio()
    municipios_municipio_521.cod_ibge = '3547106'
    municipios_municipio_521.nome = 'Santa Mercedes'
    municipios_municipio_521.capital = False
    municipios_municipio_521.uf = 'SP'
    municipios_municipio_521.lat = Decimal('-21.3495000')
    municipios_municipio_521.lng = Decimal('-51.7564000')
    municipios_municipio_521 = importer.save_or_locate(municipios_municipio_521)

    municipios_municipio_522 = Municipio()
    municipios_municipio_522.cod_ibge = '3547403'
    municipios_municipio_522.nome = "Santa Rita d'Oeste"
    municipios_municipio_522.capital = False
    municipios_municipio_522.uf = 'SP'
    municipios_municipio_522.lat = Decimal('-20.1414000')
    municipios_municipio_522.lng = Decimal('-50.8358000')
    municipios_municipio_522 = importer.save_or_locate(municipios_municipio_522)

    municipios_municipio_523 = Municipio()
    municipios_municipio_523.cod_ibge = '3547502'
    municipios_municipio_523.nome = 'Santa Rita do Passa Quatro'
    municipios_municipio_523.capital = False
    municipios_municipio_523.uf = 'SP'
    municipios_municipio_523.lat = Decimal('-21.7083000')
    municipios_municipio_523.lng = Decimal('-47.4780000')
    municipios_municipio_523 = importer.save_or_locate(municipios_municipio_523)

    municipios_municipio_524 = Municipio()
    municipios_municipio_524.cod_ibge = '3547601'
    municipios_municipio_524.nome = 'Santa Rosa de Viterbo'
    municipios_municipio_524.capital = False
    municipios_municipio_524.uf = 'SP'
    municipios_municipio_524.lat = Decimal('-21.4776000')
    municipios_municipio_524.lng = Decimal('-47.3622000')
    municipios_municipio_524 = importer.save_or_locate(municipios_municipio_524)

    municipios_municipio_525 = Municipio()
    municipios_municipio_525.cod_ibge = '3547650'
    municipios_municipio_525.nome = 'Santa Salete'
    municipios_municipio_525.capital = False
    municipios_municipio_525.uf = 'SP'
    municipios_municipio_525.lat = Decimal('-20.2429000')
    municipios_municipio_525.lng = Decimal('-50.6887000')
    municipios_municipio_525 = importer.save_or_locate(municipios_municipio_525)

    municipios_municipio_526 = Municipio()
    municipios_municipio_526.cod_ibge = '3547205'
    municipios_municipio_526.nome = 'Santana da Ponte Pensa'
    municipios_municipio_526.capital = False
    municipios_municipio_526.uf = 'SP'
    municipios_municipio_526.lat = Decimal('-20.2523000')
    municipios_municipio_526.lng = Decimal('-50.8014000')
    municipios_municipio_526 = importer.save_or_locate(municipios_municipio_526)

    municipios_municipio_527 = Municipio()
    municipios_municipio_527.cod_ibge = '3547304'
    municipios_municipio_527.nome = 'Santana de Parnaíba'
    municipios_municipio_527.capital = False
    municipios_municipio_527.uf = 'SP'
    municipios_municipio_527.lat = Decimal('-23.4439000')
    municipios_municipio_527.lng = Decimal('-46.9178000')
    municipios_municipio_527 = importer.save_or_locate(municipios_municipio_527)

    municipios_municipio_528 = Municipio()
    municipios_municipio_528.cod_ibge = '3547700'
    municipios_municipio_528.nome = 'Santo Anastácio'
    municipios_municipio_528.capital = False
    municipios_municipio_528.uf = 'SP'
    municipios_municipio_528.lat = Decimal('-21.9747000')
    municipios_municipio_528.lng = Decimal('-51.6527000')
    municipios_municipio_528 = importer.save_or_locate(municipios_municipio_528)

    municipios_municipio_529 = Municipio()
    municipios_municipio_529.cod_ibge = '3547809'
    municipios_municipio_529.nome = 'Santo André'
    municipios_municipio_529.capital = False
    municipios_municipio_529.uf = 'SP'
    municipios_municipio_529.lat = Decimal('-23.6737000')
    municipios_municipio_529.lng = Decimal('-46.5432000')
    municipios_municipio_529 = importer.save_or_locate(municipios_municipio_529)

    municipios_municipio_530 = Municipio()
    municipios_municipio_530.cod_ibge = '3547908'
    municipios_municipio_530.nome = 'Santo Antônio da Alegria'
    municipios_municipio_530.capital = False
    municipios_municipio_530.uf = 'SP'
    municipios_municipio_530.lat = Decimal('-21.0864000')
    municipios_municipio_530.lng = Decimal('-47.1464000')
    municipios_municipio_530 = importer.save_or_locate(municipios_municipio_530)

    municipios_municipio_531 = Municipio()
    municipios_municipio_531.cod_ibge = '3548005'
    municipios_municipio_531.nome = 'Santo Antônio de Posse'
    municipios_municipio_531.capital = False
    municipios_municipio_531.uf = 'SP'
    municipios_municipio_531.lat = Decimal('-22.6029000')
    municipios_municipio_531.lng = Decimal('-46.9192000')
    municipios_municipio_531 = importer.save_or_locate(municipios_municipio_531)

    municipios_municipio_532 = Municipio()
    municipios_municipio_532.cod_ibge = '3548054'
    municipios_municipio_532.nome = 'Santo Antônio do Aracanguá'
    municipios_municipio_532.capital = False
    municipios_municipio_532.uf = 'SP'
    municipios_municipio_532.lat = Decimal('-20.9331000')
    municipios_municipio_532.lng = Decimal('-50.4980000')
    municipios_municipio_532 = importer.save_or_locate(municipios_municipio_532)

    municipios_municipio_533 = Municipio()
    municipios_municipio_533.cod_ibge = '3548104'
    municipios_municipio_533.nome = 'Santo Antônio do Jardim'
    municipios_municipio_533.capital = False
    municipios_municipio_533.uf = 'SP'
    municipios_municipio_533.lat = Decimal('-22.1121000')
    municipios_municipio_533.lng = Decimal('-46.6845000')
    municipios_municipio_533 = importer.save_or_locate(municipios_municipio_533)

    municipios_municipio_534 = Municipio()
    municipios_municipio_534.cod_ibge = '3548203'
    municipios_municipio_534.nome = 'Santo Antônio do Pinhal'
    municipios_municipio_534.capital = False
    municipios_municipio_534.uf = 'SP'
    municipios_municipio_534.lat = Decimal('-22.8270000')
    municipios_municipio_534.lng = Decimal('-45.6630000')
    municipios_municipio_534 = importer.save_or_locate(municipios_municipio_534)

    municipios_municipio_535 = Municipio()
    municipios_municipio_535.cod_ibge = '3548302'
    municipios_municipio_535.nome = 'Santo Expedito'
    municipios_municipio_535.capital = False
    municipios_municipio_535.uf = 'SP'
    municipios_municipio_535.lat = Decimal('-21.8467000')
    municipios_municipio_535.lng = Decimal('-51.3929000')
    municipios_municipio_535 = importer.save_or_locate(municipios_municipio_535)

    municipios_municipio_536 = Municipio()
    municipios_municipio_536.cod_ibge = '3548500'
    municipios_municipio_536.nome = 'Santos'
    municipios_municipio_536.capital = False
    municipios_municipio_536.uf = 'SP'
    municipios_municipio_536.lat = Decimal('-23.9535000')
    municipios_municipio_536.lng = Decimal('-46.3350000')
    municipios_municipio_536 = importer.save_or_locate(municipios_municipio_536)

    municipios_municipio_537 = Municipio()
    municipios_municipio_537.cod_ibge = '3548401'
    municipios_municipio_537.nome = 'Santópolis do Aguapeí'
    municipios_municipio_537.capital = False
    municipios_municipio_537.uf = 'SP'
    municipios_municipio_537.lat = Decimal('-21.6376000')
    municipios_municipio_537.lng = Decimal('-50.5044000')
    municipios_municipio_537 = importer.save_or_locate(municipios_municipio_537)

    municipios_municipio_538 = Municipio()
    municipios_municipio_538.cod_ibge = '3551108'
    municipios_municipio_538.nome = 'Sarapuí'
    municipios_municipio_538.capital = False
    municipios_municipio_538.uf = 'SP'
    municipios_municipio_538.lat = Decimal('-23.6397000')
    municipios_municipio_538.lng = Decimal('-47.8249000')
    municipios_municipio_538 = importer.save_or_locate(municipios_municipio_538)

    municipios_municipio_539 = Municipio()
    municipios_municipio_539.cod_ibge = '3551207'
    municipios_municipio_539.nome = 'Sarutaiá'
    municipios_municipio_539.capital = False
    municipios_municipio_539.uf = 'SP'
    municipios_municipio_539.lat = Decimal('-23.2721000')
    municipios_municipio_539.lng = Decimal('-49.4763000')
    municipios_municipio_539 = importer.save_or_locate(municipios_municipio_539)

    municipios_municipio_540 = Municipio()
    municipios_municipio_540.cod_ibge = '3551306'
    municipios_municipio_540.nome = 'Sebastianópolis do Sul'
    municipios_municipio_540.capital = False
    municipios_municipio_540.uf = 'SP'
    municipios_municipio_540.lat = Decimal('-20.6523000')
    municipios_municipio_540.lng = Decimal('-49.9250000')
    municipios_municipio_540 = importer.save_or_locate(municipios_municipio_540)

    municipios_municipio_541 = Municipio()
    municipios_municipio_541.cod_ibge = '3551405'
    municipios_municipio_541.nome = 'Serra Azul'
    municipios_municipio_541.capital = False
    municipios_municipio_541.uf = 'SP'
    municipios_municipio_541.lat = Decimal('-21.3074000')
    municipios_municipio_541.lng = Decimal('-47.5602000')
    municipios_municipio_541 = importer.save_or_locate(municipios_municipio_541)

    municipios_municipio_542 = Municipio()
    municipios_municipio_542.cod_ibge = '3551603'
    municipios_municipio_542.nome = 'Serra Negra'
    municipios_municipio_542.capital = False
    municipios_municipio_542.uf = 'SP'
    municipios_municipio_542.lat = Decimal('-22.6139000')
    municipios_municipio_542.lng = Decimal('-46.7033000')
    municipios_municipio_542 = importer.save_or_locate(municipios_municipio_542)

    municipios_municipio_543 = Municipio()
    municipios_municipio_543.cod_ibge = '3551504'
    municipios_municipio_543.nome = 'Serrana'
    municipios_municipio_543.capital = False
    municipios_municipio_543.uf = 'SP'
    municipios_municipio_543.lat = Decimal('-21.2043000')
    municipios_municipio_543.lng = Decimal('-47.5952000')
    municipios_municipio_543 = importer.save_or_locate(municipios_municipio_543)

    municipios_municipio_544 = Municipio()
    municipios_municipio_544.cod_ibge = '3551702'
    municipios_municipio_544.nome = 'Sertãozinho'
    municipios_municipio_544.capital = False
    municipios_municipio_544.uf = 'SP'
    municipios_municipio_544.lat = Decimal('-21.1316000')
    municipios_municipio_544.lng = Decimal('-47.9875000')
    municipios_municipio_544 = importer.save_or_locate(municipios_municipio_544)

    municipios_municipio_545 = Municipio()
    municipios_municipio_545.cod_ibge = '3551801'
    municipios_municipio_545.nome = 'Sete Barras'
    municipios_municipio_545.capital = False
    municipios_municipio_545.uf = 'SP'
    municipios_municipio_545.lat = Decimal('-24.3820000')
    municipios_municipio_545.lng = Decimal('-47.9279000')
    municipios_municipio_545 = importer.save_or_locate(municipios_municipio_545)

    municipios_municipio_546 = Municipio()
    municipios_municipio_546.cod_ibge = '3551900'
    municipios_municipio_546.nome = 'Severínia'
    municipios_municipio_546.capital = False
    municipios_municipio_546.uf = 'SP'
    municipios_municipio_546.lat = Decimal('-20.8108000')
    municipios_municipio_546.lng = Decimal('-48.8054000')
    municipios_municipio_546 = importer.save_or_locate(municipios_municipio_546)

    municipios_municipio_547 = Municipio()
    municipios_municipio_547.cod_ibge = '3552007'
    municipios_municipio_547.nome = 'Silveiras'
    municipios_municipio_547.capital = False
    municipios_municipio_547.uf = 'SP'
    municipios_municipio_547.lat = Decimal('-22.6638000')
    municipios_municipio_547.lng = Decimal('-44.8522000')
    municipios_municipio_547 = importer.save_or_locate(municipios_municipio_547)

    municipios_municipio_548 = Municipio()
    municipios_municipio_548.cod_ibge = '3552106'
    municipios_municipio_548.nome = 'Socorro'
    municipios_municipio_548.capital = False
    municipios_municipio_548.uf = 'SP'
    municipios_municipio_548.lat = Decimal('-22.5903000')
    municipios_municipio_548.lng = Decimal('-46.5251000')
    municipios_municipio_548 = importer.save_or_locate(municipios_municipio_548)

    municipios_municipio_549 = Municipio()
    municipios_municipio_549.cod_ibge = '3552205'
    municipios_municipio_549.nome = 'Sorocaba'
    municipios_municipio_549.capital = False
    municipios_municipio_549.uf = 'SP'
    municipios_municipio_549.lat = Decimal('-23.4969000')
    municipios_municipio_549.lng = Decimal('-47.4451000')
    municipios_municipio_549 = importer.save_or_locate(municipios_municipio_549)

    municipios_municipio_550 = Municipio()
    municipios_municipio_550.cod_ibge = '3552304'
    municipios_municipio_550.nome = 'Sud Mennucci'
    municipios_municipio_550.capital = False
    municipios_municipio_550.uf = 'SP'
    municipios_municipio_550.lat = Decimal('-20.6872000')
    municipios_municipio_550.lng = Decimal('-50.9238000')
    municipios_municipio_550 = importer.save_or_locate(municipios_municipio_550)

    municipios_municipio_551 = Municipio()
    municipios_municipio_551.cod_ibge = '3552403'
    municipios_municipio_551.nome = 'Sumaré'
    municipios_municipio_551.capital = False
    municipios_municipio_551.uf = 'SP'
    municipios_municipio_551.lat = Decimal('-22.8204000')
    municipios_municipio_551.lng = Decimal('-47.2728000')
    municipios_municipio_551 = importer.save_or_locate(municipios_municipio_551)

    municipios_municipio_552 = Municipio()
    municipios_municipio_552.cod_ibge = '3552502'
    municipios_municipio_552.nome = 'Suzano'
    municipios_municipio_552.capital = False
    municipios_municipio_552.uf = 'SP'
    municipios_municipio_552.lat = Decimal('-23.5448000')
    municipios_municipio_552.lng = Decimal('-46.3112000')
    municipios_municipio_552 = importer.save_or_locate(municipios_municipio_552)

    municipios_municipio_553 = Municipio()
    municipios_municipio_553.cod_ibge = '3552551'
    municipios_municipio_553.nome = 'Suzanápolis'
    municipios_municipio_553.capital = False
    municipios_municipio_553.uf = 'SP'
    municipios_municipio_553.lat = Decimal('-20.4981000')
    municipios_municipio_553.lng = Decimal('-51.0268000')
    municipios_municipio_553 = importer.save_or_locate(municipios_municipio_553)

    municipios_municipio_554 = Municipio()
    municipios_municipio_554.cod_ibge = '3548609'
    municipios_municipio_554.nome = 'São Bento do Sapucaí'
    municipios_municipio_554.capital = False
    municipios_municipio_554.uf = 'SP'
    municipios_municipio_554.lat = Decimal('-22.6837000')
    municipios_municipio_554.lng = Decimal('-45.7287000')
    municipios_municipio_554 = importer.save_or_locate(municipios_municipio_554)

    municipios_municipio_555 = Municipio()
    municipios_municipio_555.cod_ibge = '3548708'
    municipios_municipio_555.nome = 'São Bernardo do Campo'
    municipios_municipio_555.capital = False
    municipios_municipio_555.uf = 'SP'
    municipios_municipio_555.lat = Decimal('-23.6914000')
    municipios_municipio_555.lng = Decimal('-46.5646000')
    municipios_municipio_555 = importer.save_or_locate(municipios_municipio_555)

    municipios_municipio_556 = Municipio()
    municipios_municipio_556.cod_ibge = '3548807'
    municipios_municipio_556.nome = 'São Caetano do Sul'
    municipios_municipio_556.capital = False
    municipios_municipio_556.uf = 'SP'
    municipios_municipio_556.lat = Decimal('-23.6229000')
    municipios_municipio_556.lng = Decimal('-46.5548000')
    municipios_municipio_556 = importer.save_or_locate(municipios_municipio_556)

    municipios_municipio_557 = Municipio()
    municipios_municipio_557.cod_ibge = '3548906'
    municipios_municipio_557.nome = 'São Carlos'
    municipios_municipio_557.capital = False
    municipios_municipio_557.uf = 'SP'
    municipios_municipio_557.lat = Decimal('-22.0174000')
    municipios_municipio_557.lng = Decimal('-47.8860000')
    municipios_municipio_557 = importer.save_or_locate(municipios_municipio_557)

    municipios_municipio_558 = Municipio()
    municipios_municipio_558.cod_ibge = '3549003'
    municipios_municipio_558.nome = 'São Francisco'
    municipios_municipio_558.capital = False
    municipios_municipio_558.uf = 'SP'
    municipios_municipio_558.lat = Decimal('-20.3623000')
    municipios_municipio_558.lng = Decimal('-50.6952000')
    municipios_municipio_558 = importer.save_or_locate(municipios_municipio_558)

    municipios_municipio_559 = Municipio()
    municipios_municipio_559.cod_ibge = '3549409'
    municipios_municipio_559.nome = 'São Joaquim da Barra'
    municipios_municipio_559.capital = False
    municipios_municipio_559.uf = 'SP'
    municipios_municipio_559.lat = Decimal('-20.5812000')
    municipios_municipio_559.lng = Decimal('-47.8593000')
    municipios_municipio_559 = importer.save_or_locate(municipios_municipio_559)

    municipios_municipio_560 = Municipio()
    municipios_municipio_560.cod_ibge = '3549508'
    municipios_municipio_560.nome = 'São José da Bela Vista'
    municipios_municipio_560.capital = False
    municipios_municipio_560.uf = 'SP'
    municipios_municipio_560.lat = Decimal('-20.5935000')
    municipios_municipio_560.lng = Decimal('-47.6424000')
    municipios_municipio_560 = importer.save_or_locate(municipios_municipio_560)

    municipios_municipio_561 = Municipio()
    municipios_municipio_561.cod_ibge = '3549607'
    municipios_municipio_561.nome = 'São José do Barreiro'
    municipios_municipio_561.capital = False
    municipios_municipio_561.uf = 'SP'
    municipios_municipio_561.lat = Decimal('-22.6414000')
    municipios_municipio_561.lng = Decimal('-44.5774000')
    municipios_municipio_561 = importer.save_or_locate(municipios_municipio_561)

    municipios_municipio_562 = Municipio()
    municipios_municipio_562.cod_ibge = '3549706'
    municipios_municipio_562.nome = 'São José do Rio Pardo'
    municipios_municipio_562.capital = False
    municipios_municipio_562.uf = 'SP'
    municipios_municipio_562.lat = Decimal('-21.5953000')
    municipios_municipio_562.lng = Decimal('-46.8873000')
    municipios_municipio_562 = importer.save_or_locate(municipios_municipio_562)

    municipios_municipio_563 = Municipio()
    municipios_municipio_563.cod_ibge = '3549805'
    municipios_municipio_563.nome = 'São José do Rio Preto'
    municipios_municipio_563.capital = False
    municipios_municipio_563.uf = 'SP'
    municipios_municipio_563.lat = Decimal('-20.8113000')
    municipios_municipio_563.lng = Decimal('-49.3758000')
    municipios_municipio_563 = importer.save_or_locate(municipios_municipio_563)

    municipios_municipio_564 = Municipio()
    municipios_municipio_564.cod_ibge = '3549904'
    municipios_municipio_564.nome = 'São José dos Campos'
    municipios_municipio_564.capital = False
    municipios_municipio_564.uf = 'SP'
    municipios_municipio_564.lat = Decimal('-23.1896000')
    municipios_municipio_564.lng = Decimal('-45.8841000')
    municipios_municipio_564 = importer.save_or_locate(municipios_municipio_564)

    municipios_municipio_565 = Municipio()
    municipios_municipio_565.cod_ibge = '3549102'
    municipios_municipio_565.nome = 'São João da Boa Vista'
    municipios_municipio_565.capital = False
    municipios_municipio_565.uf = 'SP'
    municipios_municipio_565.lat = Decimal('-21.9707000')
    municipios_municipio_565.lng = Decimal('-46.7944000')
    municipios_municipio_565 = importer.save_or_locate(municipios_municipio_565)

    municipios_municipio_566 = Municipio()
    municipios_municipio_566.cod_ibge = '3549201'
    municipios_municipio_566.nome = 'São João das Duas Pontes'
    municipios_municipio_566.capital = False
    municipios_municipio_566.uf = 'SP'
    municipios_municipio_566.lat = Decimal('-20.3879000')
    municipios_municipio_566.lng = Decimal('-50.3792000')
    municipios_municipio_566 = importer.save_or_locate(municipios_municipio_566)

    municipios_municipio_567 = Municipio()
    municipios_municipio_567.cod_ibge = '3549250'
    municipios_municipio_567.nome = 'São João de Iracema'
    municipios_municipio_567.capital = False
    municipios_municipio_567.uf = 'SP'
    municipios_municipio_567.lat = Decimal('-20.5111000')
    municipios_municipio_567.lng = Decimal('-50.3561000')
    municipios_municipio_567 = importer.save_or_locate(municipios_municipio_567)

    municipios_municipio_568 = Municipio()
    municipios_municipio_568.cod_ibge = '3549300'
    municipios_municipio_568.nome = "São João do Pau d'Alho"
    municipios_municipio_568.capital = False
    municipios_municipio_568.uf = 'SP'
    municipios_municipio_568.lat = Decimal('-21.2662000')
    municipios_municipio_568.lng = Decimal('-51.6672000')
    municipios_municipio_568 = importer.save_or_locate(municipios_municipio_568)

    municipios_municipio_569 = Municipio()
    municipios_municipio_569.cod_ibge = '3549953'
    municipios_municipio_569.nome = 'São Lourenço da Serra'
    municipios_municipio_569.capital = False
    municipios_municipio_569.uf = 'SP'
    municipios_municipio_569.lat = Decimal('-23.8491000')
    municipios_municipio_569.lng = Decimal('-46.9432000')
    municipios_municipio_569 = importer.save_or_locate(municipios_municipio_569)

    municipios_municipio_570 = Municipio()
    municipios_municipio_570.cod_ibge = '3550001'
    municipios_municipio_570.nome = 'São Luiz do Paraitinga'
    municipios_municipio_570.capital = False
    municipios_municipio_570.uf = 'SP'
    municipios_municipio_570.lat = Decimal('-23.2220000')
    municipios_municipio_570.lng = Decimal('-45.3109000')
    municipios_municipio_570 = importer.save_or_locate(municipios_municipio_570)

    municipios_municipio_571 = Municipio()
    municipios_municipio_571.cod_ibge = '3550100'
    municipios_municipio_571.nome = 'São Manuel'
    municipios_municipio_571.capital = False
    municipios_municipio_571.uf = 'SP'
    municipios_municipio_571.lat = Decimal('-22.7321000')
    municipios_municipio_571.lng = Decimal('-48.5723000')
    municipios_municipio_571 = importer.save_or_locate(municipios_municipio_571)

    municipios_municipio_572 = Municipio()
    municipios_municipio_572.cod_ibge = '3550209'
    municipios_municipio_572.nome = 'São Miguel Arcanjo'
    municipios_municipio_572.capital = False
    municipios_municipio_572.uf = 'SP'
    municipios_municipio_572.lat = Decimal('-23.8782000')
    municipios_municipio_572.lng = Decimal('-47.9935000')
    municipios_municipio_572 = importer.save_or_locate(municipios_municipio_572)

    municipios_municipio_573 = Municipio()
    municipios_municipio_573.cod_ibge = '3550308'
    municipios_municipio_573.nome = 'São Paulo'
    municipios_municipio_573.capital = True
    municipios_municipio_573.uf = 'SP'
    municipios_municipio_573.lat = Decimal('-23.5329000')
    municipios_municipio_573.lng = Decimal('-46.6395000')
    municipios_municipio_573 = importer.save_or_locate(municipios_municipio_573)

    municipios_municipio_574 = Municipio()
    municipios_municipio_574.cod_ibge = '3550407'
    municipios_municipio_574.nome = 'São Pedro'
    municipios_municipio_574.capital = False
    municipios_municipio_574.uf = 'SP'
    municipios_municipio_574.lat = Decimal('-22.5483000')
    municipios_municipio_574.lng = Decimal('-47.9096000')
    municipios_municipio_574 = importer.save_or_locate(municipios_municipio_574)

    municipios_municipio_575 = Municipio()
    municipios_municipio_575.cod_ibge = '3550506'
    municipios_municipio_575.nome = 'São Pedro do Turvo'
    municipios_municipio_575.capital = False
    municipios_municipio_575.uf = 'SP'
    municipios_municipio_575.lat = Decimal('-22.7453000')
    municipios_municipio_575.lng = Decimal('-49.7428000')
    municipios_municipio_575 = importer.save_or_locate(municipios_municipio_575)

    municipios_municipio_576 = Municipio()
    municipios_municipio_576.cod_ibge = '3550605'
    municipios_municipio_576.nome = 'São Roque'
    municipios_municipio_576.capital = False
    municipios_municipio_576.uf = 'SP'
    municipios_municipio_576.lat = Decimal('-23.5226000')
    municipios_municipio_576.lng = Decimal('-47.1357000')
    municipios_municipio_576 = importer.save_or_locate(municipios_municipio_576)

    municipios_municipio_577 = Municipio()
    municipios_municipio_577.cod_ibge = '3550704'
    municipios_municipio_577.nome = 'São Sebastião'
    municipios_municipio_577.capital = False
    municipios_municipio_577.uf = 'SP'
    municipios_municipio_577.lat = Decimal('-23.7951000')
    municipios_municipio_577.lng = Decimal('-45.4143000')
    municipios_municipio_577 = importer.save_or_locate(municipios_municipio_577)

    municipios_municipio_578 = Municipio()
    municipios_municipio_578.cod_ibge = '3550803'
    municipios_municipio_578.nome = 'São Sebastião da Grama'
    municipios_municipio_578.capital = False
    municipios_municipio_578.uf = 'SP'
    municipios_municipio_578.lat = Decimal('-21.7041000')
    municipios_municipio_578.lng = Decimal('-46.8208000')
    municipios_municipio_578 = importer.save_or_locate(municipios_municipio_578)

    municipios_municipio_579 = Municipio()
    municipios_municipio_579.cod_ibge = '3550902'
    municipios_municipio_579.nome = 'São Simão'
    municipios_municipio_579.capital = False
    municipios_municipio_579.uf = 'SP'
    municipios_municipio_579.lat = Decimal('-21.4732000')
    municipios_municipio_579.lng = Decimal('-47.5518000')
    municipios_municipio_579 = importer.save_or_locate(municipios_municipio_579)

    municipios_municipio_580 = Municipio()
    municipios_municipio_580.cod_ibge = '3551009'
    municipios_municipio_580.nome = 'São Vicente'
    municipios_municipio_580.capital = False
    municipios_municipio_580.uf = 'SP'
    municipios_municipio_580.lat = Decimal('-23.9574000')
    municipios_municipio_580.lng = Decimal('-46.3883000')
    municipios_municipio_580 = importer.save_or_locate(municipios_municipio_580)

    municipios_municipio_581 = Municipio()
    municipios_municipio_581.cod_ibge = '3552601'
    municipios_municipio_581.nome = 'Tabapuã'
    municipios_municipio_581.capital = False
    municipios_municipio_581.uf = 'SP'
    municipios_municipio_581.lat = Decimal('-20.9602000')
    municipios_municipio_581.lng = Decimal('-49.0307000')
    municipios_municipio_581 = importer.save_or_locate(municipios_municipio_581)

    municipios_municipio_582 = Municipio()
    municipios_municipio_582.cod_ibge = '3552700'
    municipios_municipio_582.nome = 'Tabatinga'
    municipios_municipio_582.capital = False
    municipios_municipio_582.uf = 'SP'
    municipios_municipio_582.lat = Decimal('-21.7239000')
    municipios_municipio_582.lng = Decimal('-48.6896000')
    municipios_municipio_582 = importer.save_or_locate(municipios_municipio_582)

    municipios_municipio_583 = Municipio()
    municipios_municipio_583.cod_ibge = '3552809'
    municipios_municipio_583.nome = 'Taboão da Serra'
    municipios_municipio_583.capital = False
    municipios_municipio_583.uf = 'SP'
    municipios_municipio_583.lat = Decimal('-23.6019000')
    municipios_municipio_583.lng = Decimal('-46.7526000')
    municipios_municipio_583 = importer.save_or_locate(municipios_municipio_583)

    municipios_municipio_584 = Municipio()
    municipios_municipio_584.cod_ibge = '3552908'
    municipios_municipio_584.nome = 'Taciba'
    municipios_municipio_584.capital = False
    municipios_municipio_584.uf = 'SP'
    municipios_municipio_584.lat = Decimal('-22.3866000')
    municipios_municipio_584.lng = Decimal('-51.2882000')
    municipios_municipio_584 = importer.save_or_locate(municipios_municipio_584)

    municipios_municipio_585 = Municipio()
    municipios_municipio_585.cod_ibge = '3553005'
    municipios_municipio_585.nome = 'Taguaí'
    municipios_municipio_585.capital = False
    municipios_municipio_585.uf = 'SP'
    municipios_municipio_585.lat = Decimal('-23.4452000')
    municipios_municipio_585.lng = Decimal('-49.4024000')
    municipios_municipio_585 = importer.save_or_locate(municipios_municipio_585)

    municipios_municipio_586 = Municipio()
    municipios_municipio_586.cod_ibge = '3553104'
    municipios_municipio_586.nome = 'Taiaçu'
    municipios_municipio_586.capital = False
    municipios_municipio_586.uf = 'SP'
    municipios_municipio_586.lat = Decimal('-21.1431000')
    municipios_municipio_586.lng = Decimal('-48.5112000')
    municipios_municipio_586 = importer.save_or_locate(municipios_municipio_586)

    municipios_municipio_587 = Municipio()
    municipios_municipio_587.cod_ibge = '3553203'
    municipios_municipio_587.nome = 'Taiúva'
    municipios_municipio_587.capital = False
    municipios_municipio_587.uf = 'SP'
    municipios_municipio_587.lat = Decimal('-21.1223000')
    municipios_municipio_587.lng = Decimal('-48.4528000')
    municipios_municipio_587 = importer.save_or_locate(municipios_municipio_587)

    municipios_municipio_588 = Municipio()
    municipios_municipio_588.cod_ibge = '3553302'
    municipios_municipio_588.nome = 'Tambaú'
    municipios_municipio_588.capital = False
    municipios_municipio_588.uf = 'SP'
    municipios_municipio_588.lat = Decimal('-21.7029000')
    municipios_municipio_588.lng = Decimal('-47.2703000')
    municipios_municipio_588 = importer.save_or_locate(municipios_municipio_588)

    municipios_municipio_589 = Municipio()
    municipios_municipio_589.cod_ibge = '3553401'
    municipios_municipio_589.nome = 'Tanabi'
    municipios_municipio_589.capital = False
    municipios_municipio_589.uf = 'SP'
    municipios_municipio_589.lat = Decimal('-20.6228000')
    municipios_municipio_589.lng = Decimal('-49.6563000')
    municipios_municipio_589 = importer.save_or_locate(municipios_municipio_589)

    municipios_municipio_590 = Municipio()
    municipios_municipio_590.cod_ibge = '3553609'
    municipios_municipio_590.nome = 'Tapiratiba'
    municipios_municipio_590.capital = False
    municipios_municipio_590.uf = 'SP'
    municipios_municipio_590.lat = Decimal('-21.4713000')
    municipios_municipio_590.lng = Decimal('-46.7448000')
    municipios_municipio_590 = importer.save_or_locate(municipios_municipio_590)

    municipios_municipio_591 = Municipio()
    municipios_municipio_591.cod_ibge = '3553500'
    municipios_municipio_591.nome = 'Tapiraí'
    municipios_municipio_591.capital = False
    municipios_municipio_591.uf = 'SP'
    municipios_municipio_591.lat = Decimal('-23.9612000')
    municipios_municipio_591.lng = Decimal('-47.5062000')
    municipios_municipio_591 = importer.save_or_locate(municipios_municipio_591)

    municipios_municipio_592 = Municipio()
    municipios_municipio_592.cod_ibge = '3553658'
    municipios_municipio_592.nome = 'Taquaral'
    municipios_municipio_592.capital = False
    municipios_municipio_592.uf = 'SP'
    municipios_municipio_592.lat = Decimal('-21.0737000')
    municipios_municipio_592.lng = Decimal('-48.4126000')
    municipios_municipio_592 = importer.save_or_locate(municipios_municipio_592)

    municipios_municipio_593 = Municipio()
    municipios_municipio_593.cod_ibge = '3553708'
    municipios_municipio_593.nome = 'Taquaritinga'
    municipios_municipio_593.capital = False
    municipios_municipio_593.uf = 'SP'
    municipios_municipio_593.lat = Decimal('-21.4049000')
    municipios_municipio_593.lng = Decimal('-48.5103000')
    municipios_municipio_593 = importer.save_or_locate(municipios_municipio_593)

    municipios_municipio_594 = Municipio()
    municipios_municipio_594.cod_ibge = '3553807'
    municipios_municipio_594.nome = 'Taquarituba'
    municipios_municipio_594.capital = False
    municipios_municipio_594.uf = 'SP'
    municipios_municipio_594.lat = Decimal('-23.5307000')
    municipios_municipio_594.lng = Decimal('-49.2410000')
    municipios_municipio_594 = importer.save_or_locate(municipios_municipio_594)

    municipios_municipio_595 = Municipio()
    municipios_municipio_595.cod_ibge = '3553856'
    municipios_municipio_595.nome = 'Taquarivaí'
    municipios_municipio_595.capital = False
    municipios_municipio_595.uf = 'SP'
    municipios_municipio_595.lat = Decimal('-23.9211000')
    municipios_municipio_595.lng = Decimal('-48.6948000')
    municipios_municipio_595 = importer.save_or_locate(municipios_municipio_595)

    municipios_municipio_596 = Municipio()
    municipios_municipio_596.cod_ibge = '3553906'
    municipios_municipio_596.nome = 'Tarabai'
    municipios_municipio_596.capital = False
    municipios_municipio_596.uf = 'SP'
    municipios_municipio_596.lat = Decimal('-22.3016000')
    municipios_municipio_596.lng = Decimal('-51.5621000')
    municipios_municipio_596 = importer.save_or_locate(municipios_municipio_596)

    municipios_municipio_597 = Municipio()
    municipios_municipio_597.cod_ibge = '3553955'
    municipios_municipio_597.nome = 'Tarumã'
    municipios_municipio_597.capital = False
    municipios_municipio_597.uf = 'SP'
    municipios_municipio_597.lat = Decimal('-22.7429000')
    municipios_municipio_597.lng = Decimal('-50.5786000')
    municipios_municipio_597 = importer.save_or_locate(municipios_municipio_597)

    municipios_municipio_598 = Municipio()
    municipios_municipio_598.cod_ibge = '3554003'
    municipios_municipio_598.nome = 'Tatuí'
    municipios_municipio_598.capital = False
    municipios_municipio_598.uf = 'SP'
    municipios_municipio_598.lat = Decimal('-23.3487000')
    municipios_municipio_598.lng = Decimal('-47.8461000')
    municipios_municipio_598 = importer.save_or_locate(municipios_municipio_598)

    municipios_municipio_599 = Municipio()
    municipios_municipio_599.cod_ibge = '3554102'
    municipios_municipio_599.nome = 'Taubaté'
    municipios_municipio_599.capital = False
    municipios_municipio_599.uf = 'SP'
    municipios_municipio_599.lat = Decimal('-23.0104000')
    municipios_municipio_599.lng = Decimal('-45.5593000')
    municipios_municipio_599 = importer.save_or_locate(municipios_municipio_599)

    municipios_municipio_600 = Municipio()
    municipios_municipio_600.cod_ibge = '3554201'
    municipios_municipio_600.nome = 'Tejupá'
    municipios_municipio_600.capital = False
    municipios_municipio_600.uf = 'SP'
    municipios_municipio_600.lat = Decimal('-23.3425000')
    municipios_municipio_600.lng = Decimal('-49.3722000')
    municipios_municipio_600 = importer.save_or_locate(municipios_municipio_600)

    municipios_municipio_601 = Municipio()
    municipios_municipio_601.cod_ibge = '3554300'
    municipios_municipio_601.nome = 'Teodoro Sampaio'
    municipios_municipio_601.capital = False
    municipios_municipio_601.uf = 'SP'
    municipios_municipio_601.lat = Decimal('-22.5299000')
    municipios_municipio_601.lng = Decimal('-52.1682000')
    municipios_municipio_601 = importer.save_or_locate(municipios_municipio_601)

    municipios_municipio_602 = Municipio()
    municipios_municipio_602.cod_ibge = '3554409'
    municipios_municipio_602.nome = 'Terra Roxa'
    municipios_municipio_602.capital = False
    municipios_municipio_602.uf = 'SP'
    municipios_municipio_602.lat = Decimal('-20.7870000')
    municipios_municipio_602.lng = Decimal('-48.3314000')
    municipios_municipio_602 = importer.save_or_locate(municipios_municipio_602)

    municipios_municipio_603 = Municipio()
    municipios_municipio_603.cod_ibge = '3554508'
    municipios_municipio_603.nome = 'Tietê'
    municipios_municipio_603.capital = False
    municipios_municipio_603.uf = 'SP'
    municipios_municipio_603.lat = Decimal('-23.1101000')
    municipios_municipio_603.lng = Decimal('-47.7164000')
    municipios_municipio_603 = importer.save_or_locate(municipios_municipio_603)

    municipios_municipio_604 = Municipio()
    municipios_municipio_604.cod_ibge = '3554607'
    municipios_municipio_604.nome = 'Timburi'
    municipios_municipio_604.capital = False
    municipios_municipio_604.uf = 'SP'
    municipios_municipio_604.lat = Decimal('-23.2057000')
    municipios_municipio_604.lng = Decimal('-49.6096000')
    municipios_municipio_604 = importer.save_or_locate(municipios_municipio_604)

    municipios_municipio_605 = Municipio()
    municipios_municipio_605.cod_ibge = '3554656'
    municipios_municipio_605.nome = 'Torre de Pedra'
    municipios_municipio_605.capital = False
    municipios_municipio_605.uf = 'SP'
    municipios_municipio_605.lat = Decimal('-23.2462000')
    municipios_municipio_605.lng = Decimal('-48.1955000')
    municipios_municipio_605 = importer.save_or_locate(municipios_municipio_605)

    municipios_municipio_606 = Municipio()
    municipios_municipio_606.cod_ibge = '3554706'
    municipios_municipio_606.nome = 'Torrinha'
    municipios_municipio_606.capital = False
    municipios_municipio_606.uf = 'SP'
    municipios_municipio_606.lat = Decimal('-22.4237000')
    municipios_municipio_606.lng = Decimal('-48.1731000')
    municipios_municipio_606 = importer.save_or_locate(municipios_municipio_606)

    municipios_municipio_607 = Municipio()
    municipios_municipio_607.cod_ibge = '3554755'
    municipios_municipio_607.nome = 'Trabiju'
    municipios_municipio_607.capital = False
    municipios_municipio_607.uf = 'SP'
    municipios_municipio_607.lat = Decimal('-22.0388000')
    municipios_municipio_607.lng = Decimal('-48.3342000')
    municipios_municipio_607 = importer.save_or_locate(municipios_municipio_607)

    municipios_municipio_608 = Municipio()
    municipios_municipio_608.cod_ibge = '3554805'
    municipios_municipio_608.nome = 'Tremembé'
    municipios_municipio_608.capital = False
    municipios_municipio_608.uf = 'SP'
    municipios_municipio_608.lat = Decimal('-22.9571000')
    municipios_municipio_608.lng = Decimal('-45.5475000')
    municipios_municipio_608 = importer.save_or_locate(municipios_municipio_608)

    municipios_municipio_609 = Municipio()
    municipios_municipio_609.cod_ibge = '3554904'
    municipios_municipio_609.nome = 'Três Fronteiras'
    municipios_municipio_609.capital = False
    municipios_municipio_609.uf = 'SP'
    municipios_municipio_609.lat = Decimal('-20.2344000')
    municipios_municipio_609.lng = Decimal('-50.8905000')
    municipios_municipio_609 = importer.save_or_locate(municipios_municipio_609)

    municipios_municipio_610 = Municipio()
    municipios_municipio_610.cod_ibge = '3554953'
    municipios_municipio_610.nome = 'Tuiuti'
    municipios_municipio_610.capital = False
    municipios_municipio_610.uf = 'SP'
    municipios_municipio_610.lat = Decimal('-22.8193000')
    municipios_municipio_610.lng = Decimal('-46.6937000')
    municipios_municipio_610 = importer.save_or_locate(municipios_municipio_610)

    municipios_municipio_611 = Municipio()
    municipios_municipio_611.cod_ibge = '3555109'
    municipios_municipio_611.nome = 'Tupi Paulista'
    municipios_municipio_611.capital = False
    municipios_municipio_611.uf = 'SP'
    municipios_municipio_611.lat = Decimal('-21.3825000')
    municipios_municipio_611.lng = Decimal('-51.5750000')
    municipios_municipio_611 = importer.save_or_locate(municipios_municipio_611)

    municipios_municipio_612 = Municipio()
    municipios_municipio_612.cod_ibge = '3555000'
    municipios_municipio_612.nome = 'Tupã'
    municipios_municipio_612.capital = False
    municipios_municipio_612.uf = 'SP'
    municipios_municipio_612.lat = Decimal('-21.9335000')
    municipios_municipio_612.lng = Decimal('-50.5191000')
    municipios_municipio_612 = importer.save_or_locate(municipios_municipio_612)

    municipios_municipio_613 = Municipio()
    municipios_municipio_613.cod_ibge = '3555208'
    municipios_municipio_613.nome = 'Turiúba'
    municipios_municipio_613.capital = False
    municipios_municipio_613.uf = 'SP'
    municipios_municipio_613.lat = Decimal('-20.9428000')
    municipios_municipio_613.lng = Decimal('-50.1135000')
    municipios_municipio_613 = importer.save_or_locate(municipios_municipio_613)

    municipios_municipio_614 = Municipio()
    municipios_municipio_614.cod_ibge = '3555307'
    municipios_municipio_614.nome = 'Turmalina'
    municipios_municipio_614.capital = False
    municipios_municipio_614.uf = 'SP'
    municipios_municipio_614.lat = Decimal('-20.0486000')
    municipios_municipio_614.lng = Decimal('-50.4792000')
    municipios_municipio_614 = importer.save_or_locate(municipios_municipio_614)

    municipios_municipio_615 = Municipio()
    municipios_municipio_615.cod_ibge = '3555356'
    municipios_municipio_615.nome = 'Ubarana'
    municipios_municipio_615.capital = False
    municipios_municipio_615.uf = 'SP'
    municipios_municipio_615.lat = Decimal('-21.1650000')
    municipios_municipio_615.lng = Decimal('-49.7198000')
    municipios_municipio_615 = importer.save_or_locate(municipios_municipio_615)

    municipios_municipio_616 = Municipio()
    municipios_municipio_616.cod_ibge = '3555406'
    municipios_municipio_616.nome = 'Ubatuba'
    municipios_municipio_616.capital = False
    municipios_municipio_616.uf = 'SP'
    municipios_municipio_616.lat = Decimal('-23.4332000')
    municipios_municipio_616.lng = Decimal('-45.0834000')
    municipios_municipio_616 = importer.save_or_locate(municipios_municipio_616)

    municipios_municipio_617 = Municipio()
    municipios_municipio_617.cod_ibge = '3555505'
    municipios_municipio_617.nome = 'Ubirajara'
    municipios_municipio_617.capital = False
    municipios_municipio_617.uf = 'SP'
    municipios_municipio_617.lat = Decimal('-22.5272000')
    municipios_municipio_617.lng = Decimal('-49.6613000')
    municipios_municipio_617 = importer.save_or_locate(municipios_municipio_617)

    municipios_municipio_618 = Municipio()
    municipios_municipio_618.cod_ibge = '3555604'
    municipios_municipio_618.nome = 'Uchoa'
    municipios_municipio_618.capital = False
    municipios_municipio_618.uf = 'SP'
    municipios_municipio_618.lat = Decimal('-20.9511000')
    municipios_municipio_618.lng = Decimal('-49.1713000')
    municipios_municipio_618 = importer.save_or_locate(municipios_municipio_618)

    municipios_municipio_619 = Municipio()
    municipios_municipio_619.cod_ibge = '3555703'
    municipios_municipio_619.nome = 'União Paulista'
    municipios_municipio_619.capital = False
    municipios_municipio_619.uf = 'SP'
    municipios_municipio_619.lat = Decimal('-20.8862000')
    municipios_municipio_619.lng = Decimal('-49.9025000')
    municipios_municipio_619 = importer.save_or_locate(municipios_municipio_619)

    municipios_municipio_620 = Municipio()
    municipios_municipio_620.cod_ibge = '3555901'
    municipios_municipio_620.nome = 'Uru'
    municipios_municipio_620.capital = False
    municipios_municipio_620.uf = 'SP'
    municipios_municipio_620.lat = Decimal('-21.7866000')
    municipios_municipio_620.lng = Decimal('-49.2848000')
    municipios_municipio_620 = importer.save_or_locate(municipios_municipio_620)

    municipios_municipio_621 = Municipio()
    municipios_municipio_621.cod_ibge = '3556008'
    municipios_municipio_621.nome = 'Urupês'
    municipios_municipio_621.capital = False
    municipios_municipio_621.uf = 'SP'
    municipios_municipio_621.lat = Decimal('-21.2032000')
    municipios_municipio_621.lng = Decimal('-49.2931000')
    municipios_municipio_621 = importer.save_or_locate(municipios_municipio_621)

    municipios_municipio_622 = Municipio()
    municipios_municipio_622.cod_ibge = '3555802'
    municipios_municipio_622.nome = 'Urânia'
    municipios_municipio_622.capital = False
    municipios_municipio_622.uf = 'SP'
    municipios_municipio_622.lat = Decimal('-20.2455000')
    municipios_municipio_622.lng = Decimal('-50.6455000')
    municipios_municipio_622 = importer.save_or_locate(municipios_municipio_622)

    municipios_municipio_623 = Municipio()
    municipios_municipio_623.cod_ibge = '3556107'
    municipios_municipio_623.nome = 'Valentim Gentil'
    municipios_municipio_623.capital = False
    municipios_municipio_623.uf = 'SP'
    municipios_municipio_623.lat = Decimal('-20.4217000')
    municipios_municipio_623.lng = Decimal('-50.0889000')
    municipios_municipio_623 = importer.save_or_locate(municipios_municipio_623)

    municipios_municipio_624 = Municipio()
    municipios_municipio_624.cod_ibge = '3556206'
    municipios_municipio_624.nome = 'Valinhos'
    municipios_municipio_624.capital = False
    municipios_municipio_624.uf = 'SP'
    municipios_municipio_624.lat = Decimal('-22.9698000')
    municipios_municipio_624.lng = Decimal('-46.9974000')
    municipios_municipio_624 = importer.save_or_locate(municipios_municipio_624)

    municipios_municipio_625 = Municipio()
    municipios_municipio_625.cod_ibge = '3556305'
    municipios_municipio_625.nome = 'Valparaíso'
    municipios_municipio_625.capital = False
    municipios_municipio_625.uf = 'SP'
    municipios_municipio_625.lat = Decimal('-21.2229000')
    municipios_municipio_625.lng = Decimal('-50.8699000')
    municipios_municipio_625 = importer.save_or_locate(municipios_municipio_625)

    municipios_municipio_626 = Municipio()
    municipios_municipio_626.cod_ibge = '3556354'
    municipios_municipio_626.nome = 'Vargem'
    municipios_municipio_626.capital = False
    municipios_municipio_626.uf = 'SP'
    municipios_municipio_626.lat = Decimal('-22.8870000')
    municipios_municipio_626.lng = Decimal('-46.4124000')
    municipios_municipio_626 = importer.save_or_locate(municipios_municipio_626)

    municipios_municipio_627 = Municipio()
    municipios_municipio_627.cod_ibge = '3556453'
    municipios_municipio_627.nome = 'Vargem Grande Paulista'
    municipios_municipio_627.capital = False
    municipios_municipio_627.uf = 'SP'
    municipios_municipio_627.lat = Decimal('-23.5993000')
    municipios_municipio_627.lng = Decimal('-47.0220000')
    municipios_municipio_627 = importer.save_or_locate(municipios_municipio_627)

    municipios_municipio_628 = Municipio()
    municipios_municipio_628.cod_ibge = '3556404'
    municipios_municipio_628.nome = 'Vargem Grande do Sul'
    municipios_municipio_628.capital = False
    municipios_municipio_628.uf = 'SP'
    municipios_municipio_628.lat = Decimal('-21.8322000')
    municipios_municipio_628.lng = Decimal('-46.8913000')
    municipios_municipio_628 = importer.save_or_locate(municipios_municipio_628)

    municipios_municipio_629 = Municipio()
    municipios_municipio_629.cod_ibge = '3556602'
    municipios_municipio_629.nome = 'Vera Cruz'
    municipios_municipio_629.capital = False
    municipios_municipio_629.uf = 'SP'
    municipios_municipio_629.lat = Decimal('-22.2183000')
    municipios_municipio_629.lng = Decimal('-49.8207000')
    municipios_municipio_629 = importer.save_or_locate(municipios_municipio_629)

    municipios_municipio_630 = Municipio()
    municipios_municipio_630.cod_ibge = '3556701'
    municipios_municipio_630.nome = 'Vinhedo'
    municipios_municipio_630.capital = False
    municipios_municipio_630.uf = 'SP'
    municipios_municipio_630.lat = Decimal('-23.0302000')
    municipios_municipio_630.lng = Decimal('-46.9833000')
    municipios_municipio_630 = importer.save_or_locate(municipios_municipio_630)

    municipios_municipio_631 = Municipio()
    municipios_municipio_631.cod_ibge = '3556800'
    municipios_municipio_631.nome = 'Viradouro'
    municipios_municipio_631.capital = False
    municipios_municipio_631.uf = 'SP'
    municipios_municipio_631.lat = Decimal('-20.8734000')
    municipios_municipio_631.lng = Decimal('-48.2930000')
    municipios_municipio_631 = importer.save_or_locate(municipios_municipio_631)

    municipios_municipio_632 = Municipio()
    municipios_municipio_632.cod_ibge = '3556909'
    municipios_municipio_632.nome = 'Vista Alegre do Alto'
    municipios_municipio_632.capital = False
    municipios_municipio_632.uf = 'SP'
    municipios_municipio_632.lat = Decimal('-21.1692000')
    municipios_municipio_632.lng = Decimal('-48.6284000')
    municipios_municipio_632 = importer.save_or_locate(municipios_municipio_632)

    municipios_municipio_633 = Municipio()
    municipios_municipio_633.cod_ibge = '3556958'
    municipios_municipio_633.nome = 'Vitória Brasil'
    municipios_municipio_633.capital = False
    municipios_municipio_633.uf = 'SP'
    municipios_municipio_633.lat = Decimal('-20.1956000')
    municipios_municipio_633.lng = Decimal('-50.4875000')
    municipios_municipio_633 = importer.save_or_locate(municipios_municipio_633)

    municipios_municipio_634 = Municipio()
    municipios_municipio_634.cod_ibge = '3557006'
    municipios_municipio_634.nome = 'Votorantim'
    municipios_municipio_634.capital = False
    municipios_municipio_634.uf = 'SP'
    municipios_municipio_634.lat = Decimal('-23.5446000')
    municipios_municipio_634.lng = Decimal('-47.4388000')
    municipios_municipio_634 = importer.save_or_locate(municipios_municipio_634)

    municipios_municipio_635 = Municipio()
    municipios_municipio_635.cod_ibge = '3557105'
    municipios_municipio_635.nome = 'Votuporanga'
    municipios_municipio_635.capital = False
    municipios_municipio_635.uf = 'SP'
    municipios_municipio_635.lat = Decimal('-20.4237000')
    municipios_municipio_635.lng = Decimal('-49.9781000')
    municipios_municipio_635 = importer.save_or_locate(municipios_municipio_635)

    municipios_municipio_636 = Municipio()
    municipios_municipio_636.cod_ibge = '3556503'
    municipios_municipio_636.nome = 'Várzea Paulista'
    municipios_municipio_636.capital = False
    municipios_municipio_636.uf = 'SP'
    municipios_municipio_636.lat = Decimal('-23.2136000')
    municipios_municipio_636.lng = Decimal('-46.8234000')
    municipios_municipio_636 = importer.save_or_locate(municipios_municipio_636)

    municipios_municipio_637 = Municipio()
    municipios_municipio_637.cod_ibge = '3557154'
    municipios_municipio_637.nome = 'Zacarias'
    municipios_municipio_637.capital = False
    municipios_municipio_637.uf = 'SP'
    municipios_municipio_637.lat = Decimal('-21.0506000')
    municipios_municipio_637.lng = Decimal('-50.0552000')
    municipios_municipio_637 = importer.save_or_locate(municipios_municipio_637)

    municipios_municipio_638 = Municipio()
    municipios_municipio_638.cod_ibge = '3500402'
    municipios_municipio_638.nome = 'Águas da Prata'
    municipios_municipio_638.capital = False
    municipios_municipio_638.uf = 'SP'
    municipios_municipio_638.lat = Decimal('-21.9319000')
    municipios_municipio_638.lng = Decimal('-46.7176000')
    municipios_municipio_638 = importer.save_or_locate(municipios_municipio_638)

    municipios_municipio_639 = Municipio()
    municipios_municipio_639.cod_ibge = '3500501'
    municipios_municipio_639.nome = 'Águas de Lindóia'
    municipios_municipio_639.capital = False
    municipios_municipio_639.uf = 'SP'
    municipios_municipio_639.lat = Decimal('-22.4733000')
    municipios_municipio_639.lng = Decimal('-46.6314000')
    municipios_municipio_639 = importer.save_or_locate(municipios_municipio_639)

    municipios_municipio_640 = Municipio()
    municipios_municipio_640.cod_ibge = '3500550'
    municipios_municipio_640.nome = 'Águas de Santa Bárbara'
    municipios_municipio_640.capital = False
    municipios_municipio_640.uf = 'SP'
    municipios_municipio_640.lat = Decimal('-22.8812000')
    municipios_municipio_640.lng = Decimal('-49.2421000')
    municipios_municipio_640 = importer.save_or_locate(municipios_municipio_640)

    municipios_municipio_641 = Municipio()
    municipios_municipio_641.cod_ibge = '3500600'
    municipios_municipio_641.nome = 'Águas de São Pedro'
    municipios_municipio_641.capital = False
    municipios_municipio_641.uf = 'SP'
    municipios_municipio_641.lat = Decimal('-22.5977000')
    municipios_municipio_641.lng = Decimal('-47.8734000')
    municipios_municipio_641 = importer.save_or_locate(municipios_municipio_641)

    municipios_municipio_642 = Municipio()
    municipios_municipio_642.cod_ibge = '3501202'
    municipios_municipio_642.nome = 'Álvares Florence'
    municipios_municipio_642.capital = False
    municipios_municipio_642.uf = 'SP'
    municipios_municipio_642.lat = Decimal('-20.3203000')
    municipios_municipio_642.lng = Decimal('-49.9141000')
    municipios_municipio_642 = importer.save_or_locate(municipios_municipio_642)

    municipios_municipio_643 = Municipio()
    municipios_municipio_643.cod_ibge = '3501301'
    municipios_municipio_643.nome = 'Álvares Machado'
    municipios_municipio_643.capital = False
    municipios_municipio_643.uf = 'SP'
    municipios_municipio_643.lat = Decimal('-22.0764000')
    municipios_municipio_643.lng = Decimal('-51.4722000')
    municipios_municipio_643 = importer.save_or_locate(municipios_municipio_643)

    municipios_municipio_644 = Municipio()
    municipios_municipio_644.cod_ibge = '3501400'
    municipios_municipio_644.nome = 'Álvaro de Carvalho'
    municipios_municipio_644.capital = False
    municipios_municipio_644.uf = 'SP'
    municipios_municipio_644.lat = Decimal('-22.0841000')
    municipios_municipio_644.lng = Decimal('-49.7190000')
    municipios_municipio_644 = importer.save_or_locate(municipios_municipio_644)

    municipios_municipio_645 = Municipio()
    municipios_municipio_645.cod_ibge = '3533809'
    municipios_municipio_645.nome = 'Óleo'
    municipios_municipio_645.capital = False
    municipios_municipio_645.uf = 'SP'
    municipios_municipio_645.lat = Decimal('-22.9435000')
    municipios_municipio_645.lng = Decimal('-49.3419000')
    municipios_municipio_645 = importer.save_or_locate(municipios_municipio_645)

