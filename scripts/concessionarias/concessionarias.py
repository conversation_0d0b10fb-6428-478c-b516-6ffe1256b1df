#!/usr/bin/env python


# This file has been automatically generated.
# Instead of changing it, create a file called import_helper.py
# and put there a class called ImportHelper(object) in it.
#
# This class will be specially casted so that instead of extending object,
# it will actually extend the class BasicImportHelper()
#
# That means you just have to overload the methods you want to
# change, leaving the other ones intact.
#
# Something that you might want to do is use transactions, for example.
#
# Also, don't forget to add the necessary Django imports.
#
# This file was generated with the following command:
# manage.py dumpscript concessionarias
#
# to restore it, run
# manage.py runscript module_name.this_script_name
#
# example: if manage.py is at ./manage.py
# and the script is at ./some_folder/some_script.py
# you must make sure ./some_folder/__init__.py exists
# and run  ./manage.py runscript some_folder.some_script
import os, sys
from django.db import transaction

class BasicImportHelper:

    def pre_import(self):
        pass

    @transaction.atomic
    def run_import(self, import_data):
        import_data()

    def post_import(self):
        pass

    def locate_similar(self, current_object, search_data):
        # You will probably want to call this method from save_or_locate()
        # Example:
        #   new_obj = self.locate_similar(the_obj, {"national_id": the_obj.national_id } )

        the_obj = current_object.__class__.objects.get(**search_data)
        return the_obj

    def locate_object(self, original_class, original_pk_name, the_class, pk_name, pk_value, obj_content):
        # You may change this function to do specific lookup for specific objects
        #
        # original_class class of the django orm's object that needs to be located
        # original_pk_name the primary key of original_class
        # the_class      parent class of original_class which contains obj_content
        # pk_name        the primary key of original_class
        # pk_value       value of the primary_key
        # obj_content    content of the object which was not exported.
        #
        # You should use obj_content to locate the object on the target db
        #
        # An example where original_class and the_class are different is
        # when original_class is Farmer and the_class is Person. The table
        # may refer to a Farmer but you will actually need to locate Person
        # in order to instantiate that Farmer
        #
        # Example:
        #   if the_class == SurveyResultFormat or the_class == SurveyType or the_class == SurveyState:
        #       pk_name="name"
        #       pk_value=obj_content[pk_name]
        #   if the_class == StaffGroup:
        #       pk_value=8

        search_data = { pk_name: pk_value }
        the_obj = the_class.objects.get(**search_data)
        #print(the_obj)
        return the_obj


    def save_or_locate(self, the_obj):
        # Change this if you want to locate the object in the database
        try:
            the_obj.save()
        except:
            print("---------------")
            print("Error saving the following object:")
            print(the_obj.__class__)
            print(" ")
            print(the_obj.__dict__)
            print(" ")
            print(the_obj)
            print(" ")
            print("---------------")

            raise
        return the_obj


importer = None
try:
    import import_helper
    # We need this so ImportHelper can extend BasicImportHelper, although import_helper.py
    # has no knowlodge of this class
    importer = type("DynamicImportHelper", (import_helper.ImportHelper, BasicImportHelper ) , {} )()
except ImportError as e:
    # From Python 3.3 we can check e.name - string match is for backward compatibility.
    if 'import_helper' in str(e):
        importer = BasicImportHelper()
    else:
        raise

import datetime
from decimal import Decimal
from django.contrib.contenttypes.models import ContentType

try:
    import dateutil.parser
    from dateutil.tz import tzoffset
except ImportError:
    print("Please install python-dateutil")
    sys.exit(os.EX_USAGE)

def run():
    importer.pre_import()
    importer.run_import(import_data)
    importer.post_import()

def import_data():
    # Initial Imports

    # Processing model: concessionarias.models.Concessionaria

    from concessionarias.models import Concessionaria

    concessionarias_concessionaria_1 = Concessionaria()
    concessionarias_concessionaria_1.nome = '01.01'
    concessionarias_concessionaria_1.slug = '0101'
    concessionarias_concessionaria_1.tipo = 'DER'
    concessionarias_concessionaria_1.publica = True
    concessionarias_concessionaria_1.nome_fantasia = None
    concessionarias_concessionaria_1.razao_social = '01.01'
    concessionarias_concessionaria_1.logo = ''
    concessionarias_concessionaria_1.telefone_adm = None
    concessionarias_concessionaria_1.telefone_cco = None
    concessionarias_concessionaria_1.logradouro = None
    concessionarias_concessionaria_1.numero = None
    concessionarias_concessionaria_1.bairro = None
    concessionarias_concessionaria_1.cidade = None
    concessionarias_concessionaria_1.cep = None
    concessionarias_concessionaria_1.uf = None
    concessionarias_concessionaria_1.ativa = True
    concessionarias_concessionaria_1.desc = None
    concessionarias_concessionaria_1.lote = None
    concessionarias_concessionaria_1.etapa = None
    concessionarias_concessionaria_1.contrato = None
    concessionarias_concessionaria_1.contrato_dt_ass = None
    concessionarias_concessionaria_1.inicio = None
    concessionarias_concessionaria_1.termino = None
    concessionarias_concessionaria_1.edital = None
    concessionarias_concessionaria_1.grupo = None
    concessionarias_concessionaria_1.link = None
    concessionarias_concessionaria_1.senha = None
    concessionarias_concessionaria_1.web = None
    concessionarias_concessionaria_1.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.093899+00:00")
    concessionarias_concessionaria_1.atualizado_em = None
    concessionarias_concessionaria_1 = importer.save_or_locate(concessionarias_concessionaria_1)

    concessionarias_concessionaria_2 = Concessionaria()
    concessionarias_concessionaria_2.nome = '01.02'
    concessionarias_concessionaria_2.slug = '0102'
    concessionarias_concessionaria_2.tipo = 'DER'
    concessionarias_concessionaria_2.publica = True
    concessionarias_concessionaria_2.nome_fantasia = None
    concessionarias_concessionaria_2.razao_social = '01.02'
    concessionarias_concessionaria_2.logo = ''
    concessionarias_concessionaria_2.telefone_adm = None
    concessionarias_concessionaria_2.telefone_cco = None
    concessionarias_concessionaria_2.logradouro = None
    concessionarias_concessionaria_2.numero = None
    concessionarias_concessionaria_2.bairro = None
    concessionarias_concessionaria_2.cidade = None
    concessionarias_concessionaria_2.cep = None
    concessionarias_concessionaria_2.uf = None
    concessionarias_concessionaria_2.ativa = True
    concessionarias_concessionaria_2.desc = None
    concessionarias_concessionaria_2.lote = None
    concessionarias_concessionaria_2.etapa = None
    concessionarias_concessionaria_2.contrato = None
    concessionarias_concessionaria_2.contrato_dt_ass = None
    concessionarias_concessionaria_2.inicio = None
    concessionarias_concessionaria_2.termino = None
    concessionarias_concessionaria_2.edital = None
    concessionarias_concessionaria_2.grupo = None
    concessionarias_concessionaria_2.link = None
    concessionarias_concessionaria_2.senha = None
    concessionarias_concessionaria_2.web = None
    concessionarias_concessionaria_2.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.099887+00:00")
    concessionarias_concessionaria_2.atualizado_em = None
    concessionarias_concessionaria_2 = importer.save_or_locate(concessionarias_concessionaria_2)

    concessionarias_concessionaria_3 = Concessionaria()
    concessionarias_concessionaria_3.nome = '01.03'
    concessionarias_concessionaria_3.slug = '0103'
    concessionarias_concessionaria_3.tipo = 'DER'
    concessionarias_concessionaria_3.publica = True
    concessionarias_concessionaria_3.nome_fantasia = None
    concessionarias_concessionaria_3.razao_social = '01.03'
    concessionarias_concessionaria_3.logo = ''
    concessionarias_concessionaria_3.telefone_adm = None
    concessionarias_concessionaria_3.telefone_cco = None
    concessionarias_concessionaria_3.logradouro = None
    concessionarias_concessionaria_3.numero = None
    concessionarias_concessionaria_3.bairro = None
    concessionarias_concessionaria_3.cidade = None
    concessionarias_concessionaria_3.cep = None
    concessionarias_concessionaria_3.uf = None
    concessionarias_concessionaria_3.ativa = True
    concessionarias_concessionaria_3.desc = None
    concessionarias_concessionaria_3.lote = None
    concessionarias_concessionaria_3.etapa = None
    concessionarias_concessionaria_3.contrato = None
    concessionarias_concessionaria_3.contrato_dt_ass = None
    concessionarias_concessionaria_3.inicio = None
    concessionarias_concessionaria_3.termino = None
    concessionarias_concessionaria_3.edital = None
    concessionarias_concessionaria_3.grupo = None
    concessionarias_concessionaria_3.link = None
    concessionarias_concessionaria_3.senha = None
    concessionarias_concessionaria_3.web = None
    concessionarias_concessionaria_3.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_3.atualizado_em = None
    concessionarias_concessionaria_3 = importer.save_or_locate(concessionarias_concessionaria_3)

    concessionarias_concessionaria_4 = Concessionaria()
    concessionarias_concessionaria_4.nome = '01.04'
    concessionarias_concessionaria_4.slug = '0104'
    concessionarias_concessionaria_4.tipo = 'DER'
    concessionarias_concessionaria_4.publica = True
    concessionarias_concessionaria_4.nome_fantasia = None
    concessionarias_concessionaria_4.razao_social = '01.04'
    concessionarias_concessionaria_4.logo = ''
    concessionarias_concessionaria_4.telefone_adm = None
    concessionarias_concessionaria_4.telefone_cco = None
    concessionarias_concessionaria_4.logradouro = None
    concessionarias_concessionaria_4.numero = None
    concessionarias_concessionaria_4.bairro = None
    concessionarias_concessionaria_4.cidade = None
    concessionarias_concessionaria_4.cep = None
    concessionarias_concessionaria_4.uf = None
    concessionarias_concessionaria_4.ativa = True
    concessionarias_concessionaria_4.desc = None
    concessionarias_concessionaria_4.lote = None
    concessionarias_concessionaria_4.etapa = None
    concessionarias_concessionaria_4.contrato = None
    concessionarias_concessionaria_4.contrato_dt_ass = None
    concessionarias_concessionaria_4.inicio = None
    concessionarias_concessionaria_4.termino = None
    concessionarias_concessionaria_4.edital = None
    concessionarias_concessionaria_4.grupo = None
    concessionarias_concessionaria_4.link = None
    concessionarias_concessionaria_4.senha = None
    concessionarias_concessionaria_4.web = None
    concessionarias_concessionaria_4.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_4.atualizado_em = None
    concessionarias_concessionaria_4 = importer.save_or_locate(concessionarias_concessionaria_4)

    concessionarias_concessionaria_5 = Concessionaria()
    concessionarias_concessionaria_5.nome = '02.01'
    concessionarias_concessionaria_5.slug = '0201'
    concessionarias_concessionaria_5.tipo = 'DER'
    concessionarias_concessionaria_5.publica = True
    concessionarias_concessionaria_5.nome_fantasia = None
    concessionarias_concessionaria_5.razao_social = '02.01'
    concessionarias_concessionaria_5.logo = ''
    concessionarias_concessionaria_5.telefone_adm = None
    concessionarias_concessionaria_5.telefone_cco = None
    concessionarias_concessionaria_5.logradouro = None
    concessionarias_concessionaria_5.numero = None
    concessionarias_concessionaria_5.bairro = None
    concessionarias_concessionaria_5.cidade = None
    concessionarias_concessionaria_5.cep = None
    concessionarias_concessionaria_5.uf = None
    concessionarias_concessionaria_5.ativa = True
    concessionarias_concessionaria_5.desc = None
    concessionarias_concessionaria_5.lote = None
    concessionarias_concessionaria_5.etapa = None
    concessionarias_concessionaria_5.contrato = None
    concessionarias_concessionaria_5.contrato_dt_ass = None
    concessionarias_concessionaria_5.inicio = None
    concessionarias_concessionaria_5.termino = None
    concessionarias_concessionaria_5.edital = None
    concessionarias_concessionaria_5.grupo = None
    concessionarias_concessionaria_5.link = None
    concessionarias_concessionaria_5.senha = None
    concessionarias_concessionaria_5.web = None
    concessionarias_concessionaria_5.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_5.atualizado_em = None
    concessionarias_concessionaria_5 = importer.save_or_locate(concessionarias_concessionaria_5)

    concessionarias_concessionaria_6 = Concessionaria()
    concessionarias_concessionaria_6.nome = '02.02'
    concessionarias_concessionaria_6.slug = '0202'
    concessionarias_concessionaria_6.tipo = 'DER'
    concessionarias_concessionaria_6.publica = True
    concessionarias_concessionaria_6.nome_fantasia = None
    concessionarias_concessionaria_6.razao_social = '02.02'
    concessionarias_concessionaria_6.logo = ''
    concessionarias_concessionaria_6.telefone_adm = None
    concessionarias_concessionaria_6.telefone_cco = None
    concessionarias_concessionaria_6.logradouro = None
    concessionarias_concessionaria_6.numero = None
    concessionarias_concessionaria_6.bairro = None
    concessionarias_concessionaria_6.cidade = None
    concessionarias_concessionaria_6.cep = None
    concessionarias_concessionaria_6.uf = None
    concessionarias_concessionaria_6.ativa = True
    concessionarias_concessionaria_6.desc = None
    concessionarias_concessionaria_6.lote = None
    concessionarias_concessionaria_6.etapa = None
    concessionarias_concessionaria_6.contrato = None
    concessionarias_concessionaria_6.contrato_dt_ass = None
    concessionarias_concessionaria_6.inicio = None
    concessionarias_concessionaria_6.termino = None
    concessionarias_concessionaria_6.edital = None
    concessionarias_concessionaria_6.grupo = None
    concessionarias_concessionaria_6.link = None
    concessionarias_concessionaria_6.senha = None
    concessionarias_concessionaria_6.web = None
    concessionarias_concessionaria_6.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_6.atualizado_em = None
    concessionarias_concessionaria_6 = importer.save_or_locate(concessionarias_concessionaria_6)

    concessionarias_concessionaria_7 = Concessionaria()
    concessionarias_concessionaria_7.nome = '02.03'
    concessionarias_concessionaria_7.slug = '0203'
    concessionarias_concessionaria_7.tipo = 'DER'
    concessionarias_concessionaria_7.publica = True
    concessionarias_concessionaria_7.nome_fantasia = None
    concessionarias_concessionaria_7.razao_social = '02.03'
    concessionarias_concessionaria_7.logo = ''
    concessionarias_concessionaria_7.telefone_adm = None
    concessionarias_concessionaria_7.telefone_cco = None
    concessionarias_concessionaria_7.logradouro = None
    concessionarias_concessionaria_7.numero = None
    concessionarias_concessionaria_7.bairro = None
    concessionarias_concessionaria_7.cidade = None
    concessionarias_concessionaria_7.cep = None
    concessionarias_concessionaria_7.uf = None
    concessionarias_concessionaria_7.ativa = True
    concessionarias_concessionaria_7.desc = None
    concessionarias_concessionaria_7.lote = None
    concessionarias_concessionaria_7.etapa = None
    concessionarias_concessionaria_7.contrato = None
    concessionarias_concessionaria_7.contrato_dt_ass = None
    concessionarias_concessionaria_7.inicio = None
    concessionarias_concessionaria_7.termino = None
    concessionarias_concessionaria_7.edital = None
    concessionarias_concessionaria_7.grupo = None
    concessionarias_concessionaria_7.link = None
    concessionarias_concessionaria_7.senha = None
    concessionarias_concessionaria_7.web = None
    concessionarias_concessionaria_7.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_7.atualizado_em = None
    concessionarias_concessionaria_7 = importer.save_or_locate(concessionarias_concessionaria_7)

    concessionarias_concessionaria_8 = Concessionaria()
    concessionarias_concessionaria_8.nome = '02.04'
    concessionarias_concessionaria_8.slug = '0204'
    concessionarias_concessionaria_8.tipo = 'DER'
    concessionarias_concessionaria_8.publica = True
    concessionarias_concessionaria_8.nome_fantasia = None
    concessionarias_concessionaria_8.razao_social = '02.04'
    concessionarias_concessionaria_8.logo = ''
    concessionarias_concessionaria_8.telefone_adm = None
    concessionarias_concessionaria_8.telefone_cco = None
    concessionarias_concessionaria_8.logradouro = None
    concessionarias_concessionaria_8.numero = None
    concessionarias_concessionaria_8.bairro = None
    concessionarias_concessionaria_8.cidade = None
    concessionarias_concessionaria_8.cep = None
    concessionarias_concessionaria_8.uf = None
    concessionarias_concessionaria_8.ativa = True
    concessionarias_concessionaria_8.desc = None
    concessionarias_concessionaria_8.lote = None
    concessionarias_concessionaria_8.etapa = None
    concessionarias_concessionaria_8.contrato = None
    concessionarias_concessionaria_8.contrato_dt_ass = None
    concessionarias_concessionaria_8.inicio = None
    concessionarias_concessionaria_8.termino = None
    concessionarias_concessionaria_8.edital = None
    concessionarias_concessionaria_8.grupo = None
    concessionarias_concessionaria_8.link = None
    concessionarias_concessionaria_8.senha = None
    concessionarias_concessionaria_8.web = None
    concessionarias_concessionaria_8.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_8.atualizado_em = None
    concessionarias_concessionaria_8 = importer.save_or_locate(concessionarias_concessionaria_8)

    concessionarias_concessionaria_9 = Concessionaria()
    concessionarias_concessionaria_9.nome = '02.05'
    concessionarias_concessionaria_9.slug = '0205'
    concessionarias_concessionaria_9.tipo = 'DER'
    concessionarias_concessionaria_9.publica = True
    concessionarias_concessionaria_9.nome_fantasia = None
    concessionarias_concessionaria_9.razao_social = '02.05'
    concessionarias_concessionaria_9.logo = ''
    concessionarias_concessionaria_9.telefone_adm = None
    concessionarias_concessionaria_9.telefone_cco = None
    concessionarias_concessionaria_9.logradouro = None
    concessionarias_concessionaria_9.numero = None
    concessionarias_concessionaria_9.bairro = None
    concessionarias_concessionaria_9.cidade = None
    concessionarias_concessionaria_9.cep = None
    concessionarias_concessionaria_9.uf = None
    concessionarias_concessionaria_9.ativa = True
    concessionarias_concessionaria_9.desc = None
    concessionarias_concessionaria_9.lote = None
    concessionarias_concessionaria_9.etapa = None
    concessionarias_concessionaria_9.contrato = None
    concessionarias_concessionaria_9.contrato_dt_ass = None
    concessionarias_concessionaria_9.inicio = None
    concessionarias_concessionaria_9.termino = None
    concessionarias_concessionaria_9.edital = None
    concessionarias_concessionaria_9.grupo = None
    concessionarias_concessionaria_9.link = None
    concessionarias_concessionaria_9.senha = None
    concessionarias_concessionaria_9.web = None
    concessionarias_concessionaria_9.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_9.atualizado_em = None
    concessionarias_concessionaria_9 = importer.save_or_locate(concessionarias_concessionaria_9)

    concessionarias_concessionaria_10 = Concessionaria()
    concessionarias_concessionaria_10.nome = '02.06'
    concessionarias_concessionaria_10.slug = '0206'
    concessionarias_concessionaria_10.tipo = 'DER'
    concessionarias_concessionaria_10.publica = True
    concessionarias_concessionaria_10.nome_fantasia = None
    concessionarias_concessionaria_10.razao_social = '02.06'
    concessionarias_concessionaria_10.logo = ''
    concessionarias_concessionaria_10.telefone_adm = None
    concessionarias_concessionaria_10.telefone_cco = None
    concessionarias_concessionaria_10.logradouro = None
    concessionarias_concessionaria_10.numero = None
    concessionarias_concessionaria_10.bairro = None
    concessionarias_concessionaria_10.cidade = None
    concessionarias_concessionaria_10.cep = None
    concessionarias_concessionaria_10.uf = None
    concessionarias_concessionaria_10.ativa = True
    concessionarias_concessionaria_10.desc = None
    concessionarias_concessionaria_10.lote = None
    concessionarias_concessionaria_10.etapa = None
    concessionarias_concessionaria_10.contrato = None
    concessionarias_concessionaria_10.contrato_dt_ass = None
    concessionarias_concessionaria_10.inicio = None
    concessionarias_concessionaria_10.termino = None
    concessionarias_concessionaria_10.edital = None
    concessionarias_concessionaria_10.grupo = None
    concessionarias_concessionaria_10.link = None
    concessionarias_concessionaria_10.senha = None
    concessionarias_concessionaria_10.web = None
    concessionarias_concessionaria_10.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_10.atualizado_em = None
    concessionarias_concessionaria_10 = importer.save_or_locate(concessionarias_concessionaria_10)

    concessionarias_concessionaria_11 = Concessionaria()
    concessionarias_concessionaria_11.nome = '02.07'
    concessionarias_concessionaria_11.slug = '0207'
    concessionarias_concessionaria_11.tipo = 'DER'
    concessionarias_concessionaria_11.publica = True
    concessionarias_concessionaria_11.nome_fantasia = None
    concessionarias_concessionaria_11.razao_social = '02.07'
    concessionarias_concessionaria_11.logo = ''
    concessionarias_concessionaria_11.telefone_adm = None
    concessionarias_concessionaria_11.telefone_cco = None
    concessionarias_concessionaria_11.logradouro = None
    concessionarias_concessionaria_11.numero = None
    concessionarias_concessionaria_11.bairro = None
    concessionarias_concessionaria_11.cidade = None
    concessionarias_concessionaria_11.cep = None
    concessionarias_concessionaria_11.uf = None
    concessionarias_concessionaria_11.ativa = True
    concessionarias_concessionaria_11.desc = None
    concessionarias_concessionaria_11.lote = None
    concessionarias_concessionaria_11.etapa = None
    concessionarias_concessionaria_11.contrato = None
    concessionarias_concessionaria_11.contrato_dt_ass = None
    concessionarias_concessionaria_11.inicio = None
    concessionarias_concessionaria_11.termino = None
    concessionarias_concessionaria_11.edital = None
    concessionarias_concessionaria_11.grupo = None
    concessionarias_concessionaria_11.link = None
    concessionarias_concessionaria_11.senha = None
    concessionarias_concessionaria_11.web = None
    concessionarias_concessionaria_11.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_11.atualizado_em = None
    concessionarias_concessionaria_11 = importer.save_or_locate(concessionarias_concessionaria_11)

    concessionarias_concessionaria_12 = Concessionaria()
    concessionarias_concessionaria_12.nome = '02.08'
    concessionarias_concessionaria_12.slug = '0208'
    concessionarias_concessionaria_12.tipo = 'DER'
    concessionarias_concessionaria_12.publica = True
    concessionarias_concessionaria_12.nome_fantasia = None
    concessionarias_concessionaria_12.razao_social = '02.08'
    concessionarias_concessionaria_12.logo = ''
    concessionarias_concessionaria_12.telefone_adm = None
    concessionarias_concessionaria_12.telefone_cco = None
    concessionarias_concessionaria_12.logradouro = None
    concessionarias_concessionaria_12.numero = None
    concessionarias_concessionaria_12.bairro = None
    concessionarias_concessionaria_12.cidade = None
    concessionarias_concessionaria_12.cep = None
    concessionarias_concessionaria_12.uf = None
    concessionarias_concessionaria_12.ativa = True
    concessionarias_concessionaria_12.desc = None
    concessionarias_concessionaria_12.lote = None
    concessionarias_concessionaria_12.etapa = None
    concessionarias_concessionaria_12.contrato = None
    concessionarias_concessionaria_12.contrato_dt_ass = None
    concessionarias_concessionaria_12.inicio = None
    concessionarias_concessionaria_12.termino = None
    concessionarias_concessionaria_12.edital = None
    concessionarias_concessionaria_12.grupo = None
    concessionarias_concessionaria_12.link = None
    concessionarias_concessionaria_12.senha = None
    concessionarias_concessionaria_12.web = None
    concessionarias_concessionaria_12.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_12.atualizado_em = None
    concessionarias_concessionaria_12 = importer.save_or_locate(concessionarias_concessionaria_12)

    concessionarias_concessionaria_13 = Concessionaria()
    concessionarias_concessionaria_13.nome = '03.01'
    concessionarias_concessionaria_13.slug = '0301'
    concessionarias_concessionaria_13.tipo = 'DER'
    concessionarias_concessionaria_13.publica = True
    concessionarias_concessionaria_13.nome_fantasia = None
    concessionarias_concessionaria_13.razao_social = '03.01'
    concessionarias_concessionaria_13.logo = ''
    concessionarias_concessionaria_13.telefone_adm = None
    concessionarias_concessionaria_13.telefone_cco = None
    concessionarias_concessionaria_13.logradouro = None
    concessionarias_concessionaria_13.numero = None
    concessionarias_concessionaria_13.bairro = None
    concessionarias_concessionaria_13.cidade = None
    concessionarias_concessionaria_13.cep = None
    concessionarias_concessionaria_13.uf = None
    concessionarias_concessionaria_13.ativa = True
    concessionarias_concessionaria_13.desc = None
    concessionarias_concessionaria_13.lote = None
    concessionarias_concessionaria_13.etapa = None
    concessionarias_concessionaria_13.contrato = None
    concessionarias_concessionaria_13.contrato_dt_ass = None
    concessionarias_concessionaria_13.inicio = None
    concessionarias_concessionaria_13.termino = None
    concessionarias_concessionaria_13.edital = None
    concessionarias_concessionaria_13.grupo = None
    concessionarias_concessionaria_13.link = None
    concessionarias_concessionaria_13.senha = None
    concessionarias_concessionaria_13.web = None
    concessionarias_concessionaria_13.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_13.atualizado_em = None
    concessionarias_concessionaria_13 = importer.save_or_locate(concessionarias_concessionaria_13)

    concessionarias_concessionaria_14 = Concessionaria()
    concessionarias_concessionaria_14.nome = '03.02'
    concessionarias_concessionaria_14.slug = '0302'
    concessionarias_concessionaria_14.tipo = 'DER'
    concessionarias_concessionaria_14.publica = True
    concessionarias_concessionaria_14.nome_fantasia = None
    concessionarias_concessionaria_14.razao_social = '03.02'
    concessionarias_concessionaria_14.logo = ''
    concessionarias_concessionaria_14.telefone_adm = None
    concessionarias_concessionaria_14.telefone_cco = None
    concessionarias_concessionaria_14.logradouro = None
    concessionarias_concessionaria_14.numero = None
    concessionarias_concessionaria_14.bairro = None
    concessionarias_concessionaria_14.cidade = None
    concessionarias_concessionaria_14.cep = None
    concessionarias_concessionaria_14.uf = None
    concessionarias_concessionaria_14.ativa = True
    concessionarias_concessionaria_14.desc = None
    concessionarias_concessionaria_14.lote = None
    concessionarias_concessionaria_14.etapa = None
    concessionarias_concessionaria_14.contrato = None
    concessionarias_concessionaria_14.contrato_dt_ass = None
    concessionarias_concessionaria_14.inicio = None
    concessionarias_concessionaria_14.termino = None
    concessionarias_concessionaria_14.edital = None
    concessionarias_concessionaria_14.grupo = None
    concessionarias_concessionaria_14.link = None
    concessionarias_concessionaria_14.senha = None
    concessionarias_concessionaria_14.web = None
    concessionarias_concessionaria_14.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_14.atualizado_em = None
    concessionarias_concessionaria_14 = importer.save_or_locate(concessionarias_concessionaria_14)

    concessionarias_concessionaria_15 = Concessionaria()
    concessionarias_concessionaria_15.nome = '03.03'
    concessionarias_concessionaria_15.slug = '0303'
    concessionarias_concessionaria_15.tipo = 'DER'
    concessionarias_concessionaria_15.publica = True
    concessionarias_concessionaria_15.nome_fantasia = None
    concessionarias_concessionaria_15.razao_social = '03.03'
    concessionarias_concessionaria_15.logo = ''
    concessionarias_concessionaria_15.telefone_adm = None
    concessionarias_concessionaria_15.telefone_cco = None
    concessionarias_concessionaria_15.logradouro = None
    concessionarias_concessionaria_15.numero = None
    concessionarias_concessionaria_15.bairro = None
    concessionarias_concessionaria_15.cidade = None
    concessionarias_concessionaria_15.cep = None
    concessionarias_concessionaria_15.uf = None
    concessionarias_concessionaria_15.ativa = True
    concessionarias_concessionaria_15.desc = None
    concessionarias_concessionaria_15.lote = None
    concessionarias_concessionaria_15.etapa = None
    concessionarias_concessionaria_15.contrato = None
    concessionarias_concessionaria_15.contrato_dt_ass = None
    concessionarias_concessionaria_15.inicio = None
    concessionarias_concessionaria_15.termino = None
    concessionarias_concessionaria_15.edital = None
    concessionarias_concessionaria_15.grupo = None
    concessionarias_concessionaria_15.link = None
    concessionarias_concessionaria_15.senha = None
    concessionarias_concessionaria_15.web = None
    concessionarias_concessionaria_15.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_15.atualizado_em = None
    concessionarias_concessionaria_15 = importer.save_or_locate(concessionarias_concessionaria_15)

    concessionarias_concessionaria_16 = Concessionaria()
    concessionarias_concessionaria_16.nome = '03.04'
    concessionarias_concessionaria_16.slug = '0304'
    concessionarias_concessionaria_16.tipo = 'DER'
    concessionarias_concessionaria_16.publica = True
    concessionarias_concessionaria_16.nome_fantasia = None
    concessionarias_concessionaria_16.razao_social = '03.04'
    concessionarias_concessionaria_16.logo = ''
    concessionarias_concessionaria_16.telefone_adm = None
    concessionarias_concessionaria_16.telefone_cco = None
    concessionarias_concessionaria_16.logradouro = None
    concessionarias_concessionaria_16.numero = None
    concessionarias_concessionaria_16.bairro = None
    concessionarias_concessionaria_16.cidade = None
    concessionarias_concessionaria_16.cep = None
    concessionarias_concessionaria_16.uf = None
    concessionarias_concessionaria_16.ativa = True
    concessionarias_concessionaria_16.desc = None
    concessionarias_concessionaria_16.lote = None
    concessionarias_concessionaria_16.etapa = None
    concessionarias_concessionaria_16.contrato = None
    concessionarias_concessionaria_16.contrato_dt_ass = None
    concessionarias_concessionaria_16.inicio = None
    concessionarias_concessionaria_16.termino = None
    concessionarias_concessionaria_16.edital = None
    concessionarias_concessionaria_16.grupo = None
    concessionarias_concessionaria_16.link = None
    concessionarias_concessionaria_16.senha = None
    concessionarias_concessionaria_16.web = None
    concessionarias_concessionaria_16.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_16.atualizado_em = None
    concessionarias_concessionaria_16 = importer.save_or_locate(concessionarias_concessionaria_16)

    concessionarias_concessionaria_17 = Concessionaria()
    concessionarias_concessionaria_17.nome = '04.01'
    concessionarias_concessionaria_17.slug = '0401'
    concessionarias_concessionaria_17.tipo = 'DER'
    concessionarias_concessionaria_17.publica = True
    concessionarias_concessionaria_17.nome_fantasia = None
    concessionarias_concessionaria_17.razao_social = '04.01'
    concessionarias_concessionaria_17.logo = ''
    concessionarias_concessionaria_17.telefone_adm = None
    concessionarias_concessionaria_17.telefone_cco = None
    concessionarias_concessionaria_17.logradouro = None
    concessionarias_concessionaria_17.numero = None
    concessionarias_concessionaria_17.bairro = None
    concessionarias_concessionaria_17.cidade = None
    concessionarias_concessionaria_17.cep = None
    concessionarias_concessionaria_17.uf = None
    concessionarias_concessionaria_17.ativa = True
    concessionarias_concessionaria_17.desc = None
    concessionarias_concessionaria_17.lote = None
    concessionarias_concessionaria_17.etapa = None
    concessionarias_concessionaria_17.contrato = None
    concessionarias_concessionaria_17.contrato_dt_ass = None
    concessionarias_concessionaria_17.inicio = None
    concessionarias_concessionaria_17.termino = None
    concessionarias_concessionaria_17.edital = None
    concessionarias_concessionaria_17.grupo = None
    concessionarias_concessionaria_17.link = None
    concessionarias_concessionaria_17.senha = None
    concessionarias_concessionaria_17.web = None
    concessionarias_concessionaria_17.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_17.atualizado_em = None
    concessionarias_concessionaria_17 = importer.save_or_locate(concessionarias_concessionaria_17)

    concessionarias_concessionaria_18 = Concessionaria()
    concessionarias_concessionaria_18.nome = '04.02'
    concessionarias_concessionaria_18.slug = '0402'
    concessionarias_concessionaria_18.tipo = 'DER'
    concessionarias_concessionaria_18.publica = True
    concessionarias_concessionaria_18.nome_fantasia = None
    concessionarias_concessionaria_18.razao_social = '04.02'
    concessionarias_concessionaria_18.logo = ''
    concessionarias_concessionaria_18.telefone_adm = None
    concessionarias_concessionaria_18.telefone_cco = None
    concessionarias_concessionaria_18.logradouro = None
    concessionarias_concessionaria_18.numero = None
    concessionarias_concessionaria_18.bairro = None
    concessionarias_concessionaria_18.cidade = None
    concessionarias_concessionaria_18.cep = None
    concessionarias_concessionaria_18.uf = None
    concessionarias_concessionaria_18.ativa = True
    concessionarias_concessionaria_18.desc = None
    concessionarias_concessionaria_18.lote = None
    concessionarias_concessionaria_18.etapa = None
    concessionarias_concessionaria_18.contrato = None
    concessionarias_concessionaria_18.contrato_dt_ass = None
    concessionarias_concessionaria_18.inicio = None
    concessionarias_concessionaria_18.termino = None
    concessionarias_concessionaria_18.edital = None
    concessionarias_concessionaria_18.grupo = None
    concessionarias_concessionaria_18.link = None
    concessionarias_concessionaria_18.senha = None
    concessionarias_concessionaria_18.web = None
    concessionarias_concessionaria_18.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_18.atualizado_em = None
    concessionarias_concessionaria_18 = importer.save_or_locate(concessionarias_concessionaria_18)

    concessionarias_concessionaria_19 = Concessionaria()
    concessionarias_concessionaria_19.nome = '04.03'
    concessionarias_concessionaria_19.slug = '0403'
    concessionarias_concessionaria_19.tipo = 'DER'
    concessionarias_concessionaria_19.publica = True
    concessionarias_concessionaria_19.nome_fantasia = None
    concessionarias_concessionaria_19.razao_social = '04.03'
    concessionarias_concessionaria_19.logo = ''
    concessionarias_concessionaria_19.telefone_adm = None
    concessionarias_concessionaria_19.telefone_cco = None
    concessionarias_concessionaria_19.logradouro = None
    concessionarias_concessionaria_19.numero = None
    concessionarias_concessionaria_19.bairro = None
    concessionarias_concessionaria_19.cidade = None
    concessionarias_concessionaria_19.cep = None
    concessionarias_concessionaria_19.uf = None
    concessionarias_concessionaria_19.ativa = True
    concessionarias_concessionaria_19.desc = None
    concessionarias_concessionaria_19.lote = None
    concessionarias_concessionaria_19.etapa = None
    concessionarias_concessionaria_19.contrato = None
    concessionarias_concessionaria_19.contrato_dt_ass = None
    concessionarias_concessionaria_19.inicio = None
    concessionarias_concessionaria_19.termino = None
    concessionarias_concessionaria_19.edital = None
    concessionarias_concessionaria_19.grupo = None
    concessionarias_concessionaria_19.link = None
    concessionarias_concessionaria_19.senha = None
    concessionarias_concessionaria_19.web = None
    concessionarias_concessionaria_19.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_19.atualizado_em = None
    concessionarias_concessionaria_19 = importer.save_or_locate(concessionarias_concessionaria_19)

    concessionarias_concessionaria_20 = Concessionaria()
    concessionarias_concessionaria_20.nome = '05.01'
    concessionarias_concessionaria_20.slug = '0501'
    concessionarias_concessionaria_20.tipo = 'DER'
    concessionarias_concessionaria_20.publica = True
    concessionarias_concessionaria_20.nome_fantasia = None
    concessionarias_concessionaria_20.razao_social = '05.01'
    concessionarias_concessionaria_20.logo = ''
    concessionarias_concessionaria_20.telefone_adm = None
    concessionarias_concessionaria_20.telefone_cco = None
    concessionarias_concessionaria_20.logradouro = None
    concessionarias_concessionaria_20.numero = None
    concessionarias_concessionaria_20.bairro = None
    concessionarias_concessionaria_20.cidade = None
    concessionarias_concessionaria_20.cep = None
    concessionarias_concessionaria_20.uf = None
    concessionarias_concessionaria_20.ativa = True
    concessionarias_concessionaria_20.desc = None
    concessionarias_concessionaria_20.lote = None
    concessionarias_concessionaria_20.etapa = None
    concessionarias_concessionaria_20.contrato = None
    concessionarias_concessionaria_20.contrato_dt_ass = None
    concessionarias_concessionaria_20.inicio = None
    concessionarias_concessionaria_20.termino = None
    concessionarias_concessionaria_20.edital = None
    concessionarias_concessionaria_20.grupo = None
    concessionarias_concessionaria_20.link = None
    concessionarias_concessionaria_20.senha = None
    concessionarias_concessionaria_20.web = None
    concessionarias_concessionaria_20.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_20.atualizado_em = None
    concessionarias_concessionaria_20 = importer.save_or_locate(concessionarias_concessionaria_20)

    concessionarias_concessionaria_21 = Concessionaria()
    concessionarias_concessionaria_21.nome = '05.03'
    concessionarias_concessionaria_21.slug = '0503'
    concessionarias_concessionaria_21.tipo = 'DER'
    concessionarias_concessionaria_21.publica = True
    concessionarias_concessionaria_21.nome_fantasia = None
    concessionarias_concessionaria_21.razao_social = '05.03'
    concessionarias_concessionaria_21.logo = ''
    concessionarias_concessionaria_21.telefone_adm = None
    concessionarias_concessionaria_21.telefone_cco = None
    concessionarias_concessionaria_21.logradouro = None
    concessionarias_concessionaria_21.numero = None
    concessionarias_concessionaria_21.bairro = None
    concessionarias_concessionaria_21.cidade = None
    concessionarias_concessionaria_21.cep = None
    concessionarias_concessionaria_21.uf = None
    concessionarias_concessionaria_21.ativa = True
    concessionarias_concessionaria_21.desc = None
    concessionarias_concessionaria_21.lote = None
    concessionarias_concessionaria_21.etapa = None
    concessionarias_concessionaria_21.contrato = None
    concessionarias_concessionaria_21.contrato_dt_ass = None
    concessionarias_concessionaria_21.inicio = None
    concessionarias_concessionaria_21.termino = None
    concessionarias_concessionaria_21.edital = None
    concessionarias_concessionaria_21.grupo = None
    concessionarias_concessionaria_21.link = None
    concessionarias_concessionaria_21.senha = None
    concessionarias_concessionaria_21.web = None
    concessionarias_concessionaria_21.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.100957+00:00")
    concessionarias_concessionaria_21.atualizado_em = None
    concessionarias_concessionaria_21 = importer.save_or_locate(concessionarias_concessionaria_21)

    concessionarias_concessionaria_22 = Concessionaria()
    concessionarias_concessionaria_22.nome = '05.04'
    concessionarias_concessionaria_22.slug = '0504'
    concessionarias_concessionaria_22.tipo = 'DER'
    concessionarias_concessionaria_22.publica = True
    concessionarias_concessionaria_22.nome_fantasia = None
    concessionarias_concessionaria_22.razao_social = '05.04'
    concessionarias_concessionaria_22.logo = ''
    concessionarias_concessionaria_22.telefone_adm = None
    concessionarias_concessionaria_22.telefone_cco = None
    concessionarias_concessionaria_22.logradouro = None
    concessionarias_concessionaria_22.numero = None
    concessionarias_concessionaria_22.bairro = None
    concessionarias_concessionaria_22.cidade = None
    concessionarias_concessionaria_22.cep = None
    concessionarias_concessionaria_22.uf = None
    concessionarias_concessionaria_22.ativa = True
    concessionarias_concessionaria_22.desc = None
    concessionarias_concessionaria_22.lote = None
    concessionarias_concessionaria_22.etapa = None
    concessionarias_concessionaria_22.contrato = None
    concessionarias_concessionaria_22.contrato_dt_ass = None
    concessionarias_concessionaria_22.inicio = None
    concessionarias_concessionaria_22.termino = None
    concessionarias_concessionaria_22.edital = None
    concessionarias_concessionaria_22.grupo = None
    concessionarias_concessionaria_22.link = None
    concessionarias_concessionaria_22.senha = None
    concessionarias_concessionaria_22.web = None
    concessionarias_concessionaria_22.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.116613+00:00")
    concessionarias_concessionaria_22.atualizado_em = None
    concessionarias_concessionaria_22 = importer.save_or_locate(concessionarias_concessionaria_22)

    concessionarias_concessionaria_23 = Concessionaria()
    concessionarias_concessionaria_23.nome = '06.01'
    concessionarias_concessionaria_23.slug = '0601'
    concessionarias_concessionaria_23.tipo = 'DER'
    concessionarias_concessionaria_23.publica = True
    concessionarias_concessionaria_23.nome_fantasia = None
    concessionarias_concessionaria_23.razao_social = '06.01'
    concessionarias_concessionaria_23.logo = ''
    concessionarias_concessionaria_23.telefone_adm = None
    concessionarias_concessionaria_23.telefone_cco = None
    concessionarias_concessionaria_23.logradouro = None
    concessionarias_concessionaria_23.numero = None
    concessionarias_concessionaria_23.bairro = None
    concessionarias_concessionaria_23.cidade = None
    concessionarias_concessionaria_23.cep = None
    concessionarias_concessionaria_23.uf = None
    concessionarias_concessionaria_23.ativa = True
    concessionarias_concessionaria_23.desc = None
    concessionarias_concessionaria_23.lote = None
    concessionarias_concessionaria_23.etapa = None
    concessionarias_concessionaria_23.contrato = None
    concessionarias_concessionaria_23.contrato_dt_ass = None
    concessionarias_concessionaria_23.inicio = None
    concessionarias_concessionaria_23.termino = None
    concessionarias_concessionaria_23.edital = None
    concessionarias_concessionaria_23.grupo = None
    concessionarias_concessionaria_23.link = None
    concessionarias_concessionaria_23.senha = None
    concessionarias_concessionaria_23.web = None
    concessionarias_concessionaria_23.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.116613+00:00")
    concessionarias_concessionaria_23.atualizado_em = None
    concessionarias_concessionaria_23 = importer.save_or_locate(concessionarias_concessionaria_23)

    concessionarias_concessionaria_24 = Concessionaria()
    concessionarias_concessionaria_24.nome = '06.02'
    concessionarias_concessionaria_24.slug = '0602'
    concessionarias_concessionaria_24.tipo = 'DER'
    concessionarias_concessionaria_24.publica = True
    concessionarias_concessionaria_24.nome_fantasia = None
    concessionarias_concessionaria_24.razao_social = '06.02'
    concessionarias_concessionaria_24.logo = ''
    concessionarias_concessionaria_24.telefone_adm = None
    concessionarias_concessionaria_24.telefone_cco = None
    concessionarias_concessionaria_24.logradouro = None
    concessionarias_concessionaria_24.numero = None
    concessionarias_concessionaria_24.bairro = None
    concessionarias_concessionaria_24.cidade = None
    concessionarias_concessionaria_24.cep = None
    concessionarias_concessionaria_24.uf = None
    concessionarias_concessionaria_24.ativa = True
    concessionarias_concessionaria_24.desc = None
    concessionarias_concessionaria_24.lote = None
    concessionarias_concessionaria_24.etapa = None
    concessionarias_concessionaria_24.contrato = None
    concessionarias_concessionaria_24.contrato_dt_ass = None
    concessionarias_concessionaria_24.inicio = None
    concessionarias_concessionaria_24.termino = None
    concessionarias_concessionaria_24.edital = None
    concessionarias_concessionaria_24.grupo = None
    concessionarias_concessionaria_24.link = None
    concessionarias_concessionaria_24.senha = None
    concessionarias_concessionaria_24.web = None
    concessionarias_concessionaria_24.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.116613+00:00")
    concessionarias_concessionaria_24.atualizado_em = None
    concessionarias_concessionaria_24 = importer.save_or_locate(concessionarias_concessionaria_24)

    concessionarias_concessionaria_25 = Concessionaria()
    concessionarias_concessionaria_25.nome = '06.03'
    concessionarias_concessionaria_25.slug = '0603'
    concessionarias_concessionaria_25.tipo = 'DER'
    concessionarias_concessionaria_25.publica = True
    concessionarias_concessionaria_25.nome_fantasia = None
    concessionarias_concessionaria_25.razao_social = '06.03'
    concessionarias_concessionaria_25.logo = ''
    concessionarias_concessionaria_25.telefone_adm = None
    concessionarias_concessionaria_25.telefone_cco = None
    concessionarias_concessionaria_25.logradouro = None
    concessionarias_concessionaria_25.numero = None
    concessionarias_concessionaria_25.bairro = None
    concessionarias_concessionaria_25.cidade = None
    concessionarias_concessionaria_25.cep = None
    concessionarias_concessionaria_25.uf = None
    concessionarias_concessionaria_25.ativa = True
    concessionarias_concessionaria_25.desc = None
    concessionarias_concessionaria_25.lote = None
    concessionarias_concessionaria_25.etapa = None
    concessionarias_concessionaria_25.contrato = None
    concessionarias_concessionaria_25.contrato_dt_ass = None
    concessionarias_concessionaria_25.inicio = None
    concessionarias_concessionaria_25.termino = None
    concessionarias_concessionaria_25.edital = None
    concessionarias_concessionaria_25.grupo = None
    concessionarias_concessionaria_25.link = None
    concessionarias_concessionaria_25.senha = None
    concessionarias_concessionaria_25.web = None
    concessionarias_concessionaria_25.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.116613+00:00")
    concessionarias_concessionaria_25.atualizado_em = None
    concessionarias_concessionaria_25 = importer.save_or_locate(concessionarias_concessionaria_25)

    concessionarias_concessionaria_26 = Concessionaria()
    concessionarias_concessionaria_26.nome = '06.04'
    concessionarias_concessionaria_26.slug = '0604'
    concessionarias_concessionaria_26.tipo = 'DER'
    concessionarias_concessionaria_26.publica = True
    concessionarias_concessionaria_26.nome_fantasia = None
    concessionarias_concessionaria_26.razao_social = '06.04'
    concessionarias_concessionaria_26.logo = ''
    concessionarias_concessionaria_26.telefone_adm = None
    concessionarias_concessionaria_26.telefone_cco = None
    concessionarias_concessionaria_26.logradouro = None
    concessionarias_concessionaria_26.numero = None
    concessionarias_concessionaria_26.bairro = None
    concessionarias_concessionaria_26.cidade = None
    concessionarias_concessionaria_26.cep = None
    concessionarias_concessionaria_26.uf = None
    concessionarias_concessionaria_26.ativa = True
    concessionarias_concessionaria_26.desc = None
    concessionarias_concessionaria_26.lote = None
    concessionarias_concessionaria_26.etapa = None
    concessionarias_concessionaria_26.contrato = None
    concessionarias_concessionaria_26.contrato_dt_ass = None
    concessionarias_concessionaria_26.inicio = None
    concessionarias_concessionaria_26.termino = None
    concessionarias_concessionaria_26.edital = None
    concessionarias_concessionaria_26.grupo = None
    concessionarias_concessionaria_26.link = None
    concessionarias_concessionaria_26.senha = None
    concessionarias_concessionaria_26.web = None
    concessionarias_concessionaria_26.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.116613+00:00")
    concessionarias_concessionaria_26.atualizado_em = None
    concessionarias_concessionaria_26 = importer.save_or_locate(concessionarias_concessionaria_26)

    concessionarias_concessionaria_27 = Concessionaria()
    concessionarias_concessionaria_27.nome = '07.01'
    concessionarias_concessionaria_27.slug = '0701'
    concessionarias_concessionaria_27.tipo = 'DER'
    concessionarias_concessionaria_27.publica = True
    concessionarias_concessionaria_27.nome_fantasia = None
    concessionarias_concessionaria_27.razao_social = '07.01'
    concessionarias_concessionaria_27.logo = ''
    concessionarias_concessionaria_27.telefone_adm = None
    concessionarias_concessionaria_27.telefone_cco = None
    concessionarias_concessionaria_27.logradouro = None
    concessionarias_concessionaria_27.numero = None
    concessionarias_concessionaria_27.bairro = None
    concessionarias_concessionaria_27.cidade = None
    concessionarias_concessionaria_27.cep = None
    concessionarias_concessionaria_27.uf = None
    concessionarias_concessionaria_27.ativa = True
    concessionarias_concessionaria_27.desc = None
    concessionarias_concessionaria_27.lote = None
    concessionarias_concessionaria_27.etapa = None
    concessionarias_concessionaria_27.contrato = None
    concessionarias_concessionaria_27.contrato_dt_ass = None
    concessionarias_concessionaria_27.inicio = None
    concessionarias_concessionaria_27.termino = None
    concessionarias_concessionaria_27.edital = None
    concessionarias_concessionaria_27.grupo = None
    concessionarias_concessionaria_27.link = None
    concessionarias_concessionaria_27.senha = None
    concessionarias_concessionaria_27.web = None
    concessionarias_concessionaria_27.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.116613+00:00")
    concessionarias_concessionaria_27.atualizado_em = None
    concessionarias_concessionaria_27 = importer.save_or_locate(concessionarias_concessionaria_27)

    concessionarias_concessionaria_28 = Concessionaria()
    concessionarias_concessionaria_28.nome = '07.02'
    concessionarias_concessionaria_28.slug = '0702'
    concessionarias_concessionaria_28.tipo = 'DER'
    concessionarias_concessionaria_28.publica = True
    concessionarias_concessionaria_28.nome_fantasia = None
    concessionarias_concessionaria_28.razao_social = '07.02'
    concessionarias_concessionaria_28.logo = ''
    concessionarias_concessionaria_28.telefone_adm = None
    concessionarias_concessionaria_28.telefone_cco = None
    concessionarias_concessionaria_28.logradouro = None
    concessionarias_concessionaria_28.numero = None
    concessionarias_concessionaria_28.bairro = None
    concessionarias_concessionaria_28.cidade = None
    concessionarias_concessionaria_28.cep = None
    concessionarias_concessionaria_28.uf = None
    concessionarias_concessionaria_28.ativa = True
    concessionarias_concessionaria_28.desc = None
    concessionarias_concessionaria_28.lote = None
    concessionarias_concessionaria_28.etapa = None
    concessionarias_concessionaria_28.contrato = None
    concessionarias_concessionaria_28.contrato_dt_ass = None
    concessionarias_concessionaria_28.inicio = None
    concessionarias_concessionaria_28.termino = None
    concessionarias_concessionaria_28.edital = None
    concessionarias_concessionaria_28.grupo = None
    concessionarias_concessionaria_28.link = None
    concessionarias_concessionaria_28.senha = None
    concessionarias_concessionaria_28.web = None
    concessionarias_concessionaria_28.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.116613+00:00")
    concessionarias_concessionaria_28.atualizado_em = None
    concessionarias_concessionaria_28 = importer.save_or_locate(concessionarias_concessionaria_28)

    concessionarias_concessionaria_29 = Concessionaria()
    concessionarias_concessionaria_29.nome = '07.03'
    concessionarias_concessionaria_29.slug = '0703'
    concessionarias_concessionaria_29.tipo = 'DER'
    concessionarias_concessionaria_29.publica = True
    concessionarias_concessionaria_29.nome_fantasia = None
    concessionarias_concessionaria_29.razao_social = '07.03'
    concessionarias_concessionaria_29.logo = ''
    concessionarias_concessionaria_29.telefone_adm = None
    concessionarias_concessionaria_29.telefone_cco = None
    concessionarias_concessionaria_29.logradouro = None
    concessionarias_concessionaria_29.numero = None
    concessionarias_concessionaria_29.bairro = None
    concessionarias_concessionaria_29.cidade = None
    concessionarias_concessionaria_29.cep = None
    concessionarias_concessionaria_29.uf = None
    concessionarias_concessionaria_29.ativa = True
    concessionarias_concessionaria_29.desc = None
    concessionarias_concessionaria_29.lote = None
    concessionarias_concessionaria_29.etapa = None
    concessionarias_concessionaria_29.contrato = None
    concessionarias_concessionaria_29.contrato_dt_ass = None
    concessionarias_concessionaria_29.inicio = None
    concessionarias_concessionaria_29.termino = None
    concessionarias_concessionaria_29.edital = None
    concessionarias_concessionaria_29.grupo = None
    concessionarias_concessionaria_29.link = None
    concessionarias_concessionaria_29.senha = None
    concessionarias_concessionaria_29.web = None
    concessionarias_concessionaria_29.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.116613+00:00")
    concessionarias_concessionaria_29.atualizado_em = None
    concessionarias_concessionaria_29 = importer.save_or_locate(concessionarias_concessionaria_29)

    concessionarias_concessionaria_30 = Concessionaria()
    concessionarias_concessionaria_30.nome = '07.04'
    concessionarias_concessionaria_30.slug = '0704'
    concessionarias_concessionaria_30.tipo = 'DER'
    concessionarias_concessionaria_30.publica = True
    concessionarias_concessionaria_30.nome_fantasia = None
    concessionarias_concessionaria_30.razao_social = '07.04'
    concessionarias_concessionaria_30.logo = ''
    concessionarias_concessionaria_30.telefone_adm = None
    concessionarias_concessionaria_30.telefone_cco = None
    concessionarias_concessionaria_30.logradouro = None
    concessionarias_concessionaria_30.numero = None
    concessionarias_concessionaria_30.bairro = None
    concessionarias_concessionaria_30.cidade = None
    concessionarias_concessionaria_30.cep = None
    concessionarias_concessionaria_30.uf = None
    concessionarias_concessionaria_30.ativa = True
    concessionarias_concessionaria_30.desc = None
    concessionarias_concessionaria_30.lote = None
    concessionarias_concessionaria_30.etapa = None
    concessionarias_concessionaria_30.contrato = None
    concessionarias_concessionaria_30.contrato_dt_ass = None
    concessionarias_concessionaria_30.inicio = None
    concessionarias_concessionaria_30.termino = None
    concessionarias_concessionaria_30.edital = None
    concessionarias_concessionaria_30.grupo = None
    concessionarias_concessionaria_30.link = None
    concessionarias_concessionaria_30.senha = None
    concessionarias_concessionaria_30.web = None
    concessionarias_concessionaria_30.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_30.atualizado_em = None
    concessionarias_concessionaria_30 = importer.save_or_locate(concessionarias_concessionaria_30)

    concessionarias_concessionaria_31 = Concessionaria()
    concessionarias_concessionaria_31.nome = '08.01'
    concessionarias_concessionaria_31.slug = '0801'
    concessionarias_concessionaria_31.tipo = 'DER'
    concessionarias_concessionaria_31.publica = True
    concessionarias_concessionaria_31.nome_fantasia = None
    concessionarias_concessionaria_31.razao_social = '08.01'
    concessionarias_concessionaria_31.logo = ''
    concessionarias_concessionaria_31.telefone_adm = None
    concessionarias_concessionaria_31.telefone_cco = None
    concessionarias_concessionaria_31.logradouro = None
    concessionarias_concessionaria_31.numero = None
    concessionarias_concessionaria_31.bairro = None
    concessionarias_concessionaria_31.cidade = None
    concessionarias_concessionaria_31.cep = None
    concessionarias_concessionaria_31.uf = None
    concessionarias_concessionaria_31.ativa = True
    concessionarias_concessionaria_31.desc = None
    concessionarias_concessionaria_31.lote = None
    concessionarias_concessionaria_31.etapa = None
    concessionarias_concessionaria_31.contrato = None
    concessionarias_concessionaria_31.contrato_dt_ass = None
    concessionarias_concessionaria_31.inicio = None
    concessionarias_concessionaria_31.termino = None
    concessionarias_concessionaria_31.edital = None
    concessionarias_concessionaria_31.grupo = None
    concessionarias_concessionaria_31.link = None
    concessionarias_concessionaria_31.senha = None
    concessionarias_concessionaria_31.web = None
    concessionarias_concessionaria_31.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_31.atualizado_em = None
    concessionarias_concessionaria_31 = importer.save_or_locate(concessionarias_concessionaria_31)

    concessionarias_concessionaria_32 = Concessionaria()
    concessionarias_concessionaria_32.nome = '08.02'
    concessionarias_concessionaria_32.slug = '0802'
    concessionarias_concessionaria_32.tipo = 'DER'
    concessionarias_concessionaria_32.publica = True
    concessionarias_concessionaria_32.nome_fantasia = None
    concessionarias_concessionaria_32.razao_social = '08.02'
    concessionarias_concessionaria_32.logo = ''
    concessionarias_concessionaria_32.telefone_adm = None
    concessionarias_concessionaria_32.telefone_cco = None
    concessionarias_concessionaria_32.logradouro = None
    concessionarias_concessionaria_32.numero = None
    concessionarias_concessionaria_32.bairro = None
    concessionarias_concessionaria_32.cidade = None
    concessionarias_concessionaria_32.cep = None
    concessionarias_concessionaria_32.uf = None
    concessionarias_concessionaria_32.ativa = True
    concessionarias_concessionaria_32.desc = None
    concessionarias_concessionaria_32.lote = None
    concessionarias_concessionaria_32.etapa = None
    concessionarias_concessionaria_32.contrato = None
    concessionarias_concessionaria_32.contrato_dt_ass = None
    concessionarias_concessionaria_32.inicio = None
    concessionarias_concessionaria_32.termino = None
    concessionarias_concessionaria_32.edital = None
    concessionarias_concessionaria_32.grupo = None
    concessionarias_concessionaria_32.link = None
    concessionarias_concessionaria_32.senha = None
    concessionarias_concessionaria_32.web = None
    concessionarias_concessionaria_32.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_32.atualizado_em = None
    concessionarias_concessionaria_32 = importer.save_or_locate(concessionarias_concessionaria_32)

    concessionarias_concessionaria_33 = Concessionaria()
    concessionarias_concessionaria_33.nome = '08.03'
    concessionarias_concessionaria_33.slug = '0803'
    concessionarias_concessionaria_33.tipo = 'DER'
    concessionarias_concessionaria_33.publica = True
    concessionarias_concessionaria_33.nome_fantasia = None
    concessionarias_concessionaria_33.razao_social = '08.03'
    concessionarias_concessionaria_33.logo = ''
    concessionarias_concessionaria_33.telefone_adm = None
    concessionarias_concessionaria_33.telefone_cco = None
    concessionarias_concessionaria_33.logradouro = None
    concessionarias_concessionaria_33.numero = None
    concessionarias_concessionaria_33.bairro = None
    concessionarias_concessionaria_33.cidade = None
    concessionarias_concessionaria_33.cep = None
    concessionarias_concessionaria_33.uf = None
    concessionarias_concessionaria_33.ativa = True
    concessionarias_concessionaria_33.desc = None
    concessionarias_concessionaria_33.lote = None
    concessionarias_concessionaria_33.etapa = None
    concessionarias_concessionaria_33.contrato = None
    concessionarias_concessionaria_33.contrato_dt_ass = None
    concessionarias_concessionaria_33.inicio = None
    concessionarias_concessionaria_33.termino = None
    concessionarias_concessionaria_33.edital = None
    concessionarias_concessionaria_33.grupo = None
    concessionarias_concessionaria_33.link = None
    concessionarias_concessionaria_33.senha = None
    concessionarias_concessionaria_33.web = None
    concessionarias_concessionaria_33.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_33.atualizado_em = None
    concessionarias_concessionaria_33 = importer.save_or_locate(concessionarias_concessionaria_33)

    concessionarias_concessionaria_34 = Concessionaria()
    concessionarias_concessionaria_34.nome = '08.04'
    concessionarias_concessionaria_34.slug = '0804'
    concessionarias_concessionaria_34.tipo = 'DER'
    concessionarias_concessionaria_34.publica = True
    concessionarias_concessionaria_34.nome_fantasia = None
    concessionarias_concessionaria_34.razao_social = '08.04'
    concessionarias_concessionaria_34.logo = ''
    concessionarias_concessionaria_34.telefone_adm = None
    concessionarias_concessionaria_34.telefone_cco = None
    concessionarias_concessionaria_34.logradouro = None
    concessionarias_concessionaria_34.numero = None
    concessionarias_concessionaria_34.bairro = None
    concessionarias_concessionaria_34.cidade = None
    concessionarias_concessionaria_34.cep = None
    concessionarias_concessionaria_34.uf = None
    concessionarias_concessionaria_34.ativa = True
    concessionarias_concessionaria_34.desc = None
    concessionarias_concessionaria_34.lote = None
    concessionarias_concessionaria_34.etapa = None
    concessionarias_concessionaria_34.contrato = None
    concessionarias_concessionaria_34.contrato_dt_ass = None
    concessionarias_concessionaria_34.inicio = None
    concessionarias_concessionaria_34.termino = None
    concessionarias_concessionaria_34.edital = None
    concessionarias_concessionaria_34.grupo = None
    concessionarias_concessionaria_34.link = None
    concessionarias_concessionaria_34.senha = None
    concessionarias_concessionaria_34.web = None
    concessionarias_concessionaria_34.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_34.atualizado_em = None
    concessionarias_concessionaria_34 = importer.save_or_locate(concessionarias_concessionaria_34)

    concessionarias_concessionaria_35 = Concessionaria()
    concessionarias_concessionaria_35.nome = '09.01'
    concessionarias_concessionaria_35.slug = '0901'
    concessionarias_concessionaria_35.tipo = 'DER'
    concessionarias_concessionaria_35.publica = True
    concessionarias_concessionaria_35.nome_fantasia = None
    concessionarias_concessionaria_35.razao_social = '09.01'
    concessionarias_concessionaria_35.logo = ''
    concessionarias_concessionaria_35.telefone_adm = None
    concessionarias_concessionaria_35.telefone_cco = None
    concessionarias_concessionaria_35.logradouro = None
    concessionarias_concessionaria_35.numero = None
    concessionarias_concessionaria_35.bairro = None
    concessionarias_concessionaria_35.cidade = None
    concessionarias_concessionaria_35.cep = None
    concessionarias_concessionaria_35.uf = None
    concessionarias_concessionaria_35.ativa = True
    concessionarias_concessionaria_35.desc = None
    concessionarias_concessionaria_35.lote = None
    concessionarias_concessionaria_35.etapa = None
    concessionarias_concessionaria_35.contrato = None
    concessionarias_concessionaria_35.contrato_dt_ass = None
    concessionarias_concessionaria_35.inicio = None
    concessionarias_concessionaria_35.termino = None
    concessionarias_concessionaria_35.edital = None
    concessionarias_concessionaria_35.grupo = None
    concessionarias_concessionaria_35.link = None
    concessionarias_concessionaria_35.senha = None
    concessionarias_concessionaria_35.web = None
    concessionarias_concessionaria_35.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_35.atualizado_em = None
    concessionarias_concessionaria_35 = importer.save_or_locate(concessionarias_concessionaria_35)

    concessionarias_concessionaria_36 = Concessionaria()
    concessionarias_concessionaria_36.nome = '09.02'
    concessionarias_concessionaria_36.slug = '0902'
    concessionarias_concessionaria_36.tipo = 'DER'
    concessionarias_concessionaria_36.publica = True
    concessionarias_concessionaria_36.nome_fantasia = None
    concessionarias_concessionaria_36.razao_social = '09.02'
    concessionarias_concessionaria_36.logo = ''
    concessionarias_concessionaria_36.telefone_adm = None
    concessionarias_concessionaria_36.telefone_cco = None
    concessionarias_concessionaria_36.logradouro = None
    concessionarias_concessionaria_36.numero = None
    concessionarias_concessionaria_36.bairro = None
    concessionarias_concessionaria_36.cidade = None
    concessionarias_concessionaria_36.cep = None
    concessionarias_concessionaria_36.uf = None
    concessionarias_concessionaria_36.ativa = True
    concessionarias_concessionaria_36.desc = None
    concessionarias_concessionaria_36.lote = None
    concessionarias_concessionaria_36.etapa = None
    concessionarias_concessionaria_36.contrato = None
    concessionarias_concessionaria_36.contrato_dt_ass = None
    concessionarias_concessionaria_36.inicio = None
    concessionarias_concessionaria_36.termino = None
    concessionarias_concessionaria_36.edital = None
    concessionarias_concessionaria_36.grupo = None
    concessionarias_concessionaria_36.link = None
    concessionarias_concessionaria_36.senha = None
    concessionarias_concessionaria_36.web = None
    concessionarias_concessionaria_36.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_36.atualizado_em = None
    concessionarias_concessionaria_36 = importer.save_or_locate(concessionarias_concessionaria_36)

    concessionarias_concessionaria_37 = Concessionaria()
    concessionarias_concessionaria_37.nome = '09.03'
    concessionarias_concessionaria_37.slug = '0903'
    concessionarias_concessionaria_37.tipo = 'DER'
    concessionarias_concessionaria_37.publica = True
    concessionarias_concessionaria_37.nome_fantasia = None
    concessionarias_concessionaria_37.razao_social = '09.03'
    concessionarias_concessionaria_37.logo = ''
    concessionarias_concessionaria_37.telefone_adm = None
    concessionarias_concessionaria_37.telefone_cco = None
    concessionarias_concessionaria_37.logradouro = None
    concessionarias_concessionaria_37.numero = None
    concessionarias_concessionaria_37.bairro = None
    concessionarias_concessionaria_37.cidade = None
    concessionarias_concessionaria_37.cep = None
    concessionarias_concessionaria_37.uf = None
    concessionarias_concessionaria_37.ativa = True
    concessionarias_concessionaria_37.desc = None
    concessionarias_concessionaria_37.lote = None
    concessionarias_concessionaria_37.etapa = None
    concessionarias_concessionaria_37.contrato = None
    concessionarias_concessionaria_37.contrato_dt_ass = None
    concessionarias_concessionaria_37.inicio = None
    concessionarias_concessionaria_37.termino = None
    concessionarias_concessionaria_37.edital = None
    concessionarias_concessionaria_37.grupo = None
    concessionarias_concessionaria_37.link = None
    concessionarias_concessionaria_37.senha = None
    concessionarias_concessionaria_37.web = None
    concessionarias_concessionaria_37.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_37.atualizado_em = None
    concessionarias_concessionaria_37 = importer.save_or_locate(concessionarias_concessionaria_37)

    concessionarias_concessionaria_38 = Concessionaria()
    concessionarias_concessionaria_38.nome = '09.04'
    concessionarias_concessionaria_38.slug = '0904'
    concessionarias_concessionaria_38.tipo = 'DER'
    concessionarias_concessionaria_38.publica = True
    concessionarias_concessionaria_38.nome_fantasia = None
    concessionarias_concessionaria_38.razao_social = '09.04'
    concessionarias_concessionaria_38.logo = ''
    concessionarias_concessionaria_38.telefone_adm = None
    concessionarias_concessionaria_38.telefone_cco = None
    concessionarias_concessionaria_38.logradouro = None
    concessionarias_concessionaria_38.numero = None
    concessionarias_concessionaria_38.bairro = None
    concessionarias_concessionaria_38.cidade = None
    concessionarias_concessionaria_38.cep = None
    concessionarias_concessionaria_38.uf = None
    concessionarias_concessionaria_38.ativa = True
    concessionarias_concessionaria_38.desc = None
    concessionarias_concessionaria_38.lote = None
    concessionarias_concessionaria_38.etapa = None
    concessionarias_concessionaria_38.contrato = None
    concessionarias_concessionaria_38.contrato_dt_ass = None
    concessionarias_concessionaria_38.inicio = None
    concessionarias_concessionaria_38.termino = None
    concessionarias_concessionaria_38.edital = None
    concessionarias_concessionaria_38.grupo = None
    concessionarias_concessionaria_38.link = None
    concessionarias_concessionaria_38.senha = None
    concessionarias_concessionaria_38.web = None
    concessionarias_concessionaria_38.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_38.atualizado_em = None
    concessionarias_concessionaria_38 = importer.save_or_locate(concessionarias_concessionaria_38)

    concessionarias_concessionaria_39 = Concessionaria()
    concessionarias_concessionaria_39.nome = '10.01'
    concessionarias_concessionaria_39.slug = '1001'
    concessionarias_concessionaria_39.tipo = 'DER'
    concessionarias_concessionaria_39.publica = True
    concessionarias_concessionaria_39.nome_fantasia = None
    concessionarias_concessionaria_39.razao_social = '10.01'
    concessionarias_concessionaria_39.logo = ''
    concessionarias_concessionaria_39.telefone_adm = None
    concessionarias_concessionaria_39.telefone_cco = None
    concessionarias_concessionaria_39.logradouro = None
    concessionarias_concessionaria_39.numero = None
    concessionarias_concessionaria_39.bairro = None
    concessionarias_concessionaria_39.cidade = None
    concessionarias_concessionaria_39.cep = None
    concessionarias_concessionaria_39.uf = None
    concessionarias_concessionaria_39.ativa = True
    concessionarias_concessionaria_39.desc = None
    concessionarias_concessionaria_39.lote = None
    concessionarias_concessionaria_39.etapa = None
    concessionarias_concessionaria_39.contrato = None
    concessionarias_concessionaria_39.contrato_dt_ass = None
    concessionarias_concessionaria_39.inicio = None
    concessionarias_concessionaria_39.termino = None
    concessionarias_concessionaria_39.edital = None
    concessionarias_concessionaria_39.grupo = None
    concessionarias_concessionaria_39.link = None
    concessionarias_concessionaria_39.senha = None
    concessionarias_concessionaria_39.web = None
    concessionarias_concessionaria_39.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_39.atualizado_em = None
    concessionarias_concessionaria_39 = importer.save_or_locate(concessionarias_concessionaria_39)

    concessionarias_concessionaria_40 = Concessionaria()
    concessionarias_concessionaria_40.nome = '10.02'
    concessionarias_concessionaria_40.slug = '1002'
    concessionarias_concessionaria_40.tipo = 'DER'
    concessionarias_concessionaria_40.publica = True
    concessionarias_concessionaria_40.nome_fantasia = None
    concessionarias_concessionaria_40.razao_social = '10.02'
    concessionarias_concessionaria_40.logo = ''
    concessionarias_concessionaria_40.telefone_adm = None
    concessionarias_concessionaria_40.telefone_cco = None
    concessionarias_concessionaria_40.logradouro = None
    concessionarias_concessionaria_40.numero = None
    concessionarias_concessionaria_40.bairro = None
    concessionarias_concessionaria_40.cidade = None
    concessionarias_concessionaria_40.cep = None
    concessionarias_concessionaria_40.uf = None
    concessionarias_concessionaria_40.ativa = True
    concessionarias_concessionaria_40.desc = None
    concessionarias_concessionaria_40.lote = None
    concessionarias_concessionaria_40.etapa = None
    concessionarias_concessionaria_40.contrato = None
    concessionarias_concessionaria_40.contrato_dt_ass = None
    concessionarias_concessionaria_40.inicio = None
    concessionarias_concessionaria_40.termino = None
    concessionarias_concessionaria_40.edital = None
    concessionarias_concessionaria_40.grupo = None
    concessionarias_concessionaria_40.link = None
    concessionarias_concessionaria_40.senha = None
    concessionarias_concessionaria_40.web = None
    concessionarias_concessionaria_40.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_40.atualizado_em = None
    concessionarias_concessionaria_40 = importer.save_or_locate(concessionarias_concessionaria_40)

    concessionarias_concessionaria_41 = Concessionaria()
    concessionarias_concessionaria_41.nome = '10.03'
    concessionarias_concessionaria_41.slug = '1003'
    concessionarias_concessionaria_41.tipo = 'DER'
    concessionarias_concessionaria_41.publica = True
    concessionarias_concessionaria_41.nome_fantasia = None
    concessionarias_concessionaria_41.razao_social = '10.03'
    concessionarias_concessionaria_41.logo = ''
    concessionarias_concessionaria_41.telefone_adm = None
    concessionarias_concessionaria_41.telefone_cco = None
    concessionarias_concessionaria_41.logradouro = None
    concessionarias_concessionaria_41.numero = None
    concessionarias_concessionaria_41.bairro = None
    concessionarias_concessionaria_41.cidade = None
    concessionarias_concessionaria_41.cep = None
    concessionarias_concessionaria_41.uf = None
    concessionarias_concessionaria_41.ativa = True
    concessionarias_concessionaria_41.desc = None
    concessionarias_concessionaria_41.lote = None
    concessionarias_concessionaria_41.etapa = None
    concessionarias_concessionaria_41.contrato = None
    concessionarias_concessionaria_41.contrato_dt_ass = None
    concessionarias_concessionaria_41.inicio = None
    concessionarias_concessionaria_41.termino = None
    concessionarias_concessionaria_41.edital = None
    concessionarias_concessionaria_41.grupo = None
    concessionarias_concessionaria_41.link = None
    concessionarias_concessionaria_41.senha = None
    concessionarias_concessionaria_41.web = None
    concessionarias_concessionaria_41.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_41.atualizado_em = None
    concessionarias_concessionaria_41 = importer.save_or_locate(concessionarias_concessionaria_41)

    concessionarias_concessionaria_42 = Concessionaria()
    concessionarias_concessionaria_42.nome = '10.04'
    concessionarias_concessionaria_42.slug = '1004'
    concessionarias_concessionaria_42.tipo = 'DER'
    concessionarias_concessionaria_42.publica = True
    concessionarias_concessionaria_42.nome_fantasia = None
    concessionarias_concessionaria_42.razao_social = '10.04'
    concessionarias_concessionaria_42.logo = ''
    concessionarias_concessionaria_42.telefone_adm = None
    concessionarias_concessionaria_42.telefone_cco = None
    concessionarias_concessionaria_42.logradouro = None
    concessionarias_concessionaria_42.numero = None
    concessionarias_concessionaria_42.bairro = None
    concessionarias_concessionaria_42.cidade = None
    concessionarias_concessionaria_42.cep = None
    concessionarias_concessionaria_42.uf = None
    concessionarias_concessionaria_42.ativa = True
    concessionarias_concessionaria_42.desc = None
    concessionarias_concessionaria_42.lote = None
    concessionarias_concessionaria_42.etapa = None
    concessionarias_concessionaria_42.contrato = None
    concessionarias_concessionaria_42.contrato_dt_ass = None
    concessionarias_concessionaria_42.inicio = None
    concessionarias_concessionaria_42.termino = None
    concessionarias_concessionaria_42.edital = None
    concessionarias_concessionaria_42.grupo = None
    concessionarias_concessionaria_42.link = None
    concessionarias_concessionaria_42.senha = None
    concessionarias_concessionaria_42.web = None
    concessionarias_concessionaria_42.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_42.atualizado_em = None
    concessionarias_concessionaria_42 = importer.save_or_locate(concessionarias_concessionaria_42)

    concessionarias_concessionaria_43 = Concessionaria()
    concessionarias_concessionaria_43.nome = '11.01'
    concessionarias_concessionaria_43.slug = '1101'
    concessionarias_concessionaria_43.tipo = 'DER'
    concessionarias_concessionaria_43.publica = True
    concessionarias_concessionaria_43.nome_fantasia = None
    concessionarias_concessionaria_43.razao_social = '11.01'
    concessionarias_concessionaria_43.logo = ''
    concessionarias_concessionaria_43.telefone_adm = None
    concessionarias_concessionaria_43.telefone_cco = None
    concessionarias_concessionaria_43.logradouro = None
    concessionarias_concessionaria_43.numero = None
    concessionarias_concessionaria_43.bairro = None
    concessionarias_concessionaria_43.cidade = None
    concessionarias_concessionaria_43.cep = None
    concessionarias_concessionaria_43.uf = None
    concessionarias_concessionaria_43.ativa = True
    concessionarias_concessionaria_43.desc = None
    concessionarias_concessionaria_43.lote = None
    concessionarias_concessionaria_43.etapa = None
    concessionarias_concessionaria_43.contrato = None
    concessionarias_concessionaria_43.contrato_dt_ass = None
    concessionarias_concessionaria_43.inicio = None
    concessionarias_concessionaria_43.termino = None
    concessionarias_concessionaria_43.edital = None
    concessionarias_concessionaria_43.grupo = None
    concessionarias_concessionaria_43.link = None
    concessionarias_concessionaria_43.senha = None
    concessionarias_concessionaria_43.web = None
    concessionarias_concessionaria_43.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_43.atualizado_em = None
    concessionarias_concessionaria_43 = importer.save_or_locate(concessionarias_concessionaria_43)

    concessionarias_concessionaria_44 = Concessionaria()
    concessionarias_concessionaria_44.nome = '11.02'
    concessionarias_concessionaria_44.slug = '1102'
    concessionarias_concessionaria_44.tipo = 'DER'
    concessionarias_concessionaria_44.publica = True
    concessionarias_concessionaria_44.nome_fantasia = None
    concessionarias_concessionaria_44.razao_social = '11.02'
    concessionarias_concessionaria_44.logo = ''
    concessionarias_concessionaria_44.telefone_adm = None
    concessionarias_concessionaria_44.telefone_cco = None
    concessionarias_concessionaria_44.logradouro = None
    concessionarias_concessionaria_44.numero = None
    concessionarias_concessionaria_44.bairro = None
    concessionarias_concessionaria_44.cidade = None
    concessionarias_concessionaria_44.cep = None
    concessionarias_concessionaria_44.uf = None
    concessionarias_concessionaria_44.ativa = True
    concessionarias_concessionaria_44.desc = None
    concessionarias_concessionaria_44.lote = None
    concessionarias_concessionaria_44.etapa = None
    concessionarias_concessionaria_44.contrato = None
    concessionarias_concessionaria_44.contrato_dt_ass = None
    concessionarias_concessionaria_44.inicio = None
    concessionarias_concessionaria_44.termino = None
    concessionarias_concessionaria_44.edital = None
    concessionarias_concessionaria_44.grupo = None
    concessionarias_concessionaria_44.link = None
    concessionarias_concessionaria_44.senha = None
    concessionarias_concessionaria_44.web = None
    concessionarias_concessionaria_44.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_44.atualizado_em = None
    concessionarias_concessionaria_44 = importer.save_or_locate(concessionarias_concessionaria_44)

    concessionarias_concessionaria_45 = Concessionaria()
    concessionarias_concessionaria_45.nome = '11.03'
    concessionarias_concessionaria_45.slug = '1103'
    concessionarias_concessionaria_45.tipo = 'DER'
    concessionarias_concessionaria_45.publica = True
    concessionarias_concessionaria_45.nome_fantasia = None
    concessionarias_concessionaria_45.razao_social = '11.03'
    concessionarias_concessionaria_45.logo = ''
    concessionarias_concessionaria_45.telefone_adm = None
    concessionarias_concessionaria_45.telefone_cco = None
    concessionarias_concessionaria_45.logradouro = None
    concessionarias_concessionaria_45.numero = None
    concessionarias_concessionaria_45.bairro = None
    concessionarias_concessionaria_45.cidade = None
    concessionarias_concessionaria_45.cep = None
    concessionarias_concessionaria_45.uf = None
    concessionarias_concessionaria_45.ativa = True
    concessionarias_concessionaria_45.desc = None
    concessionarias_concessionaria_45.lote = None
    concessionarias_concessionaria_45.etapa = None
    concessionarias_concessionaria_45.contrato = None
    concessionarias_concessionaria_45.contrato_dt_ass = None
    concessionarias_concessionaria_45.inicio = None
    concessionarias_concessionaria_45.termino = None
    concessionarias_concessionaria_45.edital = None
    concessionarias_concessionaria_45.grupo = None
    concessionarias_concessionaria_45.link = None
    concessionarias_concessionaria_45.senha = None
    concessionarias_concessionaria_45.web = None
    concessionarias_concessionaria_45.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_45.atualizado_em = None
    concessionarias_concessionaria_45 = importer.save_or_locate(concessionarias_concessionaria_45)

    concessionarias_concessionaria_46 = Concessionaria()
    concessionarias_concessionaria_46.nome = '12.01'
    concessionarias_concessionaria_46.slug = '1201'
    concessionarias_concessionaria_46.tipo = 'DER'
    concessionarias_concessionaria_46.publica = True
    concessionarias_concessionaria_46.nome_fantasia = None
    concessionarias_concessionaria_46.razao_social = '12.01'
    concessionarias_concessionaria_46.logo = ''
    concessionarias_concessionaria_46.telefone_adm = None
    concessionarias_concessionaria_46.telefone_cco = None
    concessionarias_concessionaria_46.logradouro = None
    concessionarias_concessionaria_46.numero = None
    concessionarias_concessionaria_46.bairro = None
    concessionarias_concessionaria_46.cidade = None
    concessionarias_concessionaria_46.cep = None
    concessionarias_concessionaria_46.uf = None
    concessionarias_concessionaria_46.ativa = True
    concessionarias_concessionaria_46.desc = None
    concessionarias_concessionaria_46.lote = None
    concessionarias_concessionaria_46.etapa = None
    concessionarias_concessionaria_46.contrato = None
    concessionarias_concessionaria_46.contrato_dt_ass = None
    concessionarias_concessionaria_46.inicio = None
    concessionarias_concessionaria_46.termino = None
    concessionarias_concessionaria_46.edital = None
    concessionarias_concessionaria_46.grupo = None
    concessionarias_concessionaria_46.link = None
    concessionarias_concessionaria_46.senha = None
    concessionarias_concessionaria_46.web = None
    concessionarias_concessionaria_46.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_46.atualizado_em = None
    concessionarias_concessionaria_46 = importer.save_or_locate(concessionarias_concessionaria_46)

    concessionarias_concessionaria_47 = Concessionaria()
    concessionarias_concessionaria_47.nome = '12.02'
    concessionarias_concessionaria_47.slug = '1202'
    concessionarias_concessionaria_47.tipo = 'DER'
    concessionarias_concessionaria_47.publica = True
    concessionarias_concessionaria_47.nome_fantasia = None
    concessionarias_concessionaria_47.razao_social = '12.02'
    concessionarias_concessionaria_47.logo = ''
    concessionarias_concessionaria_47.telefone_adm = None
    concessionarias_concessionaria_47.telefone_cco = None
    concessionarias_concessionaria_47.logradouro = None
    concessionarias_concessionaria_47.numero = None
    concessionarias_concessionaria_47.bairro = None
    concessionarias_concessionaria_47.cidade = None
    concessionarias_concessionaria_47.cep = None
    concessionarias_concessionaria_47.uf = None
    concessionarias_concessionaria_47.ativa = True
    concessionarias_concessionaria_47.desc = None
    concessionarias_concessionaria_47.lote = None
    concessionarias_concessionaria_47.etapa = None
    concessionarias_concessionaria_47.contrato = None
    concessionarias_concessionaria_47.contrato_dt_ass = None
    concessionarias_concessionaria_47.inicio = None
    concessionarias_concessionaria_47.termino = None
    concessionarias_concessionaria_47.edital = None
    concessionarias_concessionaria_47.grupo = None
    concessionarias_concessionaria_47.link = None
    concessionarias_concessionaria_47.senha = None
    concessionarias_concessionaria_47.web = None
    concessionarias_concessionaria_47.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_47.atualizado_em = None
    concessionarias_concessionaria_47 = importer.save_or_locate(concessionarias_concessionaria_47)

    concessionarias_concessionaria_48 = Concessionaria()
    concessionarias_concessionaria_48.nome = '12.03'
    concessionarias_concessionaria_48.slug = '1203'
    concessionarias_concessionaria_48.tipo = 'DER'
    concessionarias_concessionaria_48.publica = True
    concessionarias_concessionaria_48.nome_fantasia = None
    concessionarias_concessionaria_48.razao_social = '12.03'
    concessionarias_concessionaria_48.logo = ''
    concessionarias_concessionaria_48.telefone_adm = None
    concessionarias_concessionaria_48.telefone_cco = None
    concessionarias_concessionaria_48.logradouro = None
    concessionarias_concessionaria_48.numero = None
    concessionarias_concessionaria_48.bairro = None
    concessionarias_concessionaria_48.cidade = None
    concessionarias_concessionaria_48.cep = None
    concessionarias_concessionaria_48.uf = None
    concessionarias_concessionaria_48.ativa = True
    concessionarias_concessionaria_48.desc = None
    concessionarias_concessionaria_48.lote = None
    concessionarias_concessionaria_48.etapa = None
    concessionarias_concessionaria_48.contrato = None
    concessionarias_concessionaria_48.contrato_dt_ass = None
    concessionarias_concessionaria_48.inicio = None
    concessionarias_concessionaria_48.termino = None
    concessionarias_concessionaria_48.edital = None
    concessionarias_concessionaria_48.grupo = None
    concessionarias_concessionaria_48.link = None
    concessionarias_concessionaria_48.senha = None
    concessionarias_concessionaria_48.web = None
    concessionarias_concessionaria_48.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.123653+00:00")
    concessionarias_concessionaria_48.atualizado_em = None
    concessionarias_concessionaria_48 = importer.save_or_locate(concessionarias_concessionaria_48)

    concessionarias_concessionaria_49 = Concessionaria()
    concessionarias_concessionaria_49.nome = '12.04'
    concessionarias_concessionaria_49.slug = '1204'
    concessionarias_concessionaria_49.tipo = 'DER'
    concessionarias_concessionaria_49.publica = True
    concessionarias_concessionaria_49.nome_fantasia = None
    concessionarias_concessionaria_49.razao_social = '12.04'
    concessionarias_concessionaria_49.logo = ''
    concessionarias_concessionaria_49.telefone_adm = None
    concessionarias_concessionaria_49.telefone_cco = None
    concessionarias_concessionaria_49.logradouro = None
    concessionarias_concessionaria_49.numero = None
    concessionarias_concessionaria_49.bairro = None
    concessionarias_concessionaria_49.cidade = None
    concessionarias_concessionaria_49.cep = None
    concessionarias_concessionaria_49.uf = None
    concessionarias_concessionaria_49.ativa = True
    concessionarias_concessionaria_49.desc = None
    concessionarias_concessionaria_49.lote = None
    concessionarias_concessionaria_49.etapa = None
    concessionarias_concessionaria_49.contrato = None
    concessionarias_concessionaria_49.contrato_dt_ass = None
    concessionarias_concessionaria_49.inicio = None
    concessionarias_concessionaria_49.termino = None
    concessionarias_concessionaria_49.edital = None
    concessionarias_concessionaria_49.grupo = None
    concessionarias_concessionaria_49.link = None
    concessionarias_concessionaria_49.senha = None
    concessionarias_concessionaria_49.web = None
    concessionarias_concessionaria_49.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_49.atualizado_em = None
    concessionarias_concessionaria_49 = importer.save_or_locate(concessionarias_concessionaria_49)

    concessionarias_concessionaria_50 = Concessionaria()
    concessionarias_concessionaria_50.nome = '13.01'
    concessionarias_concessionaria_50.slug = '1301'
    concessionarias_concessionaria_50.tipo = 'DER'
    concessionarias_concessionaria_50.publica = True
    concessionarias_concessionaria_50.nome_fantasia = None
    concessionarias_concessionaria_50.razao_social = '13.01'
    concessionarias_concessionaria_50.logo = ''
    concessionarias_concessionaria_50.telefone_adm = None
    concessionarias_concessionaria_50.telefone_cco = None
    concessionarias_concessionaria_50.logradouro = None
    concessionarias_concessionaria_50.numero = None
    concessionarias_concessionaria_50.bairro = None
    concessionarias_concessionaria_50.cidade = None
    concessionarias_concessionaria_50.cep = None
    concessionarias_concessionaria_50.uf = None
    concessionarias_concessionaria_50.ativa = True
    concessionarias_concessionaria_50.desc = None
    concessionarias_concessionaria_50.lote = None
    concessionarias_concessionaria_50.etapa = None
    concessionarias_concessionaria_50.contrato = None
    concessionarias_concessionaria_50.contrato_dt_ass = None
    concessionarias_concessionaria_50.inicio = None
    concessionarias_concessionaria_50.termino = None
    concessionarias_concessionaria_50.edital = None
    concessionarias_concessionaria_50.grupo = None
    concessionarias_concessionaria_50.link = None
    concessionarias_concessionaria_50.senha = None
    concessionarias_concessionaria_50.web = None
    concessionarias_concessionaria_50.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_50.atualizado_em = None
    concessionarias_concessionaria_50 = importer.save_or_locate(concessionarias_concessionaria_50)

    concessionarias_concessionaria_51 = Concessionaria()
    concessionarias_concessionaria_51.nome = '13.02'
    concessionarias_concessionaria_51.slug = '1302'
    concessionarias_concessionaria_51.tipo = 'DER'
    concessionarias_concessionaria_51.publica = True
    concessionarias_concessionaria_51.nome_fantasia = None
    concessionarias_concessionaria_51.razao_social = '13.02'
    concessionarias_concessionaria_51.logo = ''
    concessionarias_concessionaria_51.telefone_adm = None
    concessionarias_concessionaria_51.telefone_cco = None
    concessionarias_concessionaria_51.logradouro = None
    concessionarias_concessionaria_51.numero = None
    concessionarias_concessionaria_51.bairro = None
    concessionarias_concessionaria_51.cidade = None
    concessionarias_concessionaria_51.cep = None
    concessionarias_concessionaria_51.uf = None
    concessionarias_concessionaria_51.ativa = True
    concessionarias_concessionaria_51.desc = None
    concessionarias_concessionaria_51.lote = None
    concessionarias_concessionaria_51.etapa = None
    concessionarias_concessionaria_51.contrato = None
    concessionarias_concessionaria_51.contrato_dt_ass = None
    concessionarias_concessionaria_51.inicio = None
    concessionarias_concessionaria_51.termino = None
    concessionarias_concessionaria_51.edital = None
    concessionarias_concessionaria_51.grupo = None
    concessionarias_concessionaria_51.link = None
    concessionarias_concessionaria_51.senha = None
    concessionarias_concessionaria_51.web = None
    concessionarias_concessionaria_51.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_51.atualizado_em = None
    concessionarias_concessionaria_51 = importer.save_or_locate(concessionarias_concessionaria_51)

    concessionarias_concessionaria_52 = Concessionaria()
    concessionarias_concessionaria_52.nome = '13.03'
    concessionarias_concessionaria_52.slug = '1303'
    concessionarias_concessionaria_52.tipo = 'DER'
    concessionarias_concessionaria_52.publica = True
    concessionarias_concessionaria_52.nome_fantasia = None
    concessionarias_concessionaria_52.razao_social = '13.03'
    concessionarias_concessionaria_52.logo = ''
    concessionarias_concessionaria_52.telefone_adm = None
    concessionarias_concessionaria_52.telefone_cco = None
    concessionarias_concessionaria_52.logradouro = None
    concessionarias_concessionaria_52.numero = None
    concessionarias_concessionaria_52.bairro = None
    concessionarias_concessionaria_52.cidade = None
    concessionarias_concessionaria_52.cep = None
    concessionarias_concessionaria_52.uf = None
    concessionarias_concessionaria_52.ativa = True
    concessionarias_concessionaria_52.desc = None
    concessionarias_concessionaria_52.lote = None
    concessionarias_concessionaria_52.etapa = None
    concessionarias_concessionaria_52.contrato = None
    concessionarias_concessionaria_52.contrato_dt_ass = None
    concessionarias_concessionaria_52.inicio = None
    concessionarias_concessionaria_52.termino = None
    concessionarias_concessionaria_52.edital = None
    concessionarias_concessionaria_52.grupo = None
    concessionarias_concessionaria_52.link = None
    concessionarias_concessionaria_52.senha = None
    concessionarias_concessionaria_52.web = None
    concessionarias_concessionaria_52.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_52.atualizado_em = None
    concessionarias_concessionaria_52 = importer.save_or_locate(concessionarias_concessionaria_52)

    concessionarias_concessionaria_53 = Concessionaria()
    concessionarias_concessionaria_53.nome = '13.04'
    concessionarias_concessionaria_53.slug = '1304'
    concessionarias_concessionaria_53.tipo = 'DER'
    concessionarias_concessionaria_53.publica = True
    concessionarias_concessionaria_53.nome_fantasia = None
    concessionarias_concessionaria_53.razao_social = '13.04'
    concessionarias_concessionaria_53.logo = ''
    concessionarias_concessionaria_53.telefone_adm = None
    concessionarias_concessionaria_53.telefone_cco = None
    concessionarias_concessionaria_53.logradouro = None
    concessionarias_concessionaria_53.numero = None
    concessionarias_concessionaria_53.bairro = None
    concessionarias_concessionaria_53.cidade = None
    concessionarias_concessionaria_53.cep = None
    concessionarias_concessionaria_53.uf = None
    concessionarias_concessionaria_53.ativa = True
    concessionarias_concessionaria_53.desc = None
    concessionarias_concessionaria_53.lote = None
    concessionarias_concessionaria_53.etapa = None
    concessionarias_concessionaria_53.contrato = None
    concessionarias_concessionaria_53.contrato_dt_ass = None
    concessionarias_concessionaria_53.inicio = None
    concessionarias_concessionaria_53.termino = None
    concessionarias_concessionaria_53.edital = None
    concessionarias_concessionaria_53.grupo = None
    concessionarias_concessionaria_53.link = None
    concessionarias_concessionaria_53.senha = None
    concessionarias_concessionaria_53.web = None
    concessionarias_concessionaria_53.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_53.atualizado_em = None
    concessionarias_concessionaria_53 = importer.save_or_locate(concessionarias_concessionaria_53)

    concessionarias_concessionaria_54 = Concessionaria()
    concessionarias_concessionaria_54.nome = '13.05'
    concessionarias_concessionaria_54.slug = '1305'
    concessionarias_concessionaria_54.tipo = 'DER'
    concessionarias_concessionaria_54.publica = True
    concessionarias_concessionaria_54.nome_fantasia = None
    concessionarias_concessionaria_54.razao_social = '13.05'
    concessionarias_concessionaria_54.logo = ''
    concessionarias_concessionaria_54.telefone_adm = None
    concessionarias_concessionaria_54.telefone_cco = None
    concessionarias_concessionaria_54.logradouro = None
    concessionarias_concessionaria_54.numero = None
    concessionarias_concessionaria_54.bairro = None
    concessionarias_concessionaria_54.cidade = None
    concessionarias_concessionaria_54.cep = None
    concessionarias_concessionaria_54.uf = None
    concessionarias_concessionaria_54.ativa = True
    concessionarias_concessionaria_54.desc = None
    concessionarias_concessionaria_54.lote = None
    concessionarias_concessionaria_54.etapa = None
    concessionarias_concessionaria_54.contrato = None
    concessionarias_concessionaria_54.contrato_dt_ass = None
    concessionarias_concessionaria_54.inicio = None
    concessionarias_concessionaria_54.termino = None
    concessionarias_concessionaria_54.edital = None
    concessionarias_concessionaria_54.grupo = None
    concessionarias_concessionaria_54.link = None
    concessionarias_concessionaria_54.senha = None
    concessionarias_concessionaria_54.web = None
    concessionarias_concessionaria_54.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_54.atualizado_em = None
    concessionarias_concessionaria_54 = importer.save_or_locate(concessionarias_concessionaria_54)

    concessionarias_concessionaria_55 = Concessionaria()
    concessionarias_concessionaria_55.nome = '14.01'
    concessionarias_concessionaria_55.slug = '1401'
    concessionarias_concessionaria_55.tipo = 'DER'
    concessionarias_concessionaria_55.publica = True
    concessionarias_concessionaria_55.nome_fantasia = None
    concessionarias_concessionaria_55.razao_social = '14.01'
    concessionarias_concessionaria_55.logo = ''
    concessionarias_concessionaria_55.telefone_adm = None
    concessionarias_concessionaria_55.telefone_cco = None
    concessionarias_concessionaria_55.logradouro = None
    concessionarias_concessionaria_55.numero = None
    concessionarias_concessionaria_55.bairro = None
    concessionarias_concessionaria_55.cidade = None
    concessionarias_concessionaria_55.cep = None
    concessionarias_concessionaria_55.uf = None
    concessionarias_concessionaria_55.ativa = True
    concessionarias_concessionaria_55.desc = None
    concessionarias_concessionaria_55.lote = None
    concessionarias_concessionaria_55.etapa = None
    concessionarias_concessionaria_55.contrato = None
    concessionarias_concessionaria_55.contrato_dt_ass = None
    concessionarias_concessionaria_55.inicio = None
    concessionarias_concessionaria_55.termino = None
    concessionarias_concessionaria_55.edital = None
    concessionarias_concessionaria_55.grupo = None
    concessionarias_concessionaria_55.link = None
    concessionarias_concessionaria_55.senha = None
    concessionarias_concessionaria_55.web = None
    concessionarias_concessionaria_55.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_55.atualizado_em = None
    concessionarias_concessionaria_55 = importer.save_or_locate(concessionarias_concessionaria_55)

    concessionarias_concessionaria_56 = Concessionaria()
    concessionarias_concessionaria_56.nome = '14.02'
    concessionarias_concessionaria_56.slug = '1402'
    concessionarias_concessionaria_56.tipo = 'DER'
    concessionarias_concessionaria_56.publica = True
    concessionarias_concessionaria_56.nome_fantasia = None
    concessionarias_concessionaria_56.razao_social = '14.02'
    concessionarias_concessionaria_56.logo = ''
    concessionarias_concessionaria_56.telefone_adm = None
    concessionarias_concessionaria_56.telefone_cco = None
    concessionarias_concessionaria_56.logradouro = None
    concessionarias_concessionaria_56.numero = None
    concessionarias_concessionaria_56.bairro = None
    concessionarias_concessionaria_56.cidade = None
    concessionarias_concessionaria_56.cep = None
    concessionarias_concessionaria_56.uf = None
    concessionarias_concessionaria_56.ativa = True
    concessionarias_concessionaria_56.desc = None
    concessionarias_concessionaria_56.lote = None
    concessionarias_concessionaria_56.etapa = None
    concessionarias_concessionaria_56.contrato = None
    concessionarias_concessionaria_56.contrato_dt_ass = None
    concessionarias_concessionaria_56.inicio = None
    concessionarias_concessionaria_56.termino = None
    concessionarias_concessionaria_56.edital = None
    concessionarias_concessionaria_56.grupo = None
    concessionarias_concessionaria_56.link = None
    concessionarias_concessionaria_56.senha = None
    concessionarias_concessionaria_56.web = None
    concessionarias_concessionaria_56.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_56.atualizado_em = None
    concessionarias_concessionaria_56 = importer.save_or_locate(concessionarias_concessionaria_56)

    concessionarias_concessionaria_57 = Concessionaria()
    concessionarias_concessionaria_57.nome = '14.03'
    concessionarias_concessionaria_57.slug = '1403'
    concessionarias_concessionaria_57.tipo = 'DER'
    concessionarias_concessionaria_57.publica = True
    concessionarias_concessionaria_57.nome_fantasia = None
    concessionarias_concessionaria_57.razao_social = '14.03'
    concessionarias_concessionaria_57.logo = ''
    concessionarias_concessionaria_57.telefone_adm = None
    concessionarias_concessionaria_57.telefone_cco = None
    concessionarias_concessionaria_57.logradouro = None
    concessionarias_concessionaria_57.numero = None
    concessionarias_concessionaria_57.bairro = None
    concessionarias_concessionaria_57.cidade = None
    concessionarias_concessionaria_57.cep = None
    concessionarias_concessionaria_57.uf = None
    concessionarias_concessionaria_57.ativa = True
    concessionarias_concessionaria_57.desc = None
    concessionarias_concessionaria_57.lote = None
    concessionarias_concessionaria_57.etapa = None
    concessionarias_concessionaria_57.contrato = None
    concessionarias_concessionaria_57.contrato_dt_ass = None
    concessionarias_concessionaria_57.inicio = None
    concessionarias_concessionaria_57.termino = None
    concessionarias_concessionaria_57.edital = None
    concessionarias_concessionaria_57.grupo = None
    concessionarias_concessionaria_57.link = None
    concessionarias_concessionaria_57.senha = None
    concessionarias_concessionaria_57.web = None
    concessionarias_concessionaria_57.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_57.atualizado_em = None
    concessionarias_concessionaria_57 = importer.save_or_locate(concessionarias_concessionaria_57)

    concessionarias_concessionaria_58 = Concessionaria()
    concessionarias_concessionaria_58.nome = 'Autoban'
    concessionarias_concessionaria_58.slug = 'autoban'
    concessionarias_concessionaria_58.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_58.publica = False
    concessionarias_concessionaria_58.nome_fantasia = None
    concessionarias_concessionaria_58.razao_social = 'Concessionária do Sistema Anhanguera-Bandeirantes S/A'
    concessionarias_concessionaria_58.logo = ''
    concessionarias_concessionaria_58.telefone_adm = None
    concessionarias_concessionaria_58.telefone_cco = None
    concessionarias_concessionaria_58.logradouro = 'Avenida Maria do Carmo Guimarães Pellegrini'
    concessionarias_concessionaria_58.numero = '200'
    concessionarias_concessionaria_58.bairro = None
    concessionarias_concessionaria_58.cidade = 'Jundiaí'
    concessionarias_concessionaria_58.cep = '13209-500'
    concessionarias_concessionaria_58.uf = 'SP'
    concessionarias_concessionaria_58.ativa = True
    concessionarias_concessionaria_58.desc = None
    concessionarias_concessionaria_58.lote = 1
    concessionarias_concessionaria_58.etapa = None
    concessionarias_concessionaria_58.contrato = 'CR/005/1998'
    concessionarias_concessionaria_58.contrato_dt_ass = None
    concessionarias_concessionaria_58.inicio = None
    concessionarias_concessionaria_58.termino = None
    concessionarias_concessionaria_58.edital = '007/CIC/97'
    concessionarias_concessionaria_58.grupo = 'CCR'
    concessionarias_concessionaria_58.link = None
    concessionarias_concessionaria_58.senha = None
    concessionarias_concessionaria_58.web = None
    concessionarias_concessionaria_58.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_58.atualizado_em = None
    concessionarias_concessionaria_58 = importer.save_or_locate(concessionarias_concessionaria_58)

    concessionarias_concessionaria_59 = Concessionaria()
    concessionarias_concessionaria_59.nome = 'Autopista Fernão Dias'
    concessionarias_concessionaria_59.slug = 'autopista-fernao-dias'
    concessionarias_concessionaria_59.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_59.publica = False
    concessionarias_concessionaria_59.nome_fantasia = None
    concessionarias_concessionaria_59.razao_social = 'Autopista Fernão Dias'
    concessionarias_concessionaria_59.logo = ''
    concessionarias_concessionaria_59.telefone_adm = None
    concessionarias_concessionaria_59.telefone_cco = None
    concessionarias_concessionaria_59.logradouro = None
    concessionarias_concessionaria_59.numero = None
    concessionarias_concessionaria_59.bairro = None
    concessionarias_concessionaria_59.cidade = None
    concessionarias_concessionaria_59.cep = None
    concessionarias_concessionaria_59.uf = None
    concessionarias_concessionaria_59.ativa = True
    concessionarias_concessionaria_59.desc = None
    concessionarias_concessionaria_59.lote = None
    concessionarias_concessionaria_59.etapa = None
    concessionarias_concessionaria_59.contrato = None
    concessionarias_concessionaria_59.contrato_dt_ass = None
    concessionarias_concessionaria_59.inicio = None
    concessionarias_concessionaria_59.termino = None
    concessionarias_concessionaria_59.edital = None
    concessionarias_concessionaria_59.grupo = None
    concessionarias_concessionaria_59.link = None
    concessionarias_concessionaria_59.senha = None
    concessionarias_concessionaria_59.web = None
    concessionarias_concessionaria_59.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_59.atualizado_em = None
    concessionarias_concessionaria_59 = importer.save_or_locate(concessionarias_concessionaria_59)

    concessionarias_concessionaria_60 = Concessionaria()
    concessionarias_concessionaria_60.nome = 'Autopista Regis Bittencourt'
    concessionarias_concessionaria_60.slug = 'autopista-regis-bittencourt'
    concessionarias_concessionaria_60.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_60.publica = False
    concessionarias_concessionaria_60.nome_fantasia = None
    concessionarias_concessionaria_60.razao_social = 'Autopista Regis Bittencourt'
    concessionarias_concessionaria_60.logo = ''
    concessionarias_concessionaria_60.telefone_adm = None
    concessionarias_concessionaria_60.telefone_cco = None
    concessionarias_concessionaria_60.logradouro = None
    concessionarias_concessionaria_60.numero = None
    concessionarias_concessionaria_60.bairro = None
    concessionarias_concessionaria_60.cidade = None
    concessionarias_concessionaria_60.cep = None
    concessionarias_concessionaria_60.uf = None
    concessionarias_concessionaria_60.ativa = True
    concessionarias_concessionaria_60.desc = None
    concessionarias_concessionaria_60.lote = None
    concessionarias_concessionaria_60.etapa = None
    concessionarias_concessionaria_60.contrato = None
    concessionarias_concessionaria_60.contrato_dt_ass = None
    concessionarias_concessionaria_60.inicio = None
    concessionarias_concessionaria_60.termino = None
    concessionarias_concessionaria_60.edital = None
    concessionarias_concessionaria_60.grupo = None
    concessionarias_concessionaria_60.link = None
    concessionarias_concessionaria_60.senha = None
    concessionarias_concessionaria_60.web = None
    concessionarias_concessionaria_60.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_60.atualizado_em = None
    concessionarias_concessionaria_60 = importer.save_or_locate(concessionarias_concessionaria_60)

    concessionarias_concessionaria_61 = Concessionaria()
    concessionarias_concessionaria_61.nome = 'Autovias'
    concessionarias_concessionaria_61.slug = 'autovias'
    concessionarias_concessionaria_61.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_61.publica = False
    concessionarias_concessionaria_61.nome_fantasia = None
    concessionarias_concessionaria_61.razao_social = 'Autovias'
    concessionarias_concessionaria_61.logo = ''
    concessionarias_concessionaria_61.telefone_adm = None
    concessionarias_concessionaria_61.telefone_cco = None
    concessionarias_concessionaria_61.logradouro = None
    concessionarias_concessionaria_61.numero = None
    concessionarias_concessionaria_61.bairro = None
    concessionarias_concessionaria_61.cidade = None
    concessionarias_concessionaria_61.cep = None
    concessionarias_concessionaria_61.uf = None
    concessionarias_concessionaria_61.ativa = True
    concessionarias_concessionaria_61.desc = None
    concessionarias_concessionaria_61.lote = None
    concessionarias_concessionaria_61.etapa = None
    concessionarias_concessionaria_61.contrato = None
    concessionarias_concessionaria_61.contrato_dt_ass = None
    concessionarias_concessionaria_61.inicio = None
    concessionarias_concessionaria_61.termino = None
    concessionarias_concessionaria_61.edital = None
    concessionarias_concessionaria_61.grupo = None
    concessionarias_concessionaria_61.link = None
    concessionarias_concessionaria_61.senha = None
    concessionarias_concessionaria_61.web = None
    concessionarias_concessionaria_61.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_61.atualizado_em = None
    concessionarias_concessionaria_61 = importer.save_or_locate(concessionarias_concessionaria_61)

    concessionarias_concessionaria_62 = Concessionaria()
    concessionarias_concessionaria_62.nome = 'CART'
    concessionarias_concessionaria_62.slug = 'cart'
    concessionarias_concessionaria_62.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_62.publica = False
    concessionarias_concessionaria_62.nome_fantasia = None
    concessionarias_concessionaria_62.razao_social = 'Concessionária Auto Raposo Tavares S/A'
    concessionarias_concessionaria_62.logo = ''
    concessionarias_concessionaria_62.telefone_adm = None
    concessionarias_concessionaria_62.telefone_cco = None
    concessionarias_concessionaria_62.logradouro = 'Avenida Issa Marar'
    concessionarias_concessionaria_62.numero = '2-200'
    concessionarias_concessionaria_62.bairro = None
    concessionarias_concessionaria_62.cidade = 'Bauru'
    concessionarias_concessionaria_62.cep = '17018-002'
    concessionarias_concessionaria_62.uf = 'SP'
    concessionarias_concessionaria_62.ativa = True
    concessionarias_concessionaria_62.desc = None
    concessionarias_concessionaria_62.lote = 16
    concessionarias_concessionaria_62.etapa = None
    concessionarias_concessionaria_62.contrato = '002/ARTESP/2009'
    concessionarias_concessionaria_62.contrato_dt_ass = None
    concessionarias_concessionaria_62.inicio = None
    concessionarias_concessionaria_62.termino = None
    concessionarias_concessionaria_62.edital = '004/2008'
    concessionarias_concessionaria_62.grupo = 'CART'
    concessionarias_concessionaria_62.link = None
    concessionarias_concessionaria_62.senha = None
    concessionarias_concessionaria_62.web = None
    concessionarias_concessionaria_62.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_62.atualizado_em = None
    concessionarias_concessionaria_62 = importer.save_or_locate(concessionarias_concessionaria_62)

    concessionarias_concessionaria_63 = Concessionaria()
    concessionarias_concessionaria_63.nome = 'CCR RodoAnel'
    concessionarias_concessionaria_63.slug = 'ccr-rodoanel'
    concessionarias_concessionaria_63.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_63.publica = False
    concessionarias_concessionaria_63.nome_fantasia = None
    concessionarias_concessionaria_63.razao_social = 'Concessionária do Rodoanel Oeste S/A'
    concessionarias_concessionaria_63.logo = ''
    concessionarias_concessionaria_63.telefone_adm = None
    concessionarias_concessionaria_63.telefone_cco = None
    concessionarias_concessionaria_63.logradouro = 'Avenida Marcos Penteado Ulhoa Rodrigues'
    concessionarias_concessionaria_63.numero = '690'
    concessionarias_concessionaria_63.bairro = None
    concessionarias_concessionaria_63.cidade = 'Barueri'
    concessionarias_concessionaria_63.cep = '06460-040'
    concessionarias_concessionaria_63.uf = 'SP'
    concessionarias_concessionaria_63.ativa = True
    concessionarias_concessionaria_63.desc = None
    concessionarias_concessionaria_63.lote = 24
    concessionarias_concessionaria_63.etapa = None
    concessionarias_concessionaria_63.contrato = '001/ARTESP/2008'
    concessionarias_concessionaria_63.contrato_dt_ass = None
    concessionarias_concessionaria_63.inicio = None
    concessionarias_concessionaria_63.termino = None
    concessionarias_concessionaria_63.edital = '001/2008'
    concessionarias_concessionaria_63.grupo = 'CCR'
    concessionarias_concessionaria_63.link = None
    concessionarias_concessionaria_63.senha = None
    concessionarias_concessionaria_63.web = None
    concessionarias_concessionaria_63.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_63.atualizado_em = None
    concessionarias_concessionaria_63 = importer.save_or_locate(concessionarias_concessionaria_63)

    concessionarias_concessionaria_64 = Concessionaria()
    concessionarias_concessionaria_64.nome = 'Centrovias'
    concessionarias_concessionaria_64.slug = 'centrovias'
    concessionarias_concessionaria_64.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_64.publica = False
    concessionarias_concessionaria_64.nome_fantasia = None
    concessionarias_concessionaria_64.razao_social = 'Centrovias - Sistemas Rodoviários S/A'
    concessionarias_concessionaria_64.logo = ''
    concessionarias_concessionaria_64.telefone_adm = None
    concessionarias_concessionaria_64.telefone_cco = None
    concessionarias_concessionaria_64.logradouro = 'Rodovia Washington Luís'
    concessionarias_concessionaria_64.numero = 'km 216+800 Sul'
    concessionarias_concessionaria_64.bairro = None
    concessionarias_concessionaria_64.cidade = 'São Carlos'
    concessionarias_concessionaria_64.cep = '13574-970'
    concessionarias_concessionaria_64.uf = 'SP'
    concessionarias_concessionaria_64.ativa = True
    concessionarias_concessionaria_64.desc = None
    concessionarias_concessionaria_64.lote = 8
    concessionarias_concessionaria_64.etapa = None
    concessionarias_concessionaria_64.contrato = '008/CR/98'
    concessionarias_concessionaria_64.contrato_dt_ass = None
    concessionarias_concessionaria_64.inicio = None
    concessionarias_concessionaria_64.termino = None
    concessionarias_concessionaria_64.edital = '016/CIC/97'
    concessionarias_concessionaria_64.grupo = 'Arteris'
    concessionarias_concessionaria_64.link = None
    concessionarias_concessionaria_64.senha = None
    concessionarias_concessionaria_64.web = None
    concessionarias_concessionaria_64.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_64.atualizado_em = None
    concessionarias_concessionaria_64 = importer.save_or_locate(concessionarias_concessionaria_64)

    concessionarias_concessionaria_65 = Concessionaria()
    concessionarias_concessionaria_65.nome = 'Colinas'
    concessionarias_concessionaria_65.slug = 'colinas'
    concessionarias_concessionaria_65.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_65.publica = False
    concessionarias_concessionaria_65.nome_fantasia = None
    concessionarias_concessionaria_65.razao_social = 'Rodovias das Colinas S/A'
    concessionarias_concessionaria_65.logo = ''
    concessionarias_concessionaria_65.telefone_adm = None
    concessionarias_concessionaria_65.telefone_cco = None
    concessionarias_concessionaria_65.logradouro = 'Rodovia Marechal Rondon'
    concessionarias_concessionaria_65.numero = 'km 112'
    concessionarias_concessionaria_65.bairro = None
    concessionarias_concessionaria_65.cidade = 'Itú'
    concessionarias_concessionaria_65.cep = '13312-000'
    concessionarias_concessionaria_65.uf = 'SP'
    concessionarias_concessionaria_65.ativa = True
    concessionarias_concessionaria_65.desc = None
    concessionarias_concessionaria_65.lote = 13
    concessionarias_concessionaria_65.etapa = None
    concessionarias_concessionaria_65.contrato = '012/CR/2000'
    concessionarias_concessionaria_65.contrato_dt_ass = None
    concessionarias_concessionaria_65.inicio = None
    concessionarias_concessionaria_65.termino = None
    concessionarias_concessionaria_65.edital = '17/CIC/97'
    concessionarias_concessionaria_65.grupo = 'AB Concessões'
    concessionarias_concessionaria_65.link = None
    concessionarias_concessionaria_65.senha = None
    concessionarias_concessionaria_65.web = None
    concessionarias_concessionaria_65.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_65.atualizado_em = None
    concessionarias_concessionaria_65 = importer.save_or_locate(concessionarias_concessionaria_65)

    concessionarias_concessionaria_66 = Concessionaria()
    concessionarias_concessionaria_66.nome = 'DNIT'
    concessionarias_concessionaria_66.slug = 'dnit'
    concessionarias_concessionaria_66.tipo = 'DNIT'
    concessionarias_concessionaria_66.publica = True
    concessionarias_concessionaria_66.nome_fantasia = None
    concessionarias_concessionaria_66.razao_social = 'DNIT'
    concessionarias_concessionaria_66.logo = ''
    concessionarias_concessionaria_66.telefone_adm = None
    concessionarias_concessionaria_66.telefone_cco = None
    concessionarias_concessionaria_66.logradouro = None
    concessionarias_concessionaria_66.numero = None
    concessionarias_concessionaria_66.bairro = None
    concessionarias_concessionaria_66.cidade = None
    concessionarias_concessionaria_66.cep = None
    concessionarias_concessionaria_66.uf = None
    concessionarias_concessionaria_66.ativa = True
    concessionarias_concessionaria_66.desc = None
    concessionarias_concessionaria_66.lote = None
    concessionarias_concessionaria_66.etapa = None
    concessionarias_concessionaria_66.contrato = None
    concessionarias_concessionaria_66.contrato_dt_ass = None
    concessionarias_concessionaria_66.inicio = None
    concessionarias_concessionaria_66.termino = None
    concessionarias_concessionaria_66.edital = None
    concessionarias_concessionaria_66.grupo = None
    concessionarias_concessionaria_66.link = None
    concessionarias_concessionaria_66.senha = None
    concessionarias_concessionaria_66.web = None
    concessionarias_concessionaria_66.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_66.atualizado_em = None
    concessionarias_concessionaria_66 = importer.save_or_locate(concessionarias_concessionaria_66)

    concessionarias_concessionaria_67 = Concessionaria()
    concessionarias_concessionaria_67.nome = 'Ecopistas'
    concessionarias_concessionaria_67.slug = 'ecopistas'
    concessionarias_concessionaria_67.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_67.publica = False
    concessionarias_concessionaria_67.nome_fantasia = None
    concessionarias_concessionaria_67.razao_social = 'Concessionária das Rodovias Ayrton Senna e Carvalho Pinto S/A'
    concessionarias_concessionaria_67.logo = ''
    concessionarias_concessionaria_67.telefone_adm = None
    concessionarias_concessionaria_67.telefone_cco = None
    concessionarias_concessionaria_67.logradouro = 'Rodovia Ayrton Senna da Silva'
    concessionarias_concessionaria_67.numero = 'km 32'
    concessionarias_concessionaria_67.bairro = None
    concessionarias_concessionaria_67.cidade = 'Itaquaquecetuba'
    concessionarias_concessionaria_67.cep = '08578-010'
    concessionarias_concessionaria_67.uf = 'SP'
    concessionarias_concessionaria_67.ativa = True
    concessionarias_concessionaria_67.desc = None
    concessionarias_concessionaria_67.lote = 23
    concessionarias_concessionaria_67.etapa = None
    concessionarias_concessionaria_67.contrato = '006/ARTESP/2009'
    concessionarias_concessionaria_67.contrato_dt_ass = None
    concessionarias_concessionaria_67.inicio = None
    concessionarias_concessionaria_67.termino = None
    concessionarias_concessionaria_67.edital = '003/2008'
    concessionarias_concessionaria_67.grupo = 'Ecorodovias'
    concessionarias_concessionaria_67.link = None
    concessionarias_concessionaria_67.senha = None
    concessionarias_concessionaria_67.web = None
    concessionarias_concessionaria_67.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_67.atualizado_em = None
    concessionarias_concessionaria_67 = importer.save_or_locate(concessionarias_concessionaria_67)

    concessionarias_concessionaria_68 = Concessionaria()
    concessionarias_concessionaria_68.nome = 'Ecovias'
    concessionarias_concessionaria_68.slug = 'ecovias'
    concessionarias_concessionaria_68.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_68.publica = False
    concessionarias_concessionaria_68.nome_fantasia = None
    concessionarias_concessionaria_68.razao_social = 'Concessionária Ecovias dos Imigrantes S/A'
    concessionarias_concessionaria_68.logo = ''
    concessionarias_concessionaria_68.telefone_adm = None
    concessionarias_concessionaria_68.telefone_cco = None
    concessionarias_concessionaria_68.logradouro = 'Rodovia dos Imigrantes'
    concessionarias_concessionaria_68.numero = 'km 28,5'
    concessionarias_concessionaria_68.bairro = None
    concessionarias_concessionaria_68.cidade = 'São Bernardo do Campo'
    concessionarias_concessionaria_68.cep = '09845-000'
    concessionarias_concessionaria_68.uf = 'SP'
    concessionarias_concessionaria_68.ativa = True
    concessionarias_concessionaria_68.desc = None
    concessionarias_concessionaria_68.lote = 22
    concessionarias_concessionaria_68.etapa = None
    concessionarias_concessionaria_68.contrato = '007/CR/1998'
    concessionarias_concessionaria_68.contrato_dt_ass = None
    concessionarias_concessionaria_68.inicio = None
    concessionarias_concessionaria_68.termino = None
    concessionarias_concessionaria_68.edital = '15/CIC/97'
    concessionarias_concessionaria_68.grupo = 'Ecorodovias'
    concessionarias_concessionaria_68.link = None
    concessionarias_concessionaria_68.senha = None
    concessionarias_concessionaria_68.web = None
    concessionarias_concessionaria_68.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.170541+00:00")
    concessionarias_concessionaria_68.atualizado_em = None
    concessionarias_concessionaria_68 = importer.save_or_locate(concessionarias_concessionaria_68)

    concessionarias_concessionaria_69 = Concessionaria()
    concessionarias_concessionaria_69.nome = 'Eixo SP'
    concessionarias_concessionaria_69.slug = 'eixo-sp'
    concessionarias_concessionaria_69.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_69.publica = False
    concessionarias_concessionaria_69.nome_fantasia = None
    concessionarias_concessionaria_69.razao_social = 'Concessionária de Rodovias Piracicaba Panorama S/A - Eixo SP'
    concessionarias_concessionaria_69.logo = ''
    concessionarias_concessionaria_69.telefone_adm = None
    concessionarias_concessionaria_69.telefone_cco = None
    concessionarias_concessionaria_69.logradouro = 'Rodovia Washington Luís'
    concessionarias_concessionaria_69.numero = 'km 216+800 Sul'
    concessionarias_concessionaria_69.bairro = None
    concessionarias_concessionaria_69.cidade = 'São Carlos'
    concessionarias_concessionaria_69.cep = '13574-970'
    concessionarias_concessionaria_69.uf = 'SP'
    concessionarias_concessionaria_69.ativa = True
    concessionarias_concessionaria_69.desc = None
    concessionarias_concessionaria_69.lote = 30
    concessionarias_concessionaria_69.etapa = 5
    concessionarias_concessionaria_69.contrato = '0409/ARTESP/2020'
    concessionarias_concessionaria_69.contrato_dt_ass = None
    concessionarias_concessionaria_69.inicio = None
    concessionarias_concessionaria_69.termino = None
    concessionarias_concessionaria_69.edital = 'jan/19'
    concessionarias_concessionaria_69.grupo = None
    concessionarias_concessionaria_69.link = None
    concessionarias_concessionaria_69.senha = None
    concessionarias_concessionaria_69.web = None
    concessionarias_concessionaria_69.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.170541+00:00")
    concessionarias_concessionaria_69.atualizado_em = None
    concessionarias_concessionaria_69 = importer.save_or_locate(concessionarias_concessionaria_69)

    concessionarias_concessionaria_70 = Concessionaria()
    concessionarias_concessionaria_70.nome = 'Entrevias'
    concessionarias_concessionaria_70.slug = 'entrevias'
    concessionarias_concessionaria_70.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_70.publica = False
    concessionarias_concessionaria_70.nome_fantasia = None
    concessionarias_concessionaria_70.razao_social = 'Entrevias Concessionária de Rodovias S/A'
    concessionarias_concessionaria_70.logo = ''
    concessionarias_concessionaria_70.telefone_adm = None
    concessionarias_concessionaria_70.telefone_cco = None
    concessionarias_concessionaria_70.logradouro = 'Rodovia Attílio Balbo'
    concessionarias_concessionaria_70.numero = 'km 327+500'
    concessionarias_concessionaria_70.bairro = None
    concessionarias_concessionaria_70.cidade = 'Sertãozinho'
    concessionarias_concessionaria_70.cep = '14173-000'
    concessionarias_concessionaria_70.uf = 'SP'
    concessionarias_concessionaria_70.ativa = True
    concessionarias_concessionaria_70.desc = None
    concessionarias_concessionaria_70.lote = 28
    concessionarias_concessionaria_70.etapa = None
    concessionarias_concessionaria_70.contrato = '0352/ARTESP/2017'
    concessionarias_concessionaria_70.contrato_dt_ass = None
    concessionarias_concessionaria_70.inicio = None
    concessionarias_concessionaria_70.termino = None
    concessionarias_concessionaria_70.edital = '003/2016'
    concessionarias_concessionaria_70.grupo = 'Pátria'
    concessionarias_concessionaria_70.link = None
    concessionarias_concessionaria_70.senha = None
    concessionarias_concessionaria_70.web = None
    concessionarias_concessionaria_70.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.170541+00:00")
    concessionarias_concessionaria_70.atualizado_em = None
    concessionarias_concessionaria_70 = importer.save_or_locate(concessionarias_concessionaria_70)

    concessionarias_concessionaria_71 = Concessionaria()
    concessionarias_concessionaria_71.nome = 'Intervias'
    concessionarias_concessionaria_71.slug = 'intervias'
    concessionarias_concessionaria_71.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_71.publica = False
    concessionarias_concessionaria_71.nome_fantasia = None
    concessionarias_concessionaria_71.razao_social = 'Concessionária de Rodovias do Interior Paulista S/A'
    concessionarias_concessionaria_71.logo = ''
    concessionarias_concessionaria_71.telefone_adm = None
    concessionarias_concessionaria_71.telefone_cco = None
    concessionarias_concessionaria_71.logradouro = 'Rodovia Anhanguera'
    concessionarias_concessionaria_71.numero = 'km 168, Pista sul'
    concessionarias_concessionaria_71.bairro = None
    concessionarias_concessionaria_71.cidade = 'Araras'
    concessionarias_concessionaria_71.cep = '13602-040'
    concessionarias_concessionaria_71.uf = 'SP'
    concessionarias_concessionaria_71.ativa = True
    concessionarias_concessionaria_71.desc = None
    concessionarias_concessionaria_71.lote = 6
    concessionarias_concessionaria_71.etapa = None
    concessionarias_concessionaria_71.contrato = '011/CR/2000'
    concessionarias_concessionaria_71.contrato_dt_ass = None
    concessionarias_concessionaria_71.inicio = None
    concessionarias_concessionaria_71.termino = None
    concessionarias_concessionaria_71.edital = '019/CIC/98'
    concessionarias_concessionaria_71.grupo = 'Arteris'
    concessionarias_concessionaria_71.link = None
    concessionarias_concessionaria_71.senha = None
    concessionarias_concessionaria_71.web = None
    concessionarias_concessionaria_71.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.170541+00:00")
    concessionarias_concessionaria_71.atualizado_em = None
    concessionarias_concessionaria_71 = importer.save_or_locate(concessionarias_concessionaria_71)

    concessionarias_concessionaria_72 = Concessionaria()
    concessionarias_concessionaria_72.nome = 'NovaDutra'
    concessionarias_concessionaria_72.slug = 'novadutra'
    concessionarias_concessionaria_72.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_72.publica = False
    concessionarias_concessionaria_72.nome_fantasia = None
    concessionarias_concessionaria_72.razao_social = 'NovaDutra'
    concessionarias_concessionaria_72.logo = ''
    concessionarias_concessionaria_72.telefone_adm = None
    concessionarias_concessionaria_72.telefone_cco = None
    concessionarias_concessionaria_72.logradouro = None
    concessionarias_concessionaria_72.numero = None
    concessionarias_concessionaria_72.bairro = None
    concessionarias_concessionaria_72.cidade = None
    concessionarias_concessionaria_72.cep = None
    concessionarias_concessionaria_72.uf = None
    concessionarias_concessionaria_72.ativa = True
    concessionarias_concessionaria_72.desc = None
    concessionarias_concessionaria_72.lote = None
    concessionarias_concessionaria_72.etapa = None
    concessionarias_concessionaria_72.contrato = None
    concessionarias_concessionaria_72.contrato_dt_ass = None
    concessionarias_concessionaria_72.inicio = None
    concessionarias_concessionaria_72.termino = None
    concessionarias_concessionaria_72.edital = None
    concessionarias_concessionaria_72.grupo = None
    concessionarias_concessionaria_72.link = None
    concessionarias_concessionaria_72.senha = None
    concessionarias_concessionaria_72.web = None
    concessionarias_concessionaria_72.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_72.atualizado_em = None
    concessionarias_concessionaria_72 = importer.save_or_locate(concessionarias_concessionaria_72)

    concessionarias_concessionaria_73 = Concessionaria()
    concessionarias_concessionaria_73.nome = 'Prefeitura'
    concessionarias_concessionaria_73.slug = 'prefeitura'
    concessionarias_concessionaria_73.tipo = 'PREFEITURA'
    concessionarias_concessionaria_73.publica = True
    concessionarias_concessionaria_73.nome_fantasia = None
    concessionarias_concessionaria_73.razao_social = 'Prefeitura'
    concessionarias_concessionaria_73.logo = ''
    concessionarias_concessionaria_73.telefone_adm = None
    concessionarias_concessionaria_73.telefone_cco = None
    concessionarias_concessionaria_73.logradouro = None
    concessionarias_concessionaria_73.numero = None
    concessionarias_concessionaria_73.bairro = None
    concessionarias_concessionaria_73.cidade = None
    concessionarias_concessionaria_73.cep = None
    concessionarias_concessionaria_73.uf = None
    concessionarias_concessionaria_73.ativa = True
    concessionarias_concessionaria_73.desc = None
    concessionarias_concessionaria_73.lote = None
    concessionarias_concessionaria_73.etapa = None
    concessionarias_concessionaria_73.contrato = None
    concessionarias_concessionaria_73.contrato_dt_ass = None
    concessionarias_concessionaria_73.inicio = None
    concessionarias_concessionaria_73.termino = None
    concessionarias_concessionaria_73.edital = None
    concessionarias_concessionaria_73.grupo = None
    concessionarias_concessionaria_73.link = None
    concessionarias_concessionaria_73.senha = None
    concessionarias_concessionaria_73.web = None
    concessionarias_concessionaria_73.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.139318+00:00")
    concessionarias_concessionaria_73.atualizado_em = None
    concessionarias_concessionaria_73 = importer.save_or_locate(concessionarias_concessionaria_73)

    concessionarias_concessionaria_74 = Concessionaria()
    concessionarias_concessionaria_74.nome = 'Renovias'
    concessionarias_concessionaria_74.slug = 'renovias'
    concessionarias_concessionaria_74.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_74.publica = False
    concessionarias_concessionaria_74.nome_fantasia = None
    concessionarias_concessionaria_74.razao_social = 'Renovias Concessionária S/A'
    concessionarias_concessionaria_74.logo = ''
    concessionarias_concessionaria_74.telefone_adm = None
    concessionarias_concessionaria_74.telefone_cco = None
    concessionarias_concessionaria_74.logradouro = 'Rodovia SP 340'
    concessionarias_concessionaria_74.numero = 'km 161'
    concessionarias_concessionaria_74.bairro = None
    concessionarias_concessionaria_74.cidade = 'Mogi Mirim'
    concessionarias_concessionaria_74.cep = '13805-280'
    concessionarias_concessionaria_74.uf = 'SP'
    concessionarias_concessionaria_74.ativa = True
    concessionarias_concessionaria_74.desc = None
    concessionarias_concessionaria_74.lote = 11
    concessionarias_concessionaria_74.etapa = None
    concessionarias_concessionaria_74.contrato = 'CR/004/1998'
    concessionarias_concessionaria_74.contrato_dt_ass = None
    concessionarias_concessionaria_74.inicio = None
    concessionarias_concessionaria_74.termino = None
    concessionarias_concessionaria_74.edital = '10/CIC/97'
    concessionarias_concessionaria_74.grupo = 'Renovias'
    concessionarias_concessionaria_74.link = None
    concessionarias_concessionaria_74.senha = None
    concessionarias_concessionaria_74.web = None
    concessionarias_concessionaria_74.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_74.atualizado_em = None
    concessionarias_concessionaria_74 = importer.save_or_locate(concessionarias_concessionaria_74)

    concessionarias_concessionaria_75 = Concessionaria()
    concessionarias_concessionaria_75.nome = 'Rodovias do Tietê'
    concessionarias_concessionaria_75.slug = 'rodovias-do-tiete'
    concessionarias_concessionaria_75.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_75.publica = False
    concessionarias_concessionaria_75.nome_fantasia = None
    concessionarias_concessionaria_75.razao_social = 'Concessionária Rodovias do Tietê S/A'
    concessionarias_concessionaria_75.logo = ''
    concessionarias_concessionaria_75.telefone_adm = None
    concessionarias_concessionaria_75.telefone_cco = None
    concessionarias_concessionaria_75.logradouro = 'Rodovia Comendador Mário Dedini'
    concessionarias_concessionaria_75.numero = 'km 108'
    concessionarias_concessionaria_75.bairro = None
    concessionarias_concessionaria_75.cidade = 'Salto'
    concessionarias_concessionaria_75.cep = '13320-970'
    concessionarias_concessionaria_75.uf = 'SP'
    concessionarias_concessionaria_75.ativa = True
    concessionarias_concessionaria_75.desc = None
    concessionarias_concessionaria_75.lote = 21
    concessionarias_concessionaria_75.etapa = None
    concessionarias_concessionaria_75.contrato = '004/ARTESP/2009'
    concessionarias_concessionaria_75.contrato_dt_ass = None
    concessionarias_concessionaria_75.inicio = None
    concessionarias_concessionaria_75.termino = None
    concessionarias_concessionaria_75.edital = '005/2008'
    concessionarias_concessionaria_75.grupo = 'AB Concessões'
    concessionarias_concessionaria_75.link = None
    concessionarias_concessionaria_75.senha = None
    concessionarias_concessionaria_75.web = None
    concessionarias_concessionaria_75.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_75.atualizado_em = None
    concessionarias_concessionaria_75 = importer.save_or_locate(concessionarias_concessionaria_75)

    concessionarias_concessionaria_76 = Concessionaria()
    concessionarias_concessionaria_76.nome = 'Rota das Bandeiras'
    concessionarias_concessionaria_76.slug = 'rota-das-bandeiras'
    concessionarias_concessionaria_76.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_76.publica = False
    concessionarias_concessionaria_76.nome_fantasia = None
    concessionarias_concessionaria_76.razao_social = 'Concessionária Rota das Bandeiras S/A'
    concessionarias_concessionaria_76.logo = ''
    concessionarias_concessionaria_76.telefone_adm = None
    concessionarias_concessionaria_76.telefone_cco = None
    concessionarias_concessionaria_76.logradouro = 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul'
    concessionarias_concessionaria_76.numero = 'km 110+400'
    concessionarias_concessionaria_76.bairro = None
    concessionarias_concessionaria_76.cidade = 'Itatiba'
    concessionarias_concessionaria_76.cep = '13252-800'
    concessionarias_concessionaria_76.uf = 'SP'
    concessionarias_concessionaria_76.ativa = True
    concessionarias_concessionaria_76.desc = None
    concessionarias_concessionaria_76.lote = 7
    concessionarias_concessionaria_76.etapa = None
    concessionarias_concessionaria_76.contrato = '003/ARTESP/2009'
    concessionarias_concessionaria_76.contrato_dt_ass = None
    concessionarias_concessionaria_76.inicio = None
    concessionarias_concessionaria_76.termino = None
    concessionarias_concessionaria_76.edital = '003/2009'
    concessionarias_concessionaria_76.grupo = 'Odebrecht'
    concessionarias_concessionaria_76.link = None
    concessionarias_concessionaria_76.senha = None
    concessionarias_concessionaria_76.web = None
    concessionarias_concessionaria_76.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.170541+00:00")
    concessionarias_concessionaria_76.atualizado_em = None
    concessionarias_concessionaria_76 = importer.save_or_locate(concessionarias_concessionaria_76)

    concessionarias_concessionaria_77 = Concessionaria()
    concessionarias_concessionaria_77.nome = 'SPMAR'
    concessionarias_concessionaria_77.slug = 'spmar'
    concessionarias_concessionaria_77.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_77.publica = False
    concessionarias_concessionaria_77.nome_fantasia = None
    concessionarias_concessionaria_77.razao_social = 'Concessionária SPMAR S/A'
    concessionarias_concessionaria_77.logo = ''
    concessionarias_concessionaria_77.telefone_adm = None
    concessionarias_concessionaria_77.telefone_cco = None
    concessionarias_concessionaria_77.logradouro = 'Rodoanel Mário Covas'
    concessionarias_concessionaria_77.numero = 'Km 41'
    concessionarias_concessionaria_77.bairro = None
    concessionarias_concessionaria_77.cidade = 'Itapecerica da Serra'
    concessionarias_concessionaria_77.cep = '06869-000'
    concessionarias_concessionaria_77.uf = 'SP'
    concessionarias_concessionaria_77.ativa = True
    concessionarias_concessionaria_77.desc = None
    concessionarias_concessionaria_77.lote = 25
    concessionarias_concessionaria_77.etapa = None
    concessionarias_concessionaria_77.contrato = '001/ARTESP/2011'
    concessionarias_concessionaria_77.contrato_dt_ass = None
    concessionarias_concessionaria_77.inicio = None
    concessionarias_concessionaria_77.termino = None
    concessionarias_concessionaria_77.edital = '001/2010'
    concessionarias_concessionaria_77.grupo = None
    concessionarias_concessionaria_77.link = None
    concessionarias_concessionaria_77.senha = None
    concessionarias_concessionaria_77.web = None
    concessionarias_concessionaria_77.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_77.atualizado_em = None
    concessionarias_concessionaria_77 = importer.save_or_locate(concessionarias_concessionaria_77)

    concessionarias_concessionaria_78 = Concessionaria()
    concessionarias_concessionaria_78.nome = 'Spvias'
    concessionarias_concessionaria_78.slug = 'spvias'
    concessionarias_concessionaria_78.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_78.publica = False
    concessionarias_concessionaria_78.nome_fantasia = None
    concessionarias_concessionaria_78.razao_social = 'Rodovias Integradas do Oeste S/A'
    concessionarias_concessionaria_78.logo = ''
    concessionarias_concessionaria_78.telefone_adm = None
    concessionarias_concessionaria_78.telefone_cco = None
    concessionarias_concessionaria_78.logradouro = 'SP127'
    concessionarias_concessionaria_78.numero = 'KM 112+400 SUL'
    concessionarias_concessionaria_78.bairro = None
    concessionarias_concessionaria_78.cidade = 'Tatuí'
    concessionarias_concessionaria_78.cep = '18277-670'
    concessionarias_concessionaria_78.uf = 'SP'
    concessionarias_concessionaria_78.ativa = True
    concessionarias_concessionaria_78.desc = None
    concessionarias_concessionaria_78.lote = 20
    concessionarias_concessionaria_78.etapa = None
    concessionarias_concessionaria_78.contrato = '010/CR/2000'
    concessionarias_concessionaria_78.contrato_dt_ass = None
    concessionarias_concessionaria_78.inicio = None
    concessionarias_concessionaria_78.termino = None
    concessionarias_concessionaria_78.edital = '20/CIC/98'
    concessionarias_concessionaria_78.grupo = 'CCR'
    concessionarias_concessionaria_78.link = None
    concessionarias_concessionaria_78.senha = None
    concessionarias_concessionaria_78.web = None
    concessionarias_concessionaria_78.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.170541+00:00")
    concessionarias_concessionaria_78.atualizado_em = None
    concessionarias_concessionaria_78 = importer.save_or_locate(concessionarias_concessionaria_78)

    concessionarias_concessionaria_79 = Concessionaria()
    concessionarias_concessionaria_79.nome = 'Tamoios'
    concessionarias_concessionaria_79.slug = 'tamoios'
    concessionarias_concessionaria_79.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_79.publica = False
    concessionarias_concessionaria_79.nome_fantasia = None
    concessionarias_concessionaria_79.razao_social = 'Concessionaria Rodovia dos Tamoios S.A.'
    concessionarias_concessionaria_79.logo = ''
    concessionarias_concessionaria_79.telefone_adm = None
    concessionarias_concessionaria_79.telefone_cco = None
    concessionarias_concessionaria_79.logradouro = 'Avenida Cassiano Ricardo'
    concessionarias_concessionaria_79.numero = '601'
    concessionarias_concessionaria_79.bairro = None
    concessionarias_concessionaria_79.cidade = 'São Paulo'
    concessionarias_concessionaria_79.cep = '12246-870'
    concessionarias_concessionaria_79.uf = 'SP'
    concessionarias_concessionaria_79.ativa = True
    concessionarias_concessionaria_79.desc = None
    concessionarias_concessionaria_79.lote = 27
    concessionarias_concessionaria_79.etapa = None
    concessionarias_concessionaria_79.contrato = '008/2014'
    concessionarias_concessionaria_79.contrato_dt_ass = None
    concessionarias_concessionaria_79.inicio = None
    concessionarias_concessionaria_79.termino = None
    concessionarias_concessionaria_79.edital = '001/2014'
    concessionarias_concessionaria_79.grupo = 'Queiroz Galvão'
    concessionarias_concessionaria_79.link = None
    concessionarias_concessionaria_79.senha = None
    concessionarias_concessionaria_79.web = None
    concessionarias_concessionaria_79.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_79.atualizado_em = None
    concessionarias_concessionaria_79 = importer.save_or_locate(concessionarias_concessionaria_79)

    concessionarias_concessionaria_80 = Concessionaria()
    concessionarias_concessionaria_80.nome = 'Tebe'
    concessionarias_concessionaria_80.slug = 'tebe'
    concessionarias_concessionaria_80.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_80.publica = False
    concessionarias_concessionaria_80.nome_fantasia = None
    concessionarias_concessionaria_80.razao_social = 'Concessionária de Rodovias Tebe S/A'
    concessionarias_concessionaria_80.logo = ''
    concessionarias_concessionaria_80.telefone_adm = None
    concessionarias_concessionaria_80.telefone_cco = None
    concessionarias_concessionaria_80.logradouro = 'Rod. Brigadeiro Faria Lima'
    concessionarias_concessionaria_80.numero = 'km 382+982m (norte)'
    concessionarias_concessionaria_80.bairro = None
    concessionarias_concessionaria_80.cidade = 'Bebedouro'
    concessionarias_concessionaria_80.cep = '14713-000'
    concessionarias_concessionaria_80.uf = 'SP'
    concessionarias_concessionaria_80.ativa = True
    concessionarias_concessionaria_80.desc = None
    concessionarias_concessionaria_80.lote = 3
    concessionarias_concessionaria_80.etapa = None
    concessionarias_concessionaria_80.contrato = 'CR/001/1998'
    concessionarias_concessionaria_80.contrato_dt_ass = None
    concessionarias_concessionaria_80.inicio = None
    concessionarias_concessionaria_80.termino = None
    concessionarias_concessionaria_80.edital = '013/CIC/97'
    concessionarias_concessionaria_80.grupo = 'TORC-ECB'
    concessionarias_concessionaria_80.link = None
    concessionarias_concessionaria_80.senha = None
    concessionarias_concessionaria_80.web = None
    concessionarias_concessionaria_80.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_80.atualizado_em = None
    concessionarias_concessionaria_80 = importer.save_or_locate(concessionarias_concessionaria_80)

    concessionarias_concessionaria_81 = Concessionaria()
    concessionarias_concessionaria_81.nome = 'Transbrasiliana'
    concessionarias_concessionaria_81.slug = 'transbrasiliana'
    concessionarias_concessionaria_81.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_81.publica = False
    concessionarias_concessionaria_81.nome_fantasia = None
    concessionarias_concessionaria_81.razao_social = 'Transbrasiliana'
    concessionarias_concessionaria_81.logo = ''
    concessionarias_concessionaria_81.telefone_adm = None
    concessionarias_concessionaria_81.telefone_cco = None
    concessionarias_concessionaria_81.logradouro = None
    concessionarias_concessionaria_81.numero = None
    concessionarias_concessionaria_81.bairro = None
    concessionarias_concessionaria_81.cidade = None
    concessionarias_concessionaria_81.cep = None
    concessionarias_concessionaria_81.uf = None
    concessionarias_concessionaria_81.ativa = True
    concessionarias_concessionaria_81.desc = None
    concessionarias_concessionaria_81.lote = None
    concessionarias_concessionaria_81.etapa = None
    concessionarias_concessionaria_81.contrato = None
    concessionarias_concessionaria_81.contrato_dt_ass = None
    concessionarias_concessionaria_81.inicio = None
    concessionarias_concessionaria_81.termino = None
    concessionarias_concessionaria_81.edital = None
    concessionarias_concessionaria_81.grupo = None
    concessionarias_concessionaria_81.link = None
    concessionarias_concessionaria_81.senha = None
    concessionarias_concessionaria_81.web = None
    concessionarias_concessionaria_81.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_81.atualizado_em = None
    concessionarias_concessionaria_81 = importer.save_or_locate(concessionarias_concessionaria_81)

    concessionarias_concessionaria_82 = Concessionaria()
    concessionarias_concessionaria_82.nome = 'Triângulo do Sol'
    concessionarias_concessionaria_82.slug = 'triangulo-do-sol'
    concessionarias_concessionaria_82.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_82.publica = False
    concessionarias_concessionaria_82.nome_fantasia = None
    concessionarias_concessionaria_82.razao_social = 'Triângulo do Sol Auto-Estradas S/A'
    concessionarias_concessionaria_82.logo = ''
    concessionarias_concessionaria_82.telefone_adm = None
    concessionarias_concessionaria_82.telefone_cco = None
    concessionarias_concessionaria_82.logradouro = 'Rua Marlene David dos Santos'
    concessionarias_concessionaria_82.numero = '325'
    concessionarias_concessionaria_82.bairro = None
    concessionarias_concessionaria_82.cidade = 'Matão'
    concessionarias_concessionaria_82.cep = '15991-360'
    concessionarias_concessionaria_82.uf = 'SP'
    concessionarias_concessionaria_82.ativa = True
    concessionarias_concessionaria_82.desc = None
    concessionarias_concessionaria_82.lote = 9
    concessionarias_concessionaria_82.etapa = None
    concessionarias_concessionaria_82.contrato = '006/CR/1998'
    concessionarias_concessionaria_82.contrato_dt_ass = None
    concessionarias_concessionaria_82.inicio = None
    concessionarias_concessionaria_82.termino = None
    concessionarias_concessionaria_82.edital = '14/CIC/97'
    concessionarias_concessionaria_82.grupo = 'AB Concessões'
    concessionarias_concessionaria_82.link = None
    concessionarias_concessionaria_82.senha = None
    concessionarias_concessionaria_82.web = None
    concessionarias_concessionaria_82.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_82.atualizado_em = None
    concessionarias_concessionaria_82 = importer.save_or_locate(concessionarias_concessionaria_82)

    concessionarias_concessionaria_83 = Concessionaria()
    concessionarias_concessionaria_83.nome = 'ViaPaulista'
    concessionarias_concessionaria_83.slug = 'viapaulista'
    concessionarias_concessionaria_83.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_83.publica = False
    concessionarias_concessionaria_83.nome_fantasia = None
    concessionarias_concessionaria_83.razao_social = 'ViaPaulista S/A'
    concessionarias_concessionaria_83.logo = ''
    concessionarias_concessionaria_83.telefone_adm = None
    concessionarias_concessionaria_83.telefone_cco = None
    concessionarias_concessionaria_83.logradouro = 'Rodovia Anhanguera'
    concessionarias_concessionaria_83.numero = 's/n'
    concessionarias_concessionaria_83.bairro = None
    concessionarias_concessionaria_83.cidade = 'Ribeirão Preto'
    concessionarias_concessionaria_83.cep = '00000-000'
    concessionarias_concessionaria_83.uf = 'SP'
    concessionarias_concessionaria_83.ativa = True
    concessionarias_concessionaria_83.desc = None
    concessionarias_concessionaria_83.lote = 29
    concessionarias_concessionaria_83.etapa = None
    concessionarias_concessionaria_83.contrato = '0359/ARTESP/2017'
    concessionarias_concessionaria_83.contrato_dt_ass = None
    concessionarias_concessionaria_83.inicio = None
    concessionarias_concessionaria_83.termino = None
    concessionarias_concessionaria_83.edital = '005/2016'
    concessionarias_concessionaria_83.grupo = 'Arteris'
    concessionarias_concessionaria_83.link = None
    concessionarias_concessionaria_83.senha = None
    concessionarias_concessionaria_83.web = None
    concessionarias_concessionaria_83.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_83.atualizado_em = None
    concessionarias_concessionaria_83 = importer.save_or_locate(concessionarias_concessionaria_83)

    concessionarias_concessionaria_84 = Concessionaria()
    concessionarias_concessionaria_84.nome = 'ViaRondon'
    concessionarias_concessionaria_84.slug = 'viarondon'
    concessionarias_concessionaria_84.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_84.publica = False
    concessionarias_concessionaria_84.nome_fantasia = None
    concessionarias_concessionaria_84.razao_social = 'ViaRondon Concessionária de Rodovia S/A'
    concessionarias_concessionaria_84.logo = ''
    concessionarias_concessionaria_84.telefone_adm = None
    concessionarias_concessionaria_84.telefone_cco = None
    concessionarias_concessionaria_84.logradouro = 'Rua João Moreira da Silva'
    concessionarias_concessionaria_84.numero = '509'
    concessionarias_concessionaria_84.bairro = None
    concessionarias_concessionaria_84.cidade = 'Lins'
    concessionarias_concessionaria_84.cep = 'CEP: 16400-660'
    concessionarias_concessionaria_84.uf = 'SP'
    concessionarias_concessionaria_84.ativa = True
    concessionarias_concessionaria_84.desc = None
    concessionarias_concessionaria_84.lote = 19
    concessionarias_concessionaria_84.etapa = None
    concessionarias_concessionaria_84.contrato = '005/ARTESP/2009'
    concessionarias_concessionaria_84.contrato_dt_ass = None
    concessionarias_concessionaria_84.inicio = None
    concessionarias_concessionaria_84.termino = None
    concessionarias_concessionaria_84.edital = '006/2008'
    concessionarias_concessionaria_84.grupo = 'Br vias'
    concessionarias_concessionaria_84.link = None
    concessionarias_concessionaria_84.senha = None
    concessionarias_concessionaria_84.web = None
    concessionarias_concessionaria_84.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_84.atualizado_em = None
    concessionarias_concessionaria_84 = importer.save_or_locate(concessionarias_concessionaria_84)

    concessionarias_concessionaria_85 = Concessionaria()
    concessionarias_concessionaria_85.nome = 'Viaoeste'
    concessionarias_concessionaria_85.slug = 'viaoeste'
    concessionarias_concessionaria_85.tipo = 'CONCESSIONÁRIA'
    concessionarias_concessionaria_85.publica = False
    concessionarias_concessionaria_85.nome_fantasia = None
    concessionarias_concessionaria_85.razao_social = 'Concessionária de Rodovias do Oeste de São Paulo - VIAOESTE S/A'
    concessionarias_concessionaria_85.logo = ''
    concessionarias_concessionaria_85.telefone_adm = None
    concessionarias_concessionaria_85.telefone_cco = None
    concessionarias_concessionaria_85.logradouro = 'Rua São João'
    concessionarias_concessionaria_85.numero = '30'
    concessionarias_concessionaria_85.bairro = None
    concessionarias_concessionaria_85.cidade = 'Barueri'
    concessionarias_concessionaria_85.cep = '18147-000'
    concessionarias_concessionaria_85.uf = 'SP'
    concessionarias_concessionaria_85.ativa = True
    concessionarias_concessionaria_85.desc = None
    concessionarias_concessionaria_85.lote = 12
    concessionarias_concessionaria_85.etapa = None
    concessionarias_concessionaria_85.contrato = 'CR/003/1998'
    concessionarias_concessionaria_85.contrato_dt_ass = None
    concessionarias_concessionaria_85.inicio = None
    concessionarias_concessionaria_85.termino = None
    concessionarias_concessionaria_85.edital = '008/CIC/97'
    concessionarias_concessionaria_85.grupo = 'CCR'
    concessionarias_concessionaria_85.link = None
    concessionarias_concessionaria_85.senha = None
    concessionarias_concessionaria_85.web = None
    concessionarias_concessionaria_85.criado_em = dateutil.parser.parse("2021-04-19T13:30:53.154955+00:00")
    concessionarias_concessionaria_85.atualizado_em = None
    concessionarias_concessionaria_85 = importer.save_or_locate(concessionarias_concessionaria_85)

