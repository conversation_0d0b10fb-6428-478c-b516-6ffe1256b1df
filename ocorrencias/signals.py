from django.db.models.signals import pre_save, post_save
from django.utils import timezone
from django.dispatch import receiver
from .models import Foto, Ocorrencia
from django.utils.text import slugify


@receiver(pre_save)
def check_km_final(sender, instance, *args, **kwargs):
    if not issubclass(sender, Ocorrencia):
        return
    if instance.km_final is None:
        instance.km_final = instance.km_inicial


@receiver(post_save)
def gen_slug_ocorrencia(sender, instance, created, **kwargs):
    if not issubclass(sender, Ocorrencia):
        return
    if created:
        #classe = instance.get_sigla().lower()
        # slug=instance.criado_em.strftime("%Y-%m-%d")
        instance.slug = f'oc{instance.pk}'
        instance.save()


@receiver(post_save)
def delete_image_if_is_does_not_have_a_str64(sender, instance, created, **kwargs):
    if not issubclass(sender, Foto):
        return
    if created:
        if not instance.foto_blob or not instance.foto_hr_blob:
            instance.delete()


@receiver(post_save)
def populate_concs_and_municipios(sender, instance, created, **kwargs):
    if not issubclass(sender, Ocorrencia):
        return
    instance.concessionarias.clear()
    instance.municipios.clear()
    concessionarias = instance.get_concessionarias()
    municipios = instance.get_municipios()
    instance.concessionarias.add(*concessionarias)
    instance.municipios.add(*municipios)


@receiver(pre_save)
def set_criticidade(sender, instance, *args, **kwargs):
    if not issubclass(sender, Ocorrencia):
        return
    if instance.pk:
        criticidade, motivos, qtd = instance.get_criticidade_com_motivos_no_args()
        instance.criticidade = criticidade
        instance.criticidade_motivos = motivos
        instance.criticidade_motivos_qtd = qtd


@receiver(pre_save)
def set_bloqueio(sender, instance, *args, **kwargs):
    if not issubclass(sender, Ocorrencia):
        return
    if instance.pk:
        instance.bloqueio = instance.get_bloqueio(instance.interdicao.all())
