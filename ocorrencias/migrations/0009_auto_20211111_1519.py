# Generated by Django 3.2 on 2021-11-11 15:19

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0008_auto_20211110_1937'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='atualizacao_min',
            field=models.IntegerField(default=60, validators=[django.core.validators.MaxValueValidator(240)], verbose_name='Atualização - Periodicidade (minutos)'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='recursos_externos',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Recursos externos'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='recursos_internos',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Recursos internos'),
        ),
    ]
