# Generated by Django 4.2.5 on 2023-12-04 14:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0078_foto_foto_blob_foto_foto_hr_blob_alter_foto_foto_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='ocorrencia',
            name='faixa_inicio',
            field=models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('Acostamento', 'Acostamento'), ('Faixa adicional', 'Faixa adicional'), ('Faixa zebrada', 'Faixa zebrada'), ('Faixa de aceleração', 'Faixa de aceleração'), ('Faixa de desaceleração', 'Faixa de desaceleração')], max_length=255, null=True, verbose_name='Faixa início oc.'),
        ),
        migrations.AddField(
            model_name='ocorrenciaveiculo',
            name='nome',
            field=models.CharField(blank=True, help_text='Nome da viação, transportadora, etc.', max_length=100, null=True, verbose_name='Empresa'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='tipo',
            field=models.CharField(choices=[('EM AVERIGUAÇÃO', 'EM AVERIGUAÇÃO'), ('AERONAVE', 'AERONAVE'), ('AMBULÂNCIA', 'AMBULÂNCIA'), ('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('CARROÇA/CHARRETE', 'CARROÇA/CHARRETE'), ('CARRO-FORTE', 'CARRO-FORTE'), ('CVC', 'CVC'), ('HELICÓPTERO', 'HELICÓPTERO'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('PEDESTRE', 'PEDESTRE'), ('PESSOA SEM VEÍCULO', 'PESSOA SEM VEÍCULO'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA', 'VAN/PERUA'), ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo'),
        ),
    ]
