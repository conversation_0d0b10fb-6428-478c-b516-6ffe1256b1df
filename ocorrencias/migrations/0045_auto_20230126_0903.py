# Generated by Django 3.2 on 2023-01-26 09:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0044_auto_20230123_1651'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='ocorrencia',
            name='cond_climaticas_atuais',
        ),
        migrations.AddField(
            model_name='ocorrenciaatualizacao',
            name='cond_climaticas_atuais',
            field=models.CharField(choices=[('Chuva', 'Chuva'), ('Garoa', 'Garoa'), ('Neblina', 'Neblina'), ('Nublado', 'Nublado'), ('Trechos com chuva', 'Trechos com chuva'), ('Trechos com garoa', 'Trechos com garoa'), ('Trechos com neblina', 'Trechos com neblina'), ('Trechos com garoa e neblina', 'Trechos com garoa e neblina'), ('Trechos com chuva e neblina', 'Trechos com chuva e neblina'), ('Trechos com chuva e nublado', 'Trechos com chuva e nublado'), ('Chuva com ventania', 'Chuva com ventania'), ('Chuva torrencial', 'Chuva torrencial'), ('Sol', 'Sol'), ('Tempo bom', 'Tempo bom'), ('Vento forte', 'Vento forte'), ('Desconhecidas', 'Desconhecidas')], default='Desconhecidas', max_length=50, verbose_name='Cond. climáticas atuais'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='cond_climaticas',
            field=models.CharField(choices=[('Chuva', 'Chuva'), ('Garoa', 'Garoa'), ('Neblina', 'Neblina'), ('Nublado', 'Nublado'), ('Trechos com chuva', 'Trechos com chuva'), ('Trechos com garoa', 'Trechos com garoa'), ('Trechos com neblina', 'Trechos com neblina'), ('Trechos com garoa e neblina', 'Trechos com garoa e neblina'), ('Trechos com chuva e neblina', 'Trechos com chuva e neblina'), ('Trechos com chuva e nublado', 'Trechos com chuva e nublado'), ('Chuva com ventania', 'Chuva com ventania'), ('Chuva torrencial', 'Chuva torrencial'), ('Sol', 'Sol'), ('Tempo bom', 'Tempo bom'), ('Vento forte', 'Vento forte'), ('Desconhecidas', 'Desconhecidas')], max_length=50, verbose_name='Cond. climáticas no momento da ocorrência'),
        ),
    ]
