# Generated by Django 3.2 on 2021-11-19 21:10

from django.db import migrations, models
import ocorrencias.models
import ocorrencias.validators


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0013_auto_20211118_1941'),
    ]

    operations = [
        migrations.AddField(
            model_name='ocorrencia',
            name='arquivo',
            field=models.FileField(blank=True, null=True, upload_to=ocorrencias.models.get_file_path, validators=[ocorrencias.validators.validate_file_extension, ocorrencias.validators.validate_file_size], verbose_name='Arquivo complementar PDF (máx 1 arq. < 10mb)'),
        ),
        migrations.AddField(
            model_name='ocorrencia',
            name='arquivo_desc',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Descrição do arquivo'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento', 'Atropelamento'), ('Capotamento', 'Capotamento'), ('Choque', 'Choque'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('Colisão transversal', 'Colisão transversal'), ('Colisão traseira', 'Colisão traseira'), ('Engavetamento', 'Engavetamento'), ('Explosão', 'Explosão'), ('Incêndio', 'Incêndio'), ('Objeto contra veículo', 'Objeto contra veículo'), ('Queda', 'Queda'), ('Saída de Pista', 'Saída de Pista'), ('Sequência', 'Sequência'), ('Submersão', 'Submersão'), ('Suicídio', 'Suicídio'), ('Tombamento', 'Tombamento')], max_length=255, null=True, verbose_name='Classe do acidente'),
        ),
    ]
