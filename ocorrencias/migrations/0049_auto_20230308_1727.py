# Generated by Django 3.2 on 2023-03-08 17:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0048_alter_ocorrencia_subclasse_oc'),
    ]

    operations = [
        migrations.AddField(
            model_name='ocorrencia',
            name='bloqueio',
            field=models.CharField(blank=True, default='Não', max_length=255, null=True, verbose_name='Bloqueio'),
        ),
        migrations.AddField(
            model_name='ocorrencia',
            name='criticidade',
            field=models.CharField(blank=True, default='Leve', max_length=255, null=True, verbose_name='Criticidade'),
        ),
        migrations.AddField(
            model_name='ocorrencia',
            name='criticidade_motivos',
            field=models.CharField(blank=True, default='Não atende aos requisitos das criticidades moderada ou crítica', max_length=255, null=True, verbose_name='Criticidade (motivos)'),
        ),
        migrations.AddField(
            model_name='ocorrencia',
            name='criticidade_motivos_qtd',
            field=models.PositiveSmallIntegerField(default=0, null=True, verbose_name='Criticidade (qtd. motivos)'),
        ),
        migrations.AlterField(
            model_name='interdicao',
            name='faixa',
            field=models.CharField(choices=[('Acostamento', 'Acostamento'), ('Alça parcial', 'Alça parcial'), ('Alça total', 'Alça total'), ('Faixa 1', 'Faixa 1'), ('Faixa 2', 'Faixa 2'), ('Faixa 3', 'Faixa 3'), ('Faixa 4', 'Faixa 4'), ('Faixa 5', 'Faixa 5'), ('Faixa 6', 'Faixa 6'), ('Faixa zebrada', 'Faixa zebrada'), ('Faixa de aceleração', 'Faixa de aceleração'), ('Faixa de desaceleração', 'Faixa de desaceleração'), ('Praça de pedágio', 'Praça de pedágio'), ('Todas as faixas', 'Todas as faixas'), ('Todas as faixas (trecho sem AC)', 'Todas as faixas (trecho sem AC)'), ('Todas as faixas e acostamento', 'Todas as faixas e acostamento')], max_length=40, verbose_name='Faixa'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='derramamento',
            field=models.BooleanField(default=False, verbose_name='Derramamento/vazamento de carga'),
        ),
    ]
