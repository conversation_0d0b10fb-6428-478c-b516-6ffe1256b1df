# Generated by Django 3.2 on 2021-12-17 19:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0029_alter_ocorrencia_tipo_ac'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_oc',
            field=models.CharField(choices=[('Não se aplica', 'Não se aplica'), ('Em averiguação', 'Em averiguação'), ('Aferição de Radar', 'Aferição de Radar'), ('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto à Praça de Pedágio', 'Assalto à Praça de Pedágio'), ('Assalto', 'Assalto'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Barreira Sanitária', 'Barreira Sanitária'), ('Carga Especial', 'Carga Especial'), ('Congestionamento', 'Congestionamento'), ('Conservação', 'Conservação'), ('Construção/Recuperação', 'Construção/Recuperação'), ('Derramamento de Carga', 'Derramamento de Carga'), ('Erosão', 'Erosão'), ('Evento', 'Evento'), ('Excesso de Veículos', 'Excesso de Veículos'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'), ('Incidente', 'Incidente'), ('Indefinida', 'Indefinida'), ('Mal Súbito', 'Mal Súbito'), ('Manifestação', 'Manifestação'), ('Manutenção', 'Manutenção'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Objeto contra veículo', 'Objeto contra veículo'), ('Objeto na Pista', 'Objeto na Pista'), ('Obra', 'Obra'), ('Obras na Rodovia', 'Obras na Rodovia'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação de trânsito', 'Operação de trânsito'), ('Operação especial', 'Operação especial'), ('Pane', 'Pane'), ('Pane Elétrica', 'Pane Elétrica'), ('Pane Mecânica', 'Pane Mecânica'), ('Pane Seca', 'Pane Seca'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Pneu Furado', 'Pneu Furado'), ('Ponto de Interferência', 'Ponto de Interferência'), ('Queda de Barreira', 'Queda de Barreira'), ('Reintegração de Posse', 'Reintegração de Posse'), ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'), ('Tapa Buraco', 'Tapa Buraco'), ('Tentativa de Suicídio', 'Tentativa de Suicídio'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento')], default='Não se aplica', max_length=255, verbose_name='Subclasse de OC'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='tipo_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento de ambulante', 'Atropelamento de ambulante'), ('Atropelamento de andarilho', 'Atropelamento de andarilho'), ('Atropelamento de animal', 'Atropelamento de animal'), ('Atropelamento de ciclista/esportista', 'Atropelamento de ciclista/esportista'), ('Atropelamento de funcionário', 'Atropelamento de funcionário'), ('Atropelamento de morador/trabalhador/estudante', 'Atropelamento de morador/trabalhador/estudante'), ('Atropelamento de pedestre', 'Atropelamento de pedestre'), ('Atropelamento de usuário', 'Atropelamento de usuário'), ('Atropelamento de romeiro', 'Atropelamento de romeiro'), ('Atropelamento sem informação', 'Atropelamento sem informação'), ('Atropelamento de suicida', 'Atropelamento de suicida'), ('Capotamento de veículo', 'Capotamento de veículo'), ('Carreta Em L', 'Carreta Em L'), ('Choque com árvore', 'Choque com árvore'), ('Choque com barreira', 'Choque com barreira'), ('Choque com cabine de pedágio', 'Choque com cabine de pedágio'), ('Choque com cancela de pista de pedágio', 'Choque com cancela de pista de pedágio'), ('Choque com caixa de captação/fibra', 'Choque com caixa de captação/fibra'), ('Choque com canaleta', 'Choque com canaleta'), ('Choque com cerca', 'Choque com cerca'), ('Choque com defensa metálica', 'Choque com defensa metálica'), ('Choque com barreira de concreto,', 'Choque com barreira de concreto,'), ('Choque com submarino', 'Choque com submarino'), ('Choque com meio-fio', 'Choque com meio-fio'), ('Choque com elemento de drenagem', 'Choque com elemento de drenagem'), ('Choque com equipamento', 'Choque com equipamento'), ('Choque com OAE (Ponte/Viaduto/Pass)', 'Choque com OAE (Ponte/Viaduto/Pass)'), ('Choque com objeto fixo', 'Choque com objeto fixo'), ('Choque com objeto na pista', 'Choque com objeto na pista'), ('Choque com outros', 'Choque com outros'), ('Choque com talude ou barranco', 'Choque com talude ou barranco'), ('Choque com veículo na pista', 'Choque com veículo na pista'), ('Choque com veículo estacionado', 'Choque com veículo estacionado'), ('Choque contra atenuador de impacto', 'Choque contra atenuador de impacto'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('Colisão transversal', 'Colisão transversal'), ('Colisão traseira', 'Colisão traseira'), ('Colisões múltiplas', 'Colisões múltiplas'), ('Indefinido', 'Indefinido'), ('Objeto lançado contra veículo', 'Objeto lançado contra veículo'), ('Queda de carga', 'Queda de carga'), ('Queda de OAE', 'Queda de OAE'), ('Queda de veículo em ribanceira', 'Queda De veículo em ribanceira'), ('Sem informação', 'Sem informação'), ('Suicídio/atropelamento', 'Suicídio/atropelamento'), ('Suicídio/pulo de OAE', 'Suicídio/pulo de OAE'), ('Tombamento de bicicleta', 'Tombamento de bicicleta'), ('Tombamento de moto', 'Tombamento de moto'), ('Tombamento de veículo pesado', 'Tombamento de veículo pesado')], max_length=255, null=True, verbose_name='Subclasse do acidente'),
        ),
    ]
