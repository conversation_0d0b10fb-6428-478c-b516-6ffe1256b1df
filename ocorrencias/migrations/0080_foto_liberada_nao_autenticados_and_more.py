# Generated by Django 4.2.5 on 2024-01-23 18:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0079_ocorrencia_faixa_inicio_ocorrenciaveiculo_nome_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='foto',
            name='liberada_nao_autenticados',
            field=models.BooleanField(default=False, help_text='Ao ativar esta opção, a foto estará acessível para qualquer usuário, autenticado ou não.Pode ser utilizada em alguns casos, como eventos naturais e manifestações (borrar pessoas/veículos)', verbose_name='Liberar fotos para não autenticados'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='faixa_inicio',
            field=models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('Acostamento', 'Acostamento'), ('Faixa adicional', 'Faixa adicional'), ('Faixa zebrada', 'Faixa zebrada'), ('Faixa de aceleração', 'Faixa de aceleração'), ('Faixa de desaceleração', 'Faixa de desaceleração'), ('Fora da pista', 'Fora da pista'), ('Canteiro lateral', 'Canteiro lateral'), ('Canteiro central', 'Canteiro central')], max_length=255, null=True, verbose_name='Faixa início oc.'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='nome',
            field=models.CharField(blank=True, help_text='Nome da viação, transportadora, etc.', max_length=100, null=True, verbose_name='Empresa/Nome'),
        ),
    ]
