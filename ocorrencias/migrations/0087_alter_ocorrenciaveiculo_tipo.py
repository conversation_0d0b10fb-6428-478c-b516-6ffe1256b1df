# Generated by Django 4.2.5 on 2024-08-29 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0086_alter_ocorrencia_faixa_inicio_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='tipo',
            field=models.CharField(choices=[('EM AVERIGUAÇÃO', 'EM AVERIGUAÇÃO'), ('AERONAVE', 'AERONAVE'), ('AMBULÂNCIA', 'AMBULÂNCIA'), ('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('CARROÇA/CHARRETE', 'CARROÇA/CHARRETE'), ('CARRO-FORTE', 'CARRO-FORTE'), ('CVC', 'CVC'), ('EQUIPAMENTO - RURAL', 'EQUIPAMENTO - RURAL'), ('EQUIPAMENTO - OBRA', 'EQUIPAMENTO - OBRA'), ('HELICÓPTERO', 'HELICÓPTERO'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('PEDESTRE', 'PEDESTRE'), ('PESSOA SEM VEÍCULO', 'PESSOA SEM VEÍCULO'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA TRANSP. COLETIVO', 'VAN/PERUA TRANSP. COLETIVO'), ('VAN/PERUA DE MERCADORIAS', 'VAN/PERUA DE MERCADORIAS'), ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo'),
        ),
    ]
