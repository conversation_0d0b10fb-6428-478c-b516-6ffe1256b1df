# Generated by Django 3.2 on 2021-12-03 17:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0023_auto_20211203_1640'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='interdicao',
            options={'ordering': ['-dt_hora_inicio'], 'verbose_name': 'Interdição', 'verbose_name_plural': 'Interdições'},
        ),
        migrations.RemoveField(
            model_name='interdicao',
            name='faixas',
        ),
        migrations.RemoveField(
            model_name='interdicao',
            name='km_final',
        ),
        migrations.RemoveField(
            model_name='interdicao',
            name='km_inicial',
        ),
        migrations.RemoveField(
            model_name='interdicao',
            name='pista',
        ),
        migrations.RemoveField(
            model_name='interdicao',
            name='tipo',
        ),
        migrations.AddField(
            model_name='interdicao',
            name='faixa',
            field=models.Char<PERSON>ield(choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('AC', 'AC')], default=1, max_length=20, verbose_name='Faixa'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='interdicao',
            name='sentido',
            field=models.CharField(choices=[('Norte', 'Norte'), ('Sul', 'Sul'), ('Leste', 'Leste'), ('Oeste', 'Oeste'), ('Interno', 'Interno'), ('Externo', 'Externo'), ('Norte-Sul', 'Norte-Sul'), ('Leste-Oeste', 'Leste-Oeste'), ('Noroeste-Sudeste', 'Noroeste-Sudeste'), ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'), ('Sudeste', 'Sudeste'), ('Sudoeste', 'Sudoeste'), ('Nordeste', 'Nordeste'), ('Noroeste', 'Noroeste'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], max_length=20, verbose_name='Sentido'),
        ),
    ]
