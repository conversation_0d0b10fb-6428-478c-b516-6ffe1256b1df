# Generated by Django 3.2 on 2021-11-08 07:37

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0004_auto_20210902_2241'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='classe',
            field=models.CharField(choices=[('Acidente', 'Acidente'), ('Barreira Sanitária', 'Barreira Sanitária'), ('Congestionamento', 'Congestionamento'), ('<PERSON><PERSON><PERSON>', 'Erosão'), ('Evento', 'Evento'), ('Incêndio', 'Incêndio'), ('Manifestação', 'Manifestação'), ('Obra', 'Obra'), ('Ponto de interferência', 'Ponto de interferência'), ('Queda de Barreira', 'Queda de Barreira'), ('Ocorrência', 'Ocorrência')], max_length=255, verbose_name='Classe OC'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='ocorrencia',
            name='cond_climaticas',
            field=models.CharField(choices=[('<PERSON><PERSON>', '<PERSON>va'), ('<PERSON>va com ventania', 'Chuva com ventania'), ('Chuva torrencial', 'Chuva torrencial'), ('Encoberto', 'Encoberto'), ('Garoa', 'Garoa'), ('Nublado', 'Nublado'), ('Sol', 'Sol'), ('Tempo bom', 'Tempo bom'), ('Vento forte', 'Vento forte'), ('Desconhecido', 'Desconhecido')], default='DESCONHECIDO', max_length=50, verbose_name='Cond. Climáticas'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='dt_hr_conhecimento',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='Data conhecimento'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='tipo_ac',
            field=models.CharField(blank=True, choices=[('Acidente com Danos Materiais', 'Acidente com Danos Materiais'), ('Acidente com Vítima', 'Acidente com Vítima'), ('Acidente sem Vítima', 'Acidente sem Vítima'), ('Acidente com Vítima Fatal', 'Acidente com Vítima Fatal'), ('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto a Praça de Pedágio', 'Assalto a Praça de Pedágio'), ('Assalto ao Usuário da Rodovia', 'Assalto ao Usuário da Rodovia'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Carga Especial', 'Carga Especial'), ('Condições Climáticas', 'Condições Climáticas'), ('Conservação', 'Conservação'), ('Derramamento de Carga', 'Derramamento de Carga'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio', 'Incêndio'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Incidente', 'Incidente'), ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'), ('Indefinida', 'Indefinida'), ('Mal Súbito', 'Mal Súbito'), ('Manifestação', 'Manifestação'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Não Informada', 'Não Informada'), ('Objeto na Pista', 'Objeto na Pista'), ('Obras na Rodovia', 'Obras na Rodovia'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação caminho seguro', 'Operação caminho seguro'), ('Pane', 'Pane'), ('Pane Elétrica', 'Pane Elétrica'), ('Pane Mecânica', 'Pane Mecânica'), ('Pane Seca', 'Pane Seca'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Pneu Furado', 'Pneu Furado'), ('Queda de Barreira', 'Queda de Barreira'), ('Suicídio', 'Suicídio'), ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'), ('Tapa Buraco', 'Tapa Buraco'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento')], max_length=255, null=True, verbose_name='Tipo de acidente'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='tipo',
            field=models.CharField(choices=[('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('CVC', 'CVC'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA', 'VAN/PERUA'), ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo'),
        ),
    ]
