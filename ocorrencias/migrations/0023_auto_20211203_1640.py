# Generated by Django 3.2 on 2021-12-03 16:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0022_auto_20211129_1828'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='bloq_faixas',
            field=models.BooleanField(default=False, verbose_name='Houve bloqueio/interdição de faixas'),
        ),
        migrations.CreateModel(
            name='Interdicao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(choices=[('PARCIAL', 'PARCIAL'), ('TOTAL', 'TOTAL')], default='PARCIAL', max_length=50, verbose_name='Tipo')),
                ('pista', models.CharField(choices=[('Simples', 'Simples'), ('<PERSON>p<PERSON>', 'Dupla'), ('Expressa', 'Expressa'), ('<PERSON>na', 'Interna'), ('Externa', 'Externa'), ('Marginal', 'Marginal'), ('Dupla com canteiro central', 'Dupla com canteiro central'), ('Dupla com barreira central', 'Dupla com barreira central'), ('Dupla com faixa central', 'Dupla com faixa central'), ('Simples de mão dupla', 'Simples de mão dupla'), ('Simples com mão única', 'Simples com mão única'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=50, verbose_name='Pista')),
                ('sentido', models.CharField(choices=[('Norte', 'Norte'), ('Sul', 'Sul'), ('Leste', 'Leste'), ('Oeste', 'Oeste'), ('Interno', 'Interno'), ('Externo', 'Externo'), ('Norte-Sul', 'Norte-Sul'), ('Leste-Oeste', 'Leste-Oeste'), ('Noroeste-Sudeste', 'Noroeste-Sudeste'), ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'), ('Sudeste', 'Sudeste'), ('Sudoeste', 'Sudoeste'), ('Nordeste', 'Nordeste'), ('Noroeste', 'Noroeste'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=20, verbose_name='Sentido')),
                ('faixas', models.CharField(blank=True, max_length=255, null=True, verbose_name='Faixa(s)')),
                ('dt_hora_inicio', models.DateTimeField(blank=True, null=True, verbose_name='Data Hora Início')),
                ('dt_hora_termino', models.DateTimeField(blank=True, null=True, verbose_name='Data Hora Término')),
                ('km_inicial', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='KM inicial')),
                ('km_final', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='KM final')),
                ('obs', models.CharField(blank=True, max_length=255, null=True, verbose_name='Obs.')),
                ('ocorrencia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interdicao', to='ocorrencias.ocorrencia', verbose_name='Interdição')),
            ],
            options={
                'verbose_name': 'Interdição',
                'verbose_name_plural': 'Interdições',
                'ordering': ['km_inicial'],
            },
        ),
    ]
