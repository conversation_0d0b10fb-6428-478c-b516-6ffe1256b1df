# Generated by Django 3.2 on 2023-04-17 15:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ocorrencias', '0060_alter_ocorrenciaveiculo_tipo'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='ocorrenciaatualizacao',
            options={'ordering': ('atualizacao_criado_em', 'oc_atualizada_em'), 'verbose_name': 'ATUALIZAÇÃO', 'verbose_name_plural': 'ATUALIZAÇÕES'},
        ),
        migrations.AddField(
            model_name='ocorrenciaatualizacao',
            name='atualizacao_apagada_em',
            field=models.DateTimeField(null=True, verbose_name='Apagada em'),
        ),
        migrations.AddField(
            model_name='ocorrenciaatualizacao',
            name='atualizacao_apagada_por',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='oc_apagador', to=settings.AUTH_USER_MODEL, verbose_name='Apagada por'),
        ),
        migrations.AddField(
            model_name='ocorrenciaatualizacao',
            name='atualizacao_criado_em',
            field=models.DateTimeField(auto_now_add=True, null=True, verbose_name='Criado em'),
        ),
        migrations.AddField(
            model_name='ocorrenciaatualizacao',
            name='atualizacao_criado_por',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='atualizacao_criador', to=settings.AUTH_USER_MODEL, verbose_name='Criado por'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaatualizacao',
            name='oc_atualizada_em',
            field=models.DateTimeField(null=True, verbose_name='atualizado em'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='tipo',
            field=models.CharField(choices=[('AERONAVE', 'AERONAVE'), ('AMBULÂNCIA', 'AMBULÂNCIA'), ('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('CARROÇA/CHARRETE', 'CARROÇA/CHARRETE'), ('CVC', 'CVC'), ('HELICÓPTERO', 'HELICÓPTERO'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('PEDESTRE', 'PEDESTRE'), ('PESSOA SEM VEÍCULO', 'PESSOA SEM VEÍCULO'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA', 'VAN/PERUA'), ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo'),
        ),
    ]
