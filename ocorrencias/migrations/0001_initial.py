# Generated by Django 3.2 on 2021-08-29 13:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('concessionarias', '0001_initial'),
        ('municipios', '0001_initial'),
        ('rodovias', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Ocorrencia',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('slug', models.SlugField(blank=True, null=True, verbose_name='Código')),
                ('cod_conc', models.CharField(blank=True, max_length=50, null=True, verbose_name='Código Concessionária')),
                ('classe', models.CharField(choices=[('Acidente', 'Acidente'), ('Congestionamento', 'Congestionamento'), ('<PERSON><PERSON>', '<PERSON>bra'), ('Evento', 'Evento'), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), ('Incêndio', 'Incêndio'), ('Barreira Sanitária', 'Barreira Sanitária'), ('Ponto de interferência', 'Ponto de interferência'), ('Queda de Barreira', 'Queda de Barreira'), ('Ocorrência', 'Ocorrência')], max_length=255, verbose_name='Classe OC')),
                ('km_inicial', models.DecimalField(decimal_places=3, max_digits=8, verbose_name='KM inicial')),
                ('km_final', models.DecimalField(blank=True, decimal_places=3, max_digits=8, verbose_name='KM final')),
                ('cond_climaticas', models.CharField(choices=[('Chuva', 'Chuva'), ('Chuva com ventania', 'Chuva com ventania'), ('Chuva torrencial', 'Chuva torrencial'), ('Encoberto', 'Encoberto'), ('Garoa', 'Garoa'), ('Nublado', 'Nublado'), ('Sol', 'Sol'), ('Vento forte', 'Vento forte'), ('Desconhecido', 'Desconhecido')], default='DESCONHECIDO', max_length=50, verbose_name='Cond. Climáticas')),
                ('dt_hr_inicio', models.DateTimeField(blank=True, null=True, verbose_name='Data início')),
                ('dt_hr_termino', models.DateTimeField(blank=True, null=True, verbose_name='Data término')),
                ('publicada', models.BooleanField(default=True, verbose_name='Publicar')),
                ('aprovada', models.BooleanField(default=False, verbose_name='Aprovada')),
                ('finalizada', models.BooleanField(default=False, verbose_name='Finalizada')),
                ('origem_comunicacao_conc', models.CharField(blank=True, max_length=255, null=True, verbose_name='Origem da comunicação')),
                ('origem_comunicacao_cci', models.CharField(blank=True, choices=[('Concessionária', 'Concessionária'), ('Imprensa', 'Imprensa'), ('ARTESP', 'ARTESP'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='CONCESSIONÁRIA', max_length=255, null=True, verbose_name='Origem da inf.')),
                ('pista', models.CharField(blank=True, choices=[('Simples', 'Simples'), ('Dupla', 'Dupla'), ('Expressa', 'Expressa'), ('Interna', 'Interna'), ('Externa', 'Externa'), ('Marginal', 'Marginal'), ('Dupla com canteiro central', 'Dupla com canteiro central'), ('Dupla com barreira central', 'Dupla com barreira central'), ('Dupla com faixa central', 'Dupla com faixa central'), ('Simples de mão dupla', 'Simples de mão dupla'), ('Simples com mão única', 'Simples com mão única'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=50, null=True, verbose_name='Pista')),
                ('sentido', models.CharField(choices=[('Norte', 'Norte'), ('Sul', 'Sul'), ('Leste', 'Leste'), ('Oeste', 'Oeste'), ('Interno', 'Interno'), ('Externo', 'Externo'), ('Norte-Sul', 'Norte-Sul'), ('Leste-Oeste', 'Leste-Oeste'), ('Noroeste-Sudeste', 'Noroeste-Sudeste'), ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'), ('Sudeste', 'Sudeste'), ('Sudoeste', 'Sudoeste'), ('Nordeste', 'Nordeste'), ('Noroeste', 'Noroeste'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=20, verbose_name='Sentido')),
                ('lat', models.DecimalField(blank=True, decimal_places=7, max_digits=12, null=True, verbose_name='Latitude')),
                ('lng', models.DecimalField(blank=True, decimal_places=7, max_digits=12, null=True, verbose_name='Longitude')),
                ('bloq_faixas', models.BooleanField(default=False, verbose_name='Bloqueio ou interdição de faixas')),
                ('det_bloq_faixas', models.CharField(blank=True, max_length=50, null=True, verbose_name='Detalhes Bloq./.Interd. Faixas')),
                ('sinalizacao', models.CharField(blank=True, max_length=50, null=True, verbose_name='Sinalização')),
                ('comunicacao_usuario', models.CharField(blank=True, max_length=50, null=True, verbose_name='Comunicação com usuário')),
                ('danos_patrimonio', models.BooleanField(default=False, verbose_name='Danos ao patrimônio')),
                ('danos_patrimonio_obs', models.CharField(blank=True, max_length=255, null=True, verbose_name='Danos ao patrimônio obs.')),
                ('recursos_internos', models.CharField(blank=True, max_length=50, null=True, verbose_name='Recursos internos')),
                ('recursos_externos', models.CharField(blank=True, max_length=50, null=True, verbose_name='Recursos externos')),
                ('acoes_operacionais', models.CharField(blank=True, max_length=255, null=True, verbose_name='Ações operacionais')),
                ('observacoes_cci', models.TextField(blank=True, null=True, verbose_name='Observações CCI')),
                ('atualizacao_min', models.IntegerField(default=60, verbose_name='Atualização - Periodicidade (minutos)')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
                ('atualizado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='atualizador', to=settings.AUTH_USER_MODEL, verbose_name='Atualizado por')),
                ('concessionarias', models.ManyToManyField(related_name='concessionarias', to='concessionarias.Concessionaria', verbose_name='Concessionarias')),
                ('criado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='criador', to=settings.AUTH_USER_MODEL, verbose_name='Criado por')),
                ('municipios', models.ManyToManyField(related_name='municipios', to='municipios.Municipio', verbose_name='Municipios')),
                ('rodovia', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rodovia', to='rodovias.rodovia', verbose_name='Rodovia')),
            ],
            options={
                'verbose_name': 'Ocorrência',
                'verbose_name_plural': 'Ocorrências',
            },
        ),
        migrations.CreateModel(
            name='Acidente',
            fields=[
                ('ocorrencia_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='ocorrencias.ocorrencia')),
                ('subclasse', models.CharField(choices=[('Atropelamento de animal', 'Atropelamento de animal'), ('Atropelamento de pedestre', 'Atropelamento de pedestre'), ('Capotamento', 'Capotamento'), ('Choque', 'Choque'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('Colisão transversal', 'Colisão transversal'), ('Colisão traseira', 'Colisão traseira'), ('Engavetamento', 'Engavetamento'), ('Explosão', 'Explosão'), ('Incêndio', 'Incêndio'), ('Objeto contra veículo', 'Objeto contra veículo'), ('Queda', 'Queda'), ('Sequência', 'Sequência'), ('Submersão', 'Submersão'), ('Tombamento', 'Tombamento'), ('Acidente', 'Acidente')], max_length=255, verbose_name='Subclasse de acidente')),
                ('tipo', models.CharField(blank=True, choices=[('Acidente com Danos Materiais', 'Acidente com Danos Materiais'), ('Acidente com Vítima', 'Acidente com Vítima'), ('Acidente com Vítima Fatal', 'Acidente com Vítima Fatal'), ('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto a Praça de Pedágio', 'Assalto a Praça de Pedágio'), ('Assalto ao Usuário da Rodovia', 'Assalto ao Usuário da Rodovia'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Carga Especial', 'Carga Especial'), ('Condições Climáticas', 'Condições Climáticas'), ('Conservação', 'Conservação'), ('Derramamento de Carga', 'Derramamento de Carga'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio', 'Incêndio'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Incidente', 'Incidente'), ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'), ('Indefinida', 'Indefinida'), ('Mal Súbito', 'Mal Súbito'), ('Manifestação', 'Manifestação'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Não Informada', 'Não Informada'), ('Objeto na Pista', 'Objeto na Pista'), ('Obras na Rodovia', 'Obras na Rodovia'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação caminho seguro', 'Operação caminho seguro'), ('Pane', 'Pane'), ('Pane Elétrica', 'Pane Elétrica'), ('Pane Mecânica', 'Pane Mecânica'), ('Pane Seca', 'Pane Seca'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Pneu Furado', 'Pneu Furado'), ('Queda de Barreira', 'Queda de Barreira'), ('Suicídio', 'Suicídio'), ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'), ('Tapa Buraco', 'Tapa Buraco'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento'), ('Subclasse indefinida', 'Subclasse indefinida')], max_length=255, null=True, verbose_name='Tipo de acidente')),
                ('hora_acidente', models.TimeField(blank=True, null=True, verbose_name='Hora do acidente')),
                ('dinamica_acidente', models.TextField(blank=True, null=True, verbose_name='Dinâmica da ocorreência')),
            ],
            options={
                'verbose_name': 'Acidente',
                'verbose_name_plural': 'Acidentes',
                'ordering': ('-pk',),
            },
            bases=('ocorrencias.ocorrencia',),
        ),
        migrations.CreateModel(
            name='OcorrenciaConcreta',
            fields=[
                ('ocorrencia_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='ocorrencias.ocorrencia')),
                ('subclasse', models.CharField(choices=[('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto', 'Assalto'), ('Assalto à Praça de Pedágio', 'Assalto à Praça de Pedágio'), ('Assalto ao Usuário da Rodovia', 'Assalto ao Usuário da Rodovia'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Carga Especial', 'Carga Especial'), ('Condições Climáticas', 'Condições Climáticas'), ('Conservação', 'Conservação'), ('Excesso de Veículos', 'Excesso de Veículos'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Manifestação', 'Manifestação'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Objeto na Pista', 'Objeto na Pista'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação especial', 'Operação especial'), ('Pane', 'Pane'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Queda de Barreira', 'Queda de Barreira'), ('Reintegração de Posse', 'Reintegração de Posse'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento'), ('Subclasse indefinida', 'Subclasse indefinida')], default='Subclasse indefinida', max_length=255, verbose_name='Subclasse da OC')),
            ],
            options={
                'verbose_name': 'Ocorrência',
                'verbose_name_plural': 'Ocorrências',
                'ordering': ('-pk',),
            },
            bases=('ocorrencias.ocorrencia',),
        ),
        migrations.CreateModel(
            name='OcorrenciaVeiculo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(choices=[('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA', 'VAN/PERUA'), ('NAO_IDENTIF', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo')),
                ('placa', models.CharField(blank=True, max_length=10, null=True, verbose_name='Placa')),
                ('derramamento', models.BooleanField(default=False, verbose_name='Derramamento de carga')),
                ('material', models.CharField(blank=True, max_length=50, null=True, verbose_name='Material')),
                ('termino_transbordo', models.DateTimeField(blank=True, null=True, verbose_name='Término Transbordo')),
                ('tombamento', models.BooleanField(default=False, verbose_name='Veíc. tombado na pista')),
                ('termino_destombamento', models.DateTimeField(blank=True, null=True, verbose_name='Término Destombamento')),
                ('cad_antt', models.CharField(blank=True, max_length=50, null=True, verbose_name='N. ANTT')),
                ('prod_perigo', models.BooleanField(default=False, verbose_name='Carregado com prod. perigosos')),
                ('prod_perigo_cod', models.CharField(blank=True, max_length=50, null=True, verbose_name='Prod. perigoso - Cód. ONU')),
                ('origem', models.CharField(blank=True, max_length=50, null=True, verbose_name='Origem')),
                ('destino', models.CharField(blank=True, max_length=50, null=True, verbose_name='Destino')),
                ('obs', models.TextField(blank=True, null=True, verbose_name='Obs. gerais')),
                ('publica', models.BooleanField(default=True, verbose_name='Pública')),
                ('ocorrencia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='veiculos', to='ocorrencias.ocorrencia', verbose_name='Veículo')),
            ],
            options={
                'verbose_name': 'Veículo',
                'verbose_name_plural': 'Veículos',
                'ordering': ('-pk',),
            },
        ),
        migrations.CreateModel(
            name='OcorrenciaFim',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(choices=[('ILESO', 'Ileso'), ('FERIDO', 'Ferido'), ('MORTO', 'Morto')], max_length=50, verbose_name='Tipo')),
                ('qtd', models.PositiveSmallIntegerField(verbose_name='Qtd')),
                ('obs', models.CharField(blank=True, max_length=255, null=True, verbose_name='Obs.')),
                ('publica', models.BooleanField(default=True, verbose_name='Pública')),
                ('ocorrencia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fim', to='ocorrencias.ocorrencia', verbose_name='Fim')),
            ],
            options={
                'verbose_name': 'FIM',
                'verbose_name_plural': 'FIM',
                'ordering': ('-pk',),
            },
        ),
        migrations.CreateModel(
            name='OcorrenciaAtualizacao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('atualizacao', models.TextField(verbose_name='Atualização')),
                ('publica', models.BooleanField(default=True, verbose_name='Pública')),
                ('oc_atualizada_em', models.DateTimeField(auto_now_add=True, verbose_name='atualizado em')),
                ('oc_atualizada_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='oc_atualizador', to=settings.AUTH_USER_MODEL, verbose_name='Oc. atualizada em')),
                ('ocorrencia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='atualizacao', to='ocorrencias.ocorrencia', verbose_name='Atualização')),
            ],
            options={
                'verbose_name': 'ATUALIZAÇÃO',
                'verbose_name_plural': 'ATUALIZAÇÕES',
                'ordering': ('oc_atualizada_em',),
            },
        ),
        migrations.CreateModel(
            name='Foto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('foto', models.ImageField(upload_to='ocorrencias/fotos', verbose_name='Foto')),
                ('publica', models.BooleanField(default=True, verbose_name='Pública')),
                ('legenda', models.CharField(blank=True, max_length=100, null=True, verbose_name='Legenda')),
                ('ocorrencia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fotos', to='ocorrencias.ocorrencia', verbose_name='Foto')),
            ],
            options={
                'verbose_name': 'Foto',
                'verbose_name_plural': 'Fotos',
                'ordering': ['-pk'],
            },
        ),
        migrations.CreateModel(
            name='Congestionamento',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pista', models.CharField(choices=[('Simples', 'Simples'), ('Dupla', 'Dupla'), ('Expressa', 'Expressa'), ('Interna', 'Interna'), ('Externa', 'Externa'), ('Marginal', 'Marginal'), ('Dupla com canteiro central', 'Dupla com canteiro central'), ('Dupla com barreira central', 'Dupla com barreira central'), ('Dupla com faixa central', 'Dupla com faixa central'), ('Simples de mão dupla', 'Simples de mão dupla'), ('Simples com mão única', 'Simples com mão única'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=50, verbose_name='Pista')),
                ('sentido', models.CharField(choices=[('Norte', 'Norte'), ('Sul', 'Sul'), ('Leste', 'Leste'), ('Oeste', 'Oeste'), ('Interno', 'Interno'), ('Externo', 'Externo'), ('Norte-Sul', 'Norte-Sul'), ('Leste-Oeste', 'Leste-Oeste'), ('Noroeste-Sudeste', 'Noroeste-Sudeste'), ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'), ('Sudeste', 'Sudeste'), ('Sudoeste', 'Sudoeste'), ('Nordeste', 'Nordeste'), ('Noroeste', 'Noroeste'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=20, verbose_name='Sentido')),
                ('hora_inicio', models.TimeField(blank=True, null=True, verbose_name='Hora Início')),
                ('hora_termino', models.TimeField(blank=True, null=True, verbose_name='Hora Término')),
                ('km_inicial', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='KM inicial')),
                ('km_final', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='KM final')),
                ('obs', models.CharField(blank=True, max_length=255, null=True, verbose_name='Obs.')),
                ('ocorrencia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='congestionamento', to='ocorrencias.ocorrencia', verbose_name='Congestionamento')),
            ],
            options={
                'verbose_name': 'Congestionamento',
                'verbose_name_plural': 'Congestionamentos',
                'ordering': ['pista', 'sentido', 'km_inicial'],
            },
        ),
    ]
