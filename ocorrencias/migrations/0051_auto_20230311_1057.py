# Generated by Django 3.2 on 2023-03-11 10:57

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0050_alter_congestionamento_trafego'),
    ]

    operations = [
        migrations.AddField(
            model_name='ocorrenciaveiculo',
            name='qtd_material_derramada_unidade',
            field=models.CharField(blank=True, help_text="Ex.: use 't' para tonelada, 'kg', para kilograma", max_length=30, null=True, verbose_name='unidade de medida'),
        ),
        migrations.AddField(
            model_name='ocorrenciaveiculo',
            name='qtd_material_unidade',
            field=models.CharField(blank=True, help_text="Ex.: use 't' para tonelada, 'kg', para kilograma", max_length=30, null=True, verbose_name='unidade de medida'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='qtd_material',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Quantidade', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Qtd.'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='qtd_material_derramada',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Quantidade derramado', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Qtd. derramado'),
        ),
    ]
