# Generated by Django 3.2 on 2021-09-02 22:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0003_auto_20210901_2242'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_ac',
            field=models.CharField(blank=True, choices=[('Atropelamento de animal', 'Atropelamento de animal'), ('Atropelamento de pedestre', 'Atropelamento de pedestre'), ('Capotamento', 'Capotamento'), ('Choque', 'Choque'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('Colisão transversal', 'Colisão transversal'), ('Colisão traseira', 'Colisão traseira'), ('Engavetamento', 'Engavetamento'), ('Explosão', 'Explosão'), ('Incêndio', 'Incêndio'), ('Objeto contra veículo', 'Objeto contra veículo'), ('<PERSON><PERSON>', 'Que<PERSON>'), ('Sequência', 'Sequência'), ('Submersão', '<PERSON>mersão'), ('Tombamento', 'Tombamento'), ('Acidente', 'Acidente')], max_length=255, null=True, verbose_name='Subclasse de acidente'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_oc',
            field=models.CharField(choices=[('Não se aplica', 'Não se aplica'), ('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto', 'Assalto'), ('Assalto à Praça de Pedágio', 'Assalto à Praça de Pedágio'), ('Assalto ao Usuário da Rodovia', 'Assalto ao Usuário da Rodovia'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Carga Especial', 'Carga Especial'), ('Condições Climáticas', 'Condições Climáticas'), ('Conservação', 'Conservação'), ('Excesso de Veículos', 'Excesso de Veículos'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Manifestação', 'Manifestação'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Objeto na Pista', 'Objeto na Pista'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação especial', 'Operação especial'), ('Pane', 'Pane'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Queda de Barreira', 'Queda de Barreira'), ('Reintegração de Posse', 'Reintegração de Posse'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento'), ('Subclasse indefinida', 'Subclasse indefinida')], default='Não se aplica', max_length=255, verbose_name='Subclasse de OC'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='tipo_ac',
            field=models.CharField(blank=True, choices=[('Acidente com Danos Materiais', 'Acidente com Danos Materiais'), ('Acidente com Vítima', 'Acidente com Vítima'), ('Acidente com Vítima Fatal', 'Acidente com Vítima Fatal'), ('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto a Praça de Pedágio', 'Assalto a Praça de Pedágio'), ('Assalto ao Usuário da Rodovia', 'Assalto ao Usuário da Rodovia'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Carga Especial', 'Carga Especial'), ('Condições Climáticas', 'Condições Climáticas'), ('Conservação', 'Conservação'), ('Derramamento de Carga', 'Derramamento de Carga'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio', 'Incêndio'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Incidente', 'Incidente'), ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'), ('Indefinida', 'Indefinida'), ('Mal Súbito', 'Mal Súbito'), ('Manifestação', 'Manifestação'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Não Informada', 'Não Informada'), ('Objeto na Pista', 'Objeto na Pista'), ('Obras na Rodovia', 'Obras na Rodovia'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação caminho seguro', 'Operação caminho seguro'), ('Pane', 'Pane'), ('Pane Elétrica', 'Pane Elétrica'), ('Pane Mecânica', 'Pane Mecânica'), ('Pane Seca', 'Pane Seca'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Pneu Furado', 'Pneu Furado'), ('Queda de Barreira', 'Queda de Barreira'), ('Suicídio', 'Suicídio'), ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'), ('Tapa Buraco', 'Tapa Buraco'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento')], max_length=255, null=True, verbose_name='Tipo de acidente'),
        ),
    ]
