# Generated by Django 3.2 on 2022-05-15 14:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0037_alter_ocorrencia_tipo_ac'),
    ]

    operations = [
        migrations.AlterField(
            model_name='congestionamento',
            name='pista',
            field=models.CharField(choices=[('DUPLA', 'DUPLA'), ('SIMPLES', 'SIMPLES'), ('PLANEJADA', 'PLANEJADA'), ('DISPOSITIVO', 'DISPOSITIVO'), ('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO')], default='SEM INFORMAÇÃO', max_length=50, verbose_name='Pista'),
        ),
        migrations.AlterField(
            model_name='congestionamento',
            name='sentido',
            field=models.CharField(choices=[('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'), ('NORTE', 'NORTE'), ('SUL', 'SUL'), ('LESTE', 'LESTE'), ('OESTE', 'OESTE'), ('NORTE/SUL', 'NORTE/SUL'), ('LESTE/OESTE', 'LESTE/OESTE'), ('EXTERNO', 'EXTERNO'), ('INTERNO', 'INTERNO'), ('NÃO SE APLICA', 'NÃO SE APLICA')], default='SEM INFORMAÇÃO', max_length=20, verbose_name='Sentido'),
        ),
        migrations.AlterField(
            model_name='interdicao',
            name='sentido',
            field=models.CharField(choices=[('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'), ('NORTE', 'NORTE'), ('SUL', 'SUL'), ('LESTE', 'LESTE'), ('OESTE', 'OESTE'), ('NORTE/SUL', 'NORTE/SUL'), ('LESTE/OESTE', 'LESTE/OESTE'), ('EXTERNO', 'EXTERNO'), ('INTERNO', 'INTERNO'), ('NÃO SE APLICA', 'NÃO SE APLICA')], max_length=20, verbose_name='Sentido'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='pista',
            field=models.CharField(blank=True, choices=[('DUPLA', 'DUPLA'), ('SIMPLES', 'SIMPLES'), ('PLANEJADA', 'PLANEJADA'), ('DISPOSITIVO', 'DISPOSITIVO'), ('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO')], default='SEM INFORMAÇÃO', max_length=50, null=True, verbose_name='Pista'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='sentido',
            field=models.CharField(choices=[('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'), ('NORTE', 'NORTE'), ('SUL', 'SUL'), ('LESTE', 'LESTE'), ('OESTE', 'OESTE'), ('NORTE/SUL', 'NORTE/SUL'), ('LESTE/OESTE', 'LESTE/OESTE'), ('EXTERNO', 'EXTERNO'), ('INTERNO', 'INTERNO'), ('NÃO SE APLICA', 'NÃO SE APLICA')], default='SEM INFORMAÇÃO', max_length=20, verbose_name='Sentido'),
        ),
    ]
