# Generated by Django 4.2.5 on 2024-01-29 13:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0080_foto_liberada_nao_autenticados_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='faixa_inicio',
            field=models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('Alça', 'Alça'), ('Acostamento', 'Acostamento'), ('Faixa adicional', 'Faixa adicional'), ('Faixa zebrada', 'Faixa zebrada'), ('Faixa de aceleração', 'Faixa de aceleração'), ('Faixa de desaceleração', 'Faixa de desaceleração'), ('Fora da pista', 'Fora da pista'), ('Canteiro lateral', 'Canteiro lateral'), ('Canteiro central', 'Canteiro central')], max_length=255, null=True, verbose_name='Faixa início oc.'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='tipo_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento de ambulante', 'Atropelamento de ambulante'), ('Atropelamento de andarilho', 'Atropelamento de andarilho'), ('Atropelamento de animal', 'Atropelamento de animal'), ('Atropelamento de ciclista/esportista', 'Atropelamento de ciclista/esportista'), ('Atropelamento de funcionário', 'Atropelamento de funcionário'), ('Atropelamento de morador/trabalhador/estudante', 'Atropelamento de morador/trabalhador/estudante'), ('Atropelamento de pedestre', 'Atropelamento de pedestre'), ('Atropelamento de usuário', 'Atropelamento de usuário'), ('Atropelamento de romeiro', 'Atropelamento de romeiro'), ('Atropelamento sem informação', 'Atropelamento sem informação'), ('Atropelamento de suicida', 'Atropelamento de suicida'), ('Capotamento de veículo', 'Capotamento de veículo'), ('Carreta Em L', 'Carreta Em L'), ('Choque com árvore', 'Choque com árvore'), ('Choque com barreira', 'Choque com barreira'), ('Choque com cabine de pedágio', 'Choque com cabine de pedágio'), ('Choque com cancela de pista de pedágio', 'Choque com cancela de pista de pedágio'), ('Choque com caixa de captação/fibra', 'Choque com caixa de captação/fibra'), ('Choque com canaleta', 'Choque com canaleta'), ('Choque com cerca', 'Choque com cerca'), ('Choque com defensa metálica', 'Choque com defensa metálica'), ('Choque com barreira de concreto', 'Choque com barreira de concreto'), ('Choque com submarino', 'Choque com submarino'), ('Choque com meio-fio', 'Choque com meio-fio'), ('Choque com elemento de drenagem', 'Choque com elemento de drenagem'), ('Choque com equipamento', 'Choque com equipamento'), ('Choque com OAE (Ponte/Viaduto/Pass)', 'Choque com OAE (Ponte/Viaduto/Pass)'), ('Choque com objeto fixo', 'Choque com objeto fixo'), ('Choque com objeto na pista', 'Choque com objeto na pista'), ('Choque com outros', 'Choque com outros'), ('Choque com poste', 'Choque com poste'), ('Choque com talude ou barranco', 'Choque com talude ou barranco'), ('Choque com veículo na pista', 'Choque com veículo na pista'), ('Choque com veículo estacionado', 'Choque com veículo estacionado'), ('Choque contra atenuador de impacto', 'Choque contra atenuador de impacto'), ('Choque contra placa de sinalização', 'Choque contra placa de sinalização'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('Colisão transversal', 'Colisão transversal'), ('Colisão traseira', 'Colisão traseira'), ('Colisões múltiplas', 'Colisões múltiplas'), ('Indefinido', 'Indefinido'), ('Objeto lançado contra veículo', 'Objeto lançado contra veículo'), ('Queda de avião', 'Queda de avião'), ('Queda de avião civil', 'Queda de avião civil'), ('Queda de avião militar', 'Queda de avião militar'), ('Queda de helicóptero', 'Queda de helicóptero'), ('Queda de OAE', 'Queda de OAE'), ('Queda de veículo em ribanceira', 'Queda De veículo em ribanceira'), ('Sem informação', 'Sem informação'), ('Suicídio/atropelamento', 'Suicídio/atropelamento'), ('Suicídio/pulo de OAE', 'Suicídio/pulo de OAE'), ('Tombamento de bicicleta', 'Tombamento de bicicleta'), ('Tombamento de moto', 'Tombamento de moto'), ('Tombamento de ônibus', 'Tombamento de ônibus'), ('Tombamento de utilitária', 'Tombamento de utilitária'), ('Tombamento de veículo', 'Tombamento de veículo'), ('Tombamento de veículo leve', 'Tombamento de veículo leve'), ('Tombamento de veículo pesado', 'Tombamento de veículo pesado')], max_length=255, null=True, verbose_name='Subclasse do acidente'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='tipo',
            field=models.CharField(choices=[('EM AVERIGUAÇÃO', 'EM AVERIGUAÇÃO'), ('AERONAVE', 'AERONAVE'), ('AMBULÂNCIA', 'AMBULÂNCIA'), ('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('CARROÇA/CHARRETE', 'CARROÇA/CHARRETE'), ('CARRO-FORTE', 'CARRO-FORTE'), ('CVC', 'CVC'), ('HELICÓPTERO', 'HELICÓPTERO'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('PEDESTRE', 'PEDESTRE'), ('PESSOA SEM VEÍCULO', 'PESSOA SEM VEÍCULO'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA TRANSP. COLETIVO', 'VAN/PERUA TRANSP. COLETIVO'), ('VAN/PERUA DE MERCADORIAS', 'VAN/PERUA DE MERCADORIAS'), ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo'),
        ),
    ]
