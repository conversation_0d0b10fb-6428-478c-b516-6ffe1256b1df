# Generated by Django 3.2 on 2022-12-22 07:43

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0041_alter_ocorrencia_subclasse_oc'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='atualizacao_min',
            field=models.IntegerField(default=60, help_text='Tempo em minutos. Valor máximo: 720min (12 horas)', validators=[django.core.validators.MaxValueValidator(720)], verbose_name='Atualização - Periodicidade (minutos)'),
        ),
    ]
