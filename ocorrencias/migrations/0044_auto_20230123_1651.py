# Generated by Django 3.2 on 2023-01-23 16:51

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0043_auto_20221228_2046'),
    ]

    operations = [
        migrations.AddField(
            model_name='congestionamento',
            name='dt_hora_inicio',
            field=models.DateTimeField(blank=True, null=True, verbose_name='DataHora Início'),
        ),
        migrations.AddField(
            model_name='congestionamento',
            name='dt_hora_termino',
            field=models.DateTimeField(blank=True, null=True, verbose_name='DataHora Término'),
        ),
        migrations.AddField(
            model_name='congestionamento',
            name='trafego',
            field=models.CharField(choices=[('INTENSO', 'Intenso'), ('LENTO', 'Lento'), ('CONGESTIONADO', 'Congestionado'), ('INTERDITADO', 'Interditado'), ('DESCONHECIDO', 'Desconhecido')], default='DESCONHECIDO', max_length=50, verbose_name='Tráfego'),
        ),
        migrations.AddField(
            model_name='ocorrencia',
            name='complemento',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Complemento da oc.'),
        ),
        migrations.AddField(
            model_name='ocorrencia',
            name='cond_climaticas_atuais',
            field=models.CharField(choices=[('Bom', 'Bom'), ('Chuva', 'Chuva'), ('Garoa', 'Garoa'), ('Neblina', 'Neblina'), ('Nublado', 'Nublado'), ('Trechos com chuva', 'Trechos com chuva'), ('Trechos com garoa', 'Trechos com garoa'), ('Trechos com neblina', 'Trechos com neblina'), ('Trechos com garoa e neblina', 'Trechos com garoa e neblina'), ('Trechos com chuva e neblina', 'Trechos com chuva e neblina'), ('Trechos com chuva e nublado', 'Trechos com chuva e nublado'), ('Chuva com ventania', 'Chuva com ventania'), ('Chuva torrencial', 'Chuva torrencial'), ('Sol', 'Sol'), ('Tempo bom', 'Tempo bom'), ('Vento forte', 'Vento forte'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=50, verbose_name='Cond. climáticas atuais'),
        ),
        migrations.AddField(
            model_name='ocorrenciaveiculo',
            name='qtd_material',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Quantidade em toneladas', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Qtd. (t)'),
        ),
        migrations.AddField(
            model_name='ocorrenciaveiculo',
            name='qtd_material_derramada',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Quantidade derramado em toneladas', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Qtd. derramado (t)'),
        ),
        migrations.AddField(
            model_name='ocorrenciaveiculo',
            name='veiculo_oficial',
            field=models.BooleanField(default=False, verbose_name='Veículo oficial'),
        ),
        migrations.AlterField(
            model_name='congestionamento',
            name='ocorrencia',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='congestionamento', to='ocorrencias.ocorrencia', verbose_name='Condições de tráfego'),
        ),
        migrations.AlterField(
            model_name='interdicao',
            name='faixa',
            field=models.CharField(choices=[('Acostamento', 'Acostamento'), ('Alça', 'Alça'), ('Alça parcial', 'Alça parcial'), ('Alça total', 'Alça total'), ('Faixa 1', 'Faixa 1'), ('Faixa 2', 'Faixa 2'), ('Faixa 3', 'Faixa 3'), ('Faixa 4', 'Faixa 4'), ('Faixa 5', 'Faixa 5'), ('Faixa 6', 'Faixa 6'), ('Faixa zebrada', 'Faixa zebrada'), ('Faixa de aceleração', 'Faixa de aceleração'), ('Faixa de desaceleração', 'Faixa de desaceleração'), ('Praça de pedágio', 'Praça de pedágio'), ('Todas as faixas', 'Todas as faixas'), ('Todas as faixas e acostamento', 'Todas as faixas e acostamento')], max_length=30, verbose_name='Faixa'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='cond_climaticas',
            field=models.CharField(choices=[('Bom', 'Bom'), ('Chuva', 'Chuva'), ('Garoa', 'Garoa'), ('Neblina', 'Neblina'), ('Nublado', 'Nublado'), ('Trechos com chuva', 'Trechos com chuva'), ('Trechos com garoa', 'Trechos com garoa'), ('Trechos com neblina', 'Trechos com neblina'), ('Trechos com garoa e neblina', 'Trechos com garoa e neblina'), ('Trechos com chuva e neblina', 'Trechos com chuva e neblina'), ('Trechos com chuva e nublado', 'Trechos com chuva e nublado'), ('Chuva com ventania', 'Chuva com ventania'), ('Chuva torrencial', 'Chuva torrencial'), ('Sol', 'Sol'), ('Tempo bom', 'Tempo bom'), ('Vento forte', 'Vento forte'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=50, verbose_name='Cond. climáticas no momento da ocorrência'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_oc',
            field=models.CharField(choices=[('Não se aplica', 'Não se aplica'), ('Em averiguação', 'Em averiguação'), ('Aferição de Radar', 'Aferição de Radar'), ('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto à Praça de Pedágio', 'Assalto à Praça de Pedágio'), ('Assalto', 'Assalto'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Atendimento Clínico - Usuário', 'Atendimento Clínico - Usuário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Barreira Sanitária', 'Barreira Sanitária'), ('Carga Especial', 'Carga Especial'), ('Congestionamento', 'Congestionamento'), ('Conservação', 'Conservação'), ('Construção/Recuperação', 'Construção/Recuperação'), ('Derramamento de Carga', 'Derramamento de Carga'), ('Deslizamento de talude', 'Deslizamento de talude'), ('Deslizamento De Terra Ou Mat. Rochoso', 'Deslizamento De Terra Ou Mat. Rochoso'), ('Erosão', 'Erosão'), ('Evento', 'Evento'), ('Excesso de Veículos', 'Excesso de Veículos'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'), ('Incidente', 'Incidente'), ('Indefinida', 'Indefinida'), ('Interdição', 'Interdição'), ('Intervenção viária', 'Intervenção viária'), ('Mal Súbito', 'Mal Súbito'), ('Manifestação', 'Manifestação'), ('Manutenção', 'Manutenção'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Objeto contra veículo', 'Objeto contra veículo'), ('Objeto na Pista', 'Objeto na Pista'), ('Obra', 'Obra'), ('Obras na Rodovia', 'Obras na Rodovia'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação de trânsito', 'Operação de trânsito'), ('Operação especial', 'Operação especial'), ('Pane', 'Pane'), ('Pane Elétrica', 'Pane Elétrica'), ('Pane Mecânica', 'Pane Mecânica'), ('Pane Seca', 'Pane Seca'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Pneu Furado', 'Pneu Furado'), ('Ponto de Interferência', 'Ponto de Interferência'), ('Queda de Árvore', 'Queda de Árvore'), ('Queda de Barreira', 'Queda de Barreira'), ('Queda de Talude', 'Queda de Talude'), ('Reintegração de Posse', 'Reintegração de Posse'), ('Rodeiro desatrelado', 'Rodeiro desatrelado'), ('Simulação', 'Simulação'), ('Suicídio', 'Suicídio'), ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'), ('Tapa Buraco', 'Tapa Buraco'), ('Tentativa de Suicídio', 'Tentativa de Suicídio'), ('Vandalismo', 'Vandalismo'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento')], default='Não se aplica', max_length=255, verbose_name='Subclasse de OC'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='tipo_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento de ambulante', 'Atropelamento de ambulante'), ('Atropelamento de andarilho', 'Atropelamento de andarilho'), ('Atropelamento de animal', 'Atropelamento de animal'), ('Atropelamento de ciclista/esportista', 'Atropelamento de ciclista/esportista'), ('Atropelamento de funcionário', 'Atropelamento de funcionário'), ('Atropelamento de morador/trabalhador/estudante', 'Atropelamento de morador/trabalhador/estudante'), ('Atropelamento de pedestre', 'Atropelamento de pedestre'), ('Atropelamento de usuário', 'Atropelamento de usuário'), ('Atropelamento de romeiro', 'Atropelamento de romeiro'), ('Atropelamento sem informação', 'Atropelamento sem informação'), ('Atropelamento de suicida', 'Atropelamento de suicida'), ('Capotamento de veículo', 'Capotamento de veículo'), ('Carreta Em L', 'Carreta Em L'), ('Choque com árvore', 'Choque com árvore'), ('Choque com barreira', 'Choque com barreira'), ('Choque com cabine de pedágio', 'Choque com cabine de pedágio'), ('Choque com cancela de pista de pedágio', 'Choque com cancela de pista de pedágio'), ('Choque com caixa de captação/fibra', 'Choque com caixa de captação/fibra'), ('Choque com canaleta', 'Choque com canaleta'), ('Choque com cerca', 'Choque com cerca'), ('Choque com defensa metálica', 'Choque com defensa metálica'), ('Choque com barreira de concreto,', 'Choque com barreira de concreto,'), ('Choque com submarino', 'Choque com submarino'), ('Choque com meio-fio', 'Choque com meio-fio'), ('Choque com elemento de drenagem', 'Choque com elemento de drenagem'), ('Choque com equipamento', 'Choque com equipamento'), ('Choque com OAE (Ponte/Viaduto/Pass)', 'Choque com OAE (Ponte/Viaduto/Pass)'), ('Choque com objeto fixo', 'Choque com objeto fixo'), ('Choque com objeto na pista', 'Choque com objeto na pista'), ('Choque com outros', 'Choque com outros'), ('Choque com talude ou barranco', 'Choque com talude ou barranco'), ('Choque com veículo na pista', 'Choque com veículo na pista'), ('Choque com veículo estacionado', 'Choque com veículo estacionado'), ('Choque contra atenuador de impacto', 'Choque contra atenuador de impacto'), ('Choque contra placa de sinalização', 'Choque contra placa de sinalização'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('Colisão transversal', 'Colisão transversal'), ('Colisão traseira', 'Colisão traseira'), ('Colisões múltiplas', 'Colisões múltiplas'), ('Indefinido', 'Indefinido'), ('Objeto lançado contra veículo', 'Objeto lançado contra veículo'), ('Queda de carga', 'Queda de carga'), ('Queda de OAE', 'Queda de OAE'), ('Queda de veículo em ribanceira', 'Queda De veículo em ribanceira'), ('Sem informação', 'Sem informação'), ('Suicídio/atropelamento', 'Suicídio/atropelamento'), ('Suicídio/pulo de OAE', 'Suicídio/pulo de OAE'), ('Tombamento de bicicleta', 'Tombamento de bicicleta'), ('Tombamento de moto', 'Tombamento de moto'), ('Tombamento de ônibus', 'Tombamento de ônibus'), ('Tombamento de veículo pesado', 'Tombamento de veículo pesado')], max_length=255, null=True, verbose_name='Subclasse do acidente'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='tipo',
            field=models.CharField(choices=[('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('CARROÇA/CHARRETE', 'CARROÇA/CHARRETE'), ('CVC', 'CVC'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA', 'VAN/PERUA'), ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo'),
        ),
    ]
