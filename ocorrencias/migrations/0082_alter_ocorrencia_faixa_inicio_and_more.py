# Generated by Django 4.2.5 on 2024-03-28 08:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0081_alter_ocorrencia_faixa_inicio_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='faixa_inicio',
            field=models.CharField(blank=True, choices=[('fx1', 'fx1'), ('fx2', 'fx2'), ('fx3', 'fx3'), ('fx4', 'fx4'), ('fx5', 'fx5'), ('fx6', 'fx6'), ('Alça', 'Alça'), ('Acostamento', 'Acostamento'), ('Faixa adicional', 'Faixa adicional'), ('Faixa zebrada', 'Faixa zebrada'), ('Faixa de aceleração', 'Faixa de aceleração'), ('Faixa de desaceleração', 'Faixa de desaceleração'), ('Fora da pista', 'Fora da pista'), ('Canteiro lateral', 'Canteiro lateral'), ('Canteiro central', 'Canteiro central'), ('Praça Pedágio', 'Praça Pedaǵio')], max_length=255, null=True, verbose_name='Faixa início oc.'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='nro_faixas_local',
            field=models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('7', '7'), ('8', '8'), ('9', '9'), ('10', '10'), ('11', '11'), ('12', '12'), ('13', '13'), ('14', '14'), ('15', '15'), ('16', '16'), ('17', '17'), ('18', '18'), ('19', '19'), ('20', '20')], max_length=255, null=True, verbose_name='Nro fxs local'),
        ),
    ]
