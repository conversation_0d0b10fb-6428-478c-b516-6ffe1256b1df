# Generated by Django 3.2 on 2023-03-16 13:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0055_alter_ocorrenciafim_qtd'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='ocorrenciafim',
            options={'ordering': ('tipo',), 'verbose_name': 'FIM', 'verbose_name_plural': 'FIM'},
        ),
        migrations.AddField(
            model_name='ocorrencia',
            name='nro_faixas_local',
            field=models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6')], max_length=255, null=True, verbose_name='Nro fxs local'),
        ),
        migrations.AddField(
            model_name='ocorrencia',
            name='tem_ac',
            field=models.Bo<PERSON>anField(default=True, verbose_name='Tem acostamento'),
        ),
    ]
