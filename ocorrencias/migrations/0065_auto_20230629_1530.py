# Generated by Django 3.2 on 2023-06-29 15:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import ocorrencias.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ocorrencias', '0064_auto_20230619_0826'),
    ]

    operations = [
        migrations.AlterField(
            model_name='foto',
            name='foto_hr',
            field=models.ImageField(upload_to=ocorrencias.models.get_slug, validators=[ocorrencias.models.validate_image], verbose_name='Foto original'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaatualizacao',
            name='atualizacao_apagada_em',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Apagada em'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaatualizacao',
            name='atualizacao_apagada_por',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='oc_apagador', to=settings.AUTH_USER_MODEL, verbose_name='Apagada por'),
        ),
        migrations.AlterField(
            model_name='ocorrenciafim',
            name='tipo',
            field=models.CharField(choices=[('EM AVERIGUAÇÃO', 'EM AVERIGUAÇÃO'), ('ILESA', 'ILESA'), ('LEVE', 'LEVE'), ('MODERADA', 'MODERADA'), ('GRAVE', 'GRAVE'), ('FATAL', 'FATAL')], max_length=50, verbose_name='Tipo'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='tipo',
            field=models.CharField(choices=[('EM AVERIGUAÇÃO', 'EM AVERIGUAÇÃO'), ('AERONAVE', 'AERONAVE'), ('AMBULÂNCIA', 'AMBULÂNCIA'), ('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('CARROÇA/CHARRETE', 'CARROÇA/CHARRETE'), ('CVC', 'CVC'), ('HELICÓPTERO', 'HELICÓPTERO'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('PEDESTRE', 'PEDESTRE'), ('PESSOA SEM VEÍCULO', 'PESSOA SEM VEÍCULO'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA', 'VAN/PERUA'), ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo'),
        ),
    ]
