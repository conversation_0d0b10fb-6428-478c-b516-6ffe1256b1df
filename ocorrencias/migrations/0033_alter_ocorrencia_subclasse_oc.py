# Generated by Django 3.2 on 2022-02-03 17:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0032_auto_20220121_1247'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_oc',
            field=models.CharField(choices=[('Não se aplica', 'Não se aplica'), ('Em averiguação', 'Em averiguação'), ('Aferição de Radar', 'Aferição de Radar'), ('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto à Praça de Pedágio', 'Assalto à Praça de Pedágio'), ('Assalto', 'Assalto'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Barreira Sanitária', 'Barreira Sanitária'), ('Carga Especial', 'Carga Especial'), ('Congestionamento', 'Congestionamento'), ('Conservação', 'Conservação'), ('Construção/Recuperação', 'Construção/Recuperação'), ('Derramamento de Carga', 'Derramamento de Carga'), ('Erosão', 'Erosão'), ('Evento', 'Evento'), ('Excesso de Veículos', 'Excesso de Veículos'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'), ('Incidente', 'Incidente'), ('Indefinida', 'Indefinida'), ('Mal Súbito', 'Mal Súbito'), ('Manifestação', 'Manifestação'), ('Manutenção', 'Manutenção'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Objeto contra veículo', 'Objeto contra veículo'), ('Objeto na Pista', 'Objeto na Pista'), ('Obra', 'Obra'), ('Obras na Rodovia', 'Obras na Rodovia'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação de trânsito', 'Operação de trânsito'), ('Operação especial', 'Operação especial'), ('Pane', 'Pane'), ('Pane Elétrica', 'Pane Elétrica'), ('Pane Mecânica', 'Pane Mecânica'), ('Pane Seca', 'Pane Seca'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Pneu Furado', 'Pneu Furado'), ('Ponto de Interferência', 'Ponto de Interferência'), ('Queda de Barreira', 'Queda de Barreira'), ('Reintegração de Posse', 'Reintegração de Posse'), ('Suicídio', 'Suicídio'), ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'), ('Tapa Buraco', 'Tapa Buraco'), ('Tentativa de Suicídio', 'Tentativa de Suicídio'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento')], default='Não se aplica', max_length=255, verbose_name='Subclasse de OC'),
        ),
    ]
