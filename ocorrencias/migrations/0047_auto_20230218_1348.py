# Generated by Django 3.2 on 2023-02-18 13:48

from django.db import migrations, models
import ocorrencias.models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0046_foto_foto_hr'),
    ]

    operations = [
        migrations.AlterField(
            model_name='foto',
            name='foto',
            field=models.ImageField(upload_to=ocorrencias.models.get_slug, validators=[ocorrencias.models.validate_image], verbose_name='Foto'),
        ),
        migrations.AlterField(
            model_name='foto',
            name='foto_hr',
            field=models.ImageField(blank=True, null=True, upload_to=ocorrencias.models.get_slug, validators=[ocorrencias.models.validate_image], verbose_name='Foto original'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento', 'Atropelamento'), ('Capotamento', 'Capotamento'), ('Choque', 'Choque'), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), ('Engavetamento', 'Engavetamento'), ('Outras', 'Outras'), ('Queda', 'Queda'), ('Queda de aeronave', 'Queda de aeronave'), ('Saída de Pista', 'Saída de Pista'), ('Sem informação', 'Sem informação'), ('Sequência', 'Sequência'), ('Submersão', 'Submersão'), ('Suicídio', 'Suicídio'), ('Tombamento', 'Tombamento')], max_length=255, null=True, verbose_name='Classe do acidente'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='tipo_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento de ambulante', 'Atropelamento de ambulante'), ('Atropelamento de andarilho', 'Atropelamento de andarilho'), ('Atropelamento de animal', 'Atropelamento de animal'), ('Atropelamento de ciclista/esportista', 'Atropelamento de ciclista/esportista'), ('Atropelamento de funcionário', 'Atropelamento de funcionário'), ('Atropelamento de morador/trabalhador/estudante', 'Atropelamento de morador/trabalhador/estudante'), ('Atropelamento de pedestre', 'Atropelamento de pedestre'), ('Atropelamento de usuário', 'Atropelamento de usuário'), ('Atropelamento de romeiro', 'Atropelamento de romeiro'), ('Atropelamento sem informação', 'Atropelamento sem informação'), ('Atropelamento de suicida', 'Atropelamento de suicida'), ('Capotamento de veículo', 'Capotamento de veículo'), ('Carreta Em L', 'Carreta Em L'), ('Choque com árvore', 'Choque com árvore'), ('Choque com barreira', 'Choque com barreira'), ('Choque com cabine de pedágio', 'Choque com cabine de pedágio'), ('Choque com cancela de pista de pedágio', 'Choque com cancela de pista de pedágio'), ('Choque com caixa de captação/fibra', 'Choque com caixa de captação/fibra'), ('Choque com canaleta', 'Choque com canaleta'), ('Choque com cerca', 'Choque com cerca'), ('Choque com defensa metálica', 'Choque com defensa metálica'), ('Choque com barreira de concreto,', 'Choque com barreira de concreto,'), ('Choque com submarino', 'Choque com submarino'), ('Choque com meio-fio', 'Choque com meio-fio'), ('Choque com elemento de drenagem', 'Choque com elemento de drenagem'), ('Choque com equipamento', 'Choque com equipamento'), ('Choque com OAE (Ponte/Viaduto/Pass)', 'Choque com OAE (Ponte/Viaduto/Pass)'), ('Choque com objeto fixo', 'Choque com objeto fixo'), ('Choque com objeto na pista', 'Choque com objeto na pista'), ('Choque com outros', 'Choque com outros'), ('Choque com talude ou barranco', 'Choque com talude ou barranco'), ('Choque com veículo na pista', 'Choque com veículo na pista'), ('Choque com veículo estacionado', 'Choque com veículo estacionado'), ('Choque contra atenuador de impacto', 'Choque contra atenuador de impacto'), ('Choque contra placa de sinalização', 'Choque contra placa de sinalização'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('Colisão transversal', 'Colisão transversal'), ('Colisão traseira', 'Colisão traseira'), ('Colisões múltiplas', 'Colisões múltiplas'), ('Indefinido', 'Indefinido'), ('Objeto lançado contra veículo', 'Objeto lançado contra veículo'), ('Queda de avião', 'Queda de avião'), ('Queda de avião civil', 'Queda de avião civil'), ('Queda de avião militar', 'Queda de avião militar'), ('Queda de carga', 'Queda de carga'), ('Queda de helicóptero', 'Queda de helicóptero'), ('Queda de OAE', 'Queda de OAE'), ('Queda de veículo em ribanceira', 'Queda De veículo em ribanceira'), ('Sem informação', 'Sem informação'), ('Suicídio/atropelamento', 'Suicídio/atropelamento'), ('Suicídio/pulo de OAE', 'Suicídio/pulo de OAE'), ('Tombamento de bicicleta', 'Tombamento de bicicleta'), ('Tombamento de moto', 'Tombamento de moto'), ('Tombamento de ônibus', 'Tombamento de ônibus'), ('Tombamento de veículo pesado', 'Tombamento de veículo pesado')], max_length=255, null=True, verbose_name='Subclasse do acidente'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='tipo',
            field=models.CharField(choices=[('AERONAVE', 'AERONAVE'), ('AUTOMÓVEL', 'AUTOMÓVEL'), ('BICICLETA', 'BICICLETA'), ('BITREM', 'BITREM'), ('CAMINHÃO', 'CAMINHÃO'), ('CAMINHONETE', 'CAMINHONETE'), ('CARRETA', 'CARRETA'), ('CARROÇA/CHARRETE', 'CARROÇA/CHARRETE'), ('CVC', 'CVC'), ('HELICÓPTERO', 'HELICÓPTERO'), ('MICROÔNIBUS', 'MICROÔNIBUS'), ('MOTO', 'MOTO'), ('ÔNIBUS', 'ÔNIBUS'), ('REBOQUE', 'REBOQUE'), ('SEMI-REBOQUE', 'SEMI-REBOQUE'), ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'), ('TRATOR', 'TRATOR'), ('TREMINHÃO', 'TREMINHÃO'), ('TRICICLO', 'TRICICLO'), ('UTILITÁRIO', 'UTILITÁRIO'), ('VAN/PERUA', 'VAN/PERUA'), ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')], max_length=50, verbose_name='Tipo'),
        ),
    ]
