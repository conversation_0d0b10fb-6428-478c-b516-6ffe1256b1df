# Generated by Django 3.2 on 2021-11-11 16:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0009_auto_20211111_1519'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='bloq_faixas',
            field=models.BooleanField(default=False, verbose_name='Bloqueio de faixas'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='cond_climaticas',
            field=models.CharField(choices=[('Chuva', 'Chuva'), ('Chuva com ventania', 'Chuva com ventania'), ('Chuva torrencial', 'Chuva torrencial'), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), ('Garoa', 'Garoa'), ('Neblina', 'Neblina'), ('Nublado', 'Nublado'), ('Sol', 'Sol'), ('Tempo bom', 'Tempo bom'), ('Trechos com Chuva', 'Trechos com Chuva'), ('Trechos com Garoa', 'Trechos com Garoa'), ('Trechos com Neblina', 'Trechos com Neblina'), ('Vento forte', 'Vento forte'), ('Desconhecido', 'Desconhecido')], default='DESCONHECIDO', max_length=50, verbose_name='Cond. Climáticas'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='det_bloq_faixas',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Detalhes bloqueio de faixas'),
        ),
    ]
