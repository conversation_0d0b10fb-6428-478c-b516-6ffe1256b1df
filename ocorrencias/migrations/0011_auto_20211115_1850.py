# Generated by Django 3.2 on 2021-11-15 18:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0010_auto_20211111_1641'),
    ]

    operations = [
        migrations.AddField(
            model_name='ocorrencia',
            name='nro_mits',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='Nro MITS'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento de animal', 'Atropelamento de animal'), ('Atropelamento de pedestre', 'Atropelamento de pedestre'), ('Capotamento', 'Capotamento'), ('Choque', 'Choque'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('<PERSON><PERSON><PERSON> transversal', 'Colis<PERSON> transversal'), ('<PERSON><PERSON><PERSON> traseira', '<PERSON><PERSON><PERSON> traseira'), ('Engavetamento', 'Engavetamento'), ('Explosão', 'Explosão'), ('Incêndio', 'Incêndio'), ('Objeto contra veículo', 'Objeto contra veículo'), ('Queda', 'Queda'), ('Sequência', 'Sequência'), ('Submersão', 'Submersão'), ('Tombamento', 'Tombamento')], max_length=255, null=True, verbose_name='Classe do acidente'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_oc',
            field=models.CharField(choices=[('Não se aplica', 'Não se aplica'), ('Em averiguação', 'Em averiguação'), ('Alagamento', 'Alagamento'), ('Aferição de Radar', 'Aferição de Radar'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto à Praça de Pedágio', 'Assalto à Praça de Pedágio'), ('Assalto', 'Assalto'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Carga Especial', 'Carga Especial'), ('Derramamento de Carga', 'Derramamento de Carga'), ('Excesso de Veículos', 'Excesso de Veículos'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'), ('Incidente', 'Incidente'), ('Indefinida', 'Indefinida'), ('Mal Súbito', 'Mal Súbito'), ('Manifestação', 'Manifestação'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Objeto na Pista', 'Objeto na Pista'), ('Obras na Rodovia', 'Obras na Rodovia'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação especial', 'Operação especial'), ('Pane Elétrica', 'Pane Elétrica'), ('Pane Mecânica', 'Pane Mecânica'), ('Pane Seca', 'Pane Seca'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Pneu Furado', 'Pneu Furado'), ('Queda de Barreira', 'Queda de Barreira'), ('Reintegração de Posse', 'Reintegração de Posse'), ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'), ('Tapa Buraco', 'Tapa Buraco'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento')], default='Não se aplica', max_length=255, verbose_name='Subclasse de OC'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='tipo_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento - Ambulante', 'Atropelamento - Ambulante'), ('Atropelamento - Andarilho', 'Atropelamento - Andarilho'), ('Atropelamento - Animal', 'Atropelamento - Animal'), ('Atropelamento - Ciclista / Esportista', 'Atropelamento - Ciclista / Esportista'), ('Atropelamento - Funcionário', 'Atropelamento - Funcionário'), ('Atropelamento - Morador/Trabalhador/Estudante', 'Atropelamento - Morador/Trabalhador/Estudante'), ('Atropelamento - Outros', 'Atropelamento - Outros'), ('Atropelamento - Usuário', 'Atropelamento - Usuário'), ('Atropelamento - Romeiro', 'Atropelamento - Romeiro'), ('Atropelamento - Sem Informação', 'Atropelamento - Sem Informação'), ('Atropelamento - Suicida', 'Atropelamento - Suicida'), ('Atropelamento De Animal', 'Atropelamento De Animal'), ('Capotamento', 'Capotamento'), ('Carreta Em L', 'Carreta Em L'), ('Choque - Árvore', 'Choque - Árvore'), ('Choque - Barreira', 'Choque - Barreira'), ('Choque - Cabine / Cancela De Pedágio', 'Choque - Cabine / Cancela De Pedágio'), ('Choque - Caixa De Captação/Fibra', 'Choque - Caixa De Captação/Fibra'), ('Choque - Canaleta', 'Choque - Canaleta'), ('Choque - Cerca', 'Choque - Cerca'), ('Choque - Defensa, Barreira, Submarino,  Meio Fio', 'Choque - Defensa, Barreira, Submarino,  Meio Fio'), ('Choque - Elemento De Drenagem', 'Choque - Elemento De Drenagem'), ('Choque - Equipamento', 'Choque - Equipamento'), ('Choque - Oae (Ponte/Viaduto)', 'Choque - Oae (Ponte/Viaduto)'), ('Choque - Objeto Na Pista', 'Choque - Objeto Na Pista'), ('Choque - Outros', 'Choque - Outros'), ('Choque - Talude Ou Barranco', 'Choque - Talude Ou Barranco'), ('Choque - Veículo Na Pista', 'Choque - Veículo Na Pista'), ('Colisão - Frontal', 'Colisão - Frontal'), ('Colisão - Lateral', 'Colisão - Lateral'), ('Colisão - Transversal', 'Colisão - Transversal'), ('Colisão - Traseira', 'Colisão - Traseira'), ('Engavetamento', 'Engavetamento'), ('Explosão', 'Explosão'), ('Incêndio', 'Incêndio'), ('Objeto Lançado Contra Veículo', 'Objeto Lançado Contra Veículo'), ('Queda De Carga', 'Queda De Carga'), ('Queda De Ponte/Viaduto', 'Queda De Ponte/Viaduto'), ('Queda De Veículo Em Ribanceira, Ponte Ou Viaduto', 'Queda De Veículo Em Ribanceira, Ponte Ou Viaduto'), ('Suicídio', 'Suicídio'), ('Tentativo de Suicídio', 'Tentativo de Suicídio'), ('Tombamento - Bicicleta/Outros', 'Tombamento - Bicicleta/Outros'), ('Tombamento - Moto', 'Tombamento - Moto'), ('Tombamento - Veículos Pesados', 'Tombamento - Veículos Pesados')], max_length=255, null=True, verbose_name='Subclasse do acidente'),
        ),
    ]
