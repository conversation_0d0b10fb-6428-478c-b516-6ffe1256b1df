# Generated by Django 3.2 on 2023-04-26 21:53

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ocorrencias', '0061_auto_20230417_1533'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='nro_faixas_local',
            field=models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('7', '7'), ('8', '8'), ('9', '9'), ('10', '10'), ('11', '11'), ('12', '12'), ('13', '13'), ('14', '14'), ('15', '15')], max_length=255, null=True, verbose_name='Nro fxs local'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaatualizacao',
            name='oc_atualizada_por',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='oc_atualizador', to=settings.AUTH_USER_MODEL, verbose_name='Oc. atualizada por'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='qtd_material',
            field=models.DecimalField(blank=True, decimal_places=3, help_text='Quantidade', max_digits=9, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.000'))], verbose_name='Qtd.'),
        ),
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='qtd_material_derramada',
            field=models.DecimalField(blank=True, decimal_places=3, help_text='Quantidade derramado', max_digits=9, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.000'))], verbose_name='Qtd. derramado'),
        ),
    ]
