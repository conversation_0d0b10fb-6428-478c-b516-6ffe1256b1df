# Generated by Django 3.2 on 2021-11-18 19:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0012_alter_ocorrencia_subclasse_ac'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento de animal', 'Atropelamento de animal'), ('Atropelamento de pedestre', 'Atropelamento de pedestre'), ('Capotamento', 'Capotamento'), ('Choque', 'Choque'), ('Colisão frontal', 'Colisão frontal'), ('Colisão lateral', 'Colisão lateral'), ('Colisão transversal', 'Colisão transversal'), ('Colisão traseira', 'Colisão traseira'), ('Engavetamento', 'Engavetamento'), ('Explosão', 'Explos<PERSON>'), ('Incêndio', 'Incêndio'), ('Objeto contra veículo', 'Objeto contra veículo'), ('Que<PERSON>', 'Que<PERSON>'), ('Saída de Pista', 'Saída de Pista'), ('Sequência', 'Sequência'), ('Submersão', 'Submersão'), ('Suicídio', 'Suicídio'), ('Tombamento', 'Tombamento')], max_length=255, null=True, verbose_name='Classe do acidente'),
        ),
        migrations.AlterField(
            model_name='ocorrencia',
            name='tipo_ac',
            field=models.CharField(blank=True, choices=[('Em averiguação', 'Em averiguação'), ('Atropelamento - Ambulante', 'Atropelamento - Ambulante'), ('Atropelamento - Andarilho', 'Atropelamento - Andarilho'), ('Atropelamento - Animal', 'Atropelamento - Animal'), ('Atropelamento - Ciclista / Esportista', 'Atropelamento - Ciclista / Esportista'), ('Atropelamento - Funcionário', 'Atropelamento - Funcionário'), ('Atropelamento - Morador/Trabalhador/Estudante', 'Atropelamento - Morador/Trabalhador/Estudante'), ('Atropelamento - Outros', 'Atropelamento - Outros'), ('Atropelamento - Usuário', 'Atropelamento - Usuário'), ('Atropelamento - Romeiro', 'Atropelamento - Romeiro'), ('Atropelamento - Sem Informação', 'Atropelamento - Sem Informação'), ('Atropelamento - Suicida', 'Atropelamento - Suicida'), ('Atropelamento De Animal', 'Atropelamento De Animal'), ('Capotamento', 'Capotamento'), ('Carreta Em L', 'Carreta Em L'), ('Choque - Árvore', 'Choque - Árvore'), ('Choque - Barreira', 'Choque - Barreira'), ('Choque - Cabine / Cancela De Pedágio', 'Choque - Cabine / Cancela De Pedágio'), ('Choque - Caixa De Captação/Fibra', 'Choque - Caixa De Captação/Fibra'), ('Choque - Canaleta', 'Choque - Canaleta'), ('Choque - Cerca', 'Choque - Cerca'), ('Choque - Defensa, Barreira, Submarino,  Meio Fio', 'Choque - Defensa, Barreira, Submarino,  Meio Fio'), ('Choque - Elemento De Drenagem', 'Choque - Elemento De Drenagem'), ('Choque - Equipamento', 'Choque - Equipamento'), ('Choque - OAE (Ponte/Viaduto/Pass)', 'Choque - OAE (Ponte/Viaduto/Pass)'), ('Choque - Objeto fixo', 'Choque - Objeto fixo'), ('Choque - Objeto Na Pista', 'Choque - Objeto Na Pista'), ('Choque - Outros', 'Choque - Outros'), ('Choque - Talude Ou Barranco', 'Choque - Talude Ou Barranco'), ('Choque - Veículo Na Pista', 'Choque - Veículo Na Pista'), ('Choque - Veículo estacionado', 'Choque - Veículo estacionado'), ('Colisão - Frontal', 'Colisão - Frontal'), ('Colisão - Lateral', 'Colisão - Lateral'), ('Colisão - Transversal', 'Colisão - Transversal'), ('Colisão - Traseira', 'Colisão - Traseira'), ('Engavetamento', 'Engavetamento'), ('Explosão', 'Explosão'), ('Incêndio', 'Incêndio'), ('Objeto Lançado Contra Veículo', 'Objeto Lançado Contra Veículo'), ('Outros', 'Outros'), ('Queda De Carga', 'Queda De Carga'), ('Queda De Ponte/Viaduto', 'Queda De Ponte/Viaduto'), ('Queda De Veículo Em Ribanceira, Ponte Ou Viaduto', 'Queda De Veículo Em Ribanceira, Ponte Ou Viaduto'), ('Suicídio', 'Suicídio'), ('Tentativo de Suicídio', 'Tentativo de Suicídio'), ('Tombamento - Bicicleta/Outros', 'Tombamento - Bicicleta/Outros'), ('Tombamento - Moto', 'Tombamento - Moto'), ('Tombamento - Veículos Pesados', 'Tombamento - Veículos Pesados')], max_length=255, null=True, verbose_name='Subclasse do acidente'),
        ),
    ]
