# Generated by Django 3.2 on 2023-02-18 16:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0047_auto_20230218_1348'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrencia',
            name='subclasse_oc',
            field=models.CharField(choices=[('Não se aplica', 'Não se aplica'), ('Em averiguação', 'Em averiguação'), ('Aferição de Radar', 'Aferição de Radar'), ('Alagamento', 'Alagamento'), ('Ambulante no Trecho', 'Ambulante no Trecho'), ('Animal na rodovia', 'Animal na rodovia'), ('Apoio Operacional', 'Apoio Operacional'), ('Apoio PMRv', 'Apoio PMRv'), ('Apreensão de Animais', 'Apreensão de Animais'), ('Apreensão de Veículo', 'Apreensão de Veículo'), ('Assalto à Praça de Pedágio', 'Assalto à Praça de Pedágio'), ('Assalto', 'Assalto'), ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'), ('Atendimento Clínico - Usuário', 'Atendimento Clínico - Usuário'), ('Bateria Descarregada', 'Bateria Descarregada'), ('Barreira Sanitária', 'Barreira Sanitária'), ('Carga Especial', 'Carga Especial'), ('Congestionamento', 'Congestionamento'), ('Conservação', 'Conservação'), ('Construção/Recuperação', 'Construção/Recuperação'), ('Derramamento de Carga', 'Derramamento de Carga'), ('Deslizamento de talude', 'Deslizamento de talude'), ('Deslizamento De Terra Ou Mat. Rochoso', 'Deslizamento De Terra Ou Mat. Rochoso'), ('Erosão', 'Erosão'), ('Evento', 'Evento'), ('Excesso de Veículos', 'Excesso de Veículos'), ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'), ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'), ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'), ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'), ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'), ('Incêndio em Veículo', 'Incêndio em Veículo'), ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'), ('Incidente', 'Incidente'), ('Indefinida', 'Indefinida'), ('Interdição', 'Interdição'), ('Intervenção viária', 'Intervenção viária'), ('Mal Súbito', 'Mal Súbito'), ('Manifestação', 'Manifestação'), ('Manutenção', 'Manutenção'), ('Monitoração da Rodovia', 'Monitoração da Rodovia'), ('Objeto contra veículo', 'Objeto contra veículo'), ('Objeto na Pista', 'Objeto na Pista'), ('Obra', 'Obra'), ('Obras na Rodovia', 'Obras na Rodovia'), ('Ocorrência Policial', 'Ocorrência Policial'), ('Óleo na Pista', 'Óleo na Pista'), ('Operação de trânsito', 'Operação de trânsito'), ('Operação especial', 'Operação especial'), ('Pane', 'Pane'), ('Pane Elétrica', 'Pane Elétrica'), ('Pane Mecânica', 'Pane Mecânica'), ('Pane Seca', 'Pane Seca'), ('Pedestre no Trecho', 'Pedestre no Trecho'), ('Pneu Furado', 'Pneu Furado'), ('Ponto de Interferência', 'Ponto de Interferência'), ('Queda de Aeronave', 'Queda de Aeronave'), ('Queda de Árvore', 'Queda de Árvore'), ('Queda de Barreira', 'Queda de Barreira'), ('Queda de Talude', 'Queda de Talude'), ('Reintegração de Posse', 'Reintegração de Posse'), ('Rodeiro desatrelado', 'Rodeiro desatrelado'), ('Simulação', 'Simulação'), ('Suicídio', 'Suicídio'), ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'), ('Tapa Buraco', 'Tapa Buraco'), ('Tentativa de Suicídio', 'Tentativa de Suicídio'), ('Vandalismo', 'Vandalismo'), ('Veículo Abandonado', 'Veículo Abandonado'), ('Veículo Atolado', 'Veículo Atolado'), ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento')], default='Não se aplica', max_length=255, verbose_name='Subclasse de OC'),
        ),
    ]
