# Generated by Django 3.2 on 2023-07-24 13:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0066_alter_ocorrencia_tipo_ac'),
    ]

    operations = [
        migrations.AddField(
            model_name='ocorrenciaveiculo',
            name='inicio_destombamento',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Início Destombamento'),
        ),
        migrations.AddField(
            model_name='ocorrenciaveiculo',
            name='inicio_transbordo',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Início Transbordo'),
        ),
        migrations.AlterField(
            model_name='interdicao',
            name='faixa',
            field=models.CharField(choices=[('Acostamento', 'Acostamento'), ('Alça parcial', 'Alça parcial'), ('Alça total', 'Alça total'), ('Faixa 1', 'Faixa 1'), ('Faixa 2', 'Faixa 2'), ('Faixa 3', 'Faixa 3'), ('Faixa 4', 'Faixa 4'), ('Faixa 5', 'Faixa 5'), ('Faixa 6', 'Faixa 6'), ('Faixa adicional', 'Faixa adicional'), ('Faixa zebrada', 'Faixa zebrada'), ('Faixa de aceleração', 'Faixa de aceleração'), ('Faixa de desaceleração', 'Faixa de desaceleração'), ('Praça de pedágio', 'Praça de pedágio'), ('Todas as faixas', 'Todas as faixas'), ('Todas as faixas (trecho sem AC)', 'Todas as faixas (trecho sem AC)'), ('Todas as faixas e acostamento', 'Todas as faixas e acostamento')], max_length=40, verbose_name='Faixa'),
        ),
    ]
