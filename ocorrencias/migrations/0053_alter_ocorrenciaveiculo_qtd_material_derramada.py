# Generated by Django 3.2 on 2023-03-14 13:00

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ocorrencias', '0052_auto_20230314_0802'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ocorrenciaveiculo',
            name='qtd_material_derramada',
            field=models.DecimalField(blank=True, decimal_places=3, help_text='Quantidade derramado', max_digits=6, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.000'))], verbose_name='Qtd. derramado'),
        ),
    ]
