
from django.conf import settings
from django.conf.urls.static import static
from django.urls import path
from django.views.generic import TemplateView

from .views import list_view, OcorrenciaCreateView, \
    add_photo_to_ocorrencia, add_vehicle_to_ocorrencia, add_fim_to_ocorrencia, send_ocorrencia_view, \
        view_email, detail_view, edit_view, test_pdf, generate_pdf, SelectRodoviaListView, before_update_request,\
            send_update_request, generate_report, add_congest_to_ocorrencia, add_atualizacao_to_ocorrencia,\
                download_csv, add_interdicao_to_ocorrencia, validate_photo, delete_photo, show_photo_hr,\
                    download_excel_ocs_filtradas

app_name = 'ocorrencias'

urlpatterns = [
    path('', list_view, name='list'),
    path('download-csv', download_csv, name='download-csv'),
    path('select', SelectRodoviaListView.as_view(), name='select'),
    # path('relatorio', generate_report, name='relatorio'),
    path('ocs-filtradas-planilha', download_excel_ocs_filtradas, name='planilha_ocs_filtradas'),
    path('<int:rod_id>/new', OcorrenciaCreateView.as_view(), name='create-ocorrencia'),
    path('<str:oc_slug>/foto/<int:pk>', show_photo_hr, name='ver-foto'),
    path('<str:oc_slug>/foto/<int:pk>/validar-foto', validate_photo, name='validar-foto'),
    path('<str:oc_slug>/foto/<int:pk>/apagar-foto', delete_photo, name='apagar-foto'),
    path('<str:oc_slug>/<int:rod_id>/edit', edit_view, name='edit'),
    path('<str:oc_slug>/add-atualizacao', add_atualizacao_to_ocorrencia, name='add-atualizacao'),
    path('<str:oc_slug>/add-congest', add_congest_to_ocorrencia, name='add-congest'),
    path('<str:oc_slug>/add-interdicao', add_interdicao_to_ocorrencia, name='add-interdicao'),
    path('<str:oc_slug>/add-photo', add_photo_to_ocorrencia, name='add-photo'),
    path('<str:oc_slug>/add-vehicle', add_vehicle_to_ocorrencia, name='add-vehicle'),
    path('<str:oc_slug>/add-fim', add_fim_to_ocorrencia, name='add-fim'),
    # path('<str:oc_slug>/send-email', send_ocorrencia_view, name='send-email'),
    # path('<str:oc_slug>/update-request', before_update_request, name='update-request'),
    # path('<str:oc_slug>/send-update-request', send_update_request, name='send-update-request'),
    # path('<str:oc_slug>/email', view_email, name='view-email'),
    path('<str:oc_slug>/select',  SelectRodoviaListView.as_view(), name='select-edit'),
    # path('<str:oc_slug>/pdf', test_pdf, name='test-pdf'),
    # path('<str:oc_slug>/download-pdf', generate_pdf, name='pdf'),
    path('<str:oc_slug>', detail_view, name='detail'),
]
