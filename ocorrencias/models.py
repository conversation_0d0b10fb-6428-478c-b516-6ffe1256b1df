import base64
from decimal import Decimal
from typing import Tuple
import uuid
import pathlib
import datetime
import sys
from enum import Enum
from itertools import chain
from django.db import models
from django.db.models import F, Q, Sum
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.validators import MaxValueValidator, MinValueValidator
from model_utils.managers import InheritanceManager
from django.core.exceptions import EmptyResultSet, ValidationError


from PIL import Image
from PIL import ImageFont
from PIL import ImageDraw
from io import BytesIO
from django.core.files.uploadedfile import InMemoryUploadedFile

from .validators import validate_file_extension, validate_file_size
from rodovias.models import Rodovia, Trecho


def get_file_path(instance, filename):
    filename = f'{uuid.uuid4()}.pdf'
    filepath = f'ocorrencias/arquivos/{filename}'
    return filepath


def validate_image(fieldfile_obj):
    filesize = fieldfile_obj.file.size
    megabyte_limit = 3.0
    if filesize > megabyte_limit*1024*1024:
        raise ValidationError("O tamanho máximo da foto é %sMB" % str(megabyte_limit))


class Classe(models.TextChoices):
    ACIDENTE = 'Acidente', 'Acidente'
    EVENTO_NATURAL = 'Evento natural', 'Evento natural'
    OBRA = 'Obra', 'Obra'
    OCORRENCIA = 'Ocorrência', 'Ocorrência'

class Criticidade(models.TextChoices):
    LEVE = 'Leve', 'Leve'
    MODERADA = 'Moderada', 'Moderada'
    ALTA = 'Alta', 'Alta'


class Bloqueio(models.TextChoices):
    APENAS_ACOSTAMENTO = 'Acostamento', 'Acostamento'
    ALCA_PARCIAL = 'Alça parcial', 'Alça parcial'
    ALCA_TOTAL = 'Alça total', 'Alça total'
    FAIXA_1 = 'Faixa 1', 'Faixa 1'
    FAIXA_2 = 'Faixa 2', 'Faixa 2'
    FAIXA_3 = 'Faixa 3', 'Faixa 3'
    FAIXA_4 = 'Faixa 4', 'Faixa 4'
    FAIXA_5 = 'Faixa 5', 'Faixa 5'
    FAIXA_6 = 'Faixa 6', 'Faixa 6'
    FAIXA_ADICIONAL = 'Faixa adicional', 'Faixa adicional'
    FAIXA_ZEBRADA = 'Faixa zebrada', 'Faixa zebrada'
    FAIXA_DE_ACELERACAO = 'Faixa de aceleração', 'Faixa de aceleração'
    FAIXA_DE_DESACELERACAO = 'Faixa de desaceleração', 'Faixa de desaceleração'
    PRACA_DE_PEDAGIO = 'Praça de pedágio', 'Praça de pedágio'
    TODAS_AS_FAIXAS_EXCETO_AC = 'Todas as faixas', 'Todas as faixas'
    TODAS_AS_FAIXAS_TRECHO_SEM_AC = 'Todas as faixas (trecho sem AC)', 'Todas as faixas (trecho sem AC)'
    TODAS_AS_FAIXAS_E_ACOSTAMENTO = 'Todas as faixas e acostamento', 'Todas as faixas e acostamento'


class BloqueioResumido(Enum):
    TOTAL = "Total"
    TOTAL_ALCA = "Total (Alça)"
    PARCIAL_ALCA = "Parcial (Alça)"
    PRACA_PEDAGIO = "Praca de pedágio"
    APENAS_AC_LIVRE = "Apenas AC livre"
    PARCIAL = "Parcial"
    ACOSTAMENTO = "Acostamento"
    NAO = "Não"


class Ocorrencia(models.Model):
    class Classe(models.TextChoices):
        ACIDENTE = 'Acidente', 'Acidente'
        EVENTO_NATURAL = 'Evento natural', 'Evento natural'
        OBRA = 'Obra', 'Obra'
        OCORRENCIA = 'Ocorrência', 'Ocorrência'

    class Fields(models.TextChoices):
        INFORMACOES_GERAIS = 'INFORMACOES_GERAIS', 'INFORMACOES_GERAIS'

    ORIGEMCCI_CHOICES = [
        ('Concessionária', 'Concessionária'),
        ('Imprensa', 'Imprensa'),
        ('ARTESP', 'ARTESP'),
        ('Outro', 'Outro'),
        ('Desconhecido', 'Desconhecido'),
    ]
    PISTA_CHOICES = [
        ('DUPLA', 'DUPLA'),
        ('SIMPLES', 'SIMPLES'),
        ('PLANEJADA', 'PLANEJADA'),
        ('DISPOSITIVO', 'DISPOSITIVO'),
        ('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'),
    ]
    SENTIDO_CHOICES = [
        ('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'),
        ('NORTE', 'NORTE'),
        ('SUL', 'SUL'),
        ('LESTE', 'LESTE'),
        ('OESTE', 'OESTE'),
        ('NORTE/SUL', 'NORTE/SUL'),
        ('LESTE/OESTE', 'LESTE/OESTE'),
        ('EXTERNO', 'EXTERNO'),
        ('INTERNO', 'INTERNO'),
        ('NÃO SE APLICA', 'NÃO SE APLICA'),
    ]
    FAIXAS_LOCAL = [
        ('1', '1'),
        ('2', '2'),
        ('3', '3'),
        ('4', '4'),
        ('5', '5'),
        ('6', '6'),
        ('7', '7'),
        ('8', '8'),
        ('9', '9'),
        ('10', '10'),
        ('11', '11'),
        ('12', '12'),
        ('13', '13'),
        ('14', '14'),
        ('15', '15'),
        ('16', '16'),
        ('17', '17'),
        ('18', '18'),
        ('19', '19'),
        ('20', '20'),
    ]
    FAIXAS_LOCAL_INICIO = [
        ('fx1', 'fx1'),
        ('fx2', 'fx2'),
        ('fx3', 'fx3'),
        ('fx4', 'fx4'),
        ('fx5', 'fx5'),
        ('fx6', 'fx6'),
        ('Alça', 'Alça'),
        ('Acostamento', 'Acostamento'),
        ('Faixa adicional', 'Faixa adicional'),
        ('Faixa zebrada', 'Faixa zebrada'),
        ('Faixa de aceleração', 'Faixa de aceleração'),
        ('Faixa de desaceleração', 'Faixa de desaceleração'),
        ('Fora da pista', 'Fora da pista'),
        ('Canteiro lateral', 'Canteiro lateral'),
        ('Canteiro central', 'Canteiro central'),
        ('Praça Pedágio', 'Praça Pedágio'),
    ]
    CLIMA_CHOICES = [
        ('Chuva', 'Chuva'),
        ('Garoa', 'Garoa'),
        ('Neblina', 'Neblina'),
        ('Nublado', 'Nublado'),
        ('Trechos com chuva', 'Trechos com chuva'),
        ('Trechos com garoa', 'Trechos com garoa'),
        ('Trechos com neblina', 'Trechos com neblina'),
        ('Trechos com garoa e neblina', 'Trechos com garoa e neblina'),
        ('Trechos com chuva e neblina', 'Trechos com chuva e neblina'),
        ('Trechos com chuva e nublado', 'Trechos com chuva e nublado'),
        ('Chuva com ventania', 'Chuva com ventania'),
        ('Chuva torrencial', 'Chuva torrencial'),
        ('Sol', 'Sol'),
        ('Tempo bom', 'Tempo bom'),
        ('Vento forte', 'Vento forte'),
        ('Desconhecidas', 'Desconhecidas'),
    ]
    SUBCLASSE_CHOICES = [
        ('Não se aplica', 'Não se aplica'),
        ('Em averiguação', 'Em averiguação'),
        ('Aferição de Radar', 'Aferição de Radar'),
        ('Alagamento', 'Alagamento'),
        ('Ambulante no Trecho', 'Ambulante no Trecho'),
        ('Animal na rodovia', 'Animal na rodovia'),
        ('Apoio Operacional', 'Apoio Operacional'),
        ('Apoio PMRv', 'Apoio PMRv'),
        ('Apreensão de Animais', 'Apreensão de Animais'),
        ('Apreensão de Veículo', 'Apreensão de Veículo'),
        ('Assalto à Praça de Pedágio', 'Assalto à Praça de Pedágio'),
        ('Assalto', 'Assalto'),
        ('Atendimento Clínico - Funcionário', 'Atendimento Clínico - Funcionário'),
        ('Atendimento Clínico - Usuário', 'Atendimento Clínico - Usuário'),
        ('Bateria Descarregada', 'Bateria Descarregada'),
        ('Barreira Sanitária', 'Barreira Sanitária'),
        ('Carga Especial', 'Carga Especial'),
        ('Carreta Em L', 'Carreta Em L'),
        ('Congestionamento', 'Congestionamento'),
        ('Conservação', 'Conservação'),
        ('Construção/Recuperação', 'Construção/Recuperação'),
        ('Derramamento de Carga', 'Derramamento de Carga'),
        ('Deslizamento de talude', 'Deslizamento de talude'),
        ('Deslizamento De Terra Ou Mat. Rochoso', 'Deslizamento De Terra Ou Mat. Rochoso'),
        ('Erosão', 'Erosão'),
        ('Evento', 'Evento'),
        ('Excesso de Veículos', 'Excesso de Veículos'),
        ('Fiscalização da Área de Domínio', 'Fiscalização da Área de Domínio'),
        ('Furto ou Roubo de Equipamentos ITS', 'Furto ou Roubo de Equipamentos ITS'),
        ('Incêndio de Grande Porte', 'Incêndio de Grande Porte'),
        ('Incêndio de Médio Porte', 'Incêndio de Médio Porte'),
        ('Incêndio de Pequeno Porte', 'Incêndio de Pequeno Porte'),
        ('Incêndio em Veículo', 'Incêndio em Veículo'),
        ('Incidente com Veículo Canavieiro', 'Incidente com Veículo Canavieiro'),
        ('Incidente', 'Incidente'),
        ('Indefinida', 'Indefinida'),
        ('Interdição', 'Interdição'),
        ('Intervenção viária', 'Intervenção viária'),
        ('Mal Súbito', 'Mal Súbito'),
        ('Manifestação', 'Manifestação'),
        ('Manutenção', 'Manutenção'),
        ('Monitoramento de rodovia', 'Monitoramento de rodovia'),
        ('Objeto contra veículo', 'Objeto contra veículo'),
        ('Objeto na Pista', 'Objeto na Pista'),
        ('Obra', 'Obra'),
        ('Obras na Rodovia', 'Obras na Rodovia'),
        ('Ocorrência Policial', 'Ocorrência Policial'),
        ('Óleo na Pista', 'Óleo na Pista'),
        ('Operação de trânsito', 'Operação de trânsito'),
        ('Operação especial', 'Operação especial'),
        ('Pane', 'Pane'),
        ('Pane Elétrica', 'Pane Elétrica'),
        ('Pane Mecânica', 'Pane Mecânica'),
        ('Pane Seca', 'Pane Seca'),
        ('Pedestre no Trecho', 'Pedestre no Trecho'),
        ('Pneu Furado', 'Pneu Furado'),
        ('Ponto de Interferência', 'Ponto de Interferência'),
        ('Queda de Aeronave', 'Queda de Aeronave'),
        ('Queda de Árvore', 'Queda de Árvore'),
        ('Queda de Barreira', 'Queda de Barreira'),
        ('Queda de Talude', 'Queda de Talude'),
        ('Reintegração de Posse', 'Reintegração de Posse'),
        ('Rodeiro desatrelado', 'Rodeiro desatrelado'),
        ('Simulação', 'Simulação'),
        ('Suicídio', 'Suicídio'),
        ('Super Aquecimento de Motor', 'Super Aquecimento de Motor'),
        ('Tapa Buraco', 'Tapa Buraco'),
        ('Tentativa de Suicídio', 'Tentativa de Suicídio'),
        ('Vandalismo', 'Vandalismo'),
        ('Veículo Abandonado', 'Veículo Abandonado'),
        ('Veículo Atolado', 'Veículo Atolado'),
        ('Veículo sobre Faixa de Rolamento', 'Veículo sobre Faixa de Rolamento'),
    ]

    CLASSE_ACIDENTES_CHOICES = [
        ('Em averiguação', 'Em averiguação'),
        ('Atropelamento', 'Atropelamento'),
        ('Capotamento', 'Capotamento'),
        ('Choque', 'Choque'),
        ('Colisão', 'Colisão'),
        ('Engavetamento', 'Engavetamento'),
        ('Outras', 'Outras'),
        ('Queda', 'Queda'),
        ('Queda de aeronave', 'Queda de aeronave'),
        ('Saída de Pista', 'Saída de Pista'),
        ('Sem informação', 'Sem informação'),
        ('Sequência', 'Sequência'),
        ('Submersão', 'Submersão'),
        ('Suicídio', 'Suicídio'),
        ('Tombamento', 'Tombamento'),
    ]
    SUBCLASSES_ACIDENTES_CHOICES = [
        ('Em averiguação', 'Em averiguação'),
        ('Atropelamento de ambulante', 'Atropelamento de ambulante'),
        ('Atropelamento de andarilho', 'Atropelamento de andarilho'),
        ('Atropelamento de animal', 'Atropelamento de animal'),
        ('Atropelamento de ciclista/esportista',
         'Atropelamento de ciclista/esportista'),
        ('Atropelamento de funcionário', 'Atropelamento de funcionário'),
        ('Atropelamento de morador/trabalhador/estudante',
         'Atropelamento de morador/trabalhador/estudante'),
        ('Atropelamento de pedestre', 'Atropelamento de pedestre'),
        ('Atropelamento de prestador de serviço', 'Atropelamento de prestador de serviço'),
        ('Atropelamento de usuário', 'Atropelamento de usuário'),
        ('Atropelamento de romeiro', 'Atropelamento de romeiro'),
        ('Atropelamento sem informação', 'Atropelamento sem informação'),
        ('Atropelamento de suicida', 'Atropelamento de suicida'),
        ('Capotamento de veículo', 'Capotamento de veículo'),
        ('Choque com árvore', 'Choque com árvore'),
        ('Choque com barreira', 'Choque com barreira'),
        ('Choque com cabine de pedágio', 'Choque com cabine de pedágio'),
        ('Choque com cancela de pista de pedágio',
         'Choque com cancela de pista de pedágio'),
        ('Choque com caixa de captação/fibra',
         'Choque com caixa de captação/fibra'),
        ('Choque com canaleta', 'Choque com canaleta'),
        ('Choque com cerca', 'Choque com cerca'),
        ('Choque com defensa metálica', 'Choque com defensa metálica'),
        ('Choque com barreira de concreto', 'Choque com barreira de concreto'),
        ('Choque com submarino', 'Choque com submarino'),
        ('Choque com meio-fio', 'Choque com meio-fio'),
        ('Choque com elemento de drenagem', 'Choque com elemento de drenagem'),
        ('Choque com equipamento', 'Choque com equipamento'),
        ('Choque com OAE (Ponte/Viaduto/Pass)',
         'Choque com OAE (Ponte/Viaduto/Pass)'),
        ('Choque com objeto fixo', 'Choque com objeto fixo'),
        ('Choque com objeto na pista', 'Choque com objeto na pista'),
        ('Choque com outros', 'Choque com outros'),
        ('Choque com poste', 'Choque com poste'),
        ('Choque com talude ou barranco', 'Choque com talude ou barranco'),
        ('Choque com veículo na pista', 'Choque com veículo na pista'),
        ('Choque com veículo estacionado', 'Choque com veículo estacionado'),
        ('Choque contra atenuador de impacto',
         'Choque contra atenuador de impacto'),
        ('Choque contra placa de sinalização',
         'Choque contra placa de sinalização'),
        ('Colisão frontal', 'Colisão frontal'),
        ('Colisão lateral', 'Colisão lateral'),
        ('Colisão transversal', 'Colisão transversal'),
        ('Colisão traseira', 'Colisão traseira'),
        ('Colisões múltiplas', 'Colisões múltiplas'),
        ('Indefinido', 'Indefinido'),
        ('Objeto lançado contra veículo', 'Objeto lançado contra veículo'),
        ('Queda de avião', 'Queda de avião'),
        ('Queda de avião civil', 'Queda de avião civil'),
        ('Queda de avião militar', 'Queda de avião militar'),
        ('Queda de helicóptero', 'Queda de helicóptero'),
        ('Queda de OAE', 'Queda de OAE'),
        ('Queda de veículo em ribanceira', 'Queda De veículo em ribanceira'),
        ('Sem informação', 'Sem informação'),
        ('Suicídio/atropelamento', 'Suicídio/atropelamento'),
        ('Suicídio/pulo de OAE', 'Suicídio/pulo de OAE'),
        ('Tombamento de bicicleta', 'Tombamento de bicicleta'),
        ('Tombamento de moto', 'Tombamento de moto'),
        ('Tombamento de ônibus', 'Tombamento de ônibus'),
        ('Tombamento de utilitária', 'Tombamento de utilitária'),
        ('Tombamento de veículo', 'Tombamento de veículo'),
        ('Tombamento de veículo leve', 'Tombamento de veículo leve'),
        ('Tombamento de veículo pesado', 'Tombamento de veículo pesado')
    ]
    bloqueio = models.CharField(
        _("Bloqueio"), max_length=255, blank=True, null=True, default="Não"
    )
    criticidade = models.CharField(
        _("Criticidade"), max_length=255, blank=True, null=True, default=Criticidade.LEVE
    )
    criticidade_motivos = models.CharField(
        _("Criticidade (motivos)"), max_length=255, blank=True, null=True,
        default="Não atende aos requisitos das criticidades moderada ou crítica"
    )
    criticidade_motivos_qtd = models.PositiveSmallIntegerField(
        _("Criticidade (qtd. motivos)"), null=True,
        default=0
    )
    classe = models.CharField(
        _("Classe OC"), choices=Classe.choices, max_length=255
    )
    subclasse_oc = models.CharField(
        _("Subclasse de OC"), choices=SUBCLASSE_CHOICES, max_length=255, default='Não se aplica'
    )
    subclasse_ac = models.CharField(
        _("Classe do acidente"), choices=CLASSE_ACIDENTES_CHOICES, max_length=255, null=True, blank=True)
    tipo_ac = models.CharField(
        _("Subclasse do acidente"), choices=SUBCLASSES_ACIDENTES_CHOICES, max_length=255, blank=True, null=True)
    complemento = models.CharField(
        _("Complemento da oc."), max_length=255, null=True, blank=True)
    dinamica_oc = models.TextField(_("Dinâmica da oc"), blank=True, null=True)
    slug = models.SlugField(_("Código"), blank=True,
                            null=True, max_length=50, db_index=True,)
    cod_conc = models.CharField(
        _("Código Concessionária"), max_length=50, null=True, blank=True)
    rodovia = models.ForeignKey(
        Rodovia,
        verbose_name='Rodovia', related_name='rodovia', null=True, blank=False, on_delete=models.SET_NULL
    )
    concessionarias = models.ManyToManyField(
        "concessionarias.Concessionaria", verbose_name=_("Concessionarias"), related_name='concessionarias')
    municipios = models.ManyToManyField("municipios.Municipio", verbose_name=_(
        "Municipios"), related_name="municipios")
    km_inicial = models.DecimalField(
        _("KM inicial"), max_digits=8, decimal_places=3)
    km_final = models.DecimalField(
        _("KM final"), max_digits=8, decimal_places=3, blank=True)
    nro_faixas_local = models.CharField(
        _("Nro fxs local"), choices=FAIXAS_LOCAL, max_length=255, blank=True, null=True
    )
    faixa_inicio = models.CharField(
        _("Faixa início oc."), choices=FAIXAS_LOCAL_INICIO, max_length=255, blank=True, null=True
    )
    tem_ac = models.BooleanField(_("Tem AC."), default=True, help_text='Sim, há acostamento no local')
    cond_climaticas = models.CharField(
        _("Cond. climáticas no momento da ocorrência"), choices=CLIMA_CHOICES, max_length=50, blank=False, null=False,
    )
    dt_hr_conhecimento = models.DateTimeField(
        _("Data conhecimento"), auto_now=False, auto_now_add=False)
    dt_hr_oc = models.DateTimeField(
        _("Data início"), auto_now=False, auto_now_add=False, blank=True, null=True)
    dt_hr_termino = models.DateTimeField(
        _("Data término"), auto_now=False, auto_now_add=False, blank=True, null=True)
    publicada = models.BooleanField(_("Publicar"), default=True)
    aprovada = models.BooleanField(_("Aprovada"), default=False)
    sem_vitimas = models.BooleanField(_("Sem vítimas"), default=False, help_text='Oc. sem vítimas/ilesos')
    cad_retroativo = models.BooleanField(_("Cad. retroativo"), default=False, help_text='Sim, é cadastro retroativo')
    origem_comunicacao_conc = models.CharField(
        _("Origem da comunicação"), max_length=255, blank=True, null=True)
    origem_comunicacao_cci = models.CharField(
        _("Origem da inf."), choices=ORIGEMCCI_CHOICES, max_length=255,
        default="CONCESSIONÁRIA", blank=True, null=True
    )
    pista = models.CharField(
        max_length=50,
        choices=PISTA_CHOICES,
        null=True,
        blank=True,
        verbose_name='Pista',
        default="SEM INFORMAÇÃO"
    )
    sentido = models.CharField(
        max_length=20,
        choices=SENTIDO_CHOICES,
        verbose_name='Sentido',
        default="SEM INFORMAÇÃO"
    )
    lat = models.DecimalField(
        _("Latitude"), max_digits=12, decimal_places=7, blank=True, null=True)
    lng = models.DecimalField(
        _("Longitude"), max_digits=12, decimal_places=7, blank=True, null=True)
    #numero_faixas = models.PositiveSmallIntegerField(_("Faixas"), blank=True, null=True)
    #resumo_publico = models.TextField(_("Resumo público"), blank=True, null=True)
    bloq_faixas = models.BooleanField(
        _("Houve bloqueio/interdição de faixas"), blank=False, null=False, default=False)
    det_bloq_faixas = models.CharField(
        _("Detalhes bloqueio de faixas"), max_length=255, blank=True, null=True)
    sinalizacao = models.CharField(
        _("Sinalização"), max_length=50, blank=True, null=True)
    comunicacao_usuario = models.CharField(
        _("Comunicação com usuário"), max_length=50, blank=True, null=True)
    danos_patrimonio = models.BooleanField(
        _("Danos ao patrimônio"), blank=False, null=False, default=False)
    danos_patrimonio_obs = models.CharField(
        _("Danos ao patrimônio obs."), max_length=255, blank=True, null=True)
    recursos_internos = models.CharField(
        _("Recursos internos"), max_length=255, blank=True, null=True)
    recursos_externos = models.CharField(
        _("Recursos externos"), max_length=255, blank=True, null=True)
    acoes_operacionais = models.CharField(
        _("Ações operacionais"), max_length=255, blank=True, null=True)
    #observacoes_conc = models.TextField(_("Observações Conc."), blank=True, null=True)
    observacoes_cci = models.TextField(
        _("Observações CCI"), blank=True, null=True)
    atualizacao_min = models.IntegerField(
        _("Atualização - Periodicidade (minutos)"), default=60, validators=[MaxValueValidator(720)], help_text='Tempo em minutos. Valor máximo: 720min (12 horas)')
    nro_mits = models.PositiveIntegerField(
        _("Nro MITS"), blank=True, null=True)
    arquivo = models.FileField(
        _("Arquivo complementar PDF (máx 1 arq. < 10mb)"), upload_to=get_file_path, validators=[validate_file_extension, validate_file_size],
        null=True, blank=True
    )
    arquivo_desc = models.CharField(
        _("Descrição do arquivo"), max_length=50, blank=True, null=True)
    video_link = models.URLField(
        _("Vídeo (link)"), max_length=200, null=True, blank=True,
        help_text="Link para acesso à gravação"
    )
    fiscal_dpl_local = models.BooleanField(
        _("Fiscal(ais) da DPL no local"), default=False,
        help_text="Sim, há fiscal(ais) da DPL no local"
    )
    criado_em = models.DateTimeField(
        auto_now_add=True, verbose_name='Criado em')
    criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='criador'
    )
    atualizado_em = models.DateTimeField(
        auto_now=True, null=True, blank=True, verbose_name='Atualizado em')
    atualizado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Atualizado por', related_name='atualizador'
    )

    objects = InheritanceManager()
    # objects = CustomManager()
    # Example usage QueryManager
    # public = QueryManager(published=True).order_by('-pub_date')

    class Meta:
        abstract: True
        ordering: ['-pk']
        verbose_name = 'Ocorrência'
        verbose_name_plural = 'Ocorrências'

    def __str__(self):
        return f'{self.slug.upper()} | {self.classe} | {self.rodovia.codigo} | {self.km_inicial} | {self.dt_hr_oc}'

    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('home-ocorrencia-detail', args=[str(self.slug)])
    
    def get_trechos(self, tipo=None):
        if self.km_final is None:
            self.km_final = self.km_inicial

        ordenacao = None
        sentido = self.sentido if self.sentido != 'SEM INFORMAÇÃO' else None

        result = self.rodovia.trechos.all()\
                    .filter(sentido=sentido)\
                    .filter(concessionaria__fiscalizacao_artesp=True)

        if self.km_inicial > self.km_final:
            # self.km_inicial, self.km_final = self.km_final, self.km_inicial
            ordenacao = 'DECRESCENTE'
        
        elif self.km_inicial < self.km_final:
            ordenacao = 'CRESCENTE'

        else:
            ordenacao = result.values_list('sentido_ordem', flat=True).first()
        
        
        if ordenacao == 'DECRESCENTE':
            result = result\
                    .filter(km_inicial__gte=self.km_inicial).filter(km_final__lte=self.km_final)
        else:
            result = result\
                    .filter(km_final__gte=self.km_inicial).filter(km_inicial__lte=self.km_final)
        # print('result')
        # print(result)

        if result.count() < 1:
            raise EmptyResultSet('Não foram encontrados trechos com as características informadas.')       
        else:
            if tipo == 'concessionaria':
                return result.prefetch_related('concessionaria')
            if tipo == 'municipio':
                return result.prefetch_related('municipio')
            return result
    
    def get_trechos_template(self, tipo=None):
        if self.km_final is None:
            self.km_final = self.km_inicial

        ordenacao = None
        sentido = self.sentido if self.sentido != 'SEM INFORMAÇÃO' else None

        result = self.rodovia.trechos.all().filter(sentido=sentido)

        if self.km_inicial > self.km_final:
            # self.km_inicial, self.km_final = self.km_final, self.km_inicial
            ordenacao = 'DECRESCENTE'
        
        elif self.km_inicial < self.km_final:
            ordenacao = 'CRESCENTE'

        else:
            ordenacao = result.values_list('sentido_ordem', flat=True).first()
        
        
        if ordenacao == 'DECRESCENTE':
            result = result\
                    .filter(km_inicial__gte=self.km_inicial).filter(km_final__lte=self.km_final)
        else:
            result = result\
                    .filter(km_final__gte=self.km_inicial).filter(km_inicial__lte=self.km_final)
        # print('result')
        # print(result)

        if result.count() < 1:
            return Trecho.objects.none()        
        else:
            if tipo == 'concessionaria':
                return result.prefetch_related('concessionaria')
            if tipo == 'municipio':
                return result.prefetch_related('municipio')
            return result

    def get_municipios(self):
        trechos = self.get_trechos('municipio')
        municipios = [trecho.municipio for trecho in trechos]
        return list(set(municipios))

    def get_municipios_ids(self):
        trechos = self.get_trechos('municipio')
        municipios = trechos.values_list('municipio__pk', flat=True)
        return municipios

    def get_municipios_names(self):
        trechos = self.get_trechos('municipio')
        mun_names = trechos.values_list('municipio__nome', flat=True)
        return ", ".join(mun_names)

    def get_concessionarias(self):
        trechos = self.get_trechos('concessionaria')
        filtered_concessionarias = {
            trecho.concessionaria
            for trecho in trechos
            if trecho.concessionaria.fiscalizacao_artesp
            and trecho.dt_fim is None
        }
        return list(filtered_concessionarias)


    def get_concessionarias_names(self):
        trechos = self.get_trechos('concessionaria')
        concessionarias = trechos.values_list(
            'concessionaria__nome', flat=True)
        return list(set(concessionarias))

    def get_concessionarias_ids(self):
        trechos = self.get_trechos('concessionaria')
        concessionarias = trechos.values_list('concessionaria__pk', flat=True)
        return list(set(concessionarias))

    def get_coords(self):
        return f'{self.lat},{self.lng}'

    def get_icon(self):
        if self.classe == self.Classe.ACIDENTE:
            return 'images/ocorrencias/icones/acidente.png'
        elif self.classe == self.Classe.OBRA:
            return 'images/ocorrencias/icones/obra.png'
        else:
            return 'images/ocorrencias/icones/atencao.png'
    
    def get_wallmap_icon(self):
        if self.classe == self.Classe.ACIDENTE:
            if not self.dt_hr_termino:
                return 'images/ocorrencias/wallmap/caraccident_red.png'
            return 'images/ocorrencias/wallmap/caraccident_green.png'
        elif self.classe == self.Classe.OBRA:
            if not self.dt_hr_termino:
                return 'images/ocorrencias/wallmap/construction_red.png'
            return 'images/ocorrencias/wallmap/construction_green.png'
        elif self.classe == self.Classe.EVENTO_NATURAL:
            if not self.dt_hr_termino:
                return 'images/ocorrencias/wallmap/fallingrocks_red.png'
            return 'images/ocorrencias/wallmap/fallingrocks_green.png'
        else:
            if not self.dt_hr_termino:
                return 'images/ocorrencias/wallmap/caution_red.png'
            return 'images/ocorrencias/wallmap/caution_green.png'

    def get_sigla(self):
        if self.classe == self.Classe.ACIDENTE:
            return f'ACI'
        # if self.classe == self.Classe.EVENTO:
        #     return f'EVE'
        if self.classe == self.Classe.OBRA:
            return f'OBR'
        # if self.classe == self.Classe.BARREIRA_SANITARIA:
        #     return f'BSA'
        # if self.classe == self.Classe.QUEDA_BARREIRA:
        #     return f'QDB'
        # if self.classe == self.Classe.EROSAO:
        #     return f'ERO'
        # if self.classe == self.Classe.INCENDIO:
        #     return f'INC'
        # if self.classe == self.Classe.PTO_INTERFERENCIA:
        #     return f'PTI'
        # if self.classe == self.Classe.CONGESTIONAMENTO:
        #     return f'CON'
        else:
            return 'OCO'

    def get_congestionamento(self):
        if self.congestionamento.all().count():
            total = 0
            for cong in self.congestionamento.all():
                total += cong.get_total()
            return "{:.3f}".format(total)
        else:
            return None
    
    def get_tem_congestionamento_ou_lento_maior_ou_igual_que_n_quilometros(self, congestionamentos, n_quilometros) -> bool:
        total_geral = 0
        if congestionamentos:
            for cong in congestionamentos:
                if cong.trafego in ['LENTO', 'CONGESTIONADO']:
                    total = cong.get_total()
                    if total:
                        total_geral += total
        return total_geral >= n_quilometros

    def generate_vitimas_str_template(self, lista_vitimas):
        if lista_vitimas:
            vitimas = lista_vitimas.values('tipo').annotate(qtd=Sum('qtd')).order_by()
            str_list = []
            for vit in vitimas:
                str_list.append(f"{vit.get('tipo', '').title()} ({vit.get('qtd', '')})")
            return " | ".join(str_list)
        return None

    def generate_vitimas_str_template_zap(self, lista_vitimas):
        if lista_vitimas:
            vitimas = lista_vitimas.values('tipo').annotate(qtd=Sum('qtd')).order_by()
            str_list = []
            for vit in vitimas:
                str_list.append(f"{vit.get('tipo', '').title()} ({vit.get('qtd', '')})")
            result = " | ".join(str_list)
            return "Vítima(s):\n" + result
        return None
    
    def generate_vitimas_str_template_table(self, lista_vitimas):
        if lista_vitimas:
            vitimas = lista_vitimas.values('tipo').annotate(qtd=Sum('qtd')).order_by()
            str_list = []
            for vit in vitimas:
                str_list.append(f"{vit.get('tipo', '').title()} ({vit.get('qtd', '')})")
            result = " | ".join(str_list)
            return "" + result
        return None
    
    def is_closed_cond_trafego(self):
        all_closed = True
        for congest in self.congestionamento.all():
            if not congest.dt_hora_inicio or not congest.dt_hora_termino:
                all_closed = False
        return all_closed

    def get_veiculos_resumo_str(self):
        veiculos = self.veiculos.all()
        if veiculos:
            total = {}
            for veic in veiculos:
                if veic.tipo in total.keys():
                    total[veic.tipo] += 1
                else:
                    total[veic.tipo] = 1
            return ' | '.join(f"{k.title()}: {v}" for k, v in total.items())
        else:
            return ""

    def get_veiculos_resumo_dict(self):
        veiculos = self.veiculos.all()
        total = {}
        if veiculos:
            for veic in veiculos:
                if veic.tipo in total.keys():
                    total[veic.tipo] += 1
                else:
                    total[veic.tipo] = 1
        return total
    
    def get_tem_vazamento_prod_perigoso(self, veiculos) -> bool:
        if veiculos:
            for veic in veiculos:
                if veic.derramamento and veic.prod_perigo:
                    return True
        return False

    def get_status_str(self):
        if not self.dt_hr_termino:
            return 'Em andamento'
        else:
            return 'Finalizada'

    def desatualizada(self):
        """
        Uma ocorrência ativa e sem atualização por mais tempo que o definido 
        em  `atualizacao_min` será considerada desatualizada
        """
        total_seconds = (timezone.now() - self.atualizado_em).total_seconds()
        total_minutes = total_seconds / 60
        if (total_minutes > self.atualizacao_min):
            if self.dt_hr_termino:
                return False
            else:
                return True
        else:
            return False

    def get_ultima_atualizacao(self):
        return self.atualizado_em

    def get_tem_vitima_fatal(self, vitimas) -> Tuple[bool,int]:
        if vitimas:
            for vitima in vitimas:
                if vitima.tipo == "FATAL" and vitima.qtd > 0:
                    return True, vitima.qtd
        return False, 0
    
    def get_eh_atropelamento(self) -> bool:
        if self.classe == Classe.ACIDENTE and self.subclasse_ac == 'Atropelamento':
            return True
        return False

    def get_eh_manifestacao(self):
        return self.subclasse_oc == "Manifestação"

    
    def get_criticidade_com_motivos(self, congestionamentos, fim, interdicoes, veiculos) -> Tuple[str, str, int]:
        # print('criticidade com motivos')
        # return (None, None, None)
        """
        Cálcula a criticidade de uma ocorrência:
        2.	MODERADA =
        a.	qualquer trecho com congestionamento >= 5km OU 
        b.	bloqueio de fx rolamento parcial (INTERD. PARCIAL) >= 20 min OU
        c.	vazamento de produto perigoso OU 
        d.	interd. na PRACA PEDAGIO  >= 20 min. OU 
        e.	envolve ÔNIBUS/MICRO/VAN;
        f.  subclasse = MANIFESTAÇÃO

        3.	CRÍTICA = 
        a.	interd. Total com mais de 20min. OU 
        b.	interd. total (ALÇA) com mais de 20 min. OU 
        c.	vítima fatal

        """
        CONGESTIONAMENTO_MIN = 3
        TEMPO_MAX_MINUTOS_INTERDICAO_PARCIAL = 30
        TEMPO_MAX_MINUTOS_INTERDICAO_TOTAL = 30
        TEMPO_MAX_MINUTOS_PP = 30
        eh_manifestacao = self.get_eh_manifestacao()
        tem_vitima_fatal, qtd_fatais = self.get_tem_vitima_fatal(fim)
        eh_atropelamento = self.get_eh_atropelamento()
        congest_maior_que_5km = self.get_tem_congestionamento_ou_lento_maior_ou_igual_que_n_quilometros(
            congestionamentos, CONGESTIONAMENTO_MIN)
        tem_interdicao_parcial_t_maior_n_min = self.\
            get_tem_interdicao_parcial_ativa_ou_maior_que_n_minutos(
                interdicoes, TEMPO_MAX_MINUTOS_INTERDICAO_PARCIAL)
        tem_interdicao_total_t_maior_n_min = self.\
            get_tem_interdicao_total_ativa_ou_maior_que_n_minutos(interdicoes, TEMPO_MAX_MINUTOS_INTERDICAO_TOTAL)
        tem_interdicao_praca_pedagio = self.get_tem_interdicao_praca_pedagio(interdicoes, TEMPO_MAX_MINUTOS_PP)
        tem_vazamento_prod_perigoso = self.get_tem_vazamento_prod_perigoso(veiculos)
        tem_envolvimento_onibus = self.get_tem_envolvimento_de_onibus_ou_van(veiculos)
        tem_envolvimento_veic_oficial = self.get_tem_envolvimento_veiculo_oficial(veiculos)
        container_motivos = []
        if tem_vitima_fatal or tem_interdicao_total_t_maior_n_min:
            if tem_vitima_fatal:
                if qtd_fatais > 1:
                    container_motivos.append('Vítimas fatais')
                else:
                    container_motivos.append('Vítima fatal')
            if tem_interdicao_total_t_maior_n_min:
                container_motivos.append(f'Interdição total t ≥ {TEMPO_MAX_MINUTOS_INTERDICAO_TOTAL}min ou ativa')
            return (Criticidade.ALTA, ', '.join(container_motivos), len(container_motivos))
        if congest_maior_que_5km or tem_interdicao_parcial_t_maior_n_min\
                or tem_interdicao_praca_pedagio or tem_vazamento_prod_perigoso \
                    or tem_envolvimento_onibus or tem_envolvimento_veic_oficial or eh_manifestacao or eh_atropelamento:
            if congest_maior_que_5km:
                container_motivos.append(f'Extensão do congestionamento ou lentidão ≥ {CONGESTIONAMENTO_MIN}km')
            if tem_interdicao_parcial_t_maior_n_min:
                container_motivos.append(f'Interdição parcial t ≥ {TEMPO_MAX_MINUTOS_INTERDICAO_PARCIAL}min ou ativa')
            if tem_vazamento_prod_perigoso:
                container_motivos.append('Vazamento prod. perigoso')
            if tem_interdicao_praca_pedagio:
                container_motivos.append(f'Interdição em PP t ≥ {TEMPO_MAX_MINUTOS_PP}min ou ativa')
            if tem_envolvimento_onibus:
                container_motivos.append('Envolve veíc. ônibus/van/etc')
            if tem_envolvimento_veic_oficial:
                container_motivos.append('Envolve veíc. oficial')
            if eh_manifestacao:
                container_motivos.append('Manifestação')
            if eh_atropelamento:
                container_motivos.append('Atropelamento')

            return (Criticidade.MODERADA, ', '.join(container_motivos), len(container_motivos))
        return (Criticidade.LEVE, "Não atende aos requisitos das criticidades moderada ou alta", 0)

    def get_criticidade_com_motivos_no_args(self) -> Tuple[str, str, int]:
        fim = self.fim.all()
        veiculos = self.veiculos.all()
        interdicoes = self.interdicao.all()
        congestionamentos = self.congestionamento.all()
        return self.get_criticidade_com_motivos(congestionamentos, fim, interdicoes, veiculos)
    
    def get_url_ultima_foto(self):
        if self.tem_foto():
            url = self.fotos.all().last().get_absolute_image_url
            return url
        return ''
    
    def tem_foto(self):
       return bool(self.fotos.all().count())

    def get_criticidade_icone(self):
        if self.criticidade == Criticidade.ALTA:
            return "🔴"
        if self.criticidade == Criticidade.MODERADA:
            return "🟡"
        return "🟢"
    

    def get_criticidade_icone_atualizacao(self):
        if self.criticidade == Criticidade.ALTA:
            return "🟥"
        if self.criticidade == Criticidade.MODERADA:
            return "🟨"
        return "🟩"
    
    def get_criticidade_icone_list(self):
        if self.criticidade == Criticidade.ALTA:
            return "🔴"
        if self.criticidade == Criticidade.MODERADA:
            return "🟡"
        return "🟢"
    
    def get_criticidade_bg_color(self):
        if self.criticidade == Criticidade.ALTA:
            return "bg-red-800"
        if self.criticidade == Criticidade.MODERADA:
            return "bg-yellow-600"
        return "bg-green-800"


    def get_subclasse(self):
        if self.classe == self.Classe.ACIDENTE:
            return self.subclasse_ac
        return self.subclasse_oc

    def has_conc(self, conc_id):
        concessionarias_ids = self.get_concessionarias_ids()
        if conc_id in concessionarias_ids:
            return True
        return False

    def get_bloqueio(self, interdicoes):
        bloqueio_parcial = False
        bloqueio_total = False
        bloqueio_fxs_rolamento = False
        bloqueio_ac = False
        bloqueio_alca_total = False
        bloqueio_alca_parcial = False
        bloqueio_praca_pedagio = False
        todas_as_faixas_exceto_ac = Bloqueio.TODAS_AS_FAIXAS_EXCETO_AC # "Todas as faixas"
        todas_as_faixas_e_acostamento = Bloqueio.TODAS_AS_FAIXAS_E_ACOSTAMENTO
        todas_as_faixas_trecho_sem_ac = Bloqueio.TODAS_AS_FAIXAS_TRECHO_SEM_AC
        alca_total = Bloqueio.ALCA_TOTAL
        alca_parcial = Bloqueio.ALCA_PARCIAL
        praca_pedagio = Bloqueio.PRACA_DE_PEDAGIO
        apenas_ac = Bloqueio.APENAS_ACOSTAMENTO
        for interd in interdicoes.filter(dt_hora_termino__isnull=True):
            if interd.faixa == todas_as_faixas_e_acostamento or interd.faixa == todas_as_faixas_trecho_sem_ac:
                bloqueio_total = True
            elif interd.faixa == todas_as_faixas_exceto_ac:
                bloqueio_fxs_rolamento = True
            elif interd.faixa == alca_total:
                bloqueio_alca_total = True
            elif interd.faixa == alca_parcial:
                bloqueio_alca_parcial = True
            elif interd.faixa == praca_pedagio:
                bloqueio_praca_pedagio = True
            elif interd.faixa == praca_pedagio:
                bloqueio_praca_pedagio = True
            else:
                if interd.faixa == apenas_ac:
                    bloqueio_ac = True
                else:
                    bloqueio_parcial = True
        if bloqueio_total:
            return BloqueioResumido.TOTAL.value
        if bloqueio_alca_total:
            return BloqueioResumido.TOTAL_ALCA.value
        if bloqueio_alca_parcial:
            return BloqueioResumido.PARCIAL_ALCA.value
        if bloqueio_praca_pedagio:
            return BloqueioResumido.PRACA_PEDAGIO.value
        if bloqueio_fxs_rolamento:
            return BloqueioResumido.APENAS_AC_LIVRE.value
        if not bloqueio_total and bloqueio_parcial:
            return BloqueioResumido.PARCIAL.value
        if not bloqueio_total and not bloqueio_parcial and bloqueio_ac:
            return BloqueioResumido.ACOSTAMENTO.value
        return BloqueioResumido.NAO.value

    def get_tem_interdicao_parcial_ativa_ou_maior_que_n_minutos(self, interdicoes, minutos) -> bool:
        if interdicoes:
            for interd in interdicoes.filter(
                Q(faixa=Bloqueio.ALCA_PARCIAL) | 
                Q(faixa=Bloqueio.TODAS_AS_FAIXAS_EXCETO_AC) |
                Q(faixa=Bloqueio.TODAS_AS_FAIXAS_TRECHO_SEM_AC) |
                Q(faixa=Bloqueio.FAIXA_1) |
                Q(faixa=Bloqueio.FAIXA_2) |
                Q(faixa=Bloqueio.FAIXA_3) |
                Q(faixa=Bloqueio.FAIXA_4) |
                Q(faixa=Bloqueio.FAIXA_5) |
                Q(faixa=Bloqueio.FAIXA_6) |
                Q(faixa=Bloqueio.PRACA_DE_PEDAGIO)
            ):
                if interd.bloqueio_ativo_ou_maior_que_n_minutos(minutos):
                    # print(f'interdicao parcial maior que {minutos}')
                    return True
        # print(f'NAO TEM interdicao parcial maior que {minutos}')
        return False

    def get_tem_interdicao_total_ativa_ou_maior_que_n_minutos(self, interdicoes, minutos) -> bool:
        if interdicoes:
            for interd in interdicoes.filter(
                Q(faixa=Bloqueio.TODAS_AS_FAIXAS_TRECHO_SEM_AC) | 
                Q(faixa=Bloqueio.TODAS_AS_FAIXAS_E_ACOSTAMENTO) |
                Q(faixa=Bloqueio.ALCA_TOTAL)
            ):
                if interd.bloqueio_ativo_ou_maior_que_n_minutos(minutos):
                    # print(f'interdicao total maior que {minutos}')
                    return True
        # print(f'NAO TEM interdicao total maior que {minutos}')
        return False
 
    def get_tem_interdicao_praca_pedagio(self, interdicoes, minutos) -> bool:
        if interdicoes:
            for interd in interdicoes.filter(faixa=Bloqueio.PRACA_DE_PEDAGIO):
                if interd.bloqueio_ativo_ou_maior_que_n_minutos(minutos):
                    # print(f'interdicao PP maior que {minutos}')
                    return True
        # print(f'NAO TEM interdicao PP maior que {minutos}')
        return False
   
    def get_bloqueios_txt(self):
        txt_container = []
        contador = 1
        for interd in self.interdicao.all():
            rodovia = interd.rodovia
            sentido = interd.sentido
            faixa = interd.faixa
            hr_bloqueio = f'[Bloqueado em {interd.dt_hora_inicio}]' if interd.dt_hora_inicio is not None else ''
            hr_liberacao = f'[Liberado em {interd.dt_hora_termino}]' if interd.dt_hora_termino is not None else ''
            obs = interd.obs if interd.obs is not None else ''
            if rodovia:
                str_to_append = f'{contador}) {rodovia}/{sentido}/{faixa} {hr_bloqueio} {hr_liberacao} {obs}'
            else:
                str_to_append = f'{contador}) {sentido}/{faixa} {hr_bloqueio} {hr_liberacao} {obs}'
            txt_container.append(str_to_append)
            contador += 1
        return ' || '.join(txt_container)

    def get_bloqueios_txt_zap(self):
        txt_container = []
        total_itens = self.interdicao.all().count()
        if total_itens:
            for interd in self.interdicao.all():
                # pista = interd.pista
                rodovia = interd.rodovia
                sentido = interd.sentido.lower()
                faixa = interd.get_nome_faixa_resumido()
                obs = interd.obs
                if obs and type(obs) == str:
                    obs = obs.lower()
                    if len(obs) > 15:
                        obs = obs[:12] + '...'
                bloqueio = f'[{interd.dt_hora_inicio.strftime("%d/%m %H:%M")}]&nbsp;' if interd.dt_hora_inicio else ''
                liberado = f'{interd.dt_hora_termino.strftime("%d/%m %H:%M")}' if interd.dt_hora_termino else ''
                if rodovia:
                    str_to_append = f'&nbsp;&nbsp;▫️&nbsp;{bloqueio}{rodovia}/{sentido}/{faixa}'
                else:
                    str_to_append = f'&nbsp;&nbsp;▫️&nbsp;{bloqueio}{sentido}/{faixa}'
                if obs:
                    str_to_append += f'/{obs}'
                if liberado:
                    str_to_append += f' | *T*: {liberado}'
                # str_to_append += ''
                total_itens -= 1
                if total_itens > 0:
                    str_to_append += '<br />'
                txt_container.append(str_to_append)
            return ''.join(txt_container)
        return None
    
    def get_tem_envolvimento_de_onibus_ou_van(self, veiculos):
        return bool(
            veiculos.filter(
                Q(tipo="ÔNIBUS") | Q(tipo="MICROÔNIBUS") | Q(tipo="VAN/PERUA TRANSP. COLETIVO"))
        )

    def get_tem_envolvimento_veiculo_oficial(self, veiculos) -> bool:
        return bool(veiculos.filter(veiculo_oficial=True))
    
    def get_template_tem_envolvimento_veiculo_oficial(self) -> bool:
        return bool(self.veiculos.filter(veiculo_oficial=True))
    
    def get_template_nome_veic_oficiais(self) -> str:
        veics = self.veiculos.filter(veiculo_oficial=True).values_list('nome', flat=True)
        list_of_veics = []
        for i, veic in enumerate(veics, start=1):
            list_of_veics.append(f'Veículo ({i}): {veic}')
        return '<br /> '.join(list_of_veics)

    def get_onibus_van_template_str(self):
        veiculos_onibus_ou_van = self.veiculos.all().filter(
            Q(tipo="ÔNIBUS") | Q(tipo="MICROÔNIBUS") | Q(tipo="VAN/PERUA TRANSP. COLETIVO"))
        veiculos_tipos = ', '.join(list(chain.from_iterable(set(veiculos_onibus_ou_van.values_list('tipo')))))
        onibus_ou_van = {}
        if veiculos_onibus_ou_van:
            contador = 0
            for veic in veiculos_onibus_ou_van:
                placa = veic.placa
                origem = veic.origem
                destino = veic.destino
                nome = veic.nome
                if veic.onibus_artesp and veic.obs:
                    contador += 1
                    onibus_ou_van[contador] = (
                        True, 
                        veic.obs.replace("\n", "<br />"), 
                        placa,
                        origem,
                        destino,
                        veic.tipo,
                        nome
                    )
                elif veic.onibus_artesp and not veic.obs:
                    contador += 1
                    onibus_ou_van[contador] = (
                        True,
                        None,
                        placa,
                        origem,
                        destino,
                        veic.tipo,
                        nome
                    )
                elif not veic.onibus_artesp and veic.obs:
                    contador += 1
                    onibus_ou_van[contador] = (
                        False, 
                        veic.obs.replace("\n", "<br />"), 
                        placa,
                        origem,
                        destino,
                        veic.tipo,
                        nome
                    )
                else:
                    contador += 1
                    onibus_ou_van[contador] = (
                        False, 
                        None, 
                        placa,
                        origem,
                        destino,
                        veic.tipo,
                        nome
                    )
        return veiculos_tipos, onibus_ou_van

    def get_congest_txt(self):
        txt_container = []
        total_itens = self.congestionamento.all().count()
        if total_itens:
            for congest in self.congestionamento.all():
                # pista = congest.pista
                rodovia = congest.rodovia
                obs = congest.obs
                sentido = str(congest.sentido).lower()
                trafego = str(congest.get_nome_trafego_resumido()).lower()
                km_inicial = congest.km_inicial
                inicio = f'[{congest.dt_hora_inicio.strftime("%d/%m %H:%M")}]&nbsp;' if congest.dt_hora_inicio else ''
                liberado = f'{congest.dt_hora_termino.strftime("%d/%m %H:%M")}' if congest.dt_hora_termino else ''
                total = congest.get_total()
                str_to_append = '&nbsp;&nbsp;▪️&nbsp;'
                if inicio:
                    str_to_append += f'{inicio}&nbsp;'
                if sentido:
                    if rodovia:
                        str_to_append += f'{rodovia}/{sentido}/'
                    else:
                        str_to_append += f'{sentido}/'
                if trafego:
                    str_to_append += f'{trafego}/'
                if total:
                    str_to_append += f'{round(total,1)}km'
                else:
                    if km_inicial:
                        str_to_append += f'kmi:{round(km_inicial,1)}'
                if obs and type(obs) == str:
                    obs = ' (' + obs.lower() + ')'
                    if len(obs) > 15:
                        obs = obs[:10] + '...)'
                    str_to_append += obs
                if str_to_append.endswith('/'):
                    str_to_append = str_to_append[:-1]
                # str_to_append = str_to_append.lower()
                if liberado:
                    str_to_append += f' | *T*: {liberado}'
                # str_to_append += '&nbsp;]'
                total_itens -= 1
                if total_itens > 0:
                    str_to_append += '<br />'
                txt_container.append(str_to_append)
            return ''.join(txt_container)
        return None

    @staticmethod
    def get_avg_time():
        ocs_dict = Ocorrencia.objects.filter(dt_hr_oc__isnull=False)\
            .filter(dt_hr_conhecimento__isnull=False)\
            .filter(dt_hr_conhecimento__gt=F('dt_hr_oc'))\
            .prefetch_related('concessionarias')\
            .annotate(tempo_aviso=F('dt_hr_conhecimento') - F('dt_hr_oc'))\
            .filter(tempo_aviso__lt=datetime.timedelta(hours=24))\
            .values_list('tempo_aviso', 'concessionarias__nome')
        result_dict = {}
        for (time, conc) in ocs_dict:
            if conc in result_dict:
                result_dict[conc] = (
                    result_dict[conc][0] + time, result_dict[conc][1] + 1)
            else:
                result_dict[conc] = (time, 1)
        final = {}
        for k, (total_time, counter) in result_dict.items():
            final[k] = {'mean_time': round(
                (total_time.total_seconds() / 60) / counter, 0), 'ocs': counter}
            # print('conc: ', k, 'mean_time: ', round((total_time.total_seconds() / 60) / counter, 0), 'ocs: ', counter)
        return {k: v for k, v in sorted(final.items(), key=lambda item: item[1]['mean_time'], reverse=True)}

    def save(self, *args, **kwargs):
        ''' On save, update timestamps '''
        if not self.id:
            self.criado_em = timezone.now()
        self.atualizado_em = timezone.now()
        return super(Ocorrencia, self).save(*args, **kwargs)


def get_slug(instance, filename):
    _suffix = pathlib.Path(filename).suffix
    return f"ocorrencias/fotos/{instance.ocorrencia.slug}/{uuid.uuid4()}{_suffix}"


class Foto(models.Model):

    ocorrencia = models.ForeignKey(Ocorrencia, verbose_name=_(
        "Foto"), related_name='fotos', on_delete=models.CASCADE)

    foto = models.ImageField(
        _("Foto"), upload_to=get_slug, height_field=None, width_field=None, max_length=100,
        validators=[validate_image], null=True, blank=True
    )
    foto_blob = models.TextField('foto_blob', blank=True, null=True)
                            
    foto_hr = models.ImageField(
        _("Foto original"), upload_to=get_slug,
        height_field=None, width_field=None, max_length=100,
        validators=[validate_image], null=True, blank=True
    )
    foto_hr_blob = models.TextField('foto_hr_blob', blank=True, null=True)

    publica = models.BooleanField(_("Pública"), default=True)
    liberada_nao_autenticados = models.BooleanField(
        _("Liberar fotos para não autenticados"), 
        default=False, 
        help_text="Ao ativar esta opção, a foto estará acessível para qualquer usuário, autenticado ou não."\
        "Pode ser utilizada em alguns casos, como eventos naturais e manifestações (borrar pessoas/veículos)")
    legenda = models.CharField(
        _("Legenda"), max_length=100, null=True, blank=True)
    adicionada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Adicionada por', related_name='foto_adic_por'
    )
    adicionada_em = models.DateTimeField(
        auto_now_add=True, verbose_name='Adicionada em')
    validada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Validada por', related_name='foto_valid_por',
    )
    validada_em = models.DateTimeField(
        auto_now_add=False, null=True, verbose_name='Validada em')
    veiculo = models.ManyToManyField(
        "OcorrenciaVeiculo", verbose_name=_("Veículo(s)"), related_name='ft_veiculos_set', blank=True)

    class Meta:
        ordering = ['-pk']
        verbose_name = 'Foto'
        verbose_name_plural = 'Fotos'

    def __str__(self):
        if self.legenda:
            return f'{self.pk} {self.legenda} <{self.ocorrencia.slug}>'
        return f'{self.pk} <{self.ocorrencia.slug}>'

    @property
    def get_absolute_image_url(self):
        return "{0}".format(self.foto.url)

    @property
    def get_absolute_image_url_hr(self):
        return "{0}".format(self.foto_hr.url)
    
    def set_foto_blob(self, data):
        self.foto_blob = base64.encodebytes(data).decode()

    def get_foto_blob(self):
        return base64.decodebytes(self.foto_blob)

    def set_foto_hr_blob(self, data):
        self.foto_hr_blob = base64.encodebytes(data).decode()

    def get_foto_hr_blob(self):
        return base64.decodebytes(self.foto_hr_blob)
    
    def save(self, *args, **kwargs):

        validacao = kwargs.pop('validacao', False)
        oc = kwargs.pop('oc_slug', 'sem_valor')

        if not validacao and self.foto_hr:
            im = Image.open(self.foto_hr)
            im_hr = Image.open(self.foto_hr)

            if im.mode in ("RGBA", "P"):
                im = im.convert('RGB')

            if im_hr.mode in ("RGBA", "P"):
                im_hr = im_hr.convert('RGB')

            output = BytesIO()
            output_hr = BytesIO()


            # Resize/modify the image
            fixed_height = 250
            height_percent = (fixed_height / float(im.size[1]))
            width_size = int((float(im.size[0]) * float(height_percent)))

            # resize image
            im = im.resize((width_size, fixed_height), Image.NEAREST)

            # watermark
            hr_w, hr_h = im_hr.size

            x = 50 if hr_w > 800 else 25
            y = hr_h - x - 55
            font_size = 20 if hr_w > 800 else 10 if hr_w > 400 else 8
            draw = ImageDraw.Draw(im_hr)
            font = ImageFont.load_default(size=font_size)
            data_foto = datetime.datetime.now().strftime("%d/%m/%Y")
            draw.text(
                (x,y),
                text=f"PORTAL CCI | cci.artesp.sp.gov.br/{oc} ({data_foto})", 
                spacing=20,
                fill=(255, 255, 255, 128), 
                stroke_width=2,
                stroke_fill=(0, 0, 0),
                font=font,
            )
            watermark_text = 'cci.artesp.sp.gov.br'
            txt = Image.new('RGBA', (hr_w, hr_h), (255, 255, 255, 0))
            font_size = int(min(hr_w, hr_h) / len(watermark_text) * 1.5)
            font = ImageFont.load_default(size=font_size)
             # Initialize ImageDraw
            d = ImageDraw.Draw(txt)

            # Calculate the bounding box for the watermark text
            text_bbox = d.textbbox((0, 0), watermark_text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            x = (hr_w - text_width) / 2
            y = (hr_h - text_height) / 2

            # Add stroke to the text
            stroke_color = (0, 0, 0, 64)  # Black color for the stroke
            stroke_width = 1
            for adj in range(-stroke_width, stroke_width + 1):
                if adj != 0:
                    d.text((x + adj, y), watermark_text, font=font, fill=stroke_color)
                    d.text((x, y + adj), watermark_text, font=font, fill=stroke_color)
                    d.text((x + adj, y + adj), watermark_text, font=font, fill=stroke_color)
                    d.text((x - adj, y), watermark_text, font=font, fill=stroke_color)
                    d.text((x, y - adj), watermark_text, font=font, fill=stroke_color)
                    d.text((x - adj, y - adj), watermark_text, font=font, fill=stroke_color)

            # Add text to the editable image
            d.text((x, y), watermark_text, font=font, fill=(255, 255, 255, 64))

            # Combine the original image with the watermark
            im_hr = im_hr.convert('RGBA')
            watermarked = Image.alpha_composite(im_hr, txt)

            # Convert the image to RGB mode
            watermarked_rgb = watermarked.convert("RGB")

            # after modifications, save it to the output
            im.save(output, format='JPEG', quality=65)
            watermarked_rgb.save(output_hr, format='JPEG', quality=80)

            output.seek(0)
            output_hr.seek(0)

            if output:
                self.set_foto_blob(output.read())
                # self.foto_blob = bytes(output.read())
                self.foto = None
            if output_hr:
                self.set_foto_hr_blob(output_hr.read())
                # self.foto_blob_hr = bytes(output_hr.read())
                self.foto_hr = None


        super(Foto, self).save()


class Congestionamento(models.Model):
    TRAFEGO_CHOICES = [
        ('INTENSO', 'Intenso'),
        ('LENTO', 'Lento'),
        ('CONGESTIONADO', 'Congestionado'),
        ('INTERDITADO', 'Interditado'),
        ('NORMAL', 'Normal'),
        ('DESCONHECIDO', 'Desconhecido'),
    ]
    ocorrencia = models.ForeignKey(
        Ocorrencia, verbose_name=_("Condições de tráfego"), related_name='congestionamento', on_delete=models.CASCADE
    )
    rodovia = models.ForeignKey(
        Rodovia,
        verbose_name='Rodovia', related_name='rodo_congest', null=True, blank=False, on_delete=models.SET_NULL
    )
    pista = models.CharField(
        max_length=50,
        choices=Ocorrencia.PISTA_CHOICES,
        verbose_name='Pista',
        default="SEM INFORMAÇÃO"
    )
    sentido = models.CharField(
        max_length=20,
        choices=Ocorrencia.SENTIDO_CHOICES,
        verbose_name='Sentido',
        default="SEM INFORMAÇÃO"
    )
    trafego = models.CharField(_("Tráfego"), choices=TRAFEGO_CHOICES, max_length=50, default="NORMAL")
    hora_inicio = models.TimeField(
        _("Hora Início"), null=True, blank=True, auto_now=False, auto_now_add=False)
    hora_termino = models.TimeField(
        _("Hora Término"), null=True, blank=True, auto_now=False, auto_now_add=False)
    dt_hora_inicio = models.DateTimeField(
        _("DataHora Início"), null=True, blank=True, auto_now=False, auto_now_add=False)
    dt_hora_termino = models.DateTimeField(
        _("DataHora Término"), null=True, blank=True, auto_now=False, auto_now_add=False)
    km_inicial = models.DecimalField(
        _("KM inicial"), max_digits=8, decimal_places=3, null=True, blank=True,)
    km_final = models.DecimalField(
        _("KM final"), max_digits=8, decimal_places=3, null=True, blank=True,)
    obs = models.CharField(_("Obs."), max_length=255, null=True, blank=True)

    class Meta:
        ordering = ['-dt_hora_termino', 'dt_hora_inicio', 'pista', 'sentido',]
        verbose_name = 'Condições de tráfego'
        verbose_name_plural = 'Condições de tráfego'

    def __str__(self):
        return f'{self.pk} <{self.ocorrencia.slug}> kmi{self.km_inicial} kmf{self.km_final}'

    def get_total(self):
        if self.km_inicial is not None:
            km_inicial = Decimal(self.km_inicial)
        else:
            return None
        if self.km_final is not None:
            km_final = Decimal(self.km_final)
        else:
            return None
        if km_inicial > km_final:
            km_inicial, km_final = km_final, km_inicial
        return km_final - km_inicial

    def is_finished(self):
        return True if self.dt_hora_termino else False
    
    def get_nome_sentido_resumido(self):
        nomes = {
            'SEM INFORMAÇÃO': 'Ñ.D.',
            'NORTE': 'N',
            'SUL': 'S',
            'LESTE': 'L',
            'OESTE': 'O',
            'NORTE/SUL': 'N/S',
            'LESTE/OESTE': 'L/O',
            'EXTERNO': 'EXT',
            'INTERNO': 'INT',
            'NÃO SE APLICA': 'N/A',
        }
        return nomes.get(self.sentido, self.sentido)

    def get_nome_trafego_resumido(self):
        nomes = {
            'INTENSO': 'INTENSO',
            'LENTO': 'LENTO',
            'CONGESTIONADO': 'CONGEST.',
            'INTERDITADO': 'INTERD.',
            'NORMAL': 'NORMAL',
            'DESCONHECIDO': 'Ñ.D.',
        }
        return nomes.get(self.trafego, self.trafego)


class OcorrenciaAtualizacao(models.Model):
    CLIMA_CHOICES = [
        ('Chuva', 'Chuva'),
        ('Garoa', 'Garoa'),
        ('Neblina', 'Neblina'),
        ('Nublado', 'Nublado'),
        ('Trechos com chuva', 'Trechos com chuva'),
        ('Trechos com garoa', 'Trechos com garoa'),
        ('Trechos com neblina', 'Trechos com neblina'),
        ('Trechos com garoa e neblina', 'Trechos com garoa e neblina'),
        ('Trechos com chuva e neblina', 'Trechos com chuva e neblina'),
        ('Trechos com chuva e nublado', 'Trechos com chuva e nublado'),
        ('Chuva com ventania', 'Chuva com ventania'),
        ('Chuva torrencial', 'Chuva torrencial'),
        ('Sol', 'Sol'),
        ('Tempo bom', 'Tempo bom'),
        ('Vento forte', 'Vento forte'),
        ('Desconhecidas', 'Desconhecidas'),
    ]
    atualizacao = models.TextField(_("Atualização"))
    cond_climaticas_atuais = models.CharField(
        _("Cond. climáticas atuais"), choices=CLIMA_CHOICES, max_length=50, blank=False, null=False,
    )
    publica = models.BooleanField(_("Pública"), default=True)
    ocorrencia = models.ForeignKey(Ocorrencia, verbose_name=_(
        "Atualização"), on_delete=models.CASCADE,  related_name='atualizacao')
    atualizacao_criado_em = models.DateTimeField(
        auto_now_add=True, verbose_name='Criado em', null=True)
    atualizacao_criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='atualizacao_criador'
    )
    oc_atualizada_em = models.DateTimeField(verbose_name='atualizado em', null=True)
    oc_atualizada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Oc. atualizada por', related_name='oc_atualizador'
    )
    # at_atualizada_posteriormente = models.BooleanField(_("Atualizada posteriormente"), default=False)
    atualizacao_apagada_em = models.DateTimeField(verbose_name='Apagada em', null=True, blank=True,)
    atualizacao_apagada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name='Apagada por', related_name='oc_apagador'
    )

    def __str__(self):
        return f'{self.oc_atualizada_em}: {self.atualizacao} <{self.ocorrencia.slug}>'

    class Meta:
        ordering = ('atualizacao_criado_em', 'oc_atualizada_em',)
        verbose_name = 'Atualização'
        verbose_name_plural = 'Atualizações'


class OcorrenciaFim(models.Model):
    FIM_CHOICES = [
        ('EM AVERIGUAÇÃO', 'EM AVERIGUAÇÃO'),
        ('EVADIU-SE', 'EVADIU-SE'),
        ('NÃO IDENTIFICADA', 'NÃO IDENTIFICADA'),
        ('ILESA', 'ILESA'),
        ('LEVE', 'LEVE'),
        ('MODERADA', 'MODERADA'),
        ('GRAVE', 'GRAVE'),
        ('FATAL', 'FATAL'),
    ]
    tipo = models.CharField(_("Tipo"), choices=FIM_CHOICES, max_length=50)
    qtd = models.PositiveSmallIntegerField(_("Qtd"), default=0)
    obs = models.CharField(_("Obs."), max_length=255, null=True, blank=True)
    publica = models.BooleanField(_("Pública"), default=True)
    ocorrencia = models.ForeignKey(Ocorrencia, verbose_name=_(
        "Ocorrência"), on_delete=models.CASCADE,  related_name='fim')
    veiculo = models.ForeignKey("OcorrenciaVeiculo", verbose_name=_(
        "Veículo"), on_delete=models.SET_NULL,  related_name='veiculo', null=True, blank=True)

    def __str__(self):
        return f'{self.tipo}: {self.qtd} <{self.ocorrencia.slug}>'
    

    # @staticmethod
    # def vitimas_resumo_dict(vitimas_valores):
    #     vitimas_resumo = {}
    #     for vitima in vitimas_valores:
    #         key = vitima.get('fim__ocorrencia__pk')
    #         if key is None:
    #             continue
    #         tipo = vitima.get('fim__tipo')
    #         if tipo:
    #             tipo = tipo.title()
    #         qtd = vitima.get('fim__qtd')
    #         if key in vitimas_resumo:
    #             if tipo and tipo in vitimas_resumo[key]:
    #                 if qtd:
    #                     vitimas_resumo[key][tipo] += qtd
    #             if tipo and tipo not in vitimas_resumo[key]:
    #                 if qtd:
    #                     vitimas_resumo[key][tipo] = qtd
    #         if key and key not in vitimas_resumo:
    #             vitimas_resumo[key] = {}
    #             if qtd:
    #                 vitimas_resumo[key][tipo] = qtd
    #     return vitimas_resumo
    
    @staticmethod
    def vitimas_resumo_dict(vitimas_valores):
        vitimas_resumo = {}
        for oc_pk, tipo, qtd in vitimas_valores:
            if tipo and tipo.upper() == 'ILESA':
                tipo = 'Ileso'
            if tipo:
                tipo = tipo.title()
            if oc_pk:
                if oc_pk in vitimas_resumo:
                    if tipo in vitimas_resumo[oc_pk]:
                        if qtd:
                            vitimas_resumo[oc_pk][tipo] += qtd
                    else:
                        if qtd:
                            vitimas_resumo[oc_pk][tipo] = qtd
                else:
                    vitimas_resumo[oc_pk] = {}
                    if tipo and qtd:
                        vitimas_resumo[oc_pk][tipo] = qtd
                    else:
                        vitimas_resumo[oc_pk] = {'SEM_INFO': 0}
        return vitimas_resumo

    @staticmethod
    def vitimas_resumo_dict_str(vitimas_valores_dict):
        vitimas_resumo_str = {}
        for key, values in vitimas_valores_dict.items():
            vitimas_resumo_str[key] = ''
            for k,v in values.items():
                if k == 'SEM_INFO':
                    temp_str = f'Sem inf. ou 0'
                    vitimas_resumo_str[key] = temp_str
                    break
                temp_str = f'{k}: {v}<br/>'
                vitimas_resumo_str[key] += temp_str
        return vitimas_resumo_str

    @staticmethod
    def vitimas_resumo_dict_str_mobile(vitimas_valores_dict):
        vitimas_resumo_str = {}
        for key, values in vitimas_valores_dict.items():
            vitimas_resumo_str[key] = ''
            for k,v in values.items():
                if k == 'SEM_INFO':
                    temp_str = f'Sem inf. ou 0'
                    vitimas_resumo_str[key] = temp_str
                    break
                temp_str = f'{k}: {v} '
                vitimas_resumo_str[key] += temp_str
        return vitimas_resumo_str


    class Meta:
        ordering = ('tipo',)
        verbose_name = 'Vítima'
        verbose_name_plural = 'Vítimas'


class OcorrenciaVeiculo(models.Model):
    VEICULO_CHOICES = [
        ('EM AVERIGUAÇÃO', 'EM AVERIGUAÇÃO'),
        ('AERONAVE', 'AERONAVE'),
        ('AMBULÂNCIA', 'AMBULÂNCIA'),
        ('AUTOMÓVEL', 'AUTOMÓVEL'),
        ('BICICLETA', 'BICICLETA'),
        ('BITREM', 'BITREM'),
        ('CAMINHÃO', 'CAMINHÃO'),
        ('CAMINHONETE', 'CAMINHONETE'),
        ('CARRETA', 'CARRETA'),
        ('CARROÇA/CHARRETE', 'CARROÇA/CHARRETE'),
        ('CARRO-FORTE', 'CARRO-FORTE'),
        ('CVC', 'CVC'),
        ('EQUIPAMENTO - RURAL','EQUIPAMENTO - RURAL'),
        ('EQUIPAMENTO - OBRA','EQUIPAMENTO - OBRA'),
        ('HELICÓPTERO', 'HELICÓPTERO'),
        ('MICROÔNIBUS', 'MICROÔNIBUS'),
        ('MOTO', 'MOTO'),
        ('ÔNIBUS', 'ÔNIBUS'),
        ('PEDESTRE', 'PEDESTRE'),
        ('PESSOA SEM VEÍCULO', 'PESSOA SEM VEÍCULO'),
        ('REBOQUE', 'REBOQUE'),
        ('SEMI-REBOQUE', 'SEMI-REBOQUE'),
        ('TRAÇÃO ANIMAL', 'TRAÇÃO ANIMAL'),
        ('TRATOR', 'TRATOR'),
        ('TREMINHÃO', 'TREMINHÃO'),
        ('TRICICLO', 'TRICICLO'),
        ('UTILITÁRIO', 'UTILITÁRIO'),
        ('VAN/PERUA TRANSP. COLETIVO', 'VAN/PERUA TRANSP. COLETIVO'),
        ('VAN/PERUA DE MERCADORIAS', 'VAN/PERUA DE MERCADORIAS'),
        ('NÃO IDENTIFICADO', 'NÃO IDENTIFICADO')
    ]
    tipo = models.CharField(_("Tipo"), choices=VEICULO_CHOICES, max_length=50)
    modelo = models.CharField(
        _("Modelo"), max_length=50, null=True, blank=True)
    #qtd = models.PositiveSmallIntegerField(_("Qtd"))
    veiculo_oficial = models.BooleanField(
        _("Veículo oficial"), blank=False, null=False, default=False)
    placa = models.CharField(_("Placa"), max_length=10, null=True, blank=True)
    derramamento = models.BooleanField(
        _("Derramamento/vazamento de carga"), blank=False, null=False, default=False)
    material = models.CharField(
        _("Material"), max_length=50, null=True, blank=True)
    qtd_material = models.DecimalField(
        _("Qtd."), blank=True, null=True, help_text='Quantidade',
        max_digits=9, decimal_places=3, validators=[MinValueValidator(Decimal('0.000'))])
    qtd_material_unidade = models.CharField("unidade de medida", max_length=30,
        help_text="Ex.: use 't' para tonelada, 'kg', para quilograma", null=True, blank=True)
    qtd_material_derramada = models.DecimalField(
        _("Qtd. derramado"), blank=True, null=True,  help_text='Quantidade derramado',
        max_digits=9, decimal_places=3, validators=[MinValueValidator(Decimal('0.000'))])
    qtd_material_derramada_unidade = models.CharField("unidade de medida", max_length=30,
        help_text="Ex.: use 't' para tonelada, 'kg', para quilograma", null=True, blank=True)
    inicio_transbordo = models.DateTimeField(
        _("Início Transbordo"), auto_now=False, auto_now_add=False,
        null=True, blank=True
    )
    termino_transbordo = models.DateTimeField(
        _("Término Transbordo"), auto_now=False, auto_now_add=False,
        null=True, blank=True
    )
    tombamento = models.BooleanField(
        _("Veíc. tombado"), blank=False, null=False, default=False)
    inicio_destombamento = models.DateTimeField(
        _("Início Destombamento"), auto_now=False, auto_now_add=False,
        null=True, blank=True
    )
    termino_destombamento = models.DateTimeField(
        _("Término Destombamento"), auto_now=False, auto_now_add=False,
        null=True, blank=True
    )
    onibus_artesp = models.BooleanField(
        _("Ônibus fiscalizado pela ARTESP"), blank=False, null=False, default=False)
    cad_antt = models.CharField(
        _("N. ANTT"), max_length=50, blank=True, null=True)
    prod_perigo = models.BooleanField(
        _("Carregado com prod. perigosos"), blank=False, null=False, default=False)
    prod_perigo_cod = models.CharField(
        _("Prod. perigoso - Cód. ONU"), max_length=50, blank=True, null=True)
    origem = models.CharField(_("Município de origem"),
                              max_length=50, blank=True, null=True)
    destino = models.CharField(
        _("Município de destino"), max_length=50, blank=True, null=True)
    nome = models.CharField(
        _("Empresa/Nome"), max_length=100, blank=True, null=True, help_text='Nome da viação, transportadora, etc.')
    obs = models.TextField(_("Obs. gerais"), blank=True, null=True)
    publica = models.BooleanField(_("Pública"), default=True)
    ocorrencia = models.ForeignKey(Ocorrencia, verbose_name=_(
        "Veículo"), on_delete=models.CASCADE, related_name='veiculos')

    def __str__(self):
        if self.placa:
            return f'(ID: {self.pk}) {self.tipo}'
        return f'(ID: {self.pk}) {self.tipo}'

    class Meta:
        ordering = ('-pk',)
        verbose_name = 'Veículo'
        verbose_name_plural = 'Veículos'

class Interdicao(models.Model):
    FAIXAS_CHOICES = [
        ('Acostamento', 'Acostamento'),
        ('Alça parcial', 'Alça parcial'),
        ('Alça total', 'Alça total'),
        ('Faixa 1', 'Faixa 1'),
        ('Faixa 2', 'Faixa 2'),
        ('Faixa 3', 'Faixa 3'),
        ('Faixa 4', 'Faixa 4'),
        ('Faixa 5', 'Faixa 5'),
        ('Faixa 6', 'Faixa 6'),
        ('Faixa adicional', 'Faixa adicional'),
        ('Faixa zebrada', 'Faixa zebrada'),
        ('Faixa de aceleração', 'Faixa de aceleração'),
        ('Faixa de desaceleração', 'Faixa de desaceleração'),
        ('Praça de pedágio', 'Praça de pedágio'),
        ('Todas as faixas', 'Todas as faixas'),
        ('Todas as faixas (trecho sem AC)', 'Todas as faixas (trecho sem AC)'),
        ('Todas as faixas e acostamento', 'Todas as faixas e acostamento')
    ]
    ocorrencia = models.ForeignKey(
        Ocorrencia, verbose_name=_("Interdição/Bloqueio"), related_name='interdicao', on_delete=models.CASCADE
    )
    rodovia = models.ForeignKey(
        Rodovia,
        verbose_name='Rodovia', related_name='rodo_interd', null=True, blank=False, on_delete=models.SET_NULL
    )
    sentido = models.CharField(
        max_length=20,
        choices=Ocorrencia.SENTIDO_CHOICES,
        verbose_name='Sentido',
    )
    faixa = models.CharField(
        max_length=40,
        choices=Bloqueio.choices,
        verbose_name='Faixa',
    )
    dt_hora_inicio = models.DateTimeField(
        _("Data Hora Início"), null=True, blank=True, auto_now=False, auto_now_add=False)
    dt_hora_termino = models.DateTimeField(
        _("Data Hora Término"), null=True, blank=True, auto_now=False, auto_now_add=False)
    obs = models.CharField(_("Obs."), max_length=255, null=True, blank=True)

    def bloqueio_ativo_ou_maior_que_n_minutos(self, minutos) -> bool:
        if not self.dt_hora_inicio and not self.dt_hora_termino:
            return False
        if self.dt_hora_inicio and not self.dt_hora_termino:
            return True
        if self.dt_hora_inicio and self.dt_hora_termino:
            return (self.dt_hora_termino - self.dt_hora_inicio).total_seconds() >= (minutos*60)
        return False

    class Meta:
        ordering = ['-dt_hora_termino', 'dt_hora_inicio', 'faixa']
        verbose_name = 'Interdição'
        verbose_name_plural = 'Interdições'

    def __str__(self):
        return f'{self.pk} <{self.ocorrencia.slug}>'
    
    def get_nome_faixa_resumido(self):
        nomes = {
            'Acostamento': 'ac',
            'Alça parcial': 'alça parc',
            'Alça total': 'alça tot',
            'Faixa 1': 'f1',
            'Faixa 2': 'f2',
            'Faixa 3': 'f3',
            'Faixa 4': 'f4',
            'Faixa 5': 'f5',
            'Faixa 6': 'f6',
            'Faixa adicional': 'fx.adicional',
            'Faixa zebrada': 'fx.zebrada',
            'Faixa de aceleração': 'fx.aceleração',
            'Faixa de desaceleração': 'fx.desaceleração',
            'Praça de pedágio': 'pp',
            'Todas as faixas': 'total -ac',
            'Todas as faixas (trecho sem AC)': 'total',
            'Todas as faixas e acostamento': 'total',
        }
        return nomes.get(self.faixa, self.faixa)
    
    def get_nome_sentido_resumido(self):
        nomes = {
            'SEM INFORMAÇÃO': 'Ñ.D.',
            'NORTE': 'N',
            'SUL': 'S',
            'LESTE': 'L',
            'OESTE': 'O',
            'NORTE/SUL': 'N/S',
            'LESTE/OESTE': 'L/O',
            'EXTERNO': 'EXT',
            'INTERNO': 'INT',
            'NÃO SE APLICA': 'N/A',
        }
        return nomes.get(self.sentido, self.sentido)
