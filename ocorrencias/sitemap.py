
import datetime as dt
from django.contrib.sitemaps import Sitemap
from django.db.models import Q
from .models import Ocorrencia


class OcorrenciasSitemap(Sitemap):
    changefreq = "always"
    priority = 3


    def items(self):
        _now = dt.datetime.now()
        _timedelta = dt.timedelta(days=3)
        n_dias_atras = _now - _timedelta
        ocorrencias_qs = Ocorrencia.objects.all().filter(publicada=True)\
            .filter(
                Q(dt_hr_oc__gt=n_dias_atras)
            ).order_by('-pk')
        return ocorrencias_qs

    def lastmod(self, obj):
        return obj.atualizado_em