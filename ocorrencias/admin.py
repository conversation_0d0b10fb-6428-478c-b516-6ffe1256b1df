from django.forms.models import BaseInlineFormSet
from django.contrib import admin
from django.http.request import HttpRequest
from django.utils import timezone
from rangefilter.filters import DateRangeFilter
import admin_thumbnails
from django_admin_listfilter_dropdown.filters import (
    DropdownFilter, ChoiceDropdownFilter, RelatedDropdownFilter
)

from .models import Foto, Ocorrencia, OcorrenciaAtualizacao, OcorrenciaFim, OcorrenciaVeiculo,\
    Interdicao, Congestionamento
from .forms import OcorrenciaAdminForm

from concessionarias.models import Concessionaria

class OcorrenciaAdmin(admin.ModelAdmin):
    list_per_page = 20
    readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('rodovia',)
    search_fields = ('slug', )
    list_select_related = ('criado_por',)
    list_filter = (
        ('dt_hr_oc', DateRangeFilter),
        ('criado_por', RelatedDropdownFilter),
        ('concessionarias', RelatedDropdownFilter),
        ('rodovia', RelatedDropdownFilter),
    )
    list_display = ('__str__', 'criado_por',)

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False
    
    def has_delete_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False
            # return request.user.groups.filter(name='cci').exists()

    def save_model(self, request, obj, form, change):
        print('calling save_model from admin')
        if not obj.pk:
            obj.criado_por = request.user
            obj.criado_em = timezone.now()
        print('obj.atualizado_em', obj.atualizado_em)
        obj.atualizado_por = request.user
        obj.atualizado_em = timezone.now()
        print('obj.atualizado_em', obj.atualizado_em)
        obj.save()

@admin_thumbnails.thumbnail('foto')
class FotoAdminInline(admin.TabularInline):
    model = Foto
    extra = 3


class CongestionamentoAdmin(admin.ModelAdmin):
    model = Congestionamento
    list_per_page = 10
    list_select_related = False
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('ocorrencia','rodovia',)
    # list_display = ('ocorrencia',)
    search_fields = ('ocorrencia__slug',)
    # list_select_related = ('ocorrencia',)

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False
    
    def has_delete_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False


class VeiculoAdmin(admin.ModelAdmin):
    model = OcorrenciaVeiculo
    list_per_page = 10
    list_select_related = False
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('ocorrencia',)
    # list_display = ('ocorrencia',)
    search_fields = ('ocorrencia__slug',)
    # list_select_related = ('ocorrencia',)

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False
    
    def has_delete_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False


class FimAdmin(admin.ModelAdmin):
    model = OcorrenciaFim
    list_per_page = 10
    list_select_related = False
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('ocorrencia', 'veiculo',)
    # list_display = ('ocorrencia',)
    search_fields = ('ocorrencia__slug',)
    # list_select_related = ('ocorrencia',)

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False
    
    def has_delete_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False


class AtualizacaoAdmin(admin.ModelAdmin):
    model = OcorrenciaAtualizacao
    list_per_page = 10
    list_select_related = False
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('ocorrencia', 'atualizacao_criado_por', 'oc_atualizada_por', 'atualizacao_apagada_por')
    # list_display = ('ocorrencia',)
    search_fields = ('ocorrencia__slug',)
    # list_select_related = ('ocorrencia',)

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False
    
    def has_delete_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False


class InterdicaoAdmin(admin.ModelAdmin):
    model = Interdicao
    list_per_page = 10
    list_select_related = False
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('ocorrencia', 'rodovia',)
    # list_display = ('ocorrencia',)
    search_fields = ('ocorrencia__slug',)
    # list_select_related = ('ocorrencia',)

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False
    
    def has_delete_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False


class FotoAdmin(admin.ModelAdmin):
    model = Foto
    list_per_page = 10
    list_select_related = False
    raw_id_fields = ('ocorrencia', 'adicionada_por', 'validada_por', 'veiculo',)
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    # raw_id_fields = ('slug',)
    # list_display = ('ocorrencia',)
    search_fields = ('ocorrencia__slug',)
    # list_select_related = ('ocorrencia',)

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False
    
    def has_delete_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False


class OcorrenciaFimFormSet(BaseInlineFormSet):
    model = OcorrenciaFim

    def __init__(self, *args, **kwargs):
        super(OcorrenciaFimFormSet, self).__init__(*args, **kwargs)
        self.initial = [
            {'tipo': 'ILESA'},
            {'tipo': 'LEVE'},
            {'tipo': 'MODERADA'},
            {'tipo': 'GRAVE'},
            {'tipo': 'FATAL'},
        ]
        for form in self.forms:
            form.fields['tipo'].widget.attrs['readonly'] = True


class OcorrenciaFimAdminInline(admin.TabularInline):
    model = OcorrenciaFim
    extra = 3
    max_num = 3
    formset = OcorrenciaFimFormSet


class OcorrenciaVeiculoAdminInline(admin.TabularInline):
    model = OcorrenciaVeiculo
    extra = 0


class OcorrenciaAtualizacaoAdminInline(admin.TabularInline):
    model = OcorrenciaAtualizacao
    extra = 0


class OcorrenciaInterdicaoAdminInline(admin.TabularInline):
    model = Interdicao
    extra = 0

# class OcorrenciaAdmin(OcorrenciaAdmin):
#     form = OcorrenciaAdminForm
    # inlines = [
    #     OcorrenciaAtualizacaoAdminInline, 
    #     OcorrenciaFimAdminInline, 
    #     OcorrenciaVeiculoAdminInline, 
    #     FotoAdminInline,
    #     OcorrenciaInterdicaoAdminInline
    # ]

    # def get_form(self, request, obj=None, **kwargs):
    #     form = super(OcorrenciaAdmin, self).get_form(request, obj, **kwargs)
    #     form.base_fields['rodovia'].required = True
    #     form.base_fields['classe'].required = True
    #     form.base_fields['classe'].choices = [
    #         ('Acidente', 'Acidente'),
    #         ('Evento natural', 'Evento natural'),
    #         ('Obra', 'Obra'),
    #         ('Ocorrência', 'Ocorrência'),
    #     ]
    #     form.base_fields['origem_comunicacao_cci'].initial = "CONCESSIONÁRIA"
    #     return form


# class ObraAdmin(OcorrenciaAdmin):
#     form = OcorrenciaAdminForm
#     readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
#     autocomplete_fields = ('rodovia',)

#     def get_form(self, request, obj=None, **kwargs):
#         form = super(ObraAdmin, self).get_form(request, obj, **kwargs)
#         form.base_fields['classe'].initial = Ocorrencia.Classe.OBRA
#         form.base_fields['classe'].disabled = True
#         form.base_fields['classe'].help_text = "Campo não editável"
#         return form


# class EventoAdmin(admin.ModelAdmin):
#     form = OcorrenciaAdminForm
#     readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
#     autocomplete_fields = ('rodovia',)

#     def get_form(self, request, obj=None, **kwargs):
#         form = super(EventoAdmin, self).get_form(request, obj, **kwargs)
#         form.base_fields['classe'].initial = Ocorrencia.Classe.EVENTO
#         form.base_fields['classe'].disabled = True
#         form.base_fields['classe'].help_text = "Campo não editável"
#         return form


admin.site.register(Ocorrencia, OcorrenciaAdmin)
admin.site.register(Foto, FotoAdmin)
admin.site.register(Interdicao, InterdicaoAdmin)
admin.site.register(OcorrenciaAtualizacao, AtualizacaoAdmin)
admin.site.register(OcorrenciaFim, FimAdmin)
admin.site.register(OcorrenciaVeiculo, VeiculoAdmin)
admin.site.register(Congestionamento, CongestionamentoAdmin)
# admin.site.register(Evento, EventoAdmin)
# admin.site.register(Foto, FotoAdmin)
# admin.site.register(AcidenteFim, AcidenteFimAdmin)
