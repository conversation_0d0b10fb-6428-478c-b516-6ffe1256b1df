
import os

from dotenv import load_dotenv
load_dotenv('../../.env')

from django.core.mail import EmailMultiAlternatives
from django.conf import settings

from ocorrencias.models import Ocorrencia

from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Limpa as ocorrências despublicadas'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Apagando ocorrências despublicadas...'))

        # FETCH DATA
        ocs_to_delete = Ocorrencia.objects.filter(
            publicada=False
        )
        total = len(ocs_to_delete)
        texto = 'Ocorrências despublicadas apagadas com sucesso'
        self.stdout.write(self.style.WARNING(f'{total} itens encontrados'))
        ocs_to_delete.delete()
        if total:
            self.stdout.write('Enviando e-mail de confirmação...')
            email = EmailMultiAlternatives(
                subject=f'OCs despublicadas ({total}) foram apagadas com sucesso',
                body=texto,
                from_email=os.environ.get("MAIL_FROM"),
                to=[os.environ.get("MAIL_ADMIN")],
            )
            email.send(fail_silently=True)
            self.stdout.write(self.style.SUCCESS('E-mail enviado com sucesso...'))

        
        # END
        self.stdout.write(self.style.SUCCESS('Fim...'))

