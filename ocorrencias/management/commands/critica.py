
import os

from dotenv import load_dotenv
load_dotenv('../../.env')

from django.db.models import signals
from ocorrencias.models import Ocorrencia

from django.core.management.base import BaseCommand


from ...signals import check_km_final, gen_slug_ocorrencia, populate_concs_and_municipios, set_criticidade,\
    set_bloqueio

class Command(BaseCommand):
    help = 'Determina a criticidade das ocorrencias no bd'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Determinando a criticidade das ocorrencias...'))

        # FETCH DATA
        queryset = Ocorrencia.objects.prefetch_related(
            "interdicao", "fim", "congestionamento", "veiculos").iterator(chunk_size=1000)
        print(queryset)
        for i, oc in enumerate(queryset):
            print(i)
            interdicoes = oc.interdicao.all()
            fim = oc.fim.all()
            congestionamento = oc.congestionamento.all()
            veiculos = oc.veiculos.all()
            criticidade, motivos, qtd = oc.get_criticidade_com_motivos(congestionamento, fim, interdicoes, veiculos)
            oc.criticidade = criticidade
            oc.criticidade_motivos = motivos
            oc.criticidade_motivos_qtd = qtd
            signals.pre_save.disconnect(check_km_final)
            signals.pre_save.disconnect(set_bloqueio)
            signals.pre_save.disconnect(set_criticidade)
            signals.post_save.disconnect(gen_slug_ocorrencia)
            signals.post_save.disconnect(populate_concs_and_municipios)
            oc.save(update_fields=["criticidade", "criticidade_motivos", "criticidade_motivos_qtd"])


        
        # END
        self.stdout.write(self.style.SUCCESS('Fim...'))

