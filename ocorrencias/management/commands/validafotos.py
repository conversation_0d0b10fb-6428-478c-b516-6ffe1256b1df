
import datetime

from dotenv import load_dotenv
load_dotenv('../../.env')

from ocorrencias.models import Foto
from accounts.models import Account

from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Valida as fotos já inseridas no BD'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Validando fotos antigas...'))

        # FETCH DATA
        _user = Account.objects.filter(email='<EMAIL>').first()
        _now = datetime.datetime.now()
        n = Foto.objects.all().update(
            validada_por=_user,
            validada_em=_now
        )
        print(f'{n} records updated')
        # queryset = Foto.objects.all().iterator(chunk_size=1000)
        # print(queryset)
        # for i, foto in enumerate(queryset):
        #     print(i)
        #     foto.validada_por = _user
        #     foto.validada_em = _now
        #     foto.save()
        
        # END
        self.stdout.write(self.style.SUCCESS('Fim...'))

