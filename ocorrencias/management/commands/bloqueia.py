
import os

from dotenv import load_dotenv
load_dotenv('../../.env')

from django.db.models import signals
from ocorrencias.models import Ocorrencia

from django.core.management.base import BaseCommand


from ...signals import check_km_final, gen_slug_ocorrencia, populate_concs_and_municipios, set_criticidade,\
    set_bloqueio

class Command(BaseCommand):
    help = 'Determina a criticidade das ocorrencias no bd'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Determinando a criticidade das ocorrencias...'))

        # FETCH DATA
        queryset = Ocorrencia.objects.all().prefetch_related('interdicao').iterator(chunk_size=1000)
        print(queryset)
        for i, oc in enumerate(queryset):
            print(i)
            oc.bloqueio = oc.get_bloqueio(oc.interdicao.all())
            signals.pre_save.disconnect(check_km_final)
            signals.pre_save.disconnect(set_bloqueio)
            signals.pre_save.disconnect(set_criticidade)
            signals.post_save.disconnect(gen_slug_ocorrencia)
            signals.post_save.disconnect(populate_concs_and_municipios)
            signals.post_save.disconnect(set_bloqueio)
            oc.save(update_fields=["bloqueio"])


        
        # END
        self.stdout.write(self.style.SUCCESS('Fim...'))

