#models.py
from datetime import datetime, timedelta
from decimal import Decimal
from django import forms
from .models import Ocorrencia, Interdicao, Congestionamento, Foto, OcorrenciaVeiculo
from rodovias.models import Rodovia, Trecho
from concessionarias.models import Concessionaria
from django.db.models import Max, Min, Sum
from django.utils import timezone
from django.forms import (
    BaseInlineFormSet
)


from dal import autocomplete

class RodoviaForm(forms.ModelForm):
    rodovia = forms.TextInput()


class OcorrenciaAdminForm(forms.ModelForm):

    class Meta:
        model = Ocorrencia
        fields = '__all__'

    def clean(self):
        cleaned_data = super().clean()
        rodovia = cleaned_data.get('rodovia')
        km_inicial = cleaned_data.get('km_inicial')
        km_final = cleaned_data.get('km_final', None)
        if km_final < km_inicial:
            km_inicial, km_final = km_final, km_inicial
        if not rodovia:
            raise forms.ValidationError(f"Rodovia é obrigatório.")
        trechos = rodovia.trechos.filter(km_final__gte=km_inicial).filter(km_inicial__lte=km_final)
        if not trechos:
            raise forms.ValidationError(f"Não há trechos cadastrados para esta rodovia e estes kms.")
        rodovia_primeiro_km = rodovia.trechos.aggregate(Min('km_inicial'))['km_inicial__min']
        # print('rodovia_primeiro_km')
        # print(rodovia_primeiro_km)
        rodovia_ultimo_km = rodovia.trechos.aggregate(Max('km_final'))['km_final__max']
        # print('rodovia_ultimo_km')
        # print(rodovia_ultimo_km)
        if km_inicial < rodovia_primeiro_km or km_inicial > rodovia_ultimo_km:
            raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
                rodovia {rodovia.codigo} (km {rodovia_primeiro_km} ao km {rodovia_ultimo_km})")
        if km_final < rodovia_primeiro_km or km_final > rodovia_ultimo_km:
            raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
                rodovia {rodovia.codigo} (km {rodovia_primeiro_km} ao km {rodovia_ultimo_km})")
        return cleaned_data


class OcorrenciaForm(forms.ModelForm):
    class Meta:
        model = Ocorrencia
        fields = [
            'classe', 'subclasse_oc', 'subclasse_ac', 'tipo_ac', 'complemento', 'dinamica_oc', 'rodovia', 'km_inicial', 'km_final', 
            'nro_faixas_local', 'faixa_inicio', 'tem_ac', 'cond_climaticas','dt_hr_conhecimento', 'dt_hr_oc', 'dt_hr_termino',
            'publicada', 'origem_comunicacao_conc', 'origem_comunicacao_cci', 'pista',
            'sentido', 'lat', 'lng',
            'sinalizacao', 'comunicacao_usuario', 
            'danos_patrimonio', 'danos_patrimonio_obs', 'recursos_internos', 'recursos_externos',
            'acoes_operacionais', 'observacoes_cci', 'atualizacao_min', 'cod_conc', 'nro_mits',
            'arquivo', 'arquivo_desc', 'video_link', 'fiscal_dpl_local', 'cad_retroativo', 'sem_vitimas'
        ]
        widgets = {
            'dt_hr_conhecimento': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'dt_hr_oc': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'dt_hr_termino': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'rodovia': forms.HiddenInput(),
        }
        help_texts={
            'bloq_faixas': 'Sim, houve bloqueio de faixa(s)',
            'danos_patrimonio': 'Sim, houve danos ao patrimônio',
            'tem_ac': 'Sim, há acostamento no local',
            'sem_vitimas': 'Oc. sem vítimas/ilesos'
        },

    def __init__(self, rod_id, *args, **kwargs):
        super(OcorrenciaForm, self).__init__(*args, **kwargs)
        rodovia = Rodovia.objects.get(pk=rod_id)
        self.fields['rodovia'].initial = rodovia
        self.fields['classe'].choices = [
            ('Acidente', 'Acidente'),
            ('Evento natural', 'Evento natural'),
            ('Obra', 'Obra'),
            ('Ocorrência', 'Ocorrência'),
        ]
        self.fields['origem_comunicacao_cci'].initial = 'Concessionária'
        self.fields['classe'].initial = 'Acidente'
        self.fields['dt_hr_conhecimento'].initial = timezone.now()
        # self.fields['pista'].choices = rodovia.get_pistas()
        # self.fields['sentido'].choices = rodovia.get_sentidos()
        # self.fields['rodovia_nome'] = forms.ModelChoiceField(
        #     label='Rodovia', to_field_name="codigo", queryset=None, initial=rodovia,
        #     widget=forms.TextInput(attrs={'readonly': True})
        # )
        # self.fields['rodovia'].widget = forms.TextInput(attrs={'readonly': False})

    def clean(self):
        _now = timezone.now()
        cleaned_data = super().clean()
        classe = cleaned_data.get('classe')
        subclasse_oc = cleaned_data.get('subclasse_oc')
        subclasse_ac = cleaned_data.get('subclasse_ac')
        tipo_ac = cleaned_data.get('tipo_ac')
        sem_vitimas = cleaned_data.get('sem_vitimas')
        cad_retroativo = cleaned_data.get('cad_retroativo')
        faixa_inicio = cleaned_data.get('faixa_inicio')
        fotos_nao_validadas = None
        if self.instance.pk:
            fotos_nao_validadas = self.instance.fotos.filter(validada_por__isnull=True).count()


        if classe == 'Acidente':
            if subclasse_oc != 'Não se aplica':
                raise forms.ValidationError(f"Para OC tipo Acidente, a subclasse da OC deve ser"\
                    f" 'Não se aplica'.")
            if subclasse_ac is None or tipo_ac is None:
                raise forms.ValidationError(f"Para OC tipo acidente, a subclasse/tipo do acidente "\
                    f"devem ser preenchidos.")
        else:
            if subclasse_ac is not None or tipo_ac is not None:
                raise forms.ValidationError(f"Para OC que não seja acidente, a subclasse do acidente "\
                    f"deverá estar vazia (-----------).")
            if subclasse_oc == 'Não se aplica':
                raise forms.ValidationError(f"Para OC que não seja acidente, a subclasse da OC "\
                    f"não pode ser do tipo 'Não se aplica'.")

        # validacao dos campos de data
        dt_hr_conhecimento = cleaned_data.get('dt_hr_conhecimento')
        if not dt_hr_conhecimento:
            dt_hr_conhecimento = timezone.now()
        dt_hr_oc = cleaned_data.get('dt_hr_oc')
        if not dt_hr_oc:
            dt_hr_oc = dt_hr_conhecimento
        dt_hr_termino = cleaned_data.get('dt_hr_termino')

        if dt_hr_termino and (subclasse_oc == 'Em averiguação' or tipo_ac == 'Em averiguação'):
            raise forms.ValidationError(f"Verifique a subclasse da OC. Não é possível encerrar uma oc com subclasse 'Em averiguação'")
        if dt_hr_termino and (dt_hr_termino < dt_hr_oc):
            raise forms.ValidationError(f"Verifique a data de encerramento inserida, pois\
                detectou-se data hora de término < data hora da OC.")
        if dt_hr_conhecimento < dt_hr_oc:
            raise forms.ValidationError(f"Verifique a data de conhecimento inserida, pois\
                detectou-se data hora de conhecimento < data hora da OC.")
        if dt_hr_termino and (dt_hr_termino > timezone.now()):
            raise forms.ValidationError(f"Verifique a data de encerramento inserida, pois\
                detectou-se data hora de término > data hora atual.")
        
        # valida se faixa_inicial está vazia ao encerrar a oc
        if dt_hr_termino and not faixa_inicio:
            raise forms.ValidationError(f"Preencha a faixa de início da oc.")

        max_timedelta = _now + timedelta(days=-7)

        # caso 1: nova oc e dt_hr_oc tem mais de 07 dias e nao assinalado cad_retroativo
        if dt_hr_oc < max_timedelta and (not cad_retroativo) and (not self.instance):
            raise forms.ValidationError(f"Verifique a data de início da ocorrência. Esta ocorrência inicou há mais de 07 dias atrás?\
                                        Caso positivo, assinale a flag 'cad. retroativo'.")

        diff_cad_conhecimento_maior_que_sete_dias = (dt_hr_conhecimento - dt_hr_oc).days > 7
        #caso 2: diff entre data de conhecimento e inicio > 07 dias e nao foi assinalado como retroativo
        if diff_cad_conhecimento_maior_que_sete_dias and (not cad_retroativo):
            raise forms.ValidationError(f"Verifique as datas de início e conhecimento da ocorrência. \
                                        Tomamos conhecimento após 7 dias ou mais desta oc?\
                                        Caso positivo, assinale a flag 'cad. retroativo'.")
        # caso 3: cad_retroativo, porem a diff entre conhecimento e inicio nao é maior que 07 dias
        if cad_retroativo and (not diff_cad_conhecimento_maior_que_sete_dias):
            cad_retroativo = False
            cleaned_data['cad_retroativo'] = cad_retroativo

        rodovia = cleaned_data.get('rodovia')

        if not rodovia:
            raise forms.ValidationError(f"Rodovia é obrigatório.")

        km_inicial = cleaned_data.get('km_inicial')
        km_final = cleaned_data.get('km_final', None)
        sentido = cleaned_data.get('sentido')
        # pista = cleaned_data.get('pista')
        # define km_final == km_inicial
        ordenacao = None
        
        # filtra sentido
        sentido = sentido if sentido != 'SEM INFORMAÇÃO' else None
        trechos = rodovia.trechos.filter(sentido=sentido)

        if not km_inicial:
            km_inicial = 0

        if km_final is None:
            km_final = km_inicial

        if km_inicial > km_final:
            ordenacao = 'DECRESCENTE'
    
        elif km_inicial < km_final:
            ordenacao = 'CRESCENTE'
        
        else:
            ordenacao = trechos.values_list('sentido_ordem', flat=True).first()

        # print('km_inicial')
        # print(km_inicial)
        # print('km_final')
        # print(km_final)


        # print('odenacao: ')
        # print(ordenacao)

        if ordenacao == 'DECRESCENTE':
            trechos = trechos\
                .filter(km_inicial__gte=km_inicial).filter(km_final__lte=km_final)
        
        else:
            trechos = trechos\
                .filter(km_final__gte=km_inicial).filter(km_inicial__lte=km_final)


        if not trechos:
            raise forms.ValidationError(f"Não foram encontrados trechos para esta rodovia, este(s) km(s) e sentido.\
                Verifique também se os marcos quilométricos, para o sentido escolhido, são crescentes ou decrescentes")
        
        #condicoes 'CRESCENTE'
        # km_inicial > km_inicial_min
        # km_final < km_final_max
        # rodovia_km_inicial_min = trechos.aggregate(Min('km_inicial'))['km_inicial__min']
        # rodovia_km_inicial_max = trechos.aggregate(Max('km_inicial'))['km_inicial__max']
        # rodovia_km_final_min = trechos.aggregate(Min('km_final'))['km_final__min']
        # rodovia_km_final_max = trechos.aggregate(Max('km_final'))['km_final__max']

        # # if ordenacao == 'CRESCENTE':
        # if km_inicial < rodovia_km_inicial_min or km_inicial > rodovia_km_inicial_max:
        #     raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
        #         rodovia {rodovia.codigo} (km {rodovia_km_inicial_min} ao km {rodovia_km_inicial_max})")
        # if km_final < rodovia_km_final_min or km_final > rodovia_km_final_max:
        #     print('erro 2')
        #     raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
        #         rodovia {rodovia.codigo} (km {rodovia_km_final_min} ao km {rodovia_km_final_max})")

        # if ordenacao == 'DECRESCENTE':
        #     if km_inicial < rodovia_km_inicial_min or km_inicial > rodovia_km_inicial_max:
        #         raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
        #             rodovia {rodovia.codigo} (km {rodovia_km_inicial_min} ao km {rodovia_km_inicial_max})")
        #     if km_final < rodovia_km_final_min or km_final > rodovia_km_final_max:
        #         print('erro 2')
        #         raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
        #             rodovia {rodovia.codigo} (km {rodovia_km_final_min} ao km {rodovia_km_final_max})")

        # validacao de vitimas
        if dt_hr_termino and (not sem_vitimas) and self.instance.pk:
            if classe == 'Acidente':
                agg_ac = self.instance.fim.all().aggregate(Sum('qtd'))

                qtd_fim = agg_ac.get('qtd__sum', 0)
                if not qtd_fim or qtd_fim < 1:
                    raise forms.ValidationError("Você deve inserir a quantidade de ilesos/vítimas antes de \
                        encerrar o acidente.")
            else:
                agg_oc = self.instance.fim.count()
                if agg_oc < 1:
                    raise forms.ValidationError("Sem cadastro de ilesos e vítimas. Se não houver \
                        ilesos/vítimas, assinale a flag 'sem vítimas' antes de encerrar a OC.")

        if dt_hr_termino and sem_vitimas and self.instance.pk:
            agg_fim = self.instance.fim.all().aggregate(Sum('qtd'))
            qtd_fim = agg_fim.get('qtd__sum', 0)

            if qtd_fim and qtd_fim > 0:
                raise forms.ValidationError("Você marcou oc. 'sem ilesos/vítimas', porém \
                        consta cadastro de ilesos ou vítimas (mesmo que qtd. seja 0).\
                                            Desmarque a flag 'sem ilesos/vítimas'")

        # validacao de bloqueios
        if dt_hr_termino and self.instance.pk and self.instance.bloqueio != "Não":
            raise forms.ValidationError(f"Não é possível encerrar uma ocorrências se ainda\
                existirem faixas de rolamento ou acostamento interditados.")

        # validacao de cond trafego
        if dt_hr_termino and self.instance.pk and not self.instance.is_closed_cond_trafego():
            raise forms.ValidationError(f"Não é possível encerrar uma ocorrências se ainda\
                existirem condições de tráfego sem data hora de início e término.")

        # validacao fotos nao validadas
        if dt_hr_termino and fotos_nao_validadas:
            raise forms.ValidationError(f"Não é possível encerrar uma ocorrências se ainda\
                existirem fotos sem validação.")

        return cleaned_data


class BaseCongestionamentoInlineFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        
        # Ensure the parent Ocorrencia instance exists
        if not self.instance:
            raise forms.ValidationError("Ocorrência inválida.")
        
        dt_hr_oc = self.instance.dt_hr_oc

        for form in self.forms:
            if not form.cleaned_data or form.cleaned_data.get('DELETE'):
                continue  # Skip empty or deleted forms

            dt_hora_inicio = form.cleaned_data.get('dt_hora_inicio')
            dt_hora_termino = form.cleaned_data.get('dt_hora_termino')

            # Validate dt_hora_inicio
            if dt_hora_inicio:
                if dt_hora_inicio < dt_hr_oc:
                    form.add_error(
                        'dt_hora_inicio',
                        'A data e hora de início não pode ser anterior à data e hora da ocorrência.'
                    )

            # Validate dt_hora_termino
            if dt_hora_termino:
                if dt_hora_termino < dt_hr_oc:
                    form.add_error(
                        'dt_hora_termino',
                        'A data e hora de término não pode ser anterior à data e hora da ocorrência.'
                    )
                if dt_hora_inicio and dt_hora_termino <= dt_hora_inicio:
                    form.add_error(
                        'dt_hora_termino',
                        'A data e hora de término deve ser posterior à data e hora de início.'
                    )


class CustomAddCongestionamentoInlineFormSet(forms.ModelForm):
    class Meta:
        model = Congestionamento
        fields = [
            'rodovia', 'pista', 'sentido', 'trafego', 
            'dt_hora_inicio', 'dt_hora_termino',
            'km_inicial', 'km_final', 'obs',
        ]
        widgets = {
            'dt_hora_inicio': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'dt_hora_termino': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'rodovia': autocomplete.ModelSelect2(url='rodovias:rodovia-autocomplete')
        }

    def __init__(self, *args, **kwargs):
        super(CustomAddCongestionamentoInlineFormSet, self).__init__(*args, **kwargs)
        self.fields['rodovia'].required = False

    def clean(self):
        super(CustomAddCongestionamentoInlineFormSet, self).clean()
        km_inicial = self.cleaned_data.get('km_inicial')
        km_final = self.cleaned_data.get('km_final')
        
        if km_inicial is not None and km_inicial < 0:
            self.add_error('km_inicial', "Verifique o valor do marco quilométrico inicial no congestionamento.")
        
        if km_final is not None and km_final < 0:
            self.add_error('km_final', "Verifique o valor do marco quilométrico final no congestionamento.")


class CustomAddVehicleInlineFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        for i, form in enumerate(self.forms, 1):
            veiculo_oficial = form.cleaned_data.get('veiculo_oficial', None)
            nome = form.cleaned_data.get('nome', None)
            material = form.cleaned_data.get('material', None)
            qtd_material = form.cleaned_data.get('qtd_material', None)
            qtd_material_unidade = form.cleaned_data.get('qtd_material_unidade', None)
            qtd_material_derramada = form.cleaned_data.get('qtd_material_derramada', None)
            qtd_material_derramada_unidade = form.cleaned_data.get('qtd_material_derramada_unidade', None)

            if veiculo_oficial and not nome:
                raise forms.ValidationError("Veículo oficial, \
                    obrigatório preenchimento do campo empresa/nome.")

            if qtd_material and not material:
                raise forms.ValidationError("Quando o campo 'Qtd.' é preenchido, \
                    você também deve especificar o material.")
            if qtd_material and not qtd_material_unidade:
                raise forms.ValidationError("Quando o campo 'Qtd.' é preenchido, \
                    você também deve especificar a unidade de medida (ex.: use 't' para tonelada).")
        
            if qtd_material_derramada and not qtd_material_derramada_unidade:
                raise forms.ValidationError("Quando o campo 'Qtd. derramado' é preenchido, \
                    você também deve especificar a unidade de medida (ex.: use 't' para tonelada).")

            if qtd_material and qtd_material_derramada:
                try:
                    qtd_material = Decimal(qtd_material)
                    qtd_material_derramada = Decimal(qtd_material_derramada)
                except:
                    raise forms.ValidationError('Apenas números são permitidos para os campos "Quantidade de material" e\
                        "Quantidade de material derramado"')
                if (qtd_material_unidade == qtd_material_derramada_unidade) \
                    and (qtd_material_derramada > qtd_material):
                    raise forms.ValidationError(f"Verifique o valor da qtd. de material derramado no Veículo {i}, \
                        pois a qtd. derramado não pode ser superior à qtd. total de material.")


class CustomAddFimInlineFormSet(BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super(CustomAddFimInlineFormSet,self ).__init__(*args,**kwargs)
        oc = kwargs.pop('instance', None)
        veiculos = oc.veiculos.all()
        for form in self.forms:
            form.fields['veiculo'].queryset = veiculos


class CustomAddAtualizacaoInlineFormSet(BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super(CustomAddAtualizacaoInlineFormSet,self ).__init__(*args,**kwargs)
    
    def get_queryset(self):
        queryset = super(CustomAddAtualizacaoInlineFormSet, self).get_queryset()
        queryset = queryset.filter(atualizacao_apagada_em__isnull=True)
        # print(len(queryset))
        return queryset


class BaseInterdicaoInlineFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        
        # Ensure the parent Ocorrencia instance exists
        if not self.instance:
            raise forms.ValidationError("Ocorrência inválida.")
        
        dt_hr_oc = self.instance.dt_hr_oc

        for form in self.forms:
            if not form.cleaned_data or form.cleaned_data.get('DELETE'):
                continue  # Skip empty or deleted forms

            dt_hora_inicio = form.cleaned_data.get('dt_hora_inicio')
            dt_hora_termino = form.cleaned_data.get('dt_hora_termino')

            # Validate dt_hora_inicio
            if dt_hora_inicio:
                if dt_hora_inicio < dt_hr_oc:
                    form.add_error(
                        'dt_hora_inicio',
                        'A data e hora de início não pode ser anterior à data e hora da ocorrência.'
                    )

            # Validate dt_hora_termino
            if dt_hora_termino:
                if dt_hora_termino < dt_hr_oc:
                    form.add_error(
                        'dt_hora_termino',
                        'A data e hora de término não pode ser anterior à data e hora da ocorrência.'
                    )
                if dt_hora_inicio and dt_hora_termino <= dt_hora_inicio:
                    form.add_error(
                        'dt_hora_termino',
                        'A data e hora de término deve ser posterior à data e hora de início.'
                    )


class CustomAddInterdInlineFormSet(forms.ModelForm):
    class Meta:
        model = Interdicao
        fields = [
            'rodovia', 'sentido', 'faixa', 'dt_hora_inicio', 
            'dt_hora_termino', 'obs',
        ]
        widgets = {
            'dt_hora_inicio': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'dt_hora_termino': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'rodovia': autocomplete.ModelSelect2(url='rodovias:rodovia-autocomplete')
        }

    def __init__(self, *args, **kwargs):
        super(CustomAddInterdInlineFormSet, self).__init__(*args, **kwargs)
        self.fields['rodovia'].required = False

    def get_queryset(self):
        queryset = super(CustomAddInterdInlineFormSet, self).get_queryset()
        # Additional queryset filtering if needed
        return queryset

    def clean(self):
        super(CustomAddInterdInlineFormSet, self).clean()
        dt_hora_inicio = self.cleaned_data.get('dt_hora_inicio')
        dt_hora_termino = self.cleaned_data.get('dt_hora_termino')

        if dt_hora_inicio and dt_hora_termino:
            if dt_hora_termino < dt_hora_inicio:
                self.add_error(
                    'dt_hora_termino',
                    "A data e hora de término deve ser posterior à data e hora de início."
                )


class CustomAddFotoInlineForm(forms.ModelForm):
    autovalidacao = forms.BooleanField(
        label='Autovalidação da foto ( Redobre a atenção ao utilizar esta funcionalidade )',
        help_text='Sim, me responsabilizo pela validação desta foto. Não há corpos e/ou qualquer outra informação sensível.',
        required=False
    )

    class Meta:
        model = Foto
        fields = ['foto_hr', 'veiculo', 'legenda', 'publica', 'liberada_nao_autenticados']

    def __init__(self, *args, **kwargs):
        oc_pk = kwargs.pop('oc_pk', None)
        super(CustomAddFotoInlineForm,self ).__init__(*args,**kwargs)
        self.fields['veiculo'].required = False
        self.fields['veiculo'].queryset = OcorrenciaVeiculo.objects.filter(ocorrencia=oc_pk)
        self.fields['publica'].help_text = 'Esta foto será visível por todos os usuários autenticados'


class CustomAddFotoInlineFormSet(BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super(CustomAddFotoInlineFormSet,self ).__init__(*args,**kwargs)
        # for form in self.forms:
        #     form.fields['veiculo'].required = False
        #     form.fields['veiculo'].queryset = OcorrenciaVeiculo.objects.filter(ocorrencia=oc_pk)
    
    def get_queryset(self):
        queryset = super(CustomAddFotoInlineFormSet, self).get_queryset()
        queryset = queryset.filter(validada_por__isnull=True)
        # print(len(queryset))
        return queryset


class FotoValidacaoForm(forms.Form):
    foto_valida = forms.BooleanField(
        label='Eu valido esta foto, pois não há corpos e/ou qualquer outra informação sensível.',
        help_text='Sim, eu valido esta foto',
        required=False
    )

    def clean_foto_valida(self):
        foto_valida = self.cleaned_data.get('foto_valida', '')
        return foto_valida

    def clean(self):
        data = self.cleaned_data

        return data
