import os
from django.core.exceptions import ValidationError

def validate_file_extension(value):
    ext = os.path.splitext(value.name)[-1]
    valid_extensions = ['.pdf', '']
    if not ext.lower() in valid_extensions:
        raise ValidationError('Apenas arquivos PDF são permitidos.')

def validate_file_size(value):
    filesize= value.size
    
    if filesize > 10485760:
        raise ValidationError("O tamanho máximo permitido para o arquivo é 10MB")
    else:
        return value
