import re
from io import StringIO, BytesIO
import logging
import csv
import datetime
import zipfile
import tempfile
from itertools import chain
from collections import Counter
from accounts.models import Account
from django.db.models import Q
from django.db.models import Case, When, Sum, Subquery
from django.db.models.query import Prefetch
from django.core.exceptions import PermissionDenied
from django.template.loader import get_template
from django.http import HttpResponse
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.urls import reverse
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic import ListView, CreateView
from django.forms import inlineformset_factory
from django.http import Http404, HttpResponse, FileResponse
from django.contrib import messages
from django.shortcuts import render, redirect, get_object_or_404
from django import forms
from django.core.paginator import Paginator
from django.db.models import Count
from xhtml2pdf import pisa
from .models import Interdicao, Ocorrencia, Foto, OcorrenciaVeiculo, OcorrenciaFim, \
    Ocorrencia, Congestionamento, OcorrenciaAtualizacao
from .forms import CustomAddCongestionamentoInlineFormSet, OcorrenciaForm, CustomAddVehicleInlineFormSet,\
    CustomAddFimInlineFormSet, CustomAddAtualizacaoInlineFormSet, FotoValidacaoForm, \
        CustomAddFotoInlineFormSet, CustomAddFotoInlineForm, CustomAddInterdInlineFormSet, \
            CustomAddCongestionamentoInlineFormSet, BaseCongestionamentoInlineFormSet, BaseInterdicaoInlineFormSet
from concessionarias.models import ConcessionariaEquipe, Concessionaria
from rodovias.models import Rodovia
from municipios.models import Municipio

import pandas as pd

logger = logging.getLogger("django_file")

def is_member(user):
    if user.groups.filter(name='cci').exists():
        return True
    else:
        raise PermissionDenied()

def get_subclasses():
    subclasses_oc = [oc[0] for oc in Ocorrencia.SUBCLASSE_CHOICES]
    subclasses_ac = [ac[0] for ac in Ocorrencia.CLASSE_ACIDENTES_CHOICES]
    subclasses = list(set([*subclasses_ac, *subclasses_oc]))
    subclasses.sort()
    return subclasses

def get_filtered_queryset(
        ocorrencias_qs,
        filtro_nome, 
        filtro_data,
        filtro_concessionaria,
        frf,
        filtro_rodovia,
        filtro_municipio,
        filtro_classe,
        filtro_subclasse,
        filtro_finalizada,
        filtro_atualizacao,
        filtro_publicada,
    ):

    if filtro_nome:
        pattern = re.compile(r'(^oc[0-9]+)|(^[0-9]+)')
        result = pattern.match(filtro_nome)
        if result:
            term = result[0]
            filtro_nome = term.lower()
            ocorrencias_qs = ocorrencias_qs.filter(
                Q(slug__icontains=term) | Q(nro_mits__icontains=term))
        else:
            filtro_nome = ''
    
    if filtro_data:
        try:
            data_dt = datetime.datetime.strptime(filtro_data, '%Y-%m-%d')
            ano = data_dt.year
            mes = data_dt.month
            dia = data_dt.day
            _now = datetime.datetime.now()
            first = datetime.datetime(year=2021, month=11, day=16)
            if data_dt > _now or data_dt < first:
                raise ValueError()
            filtro_data = data_dt
            ocorrencias_qs = ocorrencias_qs.filter(dt_hr_oc__year=ano,
                    dt_hr_oc__month=mes,
                    dt_hr_oc__day=dia)
        except:
            filtro_data = None
    
    if filtro_concessionaria:
        try:
            filtro_concessionaria = int(filtro_concessionaria)
            ocorrencias_qs = ocorrencias_qs.filter(
                    concessionarias__pk=filtro_concessionaria)
        except:
            filtro_concessionaria = None
    
    if frf:
        try:
            frf = int(frf)
            ocorrencias_qs = ocorrencias_qs.filter(rodovia=frf)
        except:
            frf = None
    
    if filtro_municipio:
        try:
            filtro_municipio = int(filtro_municipio)
            ocorrencias_qs = ocorrencias_qs.filter(
                municipios__pk=filtro_municipio)
        except:
            filtro_municipio = None
    
    if filtro_classe:
        if filtro_classe.strip() in ['Acidente', 'Evento natural', 'Obra', 'Ocorrência']:
            ocorrencias_qs = ocorrencias_qs.filter(classe=filtro_classe)
    
    if filtro_subclasse:
        filtro_subclasse = ''.join([x for x in filtro_subclasse if x not in ['|', '+', '\\', '*', '\"', '\'', '(', ')', ',', '.']])
        ocorrencias_qs = ocorrencias_qs.filter(
            Q(subclasse_oc__exact=filtro_subclasse) | Q(subclasse_ac__exact=filtro_subclasse))
    
    if filtro_atualizacao:
            filtro_atualizacao = int(filtro_atualizacao)
            ocorrencias_qs = ocorrencias_qs.filter(atualizado_por=filtro_atualizacao)
    
    if filtro_finalizada:
        try:
            filtro_finalizada = int(filtro_finalizada)
            if filtro_finalizada == 0:
                ocorrencias_qs = ocorrencias_qs.filter(
                    dt_hr_termino__isnull=False)
            if filtro_finalizada == 1:
                ocorrencias_qs = ocorrencias_qs.filter(
                    dt_hr_termino__isnull=True)
        except:
            filtro_finalizada = None
    try:
        filtro_publicada = int(filtro_publicada)
        if filtro_publicada == 0:
            ocorrencias_qs = ocorrencias_qs.filter(publicada=False)
        elif filtro_publicada == 1:
            ocorrencias_qs = ocorrencias_qs.filter(publicada=True)
        else:
            pass
    except:
        filtro_publicada = None

    return ocorrencias_qs, ocorrencias_qs.count()

@login_required
@user_passes_test(is_member)
def list_view(request):
    try:
        # request filters
        filtro_nome = request.GET.get("filtro_nome", None)
        filtro_data = request.GET.get("filtro_data", None)
        filtro_concessionaria = request.GET.get("filtro_concessionaria", None)
        frf = request.GET.get("frf", None)
        filtro_rodovia = request.GET.get("filtro_rodovia", None)
        filtro_municipio = request.GET.get("filtro_municipio", None)
        filtro_classe = request.GET.get("filtro_classe", None)
        filtro_subclasse = request.GET.get("filtro_subclasse", None)
        filtro_finalizada = request.GET.get("filtro_finalizada", None)
        filtro_atualizacao = request.GET.get("filtro_atualizacao", None)
        filtro_publicada = request.GET.get("filtro_publicada", None)

        filters = [
            filtro_nome,
            filtro_data,
            filtro_concessionaria,
            frf,
            filtro_rodovia,
            filtro_municipio,
            filtro_classe,
            filtro_subclasse,
            filtro_finalizada,
            filtro_atualizacao,
            filtro_publicada,
        ]

        has_filters = sum([1 if f is not None else 0 for f in filters])

        ocorrencias_qs = Ocorrencia.objects.all()
        n_rows = ocorrencias_qs.count()

        resumo_qs = ocorrencias_qs.filter(dt_hr_termino__isnull=True)
        nro_ocs_ativas = resumo_qs.count()

        if has_filters:
            ocorrencias_qs, n_rows = get_filtered_queryset(
                    ocorrencias_qs,
                    filtro_nome, 
                    filtro_data,
                    filtro_concessionaria,
                    frf,
                    filtro_rodovia,
                    filtro_municipio,
                    filtro_classe,
                    filtro_subclasse,
                    filtro_finalizada,
                    filtro_atualizacao,
                    filtro_publicada,
                )

        # page filter options
        subclasses = get_subclasses()
        municipios = Municipio.objects.all().order_by("nome").values('pk', 'nome')
        concessionarias = Concessionaria.objects.filter(fiscalizacao_artesp=True)\
            .filter(ativa=True).order_by('lote').values('pk', 'lote', 'nome')
        atualizacoes = Account.objects.filter(groups__name='cci')\
                        .order_by('nome_publico').values('pk', 'nome_publico')

        # additional data
        ocorrencias_qs = ocorrencias_qs\
                    .prefetch_related(
                        # 'rodovia',
                        Prefetch('rodovia', queryset=Rodovia.objects.all().only('codigo')), 
                        # 'concessionarias',
                        Prefetch('concessionarias', queryset=Concessionaria.objects.all().only('lote', 'nome')),
                        # 'municipios',
                        Prefetch('municipios', queryset=Municipio.objects.all().only('id', 'nome')),
                        # 'vitimas',
                        Prefetch('fim', queryset=OcorrenciaFim.objects.all().only('ocorrencia', 'tipo', 'qtd')),
                        Prefetch('fotos', 
                                 queryset=Foto.objects.filter(validada_em__isnull=True),
                                   to_attr='fotos_invalidas'),
                        'atualizado_por', 'interdicao').order_by('-dt_hr_termino','-dt_hr_oc')[:100]

        # pagination
        page_number = request.GET.get('page')
        paginator = Paginator(ocorrencias_qs, 10)
        ocorrencias = paginator.get_page(page_number)

        #context
        context = dict()

        #resumo
        resumo = resumo_qs.filter(dt_hr_termino__isnull=True)
        ocs = resumo.filter(
            Q(classe='Ocorrência') | Q(classe='Obra') | Q(classe='Evento natural')
        )
        acidentes = resumo.filter(classe='Acidente')
        ocs = ocs\
            .values('classe', 'subclasse_oc')\
            .order_by().annotate(qtd=Count('subclasse_oc'))
        acidentes = acidentes\
            .values('classe', 'subclasse_ac')\
            .order_by().annotate(qtd=Count('subclasse_ac'))
        context['resumo'] = ocs.union(acidentes)

        # restante
        context['nro_ocs_filtradas'] = n_rows
        context['nro_ocorrencias_ativas'] = nro_ocs_ativas
        context['has_filters'] = has_filters
        context['concessionarias'] = concessionarias
        context['municipios'] = municipios
        context['atualizacoes'] = atualizacoes
        choices = [choice[1] for choice in Ocorrencia.Classe.choices]
        context['classes'] = choices
        context['subclasses'] = subclasses
        context['filtros_ativos'] = True
        context['filtro_nome'] = filtro_nome
        context['filtro_data'] = filtro_data
        context['filtro_concessionaria'] = filtro_concessionaria
        context['filtro_rodovia'] = filtro_rodovia
        context['frf'] = frf
        context['filtro_municipio'] = filtro_municipio
        context['filtro_classe'] = filtro_classe
        context['filtro_subclasse'] = filtro_subclasse
        context['filtro_atualizacao'] = filtro_atualizacao
        context['filtro_finalizada'] = filtro_finalizada
        context['filtro_publicada'] = filtro_publicada
        context['is_paginated'] = True
        context['paginator'] = paginator
        context['ocorrencias'] = ocorrencias
        context['current_url'] = request.build_absolute_uri()
        context['download_excel_ocs_filtradas_url'] = request\
            .build_absolute_uri(reverse('ocorrencias:planilha_ocs_filtradas'))
        context['busca_rodovias_url'] = request.build_absolute_uri(reverse('busca-rodovias'))
        return render(request, 'dashboard/ocorrencias/list.html', context)

    except Exception as err:
        logger.error(err)
        raise err

class OcorrenciaCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = Ocorrencia
    template_name = 'dashboard/ocorrencias/create-ocorrencia.html'
    form_class = OcorrenciaForm

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('ocorrencias:detail', kwargs={'oc_slug': self.object.slug })

    def form_valid(self, form):
        self.object = form.save(commit=False)
        if self.object.dt_hr_conhecimento and not self.object.dt_hr_oc:
            self.object.dt_hr_oc = self.object.dt_hr_conhecimento
        self.object.criado_por = self.request.user
        self.object.atualizado_por = self.request.user
        self.object.atualizado_em = datetime.datetime.now()
        self.object.save()
        return redirect(self.get_success_url())

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        rodovia = Rodovia.objects.get(pk=self.kwargs['rod_id'])
        context['rodovia'] = rodovia
        context['tabela'] = rodovia.get_concessionarias_km_inicial_final()
        return context

    def get_form_kwargs(self):
        """
        Returns the keyword arguments for instantiating the form.
        """
        form_kwargs  = super(OcorrenciaCreateView, self).get_form_kwargs()
        form_kwargs.update({ 'rod_id': self.kwargs['rod_id'] })
        return form_kwargs

class SelectRodoviaListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Rodovia
    paginate_by = 30
    template_name = 'dashboard/ocorrencias/select.html'
    context_object_name = 'rodovias'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


    def get_queryset(self):
        all_ocs = Ocorrencia.objects.all()
        all_ocs_counter = all_ocs.count()
        rodovias_mais_frequentes = all_ocs.values('rodovia')\
                .annotate(rcount=Count('rodovia')).order_by('-rcount')\
                .values('rodovia', 'rcount')[:30]
        
        porcentagens = {rodo['rodovia']: round(100*(rodo['rcount'] / all_ocs_counter),2)\
                         for rodo in rodovias_mais_frequentes}
        rodovias_ids = rodovias_mais_frequentes.values_list('rodovia', flat=True)

        qs = Rodovia.objects.filter(pk__in=rodovias_ids).annotate(
            pct=Case(
                *[
                    When(id=rodovia_id, then=porcentagem)
                    for rodovia_id, porcentagem in porcentagens.items()
                ]
            )
        )
        search = self.request.GET.get('search', None)
        if search:
            qs = Rodovia.objects.filter(codigo__icontains=search)
        return qs

    def get_context_data(self, **kwargs):
        context = super(SelectRodoviaListView, self).get_context_data(**kwargs)
        context['oc_slug'] = None
        oc_slug = self.kwargs.get('oc_slug', None)
        if oc_slug:
            context['oc_slug'] = oc_slug
        return context


@login_required
@user_passes_test(is_member)
def edit_view(request, oc_slug, rod_id):
    try:
        ocorrencia = Ocorrencia.objects.get(slug=oc_slug)
        rodovia_antiga = ocorrencia.rodovia.pk
        rodovia = Rodovia.objects.get(pk=rod_id)
        ocorrencia.rodovia = rodovia
        form = OcorrenciaForm(rod_id, data=None, instance=ocorrencia)
        if request.method == 'POST':
            form = OcorrenciaForm(rod_id, data=request.POST, files=request.FILES, instance=ocorrencia)
            if not form.has_changed() and rod_id == rodovia_antiga:
                messages.add_message(request, messages.ERROR, "OC não foi atualizada, pois não foram detectadas alterações.")
                return redirect('ocorrencias:list')
            if form.is_valid():
                ocorrencia.concessionarias.clear()
                ocorrencia.municipios.clear()
                ocorrencia = form.save(commit=False)
                ocorrencia.atualizado_por = request.user
                ocorrencia.atualizado_em = datetime.datetime.now()
                ocorrencia.save()
                messages.add_message(request, messages.SUCCESS, 'Ocorrência editada com sucesso.')
                return redirect('ocorrencias:detail', ocorrencia.slug)
            else:
                messages.add_message(request, messages.ERROR, "Ocorreu um erro.")

        context = {
            'rodovia': rodovia,
            'ocorrencia': ocorrencia,
            'form': form,
            'tabela': rodovia.get_concessionarias_km_inicial_final()
        }
        return render(request, 'dashboard/ocorrencias/edit.html', context)
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:list')


@login_required
@user_passes_test(is_member)
def detail_view(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.prefetch_related(
            'concessionarias', 'municipios',
            'congestionamento', 'veiculos', 
            'interdicao', 'criado_por', 'atualizado_por',
            Prefetch('fim',
                queryset=OcorrenciaFim.objects.filter(ocorrencia__slug=oc_slug).prefetch_related(
                    'veiculo',
                )
            ),
            Prefetch('fotos',
                queryset=Foto.objects.filter(ocorrencia__slug=oc_slug).prefetch_related(
                    'adicionada_por',
                    'validada_por',
                    'veiculo',
                )
            ),
            Prefetch('atualizacao',
                queryset=OcorrenciaAtualizacao.objects.filter(ocorrencia__slug=oc_slug).prefetch_related(
                    'atualizacao_criado_por',
                    'oc_atualizada_por',
                    'atualizacao_apagada_por',
                )
            )
        ).select_related('rodovia').get(slug=oc_slug)
        anterior = Ocorrencia.objects.filter(Q(pk__gt=ocorrencia.pk) & Q(publicada=True)).order_by('pk').first()
        proxima = Ocorrencia.objects.filter(Q(pk__lt=ocorrencia.pk) & Q(publicada=True)).order_by('-pk').first()

        vitimas = ocorrencia.fim.all().filter(qtd__gt=0).values('tipo', 'qtd')

        vitimas_template = ocorrencia.generate_vitimas_str_template(vitimas)
        vitimas_template_zap = None
        if vitimas_template:
            vitimas_template_zap = 'Vítima(s): ' + vitimas_template

        veiculos_tipos, onibus_ou_van = ocorrencia.get_onibus_van_template_str()
        
        context = {}

        context['ocorrencia'] = ocorrencia
        context['anterior'] = anterior
        context['proxima'] = proxima
        context['onibus'] = onibus_ou_van
        context['veiculos_tipos'] = veiculos_tipos
        context['onibus_qtd'] = len(onibus_ou_van.items())
        context['vitimas_template'] = vitimas_template
        context['vitimas_template_zap'] = vitimas_template_zap
        return render(request, 'dashboard/ocorrencias/detail.html', context)
    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:list')


@login_required
@user_passes_test(is_member)
def add_interdicao_to_ocorrencia(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get(slug=oc_slug)
        if ocorrencia.dt_hr_termino:
            messages.error(request, "Não é possível alterar esta informação para uma ocorrência encerrada.")
            return redirect('ocorrencias:detail', oc_slug)
        
        # Create the formset with the custom BaseInlineFormSet
        OcInterdFormSet = inlineformset_factory(
            Ocorrencia,
            Interdicao,
            form=CustomAddInterdInlineFormSet,
            formset=BaseInterdicaoInlineFormSet,
            extra=3,
            can_delete=True
        )
        
        formset = OcInterdFormSet(instance=ocorrencia)
        
        if request.method == "POST":
            formset = OcInterdFormSet(request.POST, instance=ocorrencia)
            if not formset.has_changed():
                messages.error(request, "OC não foi atualizada, pois não foram detectadas alterações.")
                return redirect('ocorrencias:detail', oc_slug)
            
            if formset.is_valid():
                interdicoes = formset.save(commit=False)
                for interdicao in interdicoes:
                    if not interdicao.rodovia:
                        interdicao.rodovia = ocorrencia.rodovia
                    interdicao.save()
                
                for obj in formset.deleted_objects:
                    obj.delete()
                
                ocorrencia.atualizado_em = datetime.datetime.now()
                ocorrencia.atualizado_por = request.user
                ocorrencia.save()
                
                messages.success(request, 'Ocorrência editada com sucesso.')
                return redirect('ocorrencias:detail', oc_slug)
            else:
                messages.error(request, "Verifique os erros abaixo.")

        context = {
            'ocorrencia': ocorrencia,
            'formset': formset,
        }
        return render(request, 'dashboard/ocorrencias/add-interdicao.html', context)
    
    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")
    except Exception as err:
        logger.error(err)
        messages.error(request, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def add_photo_to_ocorrencia(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get(slug=oc_slug)
        fotos = ocorrencia.fotos.all()
        qtd_fotos_total = fotos.count()
        qtd_fotos_validadas = fotos.filter(validada_por__isnull=False).count()
        if qtd_fotos_validadas > 5:
            messages.add_message(request, messages.ERROR, "O limite de fotos já foi atingido.")
            return redirect('ocorrencias:detail', oc_slug)
        if ocorrencia.dt_hr_termino:
            messages.add_message(request, messages.ERROR, "Não é possível alterar esta informação para uma ocorrência encerrada.")
            return redirect('ocorrencias:detail', oc_slug)
        OcFotoFormSet = inlineformset_factory(
            Ocorrencia, Foto, 
            form=CustomAddFotoInlineForm,
            formset=CustomAddFotoInlineFormSet,
            fields=('foto_hr', 'veiculo', 'legenda', 'publica', 'liberada_nao_autenticados'),
            extra=(6-qtd_fotos_total),
            max_num=6, can_delete=False,
            widgets={
                'veiculo': forms.CheckboxSelectMultiple()
            },
        )
        formset = OcFotoFormSet(instance=ocorrencia, form_kwargs={'oc_pk': ocorrencia.pk})
        if request.method == "POST":
            formset = OcFotoFormSet(request.POST, request.FILES, instance=ocorrencia, form_kwargs={'oc_pk': ocorrencia.pk})
            if not formset.has_changed():
                messages.add_message(request, messages.ERROR, "OC não foi atualizada, pois não foram detectadas alterações.")
                return redirect('ocorrencias:detail', oc_slug)
            if formset.is_valid():
                for form in formset:
                    # (print('form prefix: ', form.prefix))
                    if not form.has_changed():
                        # (# (print('form prefix HAS NOT CHANGED ==> ', form.prefix)))
                        continue
                    data = form.cleaned_data

                    marked_to_delete = data.get('DELETE')
                    autovalidada = data.get('autovalidacao')
                    instance = form.save(commit=False)

                    # (print(instance))
                    # (print(instance.pk))
                    if marked_to_delete and instance.pk:
                        # (print('deleting instance: ', instance))
                        instance.delete()
                        continue
                    veiculos = data.get('veiculo')
                    instance.adicionada_por = request.user
                    
                    if autovalidada:
                        instance.validada_por = request.user
                        instance.validada_em = datetime.datetime.now()

                    instance.save(oc_slug=oc_slug)
                    # (print('instance after save: ', instance))
                    # (print('instance after save: ', instance.pk))
                    if veiculos and instance.pk:
                        # (print('veiculos: ', veiculos))
                        instance.veiculo.clear()
                        instance.veiculo.add(*veiculos)

                ocorrencia.atualizado_em = datetime.datetime.now()
                ocorrencia.atualizado_por = request.user
                ocorrencia.save()
                messages.add_message(request, messages.SUCCESS, 'Ocorrência editada com sucesso.')
                return redirect('ocorrencias:detail', oc_slug)
            else:
                messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")

        context = {
            'ocorrencia': ocorrencia,
            'formset': formset,
        }
        return render(request, 'dashboard/ocorrencias/add-foto.html', context)
    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def add_congest_to_ocorrencia(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get(slug=oc_slug)
        if ocorrencia.dt_hr_termino:
            messages.error(request, "Não é possível alterar esta informação para uma ocorrência encerrada.")
            return redirect('ocorrencias:detail', oc_slug)
        
        OcCongestFormSet = inlineformset_factory(
            Ocorrencia,
            Congestionamento,
            form=CustomAddCongestionamentoInlineFormSet,
            formset=BaseCongestionamentoInlineFormSet,
            extra=3,
            can_delete=True
        )
        
        formset = OcCongestFormSet(instance=ocorrencia)
        
        if request.method == "POST":
            formset = OcCongestFormSet(request.POST, instance=ocorrencia)
            if not formset.has_changed():
                messages.error(request, "OC não foi atualizada, pois não foram detectadas alterações.")
                return redirect('ocorrencias:detail', oc_slug)
            
            if formset.is_valid():
                congestionamentos = formset.save(commit=False)
                for congestionamento in congestionamentos:
                    if not congestionamento.rodovia:
                        congestionamento.rodovia = ocorrencia.rodovia
                    congestionamento.save()
                
                for obj in formset.deleted_objects:
                    obj.delete()
                
                ocorrencia.atualizado_em = datetime.datetime.now()
                ocorrencia.atualizado_por = request.user
                ocorrencia.save()
                
                messages.success(request, 'Ocorrência editada com sucesso.')
                return redirect('ocorrencias:detail', oc_slug)
            else:
                messages.error(request, "Verifique os erros abaixo.")

        context = {
            'ocorrencia': ocorrencia,
            'formset': formset,
        }
        return render(request, 'dashboard/ocorrencias/add-congest.html', context)
    
    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")
    except Exception as err:
        logger.error(err)
        messages.error(request, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def add_vehicle_to_ocorrencia(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get(slug=oc_slug)
        if ocorrencia.dt_hr_termino:
            messages.add_message(request, messages.ERROR, "Não é possível alterar esta informação para uma ocorrência encerrada.")
            return redirect('ocorrencias:detail', oc_slug)
        OcVeiculoFormSet = inlineformset_factory(
            Ocorrencia, OcorrenciaVeiculo,
            fields=(
                'tipo', 'modelo', 'placa', 'veiculo_oficial', 'derramamento', 'material', 
                'qtd_material', 'qtd_material_unidade', 'qtd_material_derramada',
                'qtd_material_derramada_unidade', 'inicio_transbordo', 'termino_transbordo', 'tombamento',
                'inicio_destombamento', 'termino_destombamento',
                 'prod_perigo', 'prod_perigo_cod', 'origem', 'destino', 'nome', 'cad_antt', 'onibus_artesp',
                 'obs', 'publica',
                 
            ),
            extra=3,
            can_delete=True,
            help_texts={
                'veiculo_oficial': 'Sim, é veículo oficial',
                'onibus_artesp': 'Sim, este ônibus é fiscalizado pela ARTESP',
                'derramamento': 'Sim, houve derramamento de carga',
                'tombamento': 'Sim, veículo está tombado',
                'prod_perigo': 'Sim, veículo carregava produto perigoso',
            },
            widgets={
                'inicio_transbordo': forms.DateTimeInput(
                    format='%Y-%m-%dT%H:%M',
                    attrs={'type': 'datetime-local'}
                ),
                'inicio_destombamento': forms.DateTimeInput(
                    format='%Y-%m-%dT%H:%M',
                    attrs={'type': 'datetime-local'}
                ),
                'termino_transbordo': forms.DateTimeInput(
                    format='%Y-%m-%dT%H:%M',
                    attrs={'type': 'datetime-local'}
                ),
                'termino_destombamento': forms.DateTimeInput(
                    format='%Y-%m-%dT%H:%M',
                    attrs={'type': 'datetime-local'}
                ),
            },
            formset=CustomAddVehicleInlineFormSet
        )
        formset = OcVeiculoFormSet(request.POST or None, instance=ocorrencia)
        if not formset.has_changed():
            messages.add_message(request, messages.ERROR, "OC não foi atualizada, pois não foram detectadas alterações.")
            return redirect('ocorrencias:detail', oc_slug)
        if formset.is_valid():
            formset.save()
            ocorrencia.atualizado_em = datetime.datetime.now()
            ocorrencia.atualizado_por = request.user
            ocorrencia.save()
            messages.add_message(request, messages.SUCCESS, 'Ocorrência editada com sucesso.')
            return redirect('ocorrencias:detail', oc_slug)

        context = {
            'ocorrencia': ocorrencia,
            'formset': formset,
        }
        return render(request, 'dashboard/ocorrencias/add-veiculo.html', context)
    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def add_fim_to_ocorrencia(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get(slug=oc_slug)
        if ocorrencia.dt_hr_termino:
            messages.add_message(request, messages.ERROR, "Não é possível alterar esta informação para uma ocorrência encerrada.")
            return redirect('ocorrencias:detail', oc_slug)
        if ocorrencia.subclasse_oc.upper() == 'Simulação'.upper():
            messages.add_message(request, messages.ERROR, "Oc do tipo 'Simulação' não tem vítimas.")
            return redirect('ocorrencias:detail', oc_slug)
        initial_data = None
        # if ocorrencia.fim.all().count() == 0:
        #     initial_data = [
        #         {'tipo': 'ILESA', 'qtd': 0},
        #         {'tipo': 'LEVE', 'qtd': 0},
        #         {'tipo': 'MODERADA', 'qtd': 0},
        #         {'tipo': 'GRAVE', 'qtd': 0},
        #         {'tipo': 'FATAL', 'qtd': 0},
        #     ]
        OcFimFormSet = inlineformset_factory(
            Ocorrencia, OcorrenciaFim,
            fields=('tipo', 'qtd', 'obs', 'veiculo', 'publica', ),
            extra=5,
            formset=CustomAddFimInlineFormSet
        )
        formset = OcFimFormSet(initial=initial_data, instance=ocorrencia)
        if request.method == "POST":
            formset = OcFimFormSet(request.POST, instance=ocorrencia)
            if not formset.has_changed():
                messages.add_message(request, messages.ERROR, "OC não foi atualizada, pois não foram detectadas alterações.")
                return redirect('ocorrencias:detail', oc_slug)
            if formset.is_valid():
                formset.save()
                ocorrencia.atualizado_em = datetime.datetime.now()
                ocorrencia.atualizado_por = request.user
                ocorrencia.save()
                messages.add_message(request, messages.SUCCESS, 'Ocorrência editada com sucesso.')
                return redirect('ocorrencias:detail', oc_slug)
            else:
                messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")

        context = {
            'ocorrencia': ocorrencia,
            'formset': formset,
        }
        return render(request, 'dashboard/ocorrencias/add-fim.html', context)
    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def add_atualizacao_to_ocorrencia(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get(slug=oc_slug)
        if ocorrencia.dt_hr_termino:
            messages.add_message(request, messages.ERROR, "Não é possível alterar esta informação para uma ocorrência encerrada.")
            return redirect('ocorrencias:detail', oc_slug)
        OcAtualizacaoFormSet = inlineformset_factory(
            Ocorrencia, OcorrenciaAtualizacao,
            fields=(
                'atualizacao', 'cond_climaticas_atuais', 'publica'
            ),
            extra=1,
            can_delete=True,
            formset=CustomAddAtualizacaoInlineFormSet
        )
        formset = OcAtualizacaoFormSet(request.POST or None, instance=ocorrencia)
        if request.method == 'POST':
            if not formset.has_changed():
                messages.add_message(request, messages.ERROR, "OC não foi atualizada, pois não foram detectadas alterações.")
                return redirect('ocorrencias:detail', oc_slug)
            if formset.is_valid():
                atualizacoes = formset.save(commit=False)
                for atualizacao in atualizacoes:
                    _now = datetime.datetime.now()
                    if atualizacao.pk: # atualizacao foi editada pelo usuario
                        atualizacao.oc_atualizada_em = _now
                        atualizacao.oc_atualizada_por = request.user
                        # atualizacao.at_atualizada_posteriormente = True
                    else: # nova atualizacao inserida
                        atualizacao.atualizacao_criado_em = _now
                        atualizacao.atualizacao_criado_por = request.user
                        atualizacao.oc_atualizada_em = _now
                        atualizacao.oc_atualizada_por = request.user
                    # atualizacao.oc_atualizada_por = request.user
                    # # atualiza datetime apenas se for atualização nova
                    # if not atualizacao.oc_atualizada_em:
                    #     atualizacao.oc_atualizada_em = datetime.datetime.now()
                    atualizacao.save()

                # atualizacoes a apagar
                for obj in formset.deleted_objects:
                    obj.atualizacao_apagada_em = datetime.datetime.now()
                    obj.atualizacao_apagada_por = request.user
                    obj.save()

                ocorrencia.atualizado_por = request.user
                ocorrencia.atualizada_em = datetime.datetime.now()
                ocorrencia.save()
                messages.add_message(request, messages.SUCCESS, 'Ocorrência atualizada com sucesso.')
                return redirect('ocorrencias:detail', oc_slug)
            else:
                messages.add_message(request, messages.ERROR, "Verifique os campos em vermelho.")

        context = {
            'ocorrencia': ocorrencia,
            'formset': formset,
        }
        return render(request, 'dashboard/ocorrencias/add-atualizacao.html', context)
    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def send_ocorrencia_view(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get(slug=oc_slug)
        html_content = render_to_string('dashboard/ocorrencias/email.html', {'ocorrencia': ocorrencia})
        text_content = strip_tags(html_content)
        data_str = ocorrencia.criado_em.strftime("%d/%m/%Y")
        slug_upper = ocorrencia.slug.upper()
        email = EmailMultiAlternatives(
            subject=f'Ocorrência tipo {ocorrencia.classe} | {data_str} | {slug_upper}',
            body=text_content,
            from_email=settings.EMAIL_ADDRESS,
            to=(request.user.email,),
        )
        email.attach_alternative(html_content, "text/html")
        email.send(fail_silently=False)
        messages.add_message(request, messages.SUCCESS, "Ocorrência encaminhada com sucesso para seu e-mail.")
        return redirect('ocorrencias:detail', oc_slug)
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def view_email(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get_subclass(slug=oc_slug)
        return render(request, 'dashboard/ocorrencias/email.html', {'ocorrencia': ocorrencia})
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def generate_pdf(request, oc_slug):
    try:
        ocorrencia = Ocorrencia.objects.get_subclass(slug=oc_slug)

        template_path = 'dashboard/ocorrencias/pdf.html'
        context = {'ocorrencia': ocorrencia}

        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=Ocorrencia-' +\
            ocorrencia.slug + '_' +\
            datetime.datetime.now().strftime("%Y-%m-%d_%H:%M") + '.pdf'

        template = get_template(template_path)
        html = template.render(context)

        pisa_status = pisa.CreatePDF(
        html, dest=response, link_callback=None)

        if pisa_status.err:
            return HttpResponse('We had some errors <pre>' + html + '</pre>')

        return response

    except Ocorrencia.DoesNotExist:
        return Http404("Ocorrência inexistente")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def test_pdf(request, oc_slug):
    ocorrencia = Ocorrencia.objects.get_subclass(slug=oc_slug)
    return render(request, 'dashboard/ocorrencias/pdf.html', {'ocorrencia': ocorrencia})


@login_required
@user_passes_test(is_member)
def before_update_request(request, oc_slug):
    ocorrencia = get_object_or_404(Ocorrencia, slug=oc_slug)
    concessionarias_ids = ocorrencia.concessionarias.all().values_list('id', flat=True)
    conc_equipes = ConcessionariaEquipe.objects.filter(concessionaria__pk__in=concessionarias_ids)
    context = {
        'ocorrencia': ocorrencia,
        'conc_equipes': conc_equipes
    }
    return render(request, 'dashboard/ocorrencias/confirm-update.html', context)


@login_required
@user_passes_test(is_member)
def send_update_request(request, oc_slug):
    ocorrencia = get_object_or_404(Ocorrencia, slug=oc_slug)
    destinatarios = []
    response = True
    if request.method == 'POST':
        conc_equipes = request.POST.getlist('conc_equipes', '')
        if not conc_equipes:
            messages.add_message(request, messages.ERROR, "Nenhuma equipe foi selecionada. E-mail encaminhado apenas para você.")
            send_mail_update_request(ocorrencia=ocorrencia, to=(request.user.email,), cc=None)
            response = False
        conc_equipes = ConcessionariaEquipe.objects.filter(pk__in=conc_equipes)
        for ce in conc_equipes:
            funcs = ce.funcionarios.all()
            for func in funcs:
                destinatarios.append(func.email)
    if not destinatarios and response:
        messages.add_message(request, messages.ERROR, "Não há destinatários para as equipes selecionadas. E-mail"\
            " encaminhado apenas para você.")
        send_mail_update_request(ocorrencia=ocorrencia, to=(request.user.email,), cc=None)
        response = False
    if response:
        send_mail_update_request(ocorrencia=ocorrencia, to=destinatarios, cc=(request.user.email,))
        messages.add_message(request, messages.SUCCESS, "E-mail enviado com sucesso.")
    return redirect('ocorrencias:detail', oc_slug)


@login_required
@user_passes_test(is_member)
def generate_report(request):
    _now = datetime.datetime.now()
    _timedelta_5_dias = datetime.timedelta(days=5)
    cinco_dias_atras = _now - _timedelta_5_dias
    ocorrencias = Ocorrencia.objects.prefetch_related(
        'rodovia','concessionarias','municipios','atualizacao', 'atualizado_por')\
            .order_by('-dt_hr_termino', '-atualizado_em').filter(publicada=True)
    ocorrencias = ocorrencias.filter(Q(dt_hr_termino__gt=cinco_dias_atras) | Q(dt_hr_termino__isnull=True))
    context = {
        "ocorrencias": ocorrencias
    }
    return render(request, "dashboard/ocorrencias/relatorio.html", context)


@login_required
@user_passes_test(is_member)
def download_csv(request):
    csv_container = []

    #ocorrencias
    ocorrencias_csv = get_csv_ocorrencias()
    csv_container.append(('ocorrencias.csv', ocorrencias_csv.getvalue(),))

    #atualizacoes
    atualizacoes_csv = get_csv_atualizacoes()
    csv_container.append(('atualizacoes.csv', atualizacoes_csv.getvalue(),))

    #congestionamento
    congestionamento_csv = get_csv_congestionamento()
    csv_container.append(('congestionamento.csv', congestionamento_csv.getvalue(),))

    #fim
    fim_csv = get_csv_fim()
    csv_container.append(('fim.csv', fim_csv.getvalue(),))

    #veiculos
    veiculos_csv = get_csv_veiculos()
    csv_container.append(('veiculos.csv', veiculos_csv.getvalue(),))

    #fotos
    fotos_csv = get_csv_fotos()
    csv_container.append(('fotos.csv', fotos_csv.getvalue(),))

    #interdicoes
    interdicoes_csv = get_csv_interdicao()
    csv_container.append(('interdicoes.csv', interdicoes_csv.getvalue(),))


    #zip file
    with tempfile.SpooledTemporaryFile() as tmp:
        with zipfile.ZipFile(tmp, 'w', zipfile.ZIP_DEFLATED) as archive:
            for csv in csv_container:
                archive.writestr(csv[0], csv[1])

        # Reset file pointer
        tmp.seek(0)

        # Write file data to response
        response = HttpResponse(tmp, content_type='application/force-download')
        response['Content-Disposition'] = 'attachment; filename="%s"' % 'portalcci.zip'
        return response


def get_csv_atualizacoes():
    atualizacoes_qs = OcorrenciaAtualizacao.objects.all()
    atualizacoes_values = [
        'ocorrencia__pk', 'atualizacao', 'publica', 'atualizacao_criado_por__first_name', 'atualizacao_criado_em',
        'oc_atualizada_por__first_name', 'oc_atualizada_em', 'atualizacao_apagada_por__first_name', 'atualizacao_apagada_em',
    ]
    atualizacoes_header = [
        'ocid', 'atualizacao', 'publica','atualizacao_criado_por__first_name', 'atualizacao_criado_em',
        'oc_atualizada_por__first_name', 'oc_atualizada_em', 'atualizacao_apagada_por__first_name', 'atualizacao_apagada_em',]
    atualizacoes_csv: StringIO = get_csv_buffer_from_qs(atualizacoes_qs, atualizacoes_values, atualizacoes_header)
    return atualizacoes_csv

def get_csv_fotos():
    fotos_qs = Foto.objects.all()
    fotos_values = [
        'ocorrencia__pk', 'foto', 'publica', 'legenda', 'adicionada_por__first_name', 'adicionada_em',
        'validada_por__first_name', 'validada_em', 'veiculo'
    ]
    fotos_header = [
        'ocid', 'link', 'publica', 'legenda', 'adicionada_por', 'adicionada_em',
        'validada_por', 'validada_em', 'veiculo'
    ]
    fotos_csv: StringIO = get_csv_buffer_from_qs(fotos_qs, fotos_values, fotos_header)
    return fotos_csv


def get_csv_interdicao():
    interdicao_qs = Interdicao.objects.all()
    interdicao_values = [
        'ocorrencia__pk', 'sentido', 'faixa', 'dt_hora_inicio', 'dt_hora_termino', 'obs',
    ]
    interdicao_header = ['ocid', 'sentido', 'faixa', 'dt_hora_inicio', 'dt_hora_termino', 'obs',]
    interdicao_csv: StringIO = get_csv_buffer_from_qs(interdicao_qs, interdicao_values, interdicao_header)
    return interdicao_csv

def get_csv_veiculos():
    veiculos_qs = OcorrenciaVeiculo.objects.all()
    veiculos_values = [
        'ocorrencia__pk', 'tipo', 'placa', 'derramamento', 'material', 'termino_transbordo',
        'tombamento', 'termino_destombamento', 'onibus_artesp', 'cad_antt', 'prod_perigo',
        'prod_perigo_cod', 'origem', 'destino', 'obs', 'publica'
    ]
    veiculos_header = ['ocid', 'tipo', 'placa', 'derramamento', 'material', 'termino_transbordo',
        'tombamento', 'termino_destombamento', 'onibus_artesp', 'cad_antt', 'prod_perigo',
        'prod_perigo_cod', 'origem', 'destino', 'obs', 'publica' ]
    veiculos_csv: StringIO = get_csv_buffer_from_qs(veiculos_qs, veiculos_values, veiculos_header)
    return veiculos_csv

def get_csv_fim():
    fim_qs = OcorrenciaFim.objects.all()
    fim_values = [
        'ocorrencia__pk', 'tipo', 'qtd', 'obs', 'publica'
    ]
    fim_header = ['ocid', 'tipo', 'qtd', 'obs', 'publica']
    fim_csv: StringIO = get_csv_buffer_from_qs(fim_qs, fim_values, fim_header)
    return fim_csv

def get_csv_congestionamento():
    congestionamento_qs = Congestionamento.objects.all()
    congestionamento_values = [
        'ocorrencia__pk', 'pista', 'sentido', 'hora_inicio', 'hora_termino', 'km_inicial', 'km_final',
        'obs',
    ]
    congestionamento_header = ['ocid', 'pista', 'sentido', 'hora_inicio', 'hora_termino', 'km_inicial', 'km_final',
        'obs',]
    congestionamento_csv: StringIO = get_csv_buffer_from_qs(congestionamento_qs, congestionamento_values, congestionamento_header)
    return congestionamento_csv

def get_csv_ocorrencias():
    ocorrencias_qs = Ocorrencia.objects.prefetch_related(
            'rodovia','concessionarias','municipios',)\
                .order_by('-dt_hr_termino', '-atualizado_em')
    values = [
                'slug',
                'classe',
                'subclasse_oc',
                'subclasse_ac',
                'tipo_ac',
                'dinamica_oc',
                'cod_conc',
                'rodovia__codigo',
                'concessionarias__nome',
                'municipios__nome',
                'km_inicial',
                'km_final',
                'cond_climaticas',
                'dt_hr_conhecimento',
                'dt_hr_oc',
                'dt_hr_termino',
                'publicada',
                'aprovada',
                'origem_comunicacao_conc',
                'origem_comunicacao_cci',
                'pista',
                'sentido',
                'lat',
                'lng',
                'bloq_faixas',
                'det_bloq_faixas',
                'sinalizacao',
                'comunicacao_usuario',
                'danos_patrimonio',
                'danos_patrimonio_obs',
                'recursos_internos',
                'recursos_externos',
                'acoes_operacionais',
                'observacoes_cci',
                'atualizacao_min',
                'nro_mits',
                'arquivo',
                'arquivo_desc',
                'criado_em',
                'criado_por__first_name',
                'atualizado_em',
                'atualizado_por__first_name',
    ]
    header = [
            'slug',
            'classe',
            'subclasse_oc',
            'subclasse_ac',
            'tipo_ac',
            'dinamica_oc',
            'codigo_oc_conc',
            'rodovia',
            'concessionarias',
            'municipios',
            'km_inicial',
            'km_final',
            'cond_climaticas',
            'dt_hr_conhecimento',
            'dt_hr_oc',
            'dt_hr_termino',
            'publicada',
            'aprovada',
            'origem_comunicacao_conc',
            'origem_comunicacao_cci',
            'pista',
            'sentido',
            'lat',
            'lng',
            'bloq_faixas',
            'det_bloq_faixas',
            'sinalizacao',
            'comunicacao_usuario',
            'danos_patrimonio',
            'danos_patrimonio_obs',
            'recursos_internos',
            'recursos_externos',
            'acoes_operacionais',
            'observacoes_cci',
            'atualizacao_min',
            'nro_mits',
            'arquivo',
            'arquivo_desc',
            'criado_em',
            'criado_por',
            'atualizado_em',
            'atualizado_por',
        ]
    ocorrencias_csv: StringIO = get_csv_buffer_from_qs(ocorrencias_qs, values, header)
    return ocorrencias_csv


def get_csv_buffer_from_qs(qs, values, header):
    df = pd.DataFrame.from_records(
        qs.values(*values)
    )
    df_buffer = StringIO()
    df.to_csv(
        path_or_buf=df_buffer,
        index=False,
        quoting=csv.QUOTE_NONNUMERIC,
        sep="|",
        header=header
    )
    df_buffer.seek(0)
    return df_buffer


# utils
def send_mail_update_request(ocorrencia, to, cc):
    slug_upper = ocorrencia.slug.upper()
    codigo = ocorrencia.cod_conc if ocorrencia.cod_conc is not None else ''
    html_content = render_to_string('dashboard/ocorrencias/update-request-email.html', {'ocorrencia': ocorrencia})
    text_content = strip_tags(html_content)
    email = EmailMultiAlternatives(
        subject=f'Atualização da ocorrência "{slug_upper}" {codigo}',
        body=text_content,
        from_email=settings.EMAIL_ADDRESS,
        to=to,
        cc=cc
    )
    email.attach_alternative(html_content, "text/html")
    email.send(fail_silently=False)


@login_required
@user_passes_test(is_member)
def validate_photo(request, oc_slug, pk):
    try:
        foto = get_object_or_404(Foto, pk=pk)
        ocorrencia = foto.ocorrencia
        if foto.adicionada_por == request.user:
            messages.add_message(request, messages.ERROR, "A validação só pode ser realizada por um outro usuário do CCI, pois foi você quem adicionou esta foto.")
            return redirect('ocorrencias:detail', ocorrencia.slug)
        
        if foto.validada_em and foto.validada_por != request.user:
            messages.add_message(request, messages.ERROR, "A invalidação só pode ser realizada por quem a validou ou por um administrador do sistema.")
            return redirect('ocorrencias:detail', ocorrencia.slug)
        
        if request.method == "POST":
            form = FotoValidacaoForm(request.POST)
            if form.is_valid():
                data = form.cleaned_data
                validada = data.get('foto_valida', False)
                if validada:
                    foto.validada_por = request.user
                    foto.validada_em = datetime.datetime.now()
                    foto.save(validacao=True)
                    resultado = 'validada'
                else:
                    foto.validada_por = None
                    foto.validada_em = None
                    foto.save(validacao=True)
                    resultado = 'invalidada'
                messages.add_message(request, messages.SUCCESS, f'Foto {resultado} com sucesso.')
                return redirect('ocorrencias:detail', ocorrencia.slug)
            else:
                messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")
        else:
            initial_data = {
                'foto_valida': True if foto.validada_em else False
            }
            form = FotoValidacaoForm(initial=request.POST or initial_data)
        context = {
            'foto': foto,
            'ocorrencia': ocorrencia,
            'form': form,
        }
        return render(request, 'dashboard/ocorrencias/foto-validation.html', context)
    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('ocorrencias:detail', foto.ocorrencia.pk)


@login_required
@user_passes_test(is_member)
def delete_photo(request, oc_slug, pk):
    try:
        _oc_slug = 'oc' + str(oc_slug)
        foto = get_object_or_404(Foto, pk=pk)
        ocorrencia = foto.ocorrencia
        if _oc_slug != ocorrencia.slug:
            raise Http404(foto)
        if ocorrencia.dt_hr_termino:
            messages.add_message(request, messages.ERROR, "A foto de uma OC encerrada não pode ser apagada.")
            return redirect('ocorrencias:detail', ocorrencia.slug)
        if request.method == "POST":
            if 'apagar' in request.POST.keys():
                foto.delete()
            messages.add_message(request, messages.SUCCESS, f'Foto apagada com sucesso.')
            return redirect('ocorrencias:detail', ocorrencia.slug)

        context = {
            'foto': foto,
            'ocorrencia': ocorrencia,
        }
        return render(request, 'dashboard/ocorrencias/delete-photo.html', context)
    except Foto.DoesNotExist:
        logger.error(err)
        messages.add_message(request, messages.WARNING, "Foto inexistente.")
        return redirect('ocorrencias:detail', _oc_slug)
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro ou a foto não existe.")
        return redirect('ocorrencias:detail', _oc_slug)


@login_required
def view_photo_hr(request, oc_slug, photo_pk):
    try:
        foto = get_object_or_404(Foto, pk=photo_pk)
        ocorrencia_da_foto = foto.ocorrencia
        ocorrencia = get_object_or_404(Ocorrencia, slug=oc_slug)
        if ocorrencia_da_foto != ocorrencia:
            raise Http404()

        return redirect(foto.foto_hr.url)

    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")

    except Foto.DoesNotExist:
        raise Http404("Foto inexistente")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        raise Http404()


@login_required
def show_photo_hr(request, oc_slug, pk):
    try:
        foto = get_object_or_404(Foto, pk=pk)
        ocorrencia_da_foto = foto.ocorrencia
        ocorrencia = get_object_or_404(Ocorrencia, slug=oc_slug)
        if ocorrencia_da_foto.pk != ocorrencia.pk:
            raise Http404()

        context = {
            'ocorrencia': ocorrencia,
            'foto': foto
        }
        return render(request, 'dashboard/ocorrencias/foto.html', context)

    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")

    except Foto.DoesNotExist:
        raise Http404("Foto inexistente")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        raise Http404()


    

def download_excel_ocs_filtradas(request):
    try:
        filtro_nome = request.GET.get("filtro_nome", None)
        filtro_data = request.GET.get("filtro_data", None)
        filtro_concessionaria = request.GET.get("filtro_concessionaria", None)
        frf = request.GET.get("frf", None)
        filtro_rodovia = request.GET.get("filtro_rodovia", None)
        filtro_municipio = request.GET.get("filtro_municipio", None)
        filtro_classe = request.GET.get("filtro_classe", None)
        filtro_subclasse = request.GET.get("filtro_subclasse", None)
        filtro_finalizada = request.GET.get("filtro_finalizada", None)
        filtro_atualizacao = request.GET.get("filtro_atualizacao", None)
        filtro_publicada = request.GET.get("filtro_publicada", None)

        filters = [
            filtro_nome,
            filtro_data,
            filtro_concessionaria,
            frf,
            filtro_rodovia,
            filtro_municipio,
            filtro_classe,
            filtro_subclasse,
            filtro_finalizada,
            filtro_atualizacao,
            filtro_publicada,
        ]

        has_filters = sum([1 if f is not None else 0 for f in filters])

        ocorrencias_qs = Ocorrencia.objects.all()
        n_rows = ocorrencias_qs.count()

        if has_filters:
            ocorrencias_qs, n_rows = get_filtered_queryset(
                    ocorrencias_qs,
                    filtro_nome, 
                    filtro_data,
                    filtro_concessionaria,
                    frf,
                    filtro_rodovia,
                    filtro_municipio,
                    filtro_classe,
                    filtro_subclasse,
                    filtro_finalizada,
                    filtro_atualizacao,
                    filtro_publicada,
                )

        # resumo
        ocs = ocorrencias_qs.filter(
            Q(classe='Ocorrência') | Q(classe='Obra') | Q(classe='Evento natural'))
        acidentes = ocorrencias_qs.filter(classe='Acidente')
        ocs = ocs\
            .values('classe', 'subclasse_oc')\
            .order_by().annotate(qtd=Count('subclasse_oc'))
        acidentes = acidentes\
            .values('classe', 'subclasse_ac')\
            .order_by().annotate(qtd=Count('subclasse_ac'))
        resumo = ocs.union(acidentes)


        df_resumo = pd.DataFrame(resumo)
        df_resumo.columns = [
            'CLASSE',
            'SUBCLASSE',
            'QTD.',
        ]

        #atualizacoes
        qs_atualizacoes = OcorrenciaAtualizacao.objects\
            .filter(ocorrencia_id__in=Subquery(ocorrencias_qs.values('pk')))\
            .filter(atualizacao_apagada_por__isnull=True)
        df_atualizacoes = get_df_atualizacoes(qs_atualizacoes)

        #fim
        qs_fim = OcorrenciaFim.objects.filter(
            ocorrencia_id__in=Subquery(ocorrencias_qs.values('pk')))
        df_fim = get_df_fim(qs_fim)
        
        #veiculos
        qs_veiculos = OcorrenciaVeiculo.objects.filter(
            ocorrencia_id__in=Subquery(ocorrencias_qs.values('pk')))
        df_veiculos = get_df_veiculos(qs_veiculos)

        #interdicao
        qs_interdicao = Interdicao.objects.filter(
            ocorrencia_id__in=Subquery(ocorrencias_qs.values('pk')))
        df_interdicoes = get_df_interdicoes(qs_interdicao)

        df: pd.DataFrame = pd.DataFrame.from_records(ocorrencias_qs.values(
            'pk',
            'dt_hr_oc',
            'dt_hr_termino',
            'concessionarias__nome',
            'rodovia__codigo',
            'km_inicial',
            'km_final',
            'lat',
            'lng',
            'municipios__nome',
            'classe',
            'subclasse_oc',
            'subclasse_ac',
            'tipo_ac',
            'observacoes_cci',
            'atualizado_em',
            'atualizado_por__first_name',
            'publicada',
        ))
        df.columns = [
            'CÓDIGO',
            'DATA HORA INÍCIO',
            'DATA HORA TÉRMINO',
            'CONCESSIONARIA',
            'RODOVIA',
            'KM INICIAL',
            'KM FINAL',
            'LATITUDE',
            'LONGITUDE',
            'MUNICÍPIO',
            'CLASSE',
            'SUBCLASSE OC.',
            'SUBCLASSE AC.',
            'TIPO AC.',
            'OBS.',
            'ATUALIZADO EM',
            'ATUALIZADO POR',
            'PUBLICADA',
        ]

        df['DETALHES'] = settings.BASE_URL_VIEWS + 'oc' + df['CÓDIGO'].astype(str)

        dfs_to_merge = [df_atualizacoes, df_fim, df_veiculos, df_interdicoes]
        for df_to_merge in dfs_to_merge:
            if df_to_merge is None:
                continue
            df = df.merge(df_to_merge, how='left')
        
        
        df.sort_values('CÓDIGO', ascending=False, inplace=True)
        df.drop_duplicates(inplace=True)
        df.reset_index(drop=True, inplace=True)
        df.index += 1
        b = BytesIO()
        writer = pd.ExcelWriter(b, engine='xlsxwriter')
        df.to_excel(writer, sheet_name='OCS', index=True, index_label='#')
        df_resumo.to_excel(writer, sheet_name='RESUMO', index=False)
        writer.close()
        # Set up the Http response.
        filename = 'ocorrencias_filtradas.xlsx'
        b.seek(0)
        response = FileResponse(
            b,
            as_attachment=True,
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            filename=filename
        )
        # response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response

    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR,
                             "Ocorreu um erro e não foi possível gerar a planilha eletrônica.")
        return redirect('relevantes')

def get_df_interdicoes(qs_interdicao):
    df_interdicao = pd.DataFrame.from_records(qs_interdicao.values(
            'ocorrencia_id',
            'rodovia__codigo',
            'sentido',
            'faixa',
            'dt_hora_inicio',
            'dt_hora_termino',
            'obs',
        ))

    df_interdicao['resumo'] = \
        df_interdicao['rodovia__codigo'].astype(str) + ' ' +\
            df_interdicao['sentido'].astype(str) + '/' + df_interdicao['faixa'].astype(str) +\
             ' [Bloqueado em ' + df_interdicao['dt_hora_inicio'].astype(str) + ']' +\
                ' [Liberado em ' + df_interdicao['dt_hora_termino'].astype(str) + '] (' +\
                    df_interdicao['obs'].astype(str) + ')'

    df_interdicao = df_interdicao[['ocorrencia_id', 'resumo']]\
            .groupby('ocorrencia_id')['resumo']\
            .apply(lambda x: ' | '.join(x))\
            .reset_index()
    df_interdicao['resumo'] = df_interdicao['resumo'].str.replace('(None)', '-', regex=False)
    df_interdicao['resumo'] = df_interdicao['resumo'].str.replace('None', '-', regex=False)
    df_interdicao['resumo'] = df_interdicao['resumo'].str.replace('NaT', '-', regex=False)
    df_interdicao['resumo'] = df_interdicao['resumo'].str.replace('NaN', '-', regex=False)
    
    df_interdicao.columns = ['CÓDIGO',  'INTERDIÇÕES']
    return df_interdicao

def get_df_veiculos(qs_veiculos):
    if not qs_veiculos:
        return None
    df_veiculos = pd.DataFrame.from_records(qs_veiculos.values(
            'ocorrencia_id',
            'tipo',
        ))

    df_veiculos = df_veiculos[['ocorrencia_id', 'tipo']]
    df_veiculos = df_veiculos.groupby('ocorrencia_id')['tipo']\
                .apply(' | '.join).reset_index()
    df_veiculos.columns = ['CÓDIGO', 'VEÍCULOS']
    df_veiculos['VEÍCULOS'] = df_veiculos['VEÍCULOS'].apply(vehicles_counter)
    return df_veiculos

def get_df_fim(qs_fim):
    if not qs_fim:
        return None
    df_fim = pd.DataFrame.from_records(qs_fim.values(
            'ocorrencia_id',
            'tipo',
            'qtd',
            'obs'
        ))

    df_fim = df_fim[['ocorrencia_id', 'tipo', 'qtd', 'obs']]
    df_fim['resumo'] = df_fim['tipo'].astype(str) + ': ' + df_fim['qtd'].astype(str) +\
             ' (' + df_fim['obs'].astype(str) + ')'
    df_fim = df_fim.groupby('ocorrencia_id')['resumo']\
            .apply(' | '.join).reset_index()
    df_fim['resumo'] = df_fim['resumo'].str.replace('(None)', '', regex=False)
    df_fim['resumo'] = df_fim['resumo'].str.replace('None', '', regex=False)
    df_fim = df_fim[['ocorrencia_id', 'resumo']]
    df_fim.columns = ['CÓDIGO', 'VÍTIMAS']
    return df_fim


def get_df_atualizacoes(qs_atualizacoes):
    if not qs_atualizacoes:
        return None
    df_atualizacoes = pd.DataFrame.from_records(qs_atualizacoes.values(
        'ocorrencia_id',
        'atualizacao',
        'atualizacao_criado_em',
        'atualizacao_criado_por__first_name',
        'oc_atualizada_em',
        'oc_atualizada_por__first_name',
    ))

    df_atualizacoes = df_atualizacoes[['ocorrencia_id', 'oc_atualizada_em', 'atualizacao']]
    df_atualizacoes['resumo'] = '(' + df_atualizacoes['oc_atualizada_em'].astype(str) + \
        ') ' + df_atualizacoes['atualizacao'].astype(str)
    df_atualizacoes = df_atualizacoes\
        .groupby('ocorrencia_id')['resumo']\
        .apply(lambda x: ' | '.join(x))\
        .reset_index()
    df_atualizacoes.columns = ['CÓDIGO',  'ATUALIZAÇÕES']
    return df_atualizacoes


def vehicles_counter(value):
    lst = value.strip().split(' | ')
    result = str(dict(Counter(lst)))
    return result.replace('{', '').replace('}', '').replace("'", "").replace(',', ' | ')

def get_fim_acidentes(eventos_acidente):
    fim_acidentes = OcorrenciaFim.objects.filter(
            Q(ocorrencia_id__in=Subquery(eventos_acidente.values('pk'))))\
            .values('tipo')\
            .annotate(total=Sum('qtd'))\
            .order_by()
    fim_acidentes_dict = {}
    for dic in fim_acidentes:
        key = dic['tipo']
        value = dic['total']
        if key in fim_acidentes_dict.keys():
            fim_acidentes_dict[key] += value
        else:
            fim_acidentes_dict[key] = value

    total_ilesos = fim_acidentes_dict.get('ILESA', 0) + fim_acidentes_dict.get('ILESO', 0)
    total_leves = fim_acidentes_dict.get('LEVE', 0)
    total_moderados = fim_acidentes_dict.get('MODERADA', 0) + fim_acidentes_dict.get('FERIDO', 0)
    total_graves = fim_acidentes_dict.get('GRAVE', 0)
    total_obitos = fim_acidentes_dict.get('MORTO', 0) + fim_acidentes_dict.get('FATAL', 0)
    total_acidentados = sum(
            [total_ilesos, total_leves, total_moderados, total_graves, total_obitos])
        # Make sure that total is not zero
    if total_acidentados > 0:
        pct_ilesos = (total_ilesos / total_acidentados) * 100
        pct_leves = (total_leves / total_acidentados) * 100
        pct_moderados = (total_moderados / total_acidentados) * 100
        pct_graves = (total_graves / total_acidentados) * 100
        pct_obitos = (total_obitos / total_acidentados) * 100
    else:
        pct_ilesos = 0
        pct_leves = 0
        pct_moderados = 0
        pct_graves = 0
        pct_obitos = 0
    return total_ilesos,total_leves,total_moderados,total_graves,total_obitos,pct_ilesos,pct_leves,pct_moderados,pct_graves,pct_obitos


def get_fim_ocorrencias(eventos_ocorrencias):
    fim_acidentes = OcorrenciaFim.objects.filter(
            Q(ocorrencia_id__in=Subquery(eventos_ocorrencias.values('pk'))))\
            .values('tipo')\
            .annotate(total=Sum('qtd'))\
            .order_by()
    fim_acidentes_dict = {}
    for dic in fim_acidentes:
        key = dic['tipo']
        value = dic['total']
        if key in fim_acidentes_dict.keys():
            fim_acidentes_dict[key] += value
        else:
            fim_acidentes_dict[key] = value

    total_ilesos = fim_acidentes_dict.get('ILESA', 0) + fim_acidentes_dict.get('ILESO', 0)
    total_leves = fim_acidentes_dict.get('LEVE', 0)
    total_moderados = fim_acidentes_dict.get('MODERADA', 0) + fim_acidentes_dict.get('FERIDO', 0)
    total_graves = fim_acidentes_dict.get('GRAVE', 0)
    total_obitos = fim_acidentes_dict.get('MORTO', 0) + fim_acidentes_dict.get('FATAL', 0)
    total_acidentados = sum(
            [total_ilesos, total_leves, total_moderados, total_graves, total_obitos])
        # Make sure that total is not zero
    if total_acidentados > 0:
        pct_ilesos = (total_ilesos / total_acidentados) * 100
        pct_leves = (total_leves / total_acidentados) * 100
        pct_moderados = (total_moderados / total_acidentados) * 100
        pct_graves = (total_graves / total_acidentados) * 100
        pct_obitos = (total_obitos / total_acidentados) * 100
    else:
        pct_ilesos = 0
        pct_leves = 0
        pct_moderados = 0
        pct_graves = 0
        pct_obitos = 0
    return total_ilesos,total_leves,total_moderados,total_graves,total_obitos,pct_ilesos,pct_leves,pct_moderados,pct_graves,pct_obitos

