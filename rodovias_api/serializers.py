from rest_framework import serializers
from rodovias.models import Rodovia, Trecho

class RodoviaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Rodovia
        fields = [
            "id",
            "codigo",
            "nome_principal",
            "nome_secundario",
            "slug",
            "criado_em",
            "atualizado_em"
        ]


class TrechoSerializer(serializers.ModelSerializer):
    concessionaria = serializers.StringRelatedField()
    rodovia = serializers.StringRelatedField()
    municipio = serializers.StringRelatedField()
    regional = serializers.StringRelatedField()
    class Meta:
        model = Trecho
        fields = [
            'id', 'concessionaria', 'rodovia', 'regional', 'municipio', 'tipo',
            'km_inicial', 'km_final', 'nome', 'jurisdicao', 'pista',
            'sentido', 'ramo_dispositivo', 'sentido_ordem'
        ]