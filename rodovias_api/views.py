import datetime
from rest_framework.permissions import BasePermission, SAFE_METHODS
from rest_framework import viewsets
from rest_framework import pagination
from rodovias.models import Rodovia, Trecho
from .serializers import RodoviaSerializer, TrechoSerializer
from rest_framework.permissions import IsAdminUser, DjangoModelPermissions
from rest_framework.response import Response

class ReadOnly(BasePermission):
    def has_permission(self, request, view):
        return request.method in SAFE_METHODS


class LargeResultsSetPagination(pagination.LimitOffsetPagination):
    default_limit = 10_000
    limit_query_param = 'limit'
    offset_query_param = 'offset'
    max_limit = 10_000



class RodoviaViewSet(viewsets.ModelViewSet):
    permission_classes = [DjangoModelPermissions]
    queryset = Rodovia.objects.all()
    serializer_class = RodoviaSerializer


class RodoviaPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    # pagination_class = LargeResultsSetPagination
    serializer_class = RodoviaSerializer

    def get_queryset(self):
        qs = Rodovia.objects.all()
        return qs


class TrechoPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    serializer_class = TrechoSerializer

    def get_queryset(self):
        qs = Trecho.objects.filter()
        filtro_nome = self.request.query_params.get('nome', None)
        if filtro_nome:
            qs = qs.filter(rodovia__codigo__iexact=filtro_nome)

        filtro_km_inicial = self.request.query_params.get('km_inicial', None)
        if filtro_km_inicial:
            filtro_km_inicial = filtro_km_inicial.strip().replace(',', '.').replace('+', '.')
            try:
                filtro_km_inicial = float(filtro_km_inicial)
                qs = qs.filter(km_final__gte=filtro_km_inicial)
            except:
                pass
        filtro_km_final = self.request.query_params.get('km_final', None)
        if filtro_km_final:
            filtro_km_final = filtro_km_final.strip().replace(',', '.').replace('+', '.')
            try:
                filtro_km_final = float(filtro_km_final)
                qs = qs.filter(km_inicial__lte=filtro_km_final)
            except:
                pass

        filtro_concessionaria = self.request.query_params.get('concessionaria_id', None)
        if filtro_concessionaria:
            filtro_concessionaria = int(filtro_concessionaria)
            qs = qs.filter(concessionaria__pk=filtro_concessionaria)
        
        filtro_concessionaria_nome = self.request.query_params.get('concessionaria', None)
        if filtro_concessionaria_nome:
            qs = qs.filter(concessionaria__nome__icontains=filtro_concessionaria_nome)
        
        lote = self.request.query_params.get('lote', None)
        if lote:
            lote = int(lote)
            qs = qs.filter(concessionaria__lote=lote)

        filtro_regional = self.request.query_params.get('regional', None)
        if filtro_regional:
            filtro_regional = int(filtro_regional)
            qs = qs.filter(regional__pk=filtro_regional)

        filtro_tipo = self.request.query_params.get('tipo', None)
        if filtro_tipo:
            qs = qs.filter(tipo__iexact=filtro_tipo)

        filtro_jurisdicao = self.request.query_params.get('jurisdicao', None)
        if filtro_jurisdicao:
            qs = qs.filter(jurisdicao__iexact=filtro_jurisdicao)

        filtro_municipio = self.request.query_params.get('municipio', None)
        if filtro_municipio:
            filtro_municipio = int(filtro_municipio)
            qs = qs.filter(municipio__pk=filtro_municipio)

        qs = qs.prefetch_related('rodovia').prefetch_related('municipio').prefetch_related('regional')\
                .prefetch_related('concessionaria')
        return qs


def handle_qs(request, qs=None):
    print('handling qs for trecho')
    if qs is None:
        qs = Trecho.objects.all()
        

    filtro_nome = request.GET.get('nome', None)
    if filtro_nome:
        qs = qs.filter(rodovia__codigo__iexact=filtro_nome)

    filtro_km_inicial = request.GET.get('km_inicial', None)
    if filtro_km_inicial:
        filtro_km_inicial = filtro_km_inicial.strip().replace(',', '.').replace('+', '.')
        try:
            filtro_km_inicial = float(filtro_km_inicial)
            qs = qs.filter(km_final__gte=filtro_km_inicial)
        except:
            pass
    filtro_km_final = request.GET.get('km_final', None)
    if filtro_km_final:
        filtro_km_final = filtro_km_final.strip().replace(',', '.').replace('+', '.')
        try:
            filtro_km_final = float(filtro_km_final)
            qs = qs.filter(km_inicial__lte=filtro_km_final)
        except:
            pass

    filtro_concessionaria = request.GET.get('concessionaria', None)
    if filtro_concessionaria:
        filtro_concessionaria = int(filtro_concessionaria)
        qs = qs.filter(concessionaria__pk=filtro_concessionaria)

    filtro_regional = request.GET.get('regional', None)
    if filtro_regional:
        filtro_regional = int(filtro_regional)
        qs = qs.filter(regional__pk=filtro_regional)

    filtro_tipo = request.GET.get('tipo', None)
    if filtro_tipo:
        qs = qs.filter(tipo__iexact=filtro_tipo)

    filtro_jurisdicao = request.GET.get('jurisdicao', None)
    if filtro_jurisdicao:
        qs = qs.filter(jurisdicao__iexact=filtro_jurisdicao)

    filtro_municipio = request.GET.get('municipio', None)
    if filtro_municipio:
        filtro_municipio = int(filtro_municipio)
        qs = qs.filter(municipio__pk=filtro_municipio)

    return qs