from rest_framework import serializers
from relatorios.models import Relatorio, RelatorioDestinatarios, RelatorioFoto, RelatorioIndex, \
    RelatorioModelo, RelatorioModeloItens


class FotoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Foto
        fields = '__all__'


class AcidenteFimSerializer(serializers.ModelSerializer):

    class Meta:
        model = AcidenteFim
        fields = '__all__'


class AcidenteVeiculoSerializer(serializers.ModelSerializer):

    class Meta:
        model = AcidenteVeiculo
        fields = '__all__'


class AcidenteSerializer(serializers.ModelSerializer):
    fotos = FotoSerializer(many=True, read_only=True)
    fim = AcidenteFimSerializer(many=True, read_only=True)
    veiculos = AcidenteVeiculoSerializer(many=True, read_only=True)

    class Meta:
        model = Acidente
        fields = '__all__'


class OcorrenciaSerializer(serializers.ModelSerializer):
    fotos = FotoSerializer(many=True, read_only=True)

    class Meta:
        model = Ocorrencia
        fields = '__all__'
        #depth = 1
    #     extra_fields = ['fotos']

    # def get_field_names(self, declared_fields, info):
    #     expanded_fields = super(OcorrenciaSerializer, self).get_field_names(declared_fields, info)

    #     if getattr(self.Meta, 'extra_fields', None):
    #         return expanded_fields + self.Meta.extra_fields
    #     else:
    #         return expanded_fields