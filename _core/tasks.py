import logging
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from celery import Celery
from celery import shared_task
from celery.utils.log import get_logger

logger = logging.getLogger("django_file")
celery_logger = get_logger(__name__)

app = Celery()

@shared_task
def send_mail_cadastro_reprovado(nome, email, motivo_reprovacao):
    try:
        html_content = render_to_string(
            'accounts/reprovacao-cadastro-email.html', 
            {
                'nome': nome,
                'email': email,
                'motivo_reprovacao': motivo_reprovacao,
                'url_cci': 'https://cci.artesp.sp.gov.br'
            }
        )
        text_content = strip_tags(html_content)
        email = EmailMultiAlternatives(
            subject=f'[ portalcci ] Sua solicitação de cadastro foi reprovada',
            body=text_content,
            from_email='<EMAIL>',
            to=(email,),
            bcc=('<EMAIL>',),
        )
        email.attach_alternative(html_content, "text/html")
        email.send(fail_silently=False)
    except Exception as err:
        logger.error(err)
        raise err

@app.task
def add(x, y):
    from time import sleep
    sleep(5)
    return x + y

@shared_task
def test_task_delay():
    try:
        add.delay(2, 2)
    except add.OperationalError as exc:
        logger.exception('Sending task raised: %r', exc)