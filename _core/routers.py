# _core/routers.py
from django.urls import path, reverse
from rest_framework.routers import DefaultRouter
from accounts.views import (
    CookieTokenObtainPairView,
    CookieTokenRefreshView,
    BlacklistTokenView
)

from rest_framework.permissions import IsAuthenticated
from protocolo.api import ListProtocolosAPIView, UpdateProcessoSeiAPIView, RetrieveProtocoloAPIView, \
    BulkUpdateProcessoSeiAPIView, preflight_view
from protocolo.permissions import CCIGroupOrAPIKeyPermission

class CustomRouter(DefaultRouter):
    def get_urls(self):
        # Get the default URLs for any registered viewsets.
        urls = super().get_urls()
        
        # Define extra URLs from APIViews.
        custom_urls = [
            # Token endpoints:
            path('token/', CookieTokenObtainPairView.as_view(), name='token_obtain_pair'),
            path('token/refresh/', CookieTokenRefreshView.as_view(), name='token_refresh'),
            path('logout/', BlacklistTokenView.as_view(), name='logout'),
        ]
        # You can choose to prepend or append your extra URLs.
        return custom_urls + urls

    def get_api_root_view(self, api_urls=None):
        """
        Customize the API root view to include extra endpoints.
        """
        view = super().get_api_root_view(api_urls)
        # Restrict access by setting the permission classes:
        view.cls.permission_classes = [IsAuthenticated, CCIGroupOrAPIKeyPermission]
        original_get = view.cls.get

        def get(self, request, *args, **kwargs):
            response = original_get(self, request, *args, **kwargs)
            # Add extra endpoints to the API root dictionary.
            response.data.update({
                'token': request.build_absolute_uri(reverse('token_obtain_pair')),
                'token/refresh': request.build_absolute_uri(reverse('token_refresh')),
                'logout': request.build_absolute_uri(reverse('logout')),
            })
            return response

        view.cls.get = get
        return view


class CustomProtocoloRouter(DefaultRouter):
    def get_urls(self):
        # Get the default URLs for any registered viewsets.
        urls = super().get_urls()
        
        # Define extra URLs from APIViews.
        custom_urls = [
            
            # Protocolo endpoints:
            path('protocolo/atualiza-processo-sei/', UpdateProcessoSeiAPIView.as_view(), name='api-update-processo-sei'),
            path('protocolo/atualiza-bulk-processos-sei/', BulkUpdateProcessoSeiAPIView.as_view(), name='api-update-bulk-processos-sei'),
            path('protocolo/atualiza-bulk-processos-sei/', preflight_view, name='preflight-sei'),
            path('protocolo/list', ListProtocolosAPIView.as_view(), name='api-list-protocolos'),
            path('protocolo/', RetrieveProtocoloAPIView.as_view(), name='api-retrieve-protocolo'),
        ]
        # You can choose to prepend or append your extra URLs.
        return custom_urls + urls

    def get_api_root_view(self, api_urls=None):
        """
        Customize the API root view to include extra endpoints.
        """
        view = super().get_api_root_view(api_urls)
        view.cls.permission_classes = [IsAuthenticated, CCIGroupOrAPIKeyPermission]
        original_get = view.cls.get

        def get(self, request, *args, **kwargs):
            response = original_get(self, request, *args, **kwargs)
            # Add extra endpoints to the API root dictionary.
            response.data.update({
                'protocolo/atualiza-processo-sei': request.build_absolute_uri(reverse('api-update-processo-sei')),
                'protocolo/atualiza-bulk-processos-sei': request.build_absolute_uri(reverse('api-update-bulk-processos-sei')),
                'protocolo/list': request.build_absolute_uri(reverse('api-list-protocolos')),
                'protocolo': request.build_absolute_uri(reverse('api-retrieve-protocolo')),
            })
            return response

        view.cls.get = get
        return view