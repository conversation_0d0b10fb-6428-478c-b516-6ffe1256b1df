import os
from dotenv import load_dotenv
load_dotenv('../../.env')
from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', '_core.settings.base')

USE_PROXY = (os.environ.get('USE_PROXY') == 'on')

if USE_PROXY:
    os.environ["http_proxy"] = 'http://10.200.12.140:80'
    os.environ["https_proxy"] = 'http://10.200.12.140:80'

application = get_wsgi_application()
