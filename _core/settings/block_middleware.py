"""
simple middlware to block IP addresses via settings 
variable BLOCKED_IPS
  """
from django.conf import settings
from django.core.exceptions import PermissionDenied

class BlockMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        self.process_request(request)
        response = self.get_response(request)
        return response

    def process_request(self, request):
        ip_address = request.META['REMOTE_ADDR']
        # print('ip_address: ', ip_address)
        # print('ip_address: ', type(ip_address))
        # print('blocked_ips: ', settings.BLOCKED_IPS)
        if ip_address in settings.BLOCKED_IPS:
            # print('*****ip_address is in blocked ips')
            raise PermissionDenied()
        return None