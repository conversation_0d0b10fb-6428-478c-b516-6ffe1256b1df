import os
from dotenv import load_dotenv
from pathlib import Path
from datetime import timedelta

load_dotenv()

BASE_DIR = Path(__file__).resolve().parent.parent.parent

DEBUG = (os.getenv("DEBUG") == 'on')

SECRET_KEY = os.environ.get("SECRET_KEY")

MAINTENANCE_MODE = (os.environ.get("MAINTENANCE_MODE") == 'on')

BASE_URL_VIEWS = 'https://cci.artesp.sp.gov.br/'

ALLOWED_HOSTS = [
    '0.0.0.0',
    '127.0.0.1',
    'localhost',
    'cci.artesp.sp.gov.br',
    '*************',
    '*************',
    '************',
]

CORS_ALLOWED_ORIGINS = [
    'https://0.0.0.0',
    'https://127.0.0.1',
    'https://localhost',
    'https://cci.artesp.sp.gov.br',
    'https://*************',
    'https://*************',
    'https://************',
    'https://sei.sp.gov.br'
]

CORS_ALLOW_METHODS = [
    "GET",
    "POST",
    "PUT",
    "PATCH",
    "DELETE",
    "OPTIONS",
]

CORS_ALLOW_HEADERS = [
    "authorization",
    "content-type",
    "x-requested-with",
    "accept",
    "x-api-key",
]

CORS_ALLOW_CREDENTIALS = True

SESSION_COOKIE_AGE = 14 * 60 * 60
SESSION_COOKIE_SECURE = True

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': os.environ.get("DB_NAME"),
        'USER': os.environ.get("DB_USER"),
        'PASSWORD': os.environ.get("DB_PASS"),
        'HOST': os.environ.get("DB_HOST"),
        'PORT': os.environ.get("DB_PORT", 5432),
    }
}

CSRF_FAILURE_VIEW = '_core.views.csrf_failure'

SITE_ID = 1

INSTALLED_APPS = [
    'dal',
    'dal_select2',
    # 'jazzmin',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'livereload',
    'django.contrib.staticfiles',
    "django.contrib.sites",
    "django.contrib.sitemaps",
    'django.forms',

    # EXTRAS
    'debug_toolbar',
    'cookie_consent',

    'accounts',
    'concessionarias',
    'concessionarias_api',
    'rodovias',
    'rodovias_api',
    'municipios',
    'municipios_api',
    'paineis',
    'regionais',
    'regionais_api',
    'ocorrencias',
    'ocorrencias_api',
    'relatorios',
    'relatorios_api',
    'trafego',
    'viagens',
    'obras',
    'waze',
    'noticias',
    'protocolo',
    'sei',

    'rest_framework',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
    'django_extensions',
    'crispy_forms',
    'crispy_tailwind',
    'anymail',
    'rangefilter',
    'django_admin_listfilter_dropdown',
    'dbbackup',  # django-dbbackup
    'import_export', # django-import-export,
    'captcha',
    'django_celery_results',
    'health_check',
    'health_check.db',
    'health_check.cache',
    'health_check.storage',
    'health_check.contrib.migrations',
    'health_check.contrib.celery',
    'health_check.contrib.celery_ping',
    'health_check.contrib.rabbitmq',
]

FORM_RENDERER = 'django.forms.renderers.TemplatesSetting'

MIDDLEWARE = [
    'django.middleware.gzip.GZipMiddleware',
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'livereload.middleware.LiveReloadScript',
    'accounts.middleware.RegisterUserMiddleware',
    'accounts.middleware.CheckUserAcceptedTerms',
    "cookie_consent.middleware.CleanCookiesMiddleware",
    # '_core.settings.block_middleware.BlockMiddleware',
]

ROOT_URLCONF = '_core.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / "templates"],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.media',
                'accounts.context_processors.globals',
            ],
        },
    },
]

WSGI_APPLICATION = '_core.wsgi.application'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = 'pt-br'

TIME_ZONE = 'America/Sao_Paulo'

USE_I18N = True
USE_TZ = False

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'uploads'

STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 10,
}

AUTH_USER_MODEL = 'accounts.Account'

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,

    'ALGORITHM': 'HS256',
    'SIGNING_KEY': os.environ.get("SIMPLE_JWT"),
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,

    'AUTH_HEADER_TYPES': ('Bearer', 'JWT',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',

    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',

    'JTI_CLAIM': 'jti',

    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(minutes=5),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=1),
}

INTERNAL_IPS = [
    'localhost',
    '127.0.0.1',
    '*************',
]

DEBUG_TOOLBAR_CONFIG = {
    "SHOW_TOOLBAR_CALLBACK": lambda request: DEBUG,
}

LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'

CRISPY_ALLOWED_TEMPLATE_PACKS = "tailwind"
CRISPY_TEMPLATE_PACK = 'tailwind'

ANYMAIL = {
    # 'SENDGRID_API_KEY': os.environ.get("SENDGRID_API_KEY"),
    'BREVO_API_KEY': os.environ.get("BREVO_API_KEY"),
}
EMAIL_BACKEND = "anymail.backends.brevo.EmailBackend" if not DEBUG else "django.core.mail.backends.console.EmailBackend"
# EMAIL_BACKEND = "anymail.backends.sendgrid.EmailBackend" if not DEBUG else "django.core.mail.backends.console.EmailBackend"
EMAIL_ADDRESS = os.environ.get("MAIL_FROM")
SERVER_EMAIL = os.environ.get("MAIL_USERNAME")
EMAIL_HOST = os.environ.get("SMTP_URL")
EMAIL_HOST_USER = os.environ.get("MAIL_FROM")
EMAIL_HOST_PASSWORD = os.environ.get("MAIL_PASSWORD")
EMAIL_PORT = int(os.environ.get("SMTP_PORT"))
EMAIL_USE_TLS = (os.environ.get("USE_TLS") == 'on')
EMAIL_USE_SSL = (os.environ.get("USE_SSL") == 'on')
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {pathname} {funcName} {message}',
            'style': '{',
        },
        'normal': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'filters': {
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        }
    },
    'handlers': {
        'console': {
            'level': 'ERROR',
            'filters': ['require_debug_true'],
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
        'file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': 'logs/errors.log',
            'formatter': 'verbose'
        },
        'file-celery': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': 'logs/celery.log',
            'formatter': 'verbose'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django_file': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'celery': {
            'handlers': ['console', 'file-celery'],
            'level': 'DEBUG',
        },
    }
}

DBBACKUP_STORAGE = 'django.core.files.storage.FileSystemStorage'
DBBACKUP_STORAGE_OPTIONS = {'location': '/home/<USER>/cciapp/cciapp/backups/'}
DBBACKUP_CLEANUP_KEEP = 0

# Celery Configuration Options
CELERY_TIMEZONE = "America/Sao_Paulo"
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60
CELERY_RESULT_EXTENDED = True
CELERY_RESULT_BACKEND = 'django-db'
# CELERY_BROKER_URL = 'redis://127.0.0.1:6379/0'
CELERY_BROKER_URL = os.environ.get('AMQP_BROKER_URL')
# CELERY_HTTP_PROXY = 'http://*************:80'
# CELERY_HTTPS_PROXY = 'http://*************:80'

REDIS_URL = "redis://127.0.0.1:6379"

# CAPTCHA Configuration
CAPTCHA_BACKGROUND_COLOR = '#ffffff'
CAPTCHA_FONT_SIZE = 32

# Sentry Configuration (uncomment if Sentry is used)
# if not DEBUG:
#     sentry_sdk.init(
#         dsn=os.environ.get("SENTRY_URL"),
#         traces_sample_rate=1.0,
#         profiles_sample_rate=1.0,
#     )

DATA_UPLOAD_MAX_NUMBER_FIELDS = 10240

USER_GROUPS = ['cci',]