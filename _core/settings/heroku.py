import environ

from _core.settings.base import *

env = environ.Env()

DEBUG = env.bool('DEBUG', False)

SECRET_KEY = env('SECRET_KEY')

ALLOWED_HOSTS = ['127.0.0.1', 'artespcci.herokuapp.com']

DATABASES = {
    'default': env.db()
}


ANYMAIL = {
    'SENDGRID_API_KEY': env('SENDGRID_API_KEY'),
}
EMAIL_BACKEND = "anymail.backends.sendgrid.EmailBackend"
EMAIL_ADDRESS = env('EMAIL_ADDRESS'),
SERVER_EMAIL = env('SERVER_EMAIL'),
