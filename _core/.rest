###

@token = {{login.response.body.access}}

# @name login
POST http://127.0.0.1:8000/api/v1/token/ HTTP/1.1
content-type: application/json

{
    "email": "<EMAIL>",
    "password": "@rtesp"
}

###

GET http://127.0.0.1:8000/api/v1/ocorrencias/ HTTP/1.1
content-type: application/json


###

GET http://127.0.0.1:8000/public/api/ocorrencias HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/public/api/ocorrencias/fotos HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/public/api/ocorrencias/fim HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/public/api/ocorrencias/congestionamento HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/public/api/ocorrencias/veiculos HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/public/api/ocorrencias/atualizacoes HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/public/api/ocorrencias/interdicoes HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/public/api/rodovias HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/public/api/rodovias/trechos HTTP/1.1
content-type: application/json

###

GET http://127.0.0.1:8000/api/v1/rodovias HTTP/1.1
content-type: application/json
Authorization: Bearer {{token}}

###

GET http://127.0.0.1:8000/public/api/concessionarias HTTP/1.1
content-type: application/json

###
GET http://127.0.0.1:8000/api/v1/concessionarias/ HTTP/1.1
content-type: application/json
Authorization: Bearer {{token}}

###

GET http://127.0.0.1:8000/api/v1/municipios/ HTTP/1.1
content-type: application/json
Authorization: Bearer {{token}}

###

GET http://127.0.0.1:8000/public/api/municipios/ HTTP/1.1
content-type: application/json

###