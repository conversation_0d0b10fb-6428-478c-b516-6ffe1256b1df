import os
from django.apps import apps
from celery import Celery
from celery.schedules import crontab

from dotenv import load_dotenv
load_dotenv()
DEBUG = (os.getenv("DEBUG") == 'on')

# Ensure the Django settings module is set
os.environ.setdefault('DJANGO_SETTINGS_MODULE', '_core.settings.base')

# Set proxy environment variables
# os.environ.setdefault('http_proxy', 'http://*************:80')
# os.environ.setdefault('https_proxy', 'http://*************:80')

# Initialize Celery
app = Celery('_core')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Configure Celery to use Red<PERSON> as the broker and result backend
# app.conf.broker_url = 'redis://127.0.0.1:6379/0'
# app.conf.result_backend = 'redis://127.0.0.1:6379/0'

# Ensure proxy settings are passed to Celery tasks
app.conf.worker_env = {
    'http_proxy': 'http://*************:80',
    'https_proxy': 'http://*************:80',
}

# Load task modules from all registered Django apps.
app.autodiscover_tasks(lambda: [n.name for n in apps.get_app_configs()])

# test sending e-mail url
# http://localhost:8000/contas/trigger-email/?email=<EMAIL>
# http://cci.artesp.sp.gov.br/contas/trigger-email/?email=<EMAIL>

LOCAL_TASKS = {
        'gera-arquivo-waze': {
            'task': 'waze.tasks.gera_arquivo_waze',
            'schedule': crontab(minute='*/35'),
            'args': (),
        },
        'coleta-alertas-waze': {
            'task': 'waze.tasks.collect_waze_alerts',
            'schedule': crontab(minute='*/12'),
            'args': (),
        },
        'coleta-trafego-waze': {
            'task': 'waze.tasks.collect_waze_traffic',
            'schedule': crontab(minute='*/30'),
            'args': (),
        },
        'gera-arquivo-trafego-cci': {
            'task': 'trafego.tasks.make_trafego_csv',
            'schedule': crontab(minute='5'),
            'args': (),
        },
        'envia-boletim-trafego-mensal-cci': {
            'task': 'trafego.tasks.envia_boletim',
            'schedule': crontab(minute='33', hour='9', day_of_month='21'),
            'args': (),
        },
        'envia-relatorio-visitas-mensal-cci': {
            'task': 'accounts.tasks.envia_relatorio_visitas',
            'schedule': crontab(minute='43', hour='9', day_of_month='21'),
            'args': (),
        },
        'limpa-login-mensal': {
            'task': 'accounts.tasks.limpa_login',
            'schedule': crontab(minute='48', hour='9', day_of_month='21'),
            'args': (),
        },
        # 'limpa-coletas-viagens-mensal': {
        #     'task': 'viagens.tasks.limpa_coletas',
        #     'schedule': crontab(minute='53', hour='9', day_of_month='21'),
        #     'args': (),
        # },
        'limpa-coletas-waze-mensal': {
            'task': 'waze.tasks.limpa_coletas_waze',
            'schedule': crontab(minute='7', hour='10', day_of_month='21'),
            'args': (),
        },
        'limpa-visitas-audit-mensal': {
            'task': 'accounts.tasks.limpa_relatorio_visitas',
            'schedule': crontab(minute='3', hour='11', day_of_month='21'),
            'args': (),
        },
        'limpa-trafego-fotos-mensal': {
            'task': 'trafego.tasks.limpa_fotos_trafego',
            'schedule': crontab(minute='7', hour='11', day_of_month='21'),
            'args': (),
        }
        # 'coleta-tempo-viagem': {
        #     'task': 'viagens.tasks.coleta_tempos_de_viagens',
        #     'schedule': crontab(minute='0'),
        #     'args': (),
        # }
    }

PROD_TASKS = {
        'gera-arquivo-waze': {
            'task': 'waze.tasks.gera_arquivo_waze',
            'schedule': crontab(hour=5, minute=0),
            'args': (),
        },
        'gera-arquivo-trafego-cci': {
            'task': 'trafego.tasks.make_trafego_csv',
            'schedule': crontab(hour=6, minute=0),
            'args': (),
        },
        'coleta-alertas-waze': {
            'task': 'waze.tasks.collect_waze_alerts',
            'schedule': crontab(minute='*/30'),
            'args': (),
        },
        'coleta-trafego-waze': {
            'task': 'waze.tasks.collect_waze_traffic',
            'schedule': crontab(minute='*/30'),
            'args': (),
        },
        # 'coleta-tempo-viagem': {
        #     'task': 'viagens.tasks.coleta_tempos_de_viagens',
        #     'schedule': crontab(minute='0'),
        #     'args': (),
        # },
        'envia-boletim-trafego-mensal-cci': {
            'task': 'trafego.tasks.envia_boletim',
            'schedule': crontab(minute='0', hour='3', day_of_month='1,16'),
            'args': (),
        },
        'envia-relatorio-visitas-mensal-cci': {
            'task': 'accounts.tasks.envia_relatorio_visitas',
            'schedule': crontab(minute='0', hour='4', day_of_month='1,16'),
            'args': (),
        },
        'limpa-login-mensal': {
            'task': 'accounts.tasks.limpa_login',
            'schedule': crontab(minute='0', hour='3', day_of_month='2,17'),
            'args': (),
        },
        # 'limpa-coletas-viagens-mensal': {
        #     'task': 'viagens.tasks.limpa_coletas',
        #     'schedule': crontab(minute='0', hour='4', day_of_month='2,17'),
        #     'args': (),
        # },
        'limpa-coletas-waze-mensal': {
            'task': 'waze.tasks.limpa_coletas_waze',
            'schedule': crontab(minute='0', hour='5', day_of_month='2,17'),
            'args': (),
        },
        'limpa-visitas-audit-mensal': {
            'task': 'accounts.tasks.limpa_relatorio_visitas',
            'schedule': crontab(minute='0', hour='6', day_of_month='2'),
            'args': (),
        },
        'limpa-trafego-fotos-mensal': {
            'task': 'trafego.tasks.limpa_fotos_trafego',
            'schedule': crontab(minute='0', hour='7', day_of_month='2,17'),
            'args': (),
        },
        'send-warning-emails-daily': {
            'task': 'accounts.tasks.send_warning_emails',
            'schedule': crontab(hour=7, minute=0),
            'args': (),
        },
        'delete-old-users-every-day': {
            'task': 'accounts.tasks.delete_old_users',
            'schedule': crontab(hour=8, minute=0),
            'args': (),
        },
        'get-news': {
            'task': 'noticias.tasks.retrieve_news',
            'schedule': crontab(minute=0, hour='6,9,12,15,18'),
            'args': (),
        },
        'get-db-obras': {
            'task': 'protocolo.tasks.import_db_obras_data',
            'schedule': crontab(hour='6,12,18', minute='0'),
            'args': (),
        },
        'update-concs': {
            'task': 'concessionarias.tasks.update_concs_rs_nr',
            'schedule': crontab(day_of_month='30', month_of_year='3', hour=3, minute=0),
            'args': (),
        },
        
    }

if not DEBUG:
    # CRON JOBS
    app.conf.beat_schedule = PROD_TASKS
else:
    app.conf.beat_schedule = LOCAL_TASKS


@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f'Request: {self.request!r}')