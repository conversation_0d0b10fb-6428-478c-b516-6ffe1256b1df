import debug_toolbar
from django.conf import settings
from django.contrib import admin
from django.urls import path, include, re_path
from django.conf.urls.static import static
from django.contrib.sitemaps.views import sitemap
from django.views.generic import TemplateView
from rest_framework.routers import DefaultRouter
from accounts.views import CookieTokenRefreshView, CookieTokenObtainPairView, BlacklistTokenView

from ocorrencias.sitemap import OcorrenciasSitemap
from paineis.sitemap import PaineisSitemap
from trafego.sitemap import TrafegoSitemap
from noticias.sitemap import NoticiasSitemap

from .routers import CustomRouter, CustomProtocoloRouter

from .views import permission_denied_view, response_error_handler, handle_server_error, home_view, dashboard, show_ocorrencia_detail_view, show_ocorrencia_print_view, relevantes_view, PainelListView,\
    mapa_view, mapa_artesp_view, generate_ocs_pdf,\
    download_excel_ocs_filtradas, trafego_imprimir, mapa_polywall, painel_view_and_count, obras_view, \
        show_obra_detail_view, get_tipos_obras, get_rodovias, obras_download_excel, home_waze_view, waze_alertas,\
         show_photo_hr, show_trafego_photo_hr, show_trafego, show_obra_photo_hr, cadastro_lista_solicitacao, \
         cadastro_validar_usuario, politica_de_privacidade, termos_de_uso, NoticiaListView, redirect_news
from trafego.views import download_excel as traf_download_excel
from ocorrencias.views import view_photo_hr
from trafego_api.views import TrafegoPublicViewSet
from concessionarias_api.views import ConcessionariaViewSet, ConcessionariaPublicViewSet
from rodovias_api.views import RodoviaViewSet, RodoviaPublicViewSet, TrechoPublicViewSet
from municipios_api.views import MunicipioViewSet, MunicipioPublicViewSet
from ocorrencias_api.views import OcorrenciaViewSet, FotoListViewSet, \
    OcorrenciaFimListViewSet, OcorrenciaVeiculoListViewSet, OcorrenciaPublicViewSet,\
        OcorrenciaFotosPublicViewSet, OcorrenciaFimPublicViewSet, OcorrenciaCongestionamentoPublicViewSet,\
            OcorrenciaVeiculoPublicViewSet, OcorrenciaAtualizacaoPublicViewSet, OcorrenciaInterdicaoPublicViewSet

# Create a router and register our viewsets with it.
router_public = DefaultRouter()
router = CustomRouter()
router_protocolo = CustomProtocoloRouter()

router_public.get_api_root_view().cls.__name__ = "apiroot "
router_public.get_api_root_view().cls.__doc__ = "API pública dos dados de tráfego, ocorrências relevantes e cadastro rodoviário"

# public routes
router_public.register(r'ocorrencias/congestionamento', OcorrenciaCongestionamentoPublicViewSet, basename='api-public-ocorrencia-congestionamento')
# router_public.register(r'ocorrencias/fotos', OcorrenciaFotosPublicViewSet, basename='api-public-ocorrencia-fotos')
# router_public.register(r'ocorrencias/fim', OcorrenciaFimPublicViewSet, basename='api-public-ocorrencia-fim')
router_public.register(r'ocorrencias/veiculos', OcorrenciaVeiculoPublicViewSet, basename='api-public-ocorrencia-veiculos')
router_public.register(r'ocorrencias/atualizacoes', OcorrenciaAtualizacaoPublicViewSet, basename='api-public-ocorrencia-atualizacoes')
router_public.register(r'ocorrencias/interdicoes', OcorrenciaInterdicaoPublicViewSet, basename='api-public-ocorrencia-interdicoes')
router_public.register(r'ocorrencias', OcorrenciaPublicViewSet, basename='api-public-ocorrencia')
router_public.register(r'rodovias/trechos', TrechoPublicViewSet, basename='api-public-rodovias-trechos')
router_public.register(r'rodovias', RodoviaPublicViewSet, basename='api-public-rodovias')
router_public.register(r'concessionarias', ConcessionariaPublicViewSet, basename='api-public-concessionarias')
router_public.register(r'municipios', MunicipioPublicViewSet, basename='api-public-municipios')
router_public.register(r'trafego', TrafegoPublicViewSet, basename='api-public-trafego')

# private routes
router.register(r'concessionarias', viewset=ConcessionariaViewSet, basename='api-concessionaria')
router.register(r'rodovias', RodoviaViewSet, basename='api-rodovia')
router.register(r'rodovias/trechos', RodoviaViewSet, basename='api-trecho')
router.register(r'municipios', MunicipioViewSet, basename='api-municipio')
router.register(r'ocorrencias', OcorrenciaViewSet, basename='api-ocorrencia')
router.register(r'ocorrencias/fotos', FotoListViewSet, basename='api-fotos')
# router.register(r'ocorrencias/fim', OcorrenciaFimListViewSet, basename='api-ocorrencias-fim')
router.register(r'ocorrencias/veiculos', OcorrenciaVeiculoListViewSet, basename='api-ocorrencias-veiculo')

__API_PATH__ = 'api/v1/'
__API_PUBLIC__ = 'public/api/'

from django.urls import path

# def trigger_error(request):
#     division_by_zero = 1 / 0

urlpatterns = [
    # path('sentry-debug/', trigger_error),
    # ************************************************* admin ******************************************************
    path('ht/OgqwGk15VDRqXSRdQ4qH9zz_GbljonQYEE07xKbWNB8/', include('health_check.urls')),
    path('__debug__/', include(debug_toolbar.urls)),
    path('cci-adm-mgr/', admin.site.urls),
    path("politica-de-privacidade", politica_de_privacidade),
    path("termos-de-uso", termos_de_uso),
    path("403/", permission_denied_view),
    # ************************************************* cookies ******************************************************
    path("cookies/", include("cookie_consent.urls")),
    # ************************************************* api routes *************************************************
    path(__API_PATH__ + '', include(router.urls)),
    path(__API_PATH__ + '', include(router_protocolo.urls)),
    path(__API_PUBLIC__ + '', include(router_public.urls)),
    # path(__API_PATH__ + 'token/', CookieTokenObtainPairView.as_view(), name='token_obtain_pair'),
    # path(__API_PATH__ + 'token/refresh/', CookieTokenRefreshView.as_view(), name='token_refresh'),
    # path(__API_PATH__ + 'logout/', BlacklistTokenView.as_view()),
    # ************************************************* views ******************************************************
    path('', home_view, name='home'),
    path(
        'sitemap.xml',
        sitemap,
        {'sitemaps': {
            'ocorrencias': OcorrenciasSitemap,
            'trafego': TrafegoSitemap,
            'dados': PaineisSitemap,
            'noticias': NoticiasSitemap,
        }},
    ),
    path('captcha', include('captcha.urls')),
    path('waze', home_waze_view, name='home-waze'),
    path('mapa', mapa_view, name='mapa'),
    path('HBXmYSfoiTfMznM6O-BV0BQA/protocolo/', include('protocolo.urls', namespace='protocolo')),
    path('telao', mapa_polywall, name='mapa-polywall'),
    # path('mapa-concessoes', mapa_artesp_view, name='mapa-artesp'),
    path('dados/<str:slug>', painel_view_and_count, name='painel-view-and-count'),
    path('dados', PainelListView.as_view(), name='paineis'),
    path('noticias/<str:slug>', redirect_news, name='noticias-redirect'),
    path('noticias', NoticiaListView.as_view(), name='noticias'),
    # path('trafego/pdf', generate_trafego_pdf, name='trafego_pdf'),
    path('trafego/<int:pk>/foto/<int:pk_foto>', show_trafego_photo_hr, name='ver-foto-trafego'),
    path('trafego/download', traf_download_excel, name='download-excel'),
    path('trafego/imprimir', trafego_imprimir, name='trafego_imprimir'),
    path('trafego/<int:pk>', show_trafego, name='ver-trafego'),
    # path('relevantes/ocorrencias-filtradas', generate_ocs_filtradas_pdf, name='relevantes_filtradas_pdf'),
    path('relevantes/ocorrencias-filtradas-excel', download_excel_ocs_filtradas, name='download_excel_ocs_filtradas'),
    # path('relevantes/pdft', generate_ocs_test_pdf, name='relevantes_pdf_test'),
    path('relevantes/pdf', generate_ocs_pdf, name='relevantes_pdf'),
    path('relevantes', relevantes_view, name='relevantes'),
    path('intervencoes-viarias/<str:oc_slug>/foto/<int:pk>', show_obra_photo_hr, name='ver-foto-obra'),
    path('intervencoes-viarias/busca-tipos', get_tipos_obras, name='busca-tipos-obras'),
    path('intervencoes-viarias/download', obras_download_excel, name='obras-download-excel'),
    path('intervencoes-viarias/<str:slug>', show_obra_detail_view, name='home-obra-detail'),
    path('intervencoes-viarias', obras_view, name='obras'),
    path('robots.txt',TemplateView.as_view(template_name="robots.txt", content_type="text/plain")),
    path('dashboard/', dashboard, name='dashboard'),
    path('contas/', include('accounts.urls', namespace='accounts')),
    path('dashboard/concessionarias/', include('concessionarias.urls', namespace='concessionarias')),
    path('dashboard/ocorrencias/', include('ocorrencias.urls', namespace='ocorrencias')),
    path('dashboard/dados/', include('paineis.urls', namespace='paineis')),
    # path('dashboard/relatorios/', include('relatorios.urls', namespace='relatorios')),
    path('dashboard/rodovias/', include('rodovias.urls', namespace='rodovias')),
    path('dashboard/trafego/', include('trafego.urls', namespace='trafego')),
    path('dashboard/obras/', include('obras.urls', namespace='obras')),
    path('dashboard/waze/', waze_alertas, name='waze-alertas'),
    path('dashboard/solicitacao-usuarios/<int:solicitacao_id>', cadastro_validar_usuario, name='cadastro-validar-usuario'),
    path('dashboard/solicitacao-usuarios', cadastro_lista_solicitacao, name='cadastro-lista-solicitacao'),
    path('rodovias/search', get_rodovias, name='busca-rodovias'),
    path('waze-api/', include('waze.urls', namespace='waze')),
    path('sei/', include('sei.urls', namespace='sei')),
    path('<str:slug>', show_ocorrencia_detail_view, name='home-ocorrencia-detail'),
    path('<str:slug>/print', show_ocorrencia_print_view, name='home-ocorrencia-print'),
    path('<str:oc_slug>/foto/<int:pk>', show_photo_hr, name='ver-foto'),
    # path('<str:oc_slug>/vi/<int:photo_pk>', view_photo_hr, name='view-photo-hr'),
    
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.MAINTENANCE_MODE:
   urlpatterns.insert(0, re_path(r'^', TemplateView.as_view(template_name='templates/503.html'), name='maintenance'))

handler500 = handle_server_error
handler403 = response_error_handler
