import os
import re
import itertools
import logging
import datetime
import json
from typing import Dict, Union, List
from collections import Counter
from itertools import chain
from io import BytesIO
from django.http import HttpRequest, JsonResponse, FileResponse
from django.template import RequestContext, Template
from django.contrib.auth.models import Group
from django.conf import settings
from django.db.models.query import Prefetch
from django.db.models import Subquery, Sum, F, ExpressionWrapper, FloatField, QuerySet
from django.urls import reverse
from django.http import Http404, HttpResponse, HttpResponseServerError
from django.shortcuts import render, redirect, get_object_or_404
from django.core.exceptions import PermissionDenied
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.paginator import Paginator
from django.http import HttpResponseServerError
from django.db.models import Q
from django.views.generic import ListView
from django.db.models import Count
from django.templatetags.static import static
from django.utils.html import strip_tags
from django.core.mail import EmailMultiAlternatives

from xhtml2pdf import pisa
from django.template.loader import render_to_string
import pandas as pd
from accounts.forms import CadastroAdminForm
from accounts.models import Profile, SolicitacaoCadastro

from viagens.models import Viagem, Coleta, ColetaInvertidaAdmin
from waze.models import Coleta as ColetaWaze, Pontos, Waze, WazeAlert, ColetaWazeAlerta
from paineis.models import Painel
from trafego.models import TrafegoRodovia, Trafego, TrafegoFoto
from ocorrencias.models import Ocorrencia, OcorrenciaAtualizacao, Foto, OcorrenciaFim,\
    OcorrenciaVeiculo, Interdicao
from rodovias.models import Rodovia
from concessionarias.models import Concessionaria
from municipios.models import Municipio
from obras.models import Categoria as ObraCategoria, Obra, Foto as ObraFoto, \
    Atualizacao as ObraAtualizacao, Interdicao as ObraInterdicao

from noticias.models import Noticia

from viagens.forms import ColetaInvertidaAdminForm

from .tasks import send_mail_cadastro_reprovado


logger = logging.getLogger("django_file")



def is_member(user):
    if user.groups.filter(name='cci').exists():
        return True
    else:
        raise PermissionDenied()
    

def is_adm(user):
    if user.groups.filter(name='adms').exists() or user.is_superuser:
        return True
    else:
        raise PermissionDenied()

def is_lgpd(user):
    if user.groups.filter(name='lgpd').exists() or user.is_superuser:
        return True
    else:
        raise PermissionDenied()


def add_to_json_arr(oc):
    oc_dict = {}
    oc_dict['id'] = oc.id
    oc_dict['slug'] = oc.slug
    oc_dict['lat'] = str(oc.lat)
    oc_dict['lng'] = str(oc.lng)
    oc_dict['classe'] = oc.classe
    oc_dict['subclasse'] = oc.subclasse_ac if oc.classe == 'Acidente' else oc.subclasse_oc
    oc_dict['rodovia'] = oc.rodovia.codigo
    oc_dict['km'] = str(oc.km_inicial)
    oc_dict['ativa'] = 1 if oc.dt_hr_termino is None else 0
    oc_dict['icon'] = static(oc.get_wallmap_icon())
    return oc_dict


def check_if_plot(oc, plot):
    return False if oc["ativa"] == True and plot == 'ENCERRADO' else True


def home_view(request):
    try:
        context = {}

        # context['viagens'] = Viagem.objects.filter(destino__publicar=True).annotate(
        #     distancia_km=ExpressionWrapper(F('distancia_m') * 1.0 / 1000, output_field=FloatField())
        # )\
        #     .prefetch_related('destino')\
        #     .order_by('destino__municipio')
        # context['coleta'] = Coleta.objects.last()
        # coleta_invertida = ColetaInvertidaAdmin.objects.first()
        # context['coleta_invertida'] = coleta_invertida.inverted
        context['is_cci'] = request.user.groups.filter(name='cci').exists()

        trafego_rodovias, trafego_contador, ativacoes_contador = TrafegoRodovia.get_contador()

        cod_rodovias = trafego_rodovias.order_by('rodovia__codigo')\
            .values_list("concessionaria__nome", "rodovia__codigo", "nome")\
            .distinct()
        cod_rod_context = {}
        for tp in cod_rodovias:
            conc = tp[0].upper()
            rodovia_cod = tp[1].upper()
            rodovia_nome = tp[2].upper()
            key = f'{rodovia_cod} - {rodovia_nome}'
            if not key in cod_rod_context:
                cod_rod_context[key] = f'{conc}'
            else:
                cod_rod_context[key] += f',{conc}'
        context["cod_rodovias"] = cod_rod_context
        context['concs'] = trafego_rodovias.order_by('concessionaria__lote')\
            .values_list("concessionaria__lote", "concessionaria__nome").distinct()

        trafego_normal = True
        for _traf, vals in trafego_contador.items():
            if _traf != 'NORMAL':
                if vals.get('km', 0) > 0 or vals.get('trechos', 0) > 0:
                    trafego_normal = False
                    break

        context['total_km_monitorado'] = TrafegoRodovia.get_total_km_trafego_rodovias(
            trafego_rodovias)
        context['trafego_contador'] = trafego_contador
        context['trafego_rodovias'] = trafego_rodovias
        context['trafego_normal'] = trafego_normal
        context['ativacoes_contador'] = ativacoes_contador

        response = render(request, 'home/index.html', context)
        return response
    except Exception as err:
        logger.error(err)
        raise err


def home_waze_view(request):
    try:
        context = {}

        coleta= ColetaWaze.objects.filter(status=200).last()
        context['coleta'] = coleta
        trafegos = coleta.trafegos.all()\
            .annotate(atraso_min=(F('atraso_s') / 60.0))\
            .prefetch_related('pontos')\
            .order_by('-atraso_s','-extensao_m', 'local')
        
        nro_trafegos = trafegos.count()

        cod_rodovias = trafegos.order_by('rodovia')\
                .values_list("rodovia", flat=True)\
                .distinct()
        
        nro_rodovias = cod_rodovias.count()
        
        locais = trafegos.order_by('nome')\
                .values_list("nome", flat=True)\
                .distinct()
        
        resumo = (trafegos
            .values('rodovia')
            .annotate(total_atraso_minutes=(Sum('atraso_min')))
            .annotate(total_extensao_m=Sum('extensao_m'))
            .order_by('rodovia')
        )
        context['host'] = request.get_host()
        context['nro_trafegos'] = nro_trafegos
        context['nro_rodovias'] = nro_rodovias
        context['trafegos'] = trafegos
        context['cod_rodovias'] = cod_rodovias
        context['locais'] = locais
        context['resumo'] = resumo

        return render(request, 'home/index_waze.html', context)
    except Exception as err:
        logger.error(err)
        raise err


def get_subclasses():
    subclasses_oc = [oc[0] for oc in Ocorrencia.SUBCLASSE_CHOICES]
    subclasses_ac = [ac[0] for ac in Ocorrencia.CLASSE_ACIDENTES_CHOICES]
    subclasses = list(set([*subclasses_ac, *subclasses_oc]))
    subclasses.sort()
    return subclasses


def gera_resumo(ocorrencias_qs: Union[QuerySet, List[Ocorrencia]]) -> Dict:
    context = {}
    resumo_nro_ocs_total = ocorrencias_qs.count()
    resumo_ocs_ativas = ocorrencias_qs.filter(dt_hr_termino__isnull=True)
    resumo_nro_ocs_ativas = resumo_ocs_ativas.count()
    resumo_nro_ocs_finalizadas = resumo_nro_ocs_total - resumo_nro_ocs_ativas
    ocs = resumo_ocs_ativas.filter(Q(classe='Ocorrência') | Q(classe='Obra') | Q(classe='Evento natural'))
    acidentes = resumo_ocs_ativas.filter(classe='Acidente')
    ocs = ocs\
        .values('classe', 'subclasse_oc')\
        .order_by().annotate(qtd=Count('subclasse_oc'))
    acidentes = acidentes\
        .values('classe', 'subclasse_ac')\
        .order_by().annotate(qtd=Count('subclasse_ac'))
    context['resumo'] = ocs.union(acidentes)
    context['nro_ocorrencias_ativas'] = resumo_nro_ocs_ativas
    context['nro_ocorrencias_finalizadas'] = resumo_nro_ocs_finalizadas
    return context


def get_filtered_queryset(
        ocorrencias_qs,
        filtro_nome, 
        filtro_data_inicio,
        filtro_data_fim,
        filtro_concessionaria,
        frf,
        filtro_rodovia,
        filtro_municipio,
        filtro_classe,
        filtro_subclasse,
        filtro_finalizada,
    ):


    _day = datetime.datetime.now().day
    _month = datetime.datetime.now().month
    _year = datetime.datetime.now().year
    _now = datetime.datetime(year=_year, month=_month, day=_day,)

    if filtro_nome:
        pattern = re.compile(r'(^oc[0-9]+)|(^[0-9]+)')
        result = pattern.match(filtro_nome)
        if result:
            term = result[0]
            filtro_nome = term.lower()
            ocorrencias_qs = ocorrencias_qs.filter(publicada=True).filter(
                Q(slug__icontains=term) | Q(nro_mits__icontains=term))
        else:
            filtro_nome = ''
    
    if filtro_data_inicio:
        try:
            data_dt = datetime.datetime.strptime(filtro_data_inicio, '%Y-%m-%d')
            first = datetime.datetime(year=2021, month=11, day=16)
            if data_dt > _now or data_dt < first:
                raise ValueError()
            filtro_data_inicio = data_dt
            ocorrencias_qs = ocorrencias_qs.filter(dt_hr_oc__gte=filtro_data_inicio)
        except:
            filtro_data_inicio = None
    
    if filtro_data_fim:
        try:
            data_dt = datetime.datetime.strptime(filtro_data_fim, '%Y-%m-%d')
            if data_dt > _now :
                raise ValueError()
            if filtro_data_inicio and data_dt < filtro_data_inicio:
                raise ValueError()
            filtro_data_fim = data_dt
            ocorrencias_qs = ocorrencias_qs.filter(dt_hr_oc__lte=(filtro_data_fim + datetime.timedelta(days=1)))
        except:
            filtro_data_fim = None

    if filtro_concessionaria:
        try:
            filtro_concessionaria = int(filtro_concessionaria)
            ocorrencias_qs = ocorrencias_qs.filter(
                    concessionarias__pk=filtro_concessionaria)
        except:
            filtro_concessionaria = None
    
    if frf:
        try:
            frf = int(frf)
            ocorrencias_qs = ocorrencias_qs.filter(rodovia=frf)
        except:
            frf = None
    
    if filtro_municipio:
        try:
            filtro_municipio = int(filtro_municipio)
            ocorrencias_qs = ocorrencias_qs.filter(
                municipios__pk=filtro_municipio)
        except:
            filtro_municipio = None
    
    if filtro_classe:
        if filtro_classe.strip() in ['Acidente', 'Evento natural', 'Obra', 'Ocorrência']:
            ocorrencias_qs = ocorrencias_qs.filter(classe=filtro_classe)
    
    if filtro_subclasse:
        ocorrencias_qs = ocorrencias_qs.filter(
                Q(subclasse_oc__in=filtro_subclasse) | Q(subclasse_ac__in=filtro_subclasse)
            )
    
    if filtro_finalizada:
        try:
            filtro_finalizada = int(filtro_finalizada)
            if filtro_finalizada == 0:
                ocorrencias_qs = ocorrencias_qs.filter(
                    dt_hr_termino__isnull=False)
            if filtro_finalizada == 1:
                ocorrencias_qs = ocorrencias_qs.filter(
                    dt_hr_termino__isnull=True)
        except:
            filtro_finalizada = None
    
    if filtro_data_inicio:
        filtro_data_inicio = datetime.datetime.strftime(filtro_data_inicio, '%Y-%m-%d')
    
    if filtro_data_fim:
        filtro_data_fim = datetime.datetime.strftime(filtro_data_fim, '%Y-%m-%d')

    return ocorrencias_qs, ocorrencias_qs.count(), filtro_data_inicio, filtro_data_fim


def get_fim(ocorrencias_qs) -> Dict:
    context = {}
    fim_list = ocorrencias_qs.values_list('pk', 'fim__tipo', 'fim__qtd')
    vitimas_resumo_dict = OcorrenciaFim.vitimas_resumo_dict(fim_list)
    vitimas_resumo_str = OcorrenciaFim.vitimas_resumo_dict_str(vitimas_resumo_dict)
    vitimas_resumo_str_mobile = OcorrenciaFim.vitimas_resumo_dict_str_mobile(vitimas_resumo_dict)
    
    context['vitimas_resumo_str'] = vitimas_resumo_str
    context['vitimas_resumo_str_mobile'] = vitimas_resumo_str_mobile
    return context


def relevantes_view(request):
    try:
        # request filters
        filtro_nome = request.GET.get("filtro_nome", None)
        filtro_data_inicio = request.GET.get("filtro_data_inicio", None)
        filtro_data_fim = request.GET.get("filtro_data_fim", None)
        filtro_concessionaria = request.GET.get("filtro_concessionaria", None)
        frf = request.GET.get("frf", None)
        filtro_rodovia = request.GET.get("filtro_rodovia", None)
        filtro_municipio = request.GET.get("filtro_municipio", None)
        filtro_classe = request.GET.get("filtro_classe", None)
        filtro_subclasse = request.GET.get("filtro_subclasse", "")
        if filtro_subclasse:
            filtro_subclasse = filtro_subclasse.split(",")
        else:
            filtro_subclasse = None
        filtro_finalizada = request.GET.get("filtro_finalizada", None)

        ocorrencias_qs = Ocorrencia.objects.filter(publicada=True)
        n_rows = ocorrencias_qs.count()

        filters = [
            filtro_nome,
            filtro_data_inicio,
            filtro_data_fim,
            filtro_concessionaria,
            frf,
            filtro_rodovia,
            filtro_municipio,
            filtro_classe,
            filtro_subclasse,
            filtro_finalizada,
        ]
        has_filters = sum([1 if f is not None else 0 for f in filters])

        # summary
        resumo_context = gera_resumo(ocorrencias_qs)

        if has_filters:
            ocorrencias_qs, n_rows, filtro_data_inicio, filtro_data_fim = get_filtered_queryset(
                    ocorrencias_qs,
                    filtro_nome, 
                    filtro_data_inicio,
                    filtro_data_fim,
                    filtro_concessionaria,
                    frf,
                    filtro_rodovia,
                    filtro_municipio,
                    filtro_classe,
                    filtro_subclasse,
                    filtro_finalizada,
                )

        # page filter options
        subclasses = get_subclasses()
        municipios = Municipio.objects.all().order_by("nome").values('pk', 'nome')
        concessionarias = Concessionaria.objects.filter(fiscalizacao_artesp=True)\
            .filter(ativa=True).order_by('lote').values('pk', 'lote', 'nome')

        # additional data
        ocorrencias_qs = ocorrencias_qs\
                    .prefetch_related(
                        Prefetch('rodovia', queryset=Rodovia.objects.all().only('codigo')), 
                        # 'rodovia',
                        Prefetch('concessionarias', queryset=Concessionaria.objects.all().only('lote', 'nome')),
                        # 'concessionarias',
                        Prefetch('municipios', queryset=Municipio.objects.all().only('id', 'nome')),
                        # 'vitimas',
                        Prefetch('fim', queryset=OcorrenciaFim.objects.all().only('ocorrencia', 'tipo', 'qtd')),
                        # 'municipios',
                        'atualizado_por', 'interdicao').order_by('-dt_hr_termino','-dt_hr_oc')
        
        # sort if dates
        if filtro_data_inicio or filtro_data_fim:
            ocorrencias_qs = ocorrencias_qs.order_by('dt_hr_oc')

        # slicing
        ocorrencias_qs = ocorrencias_qs[:100]
        
        # vitimas
        vitimas_resumo_str_mobile_context = get_fim(ocorrencias_qs)

        # pagination
        page_number = request.GET.get('page')
        paginator = Paginator(ocorrencias_qs, 10)
        ocorrencias = paginator.get_page(page_number)

        context = dict()
        context = resumo_context | vitimas_resumo_str_mobile_context
        context['has_filters'] = True if has_filters > 0 else False
        context['nro_ocs_filtradas'] = n_rows
        context['concessionarias'] = concessionarias
        context['municipios'] = municipios
        choices = [choice[1] for choice in Ocorrencia.Classe.choices]
        context['classes'] = choices
        context['subclasses'] = subclasses
        context['filtros_ativos'] = True
        context['filtro_nome'] = filtro_nome
        context['filtro_data_inicio'] = filtro_data_inicio
        context['filtro_data_fim'] = filtro_data_fim
        context['filtro_concessionaria'] = filtro_concessionaria
        context['filtro_rodovia'] = filtro_rodovia
        context['frf'] = frf
        context['filtro_municipio'] = filtro_municipio
        context['filtro_classe'] = filtro_classe
        context['filtro_subclasse'] = filtro_subclasse
        context['filtro_finalizada'] = filtro_finalizada
        context['is_paginated'] = True
        context['paginator'] = paginator
        context['ocorrencias'] = ocorrencias
        context['current_url'] = request.build_absolute_uri()
        context['download_excel_ocs_filtradas_url'] = request.build_absolute_uri(reverse('download_excel_ocs_filtradas'))
        context['busca_rodovias_url'] = request.build_absolute_uri(reverse('busca-rodovias'))

        return render(request, 'home/relevantes.html', context)
    except Exception as err:
        logger.error(err)
        raise err

def obras_view(request):
    try:
        str_timedelta = request.GET.get('dias', 30)
        try:
            int_timedelta = int(str_timedelta)
            if int_timedelta > 30:
                int_timedelta = 30
        except:
            int_timedelta = 30
        _now = datetime.datetime.now()
        _timedelta = datetime.timedelta(days=int_timedelta)
        n_dias_atras = _now - _timedelta
        filtro_nome = request.GET.get("filtro_nome", None)
        obras_qs = Obra.objects.filter(publicada=True)
        if filtro_nome:
            obras_qs = obras_qs.filter(
                Q(slug__icontains=filtro_nome) | Q(nro_mits__icontains=filtro_nome))
        else:
            obras_qs = obras_qs.filter(
                Q(dt_hr_termino__gt=n_dias_atras) | Q(dt_hr_termino__isnull=True))

        obras_qs = obras_qs.prefetch_related(
            'categoria', 'rodovia','concessionarias','municipios', 'criado_por', 'atualizado_por')\
                .order_by('-dt_hr_termino', '-atualizado_em')

        tipos = [x[0] for x in obras_qs.values_list('tipo_obra') if x[0] is not None]
        tipos = list(set(tipos))
        tipos.sort()

        context = {}
        context['nro_obras_ativas'] = obras_qs.filter(dt_hr_termino__isnull=True).count()
        context['nro_ocorrencias_finalizadas'] = obras_qs.filter(dt_hr_termino__isnull=False).count()

        context['rodovias'] = list(set([obra.rodovia for obra in obras_qs]))
        context['rodovias'] = sorted(context['rodovias'], key=lambda x: x.codigo, reverse=False)

        context['categorias'] = list(set([obra.categoria for obra in obras_qs if obra.categoria]))
        context['categorias'] = sorted(context['categorias'], key=lambda x: x.nome, reverse=False)

        concessionarias_lst = [obra.concessionarias.all() for obra in obras_qs]
        concessionarias = list(set(list(chain.from_iterable(concessionarias_lst))))
        context['concessionarias'] = sorted(concessionarias, key=lambda x: x.lote, reverse=False)

        municipios_lst = [obra.municipios.all() for obra in obras_qs]
        municipios = list(set(list(chain.from_iterable(municipios_lst))))
        context['municipios'] = sorted(municipios, key=lambda x: x.nome, reverse=False)

        filtro_nome = request.GET["filtro_nome"] if "filtro_nome" in request.GET else None
        filtro_concessionaria = request.GET["filtro_concessionaria"] if "filtro_concessionaria" in request.GET else None
        filtro_rodovia = request.GET["filtro_rodovia"] if "filtro_rodovia" in request.GET else None
        filtro_municipio = request.GET["filtro_municipio"] if "filtro_municipio" in request.GET else None
        filtro_categoria = request.GET["filtro_categoria"] if "filtro_categoria" in request.GET else None
        filtro_tipo = request.GET["filtro_tipo"] if "filtro_tipo" in request.GET else None
        filtro_finalizada = request.GET["filtro_finalizada"] if "filtro_finalizada" in request.GET else None
        filtro_publicada = request.GET["filtro_publicada"] if "filtro_publicada" in request.GET else None
        if filtro_nome:
            try:
                numero_oc = int(filtro_nome)
                obras_qs = Obra.objects.filter(nro_mits=numero_oc).order_by('-pk')
            except Exception as err:
                obras_qs = Obra.objects.filter(slug__iexact=filtro_nome).order_by('-pk')
        if filtro_concessionaria:
            filtro_concessionaria = int(filtro_concessionaria)
            obras_qs = obras_qs.filter(concessionarias__pk=filtro_concessionaria)
        if filtro_rodovia:
            filtro_rodovia = int(filtro_rodovia)
            obras_qs = obras_qs.filter(rodovia=filtro_rodovia)
        if filtro_municipio:
            filtro_municipio = int(filtro_municipio)
            obras_qs = obras_qs.filter(municipios__pk=filtro_municipio)
        if filtro_categoria:
            filtro_categoria = int(filtro_categoria)
            obras_qs = obras_qs.filter(categoria__pk=filtro_categoria)
        if filtro_tipo:
            obras_qs = obras_qs.filter(tipo_obra__icontains=filtro_tipo)
        if filtro_finalizada:
            try:
                filtro_finalizada = int(filtro_finalizada)
                if filtro_finalizada == 0:
                    obras_qs = obras_qs.filter(dt_hr_termino__isnull=False)
                if filtro_finalizada == 1:
                    obras_qs = obras_qs.filter(dt_hr_termino__isnull=True)
                if filtro_finalizada == 2:
                    obras_qs = obras_qs.filter(paralisada=True)
            except:
                pass
        if filtro_publicada:
            filtro_publicada = int(filtro_publicada)
            obras_qs = obras_qs.filter(publicada=filtro_publicada)

        context['tipos'] = tipos
        context['filtro_nome'] = filtro_nome
        context['filtro_concessionaria'] = filtro_concessionaria
        context['filtro_rodovia'] = filtro_rodovia
        context['filtro_categoria'] = filtro_categoria
        context['filtro_municipio'] = filtro_municipio
        context['filtro_tipo'] = filtro_tipo
        context['filtro_finalizada'] = filtro_finalizada
        context['filtro_publicada'] = filtro_publicada

        paginator = Paginator(obras_qs, 10)
        page_number = request.GET.get('page')
        context['is_paginated'] = True
        context['paginator'] = paginator
        obras = paginator.get_page(page_number)
        for obra in obras:
            obra.mun_tmpl = obra\
                .get_municipios_names_template(
                    obra.municipios.values_list('nome', flat=True))
        context['obras'] = obras
        # context['current_url'] = request.build_absolute_uri()
        # context['target_excel_url'] = request.build_absolute_uri(reverse('obras-download-excel'))
        context['busca_tipos_obras_url'] = request.build_absolute_uri(reverse('busca-tipos-obras'))
        context['dias'] = int_timedelta

        #resumo
        context['resumo'] = obras_qs\
            .filter(Q(dt_hr_termino__isnull=True) & Q(paralisada=False))\
            .values('categoria__nome')\
            .order_by().annotate(qtd=Count('pk'))

        return render(request, 'home/obras.html', context)

    except Exception as err:
        logger.error(err)
        raise err

def mapa_polywall(request):
    return get_map_wall_view(request, 'home/mapa_wall.html', logger)

def mapa_view(request):
    return get_map_view(request, 'home/mapa.html', logger)

def get_map_view(request: HttpRequest, template: Template, logger: logging.Logger):
    try:
        _now = datetime.datetime.now()
        current_year = _now.year
        desde_default_dt_str = f'{current_year}-01-01T00:00'
        ocid = request.GET.get('ocid', '0')
        desde = request.GET.get('desde', desde_default_dt_str)
        ate = request.GET.get('ate', '')
        plot = request.GET.get('plot', 'ATIVOS')
        if plot not in ('ATIVOS', 'ENCERRADOS', 'TODOS'):
            plot = 'ATIVOS'
        try:
            ocid = int(ocid)
        except:
            ocid = 0
        try:
            desde_dt = datetime.datetime.strptime(desde, "%Y-%m-%dT%H:%M")
        except:
            desde_dt = datetime.datetime.strptime(desde_default_dt_str, "%Y-%m-%dT%H:%M")
        try:
            ate_dt = datetime.datetime.strptime(ate, "%Y-%m-%dT%H:%M")
        except:
            ate_dt = _now
        if desde_dt > ate_dt:
            desde_dt, ate_dt = ate_dt, desde_dt
        if ate_dt > _now:
            ate_dt = _now
        if desde_dt > _now:
            desde_dt = _now

        diff = (ate_dt - desde_dt).days

        ocs = Ocorrencia.objects\
            .prefetch_related('fim')\
            .filter(publicada=True)

        ocs_ativas = ocs.filter(
            dt_hr_termino__isnull=True)

        ocs = ocs.filter(Q(dt_hr_oc__gte=desde_dt) & Q(dt_hr_oc__lte=ate_dt))

        eventos_ocorrencia = ocs.filter(~Q(classe=Ocorrencia.Classe.ACIDENTE))
        eventos_acidente = ocs.filter(classe=Ocorrencia.Classe.ACIDENTE)
        qtd_evento_ocorrencia = eventos_ocorrencia.count()
        qtd_evento_acidente = eventos_acidente.count()
        evento_obra = Obra.objects.filter(publicada=True)
        qtd_evento_obra = evento_obra.count()
        total_eventos = qtd_evento_acidente + qtd_evento_ocorrencia + qtd_evento_obra
        if total_eventos > 0:
            pct_evento_ocorrencia = (
                qtd_evento_ocorrencia / total_eventos) * 100
            pct_evento_acidente = (qtd_evento_acidente / total_eventos) * 100
            pct_evento_obra = (qtd_evento_obra / total_eventos) * 100
        else:
            pct_evento_ocorrencia = 0
            pct_evento_acidente = 0
            pct_evento_obra = 0

        total_ilesos, total_leves, total_moderados, total_graves, total_obitos, \
            pct_ilesos, pct_leves, pct_moderados, pct_graves, pct_obitos = get_fim_acidentes(eventos_acidente)
        media_diaria_obitos_ac = round(total_obitos/diff, 2) if diff >= 1 else None

        oc_total_ilesos, oc_total_leves, oc_total_moderados, oc_total_graves, oc_total_obitos, \
            oc_pct_ilesos, oc_pct_leves, oc_pct_moderados, oc_pct_graves, oc_pct_obitos = get_fim_ocorrencias(eventos_ocorrencia)
        media_diaria_obitos_oc = round(oc_total_obitos/diff, 2) if diff >= 1 else None


        
        qtd_evento_ocorrencia_ativo = ocs_ativas.filter(
            Q(classe=Ocorrencia.Classe.OCORRENCIA) | Q(classe=Ocorrencia.Classe.EVENTO_NATURAL)
        ).count()
        qtd_evento_acidente_ativo = ocs_ativas.filter(
            classe=Ocorrencia.Classe.ACIDENTE).count()
        qtd_evento_obra_ativo = evento_obra.filter(dt_hr_termino__isnull=True).filter( 
            Q(dt_hr_termino__isnull=True) & Q(paralisada=False)
        ).count()
        qtd_ocs_ativas = ocs_ativas.count() + qtd_evento_obra_ativo
        if qtd_ocs_ativas > 0:
            pct_evento_ocorrencia_ativo = (
                qtd_evento_ocorrencia_ativo / qtd_ocs_ativas) * 100
            pct_evento_acidente_ativo = (
                qtd_evento_acidente_ativo / qtd_ocs_ativas) * 100
            pct_evento_obra_ativo = (
                qtd_evento_obra_ativo / qtd_ocs_ativas) * 100
        else:
            pct_evento_ocorrencia_ativo = 0
            pct_evento_acidente_ativo = 0
            pct_evento_obra_ativo = 0

        ocs_json = []
        ocs_to_plot = None
        if plot == 'ENCERRADOS':
            ocs_to_plot = ocs.filter(dt_hr_termino__isnull=False)
        elif plot == 'ATIVOS':
            ocs_to_plot = ocs_ativas
        else:
            ocs_to_plot = ocs.union(ocs_ativas)

        # filtra últimos 30 dias
        for oc in ocs_to_plot.order_by('-dt_hr_termino', '-dt_hr_oc')[:100]:
            ocs_json.append(add_to_json_arr(oc))

        # link de mapa
        if ocid != 0:
            oc = Ocorrencia.objects.get(pk=ocid)
            ocs_json.append(add_to_json_arr(oc))

        context = {}
        context['ocid'] = ocid
        context['plot'] = plot
        context['desde'] = desde_dt
        context['ate'] = ate_dt
        context['diff'] = diff
        # ocs filtro
        context['qtd_ocs'] = ocs.count() + qtd_evento_obra
        # fim acidentes
        context['total_ilesos'] = total_ilesos
        context['total_leves'] = total_leves
        context['total_moderados'] = total_moderados
        context['total_graves'] = total_graves
        context['total_obitos'] = total_obitos
        context['pct_ilesos'] = round(pct_ilesos, 2)
        context['pct_leves'] = round(pct_leves, 2)
        context['pct_moderados'] = round(pct_moderados, 2)
        context['pct_graves'] = round(pct_graves, 2)
        context['pct_obitos'] = round(pct_obitos, 2)
        context['media_diaria_obitos_ac'] = media_diaria_obitos_ac

        # fim ocorrencias
        context['oc_total_ilesos'] = oc_total_ilesos
        context['oc_total_leves'] = oc_total_leves
        context['oc_total_moderados'] = oc_total_moderados
        context['oc_total_graves'] = oc_total_graves
        context['oc_total_obitos'] = oc_total_obitos
        context['oc_pct_ilesos'] = round(oc_pct_ilesos, 2)
        context['oc_pct_leves'] = round(oc_pct_leves, 2)
        context['oc_pct_moderados'] = round(oc_pct_moderados, 2)
        context['oc_pct_graves'] = round(oc_pct_graves, 2)
        context['oc_pct_obitos'] = round(oc_pct_obitos, 2)
        context['media_diaria_obitos_oc'] = media_diaria_obitos_oc


        #qtds
        context['qtd_evento_ocorrencia'] = qtd_evento_ocorrencia
        context['qtd_evento_acidente'] = qtd_evento_acidente
        context['qtd_evento_obra'] = qtd_evento_obra
        context['pct_evento_ocorrencia'] = round(pct_evento_ocorrencia, 2)
        context['pct_evento_acidente'] = round(pct_evento_acidente, 2)
        context['pct_evento_obra'] = round(pct_evento_obra, 2)
        # ocs ativas
        context['ocorrencias_json'] = json.dumps(ocs_json)
        context['qtd_ocs_ativas'] = qtd_ocs_ativas
        context['qtd_evento_ocorrencia_ativo'] = qtd_evento_ocorrencia_ativo
        context['qtd_evento_acidente_ativo'] = qtd_evento_acidente_ativo
        context['qtd_evento_obra_ativo'] = qtd_evento_obra_ativo
        context['pct_evento_ocorrencia_ativo'] = round(
            pct_evento_ocorrencia_ativo, 2)
        context['pct_evento_acidente_ativo'] = round(
            pct_evento_acidente_ativo, 2)
        context['pct_evento_obra_ativo'] = round(pct_evento_obra_ativo, 2)
        return render(request, template, context)
    except Exception as err:
        logger.error(err)
        raise err

def get_map_wall_view(request: HttpRequest, template: Template, logger: logging.Logger):
    try:
        _now = datetime.datetime.now()
        current_year = _now.year
        desde_default_dt_str = f'{current_year}-01-01T00:00'
        ocid = request.GET.get('ocid', '0')
        desde = request.GET.get('desde', desde_default_dt_str)
        ate = request.GET.get('ate', '')
        plot = request.GET.get('plot', 'ATIVOS')
        if plot not in ('ATIVOS', 'ENCERRADOS', 'TODOS'):
            plot = 'ATIVOS'
        try:
            ocid = int(ocid)
        except:
            ocid = 0
        try:
            desde_dt = datetime.datetime.strptime(desde, "%Y-%m-%dT%H:%M")
        except:
            desde_dt = datetime.datetime.strptime(desde_default_dt_str, "%Y-%m-%dT%H:%M")
        try:
            ate_dt = datetime.datetime.strptime(ate, "%Y-%m-%dT%H:%M")
        except:
            ate_dt = _now
        if desde_dt > ate_dt:
            desde_dt, ate_dt = ate_dt, desde_dt
        if ate_dt > _now:
            ate_dt = _now
        if desde_dt > _now:
            desde_dt = _now

        diff = (ate_dt - desde_dt).days

        ocs = Ocorrencia.objects\
            .prefetch_related('fim')\
            .filter(
                Q(dt_hr_oc__gte=desde_dt) & (Q(dt_hr_oc__lte=ate_dt) | Q(dt_hr_termino__isnull=True))
            ).filter(publicada=True)
        eventos_ocorrencia = ocs.filter(~Q(classe=Ocorrencia.Classe.ACIDENTE) & ~Q(classe=Ocorrencia.Classe.OBRA))
        eventos_acidente = ocs.filter(classe=Ocorrencia.Classe.ACIDENTE)
        qtd_evento_ocorrencia = eventos_ocorrencia.count()
        qtd_evento_acidente = eventos_acidente.count()
        evento_obra = Obra.objects.filter(publicada=True)
        qtd_evento_obra = evento_obra.count()

        ocs_ativas = ocs.filter(
            dt_hr_termino__isnull=True)
        qtd_evento_ocorrencia_ativo = ocs_ativas.filter(
            Q(classe=Ocorrencia.Classe.OCORRENCIA) | Q(classe=Ocorrencia.Classe.EVENTO_NATURAL)
        ).count()
        qtd_evento_acidente_ativo = ocs_ativas.filter(
            classe=Ocorrencia.Classe.ACIDENTE).count()
        qtd_evento_obra_ativo = evento_obra.filter(dt_hr_termino__isnull=True).filter( 
            Q(dt_hr_termino__isnull=True) & Q(paralisada=False)
        ).count()
        qtd_ocs_ativas = ocs_ativas.count() + qtd_evento_obra_ativo
        ocs_json = []

        # filtra últimos 30 dias
        for oc in ocs_ativas.order_by('-dt_hr_termino', '-dt_hr_oc')[:100]:
            ocs_json.append(add_to_json_arr(oc))

        # link de mapa
        if ocid != 0:
            oc = Ocorrencia.objects.get(pk=ocid)
            ocs_json.append(add_to_json_arr(oc))

        context = {}
        context['ocid'] = ocid
        context['plot'] = plot
        context['desde'] = desde_dt
        context['ate'] = ate_dt
        context['diff'] = diff
        # ocs filtro
        context['qtd_ocs'] = ocs.count()

        #qtds
        context['qtd_evento_ocorrencia'] = qtd_evento_ocorrencia
        context['qtd_evento_acidente'] = qtd_evento_acidente
        context['qtd_evento_obra'] = qtd_evento_obra
        
        # ocs ativas
        context['ocorrencias_json'] = json.dumps(ocs_json)
        context['qtd_ocs_ativas'] = qtd_ocs_ativas
        context['qtd_evento_ocorrencia_ativo'] = qtd_evento_ocorrencia_ativo
        context['qtd_evento_acidente_ativo'] = qtd_evento_acidente_ativo
        context['qtd_evento_obra_ativo'] = qtd_evento_obra_ativo
        return render(request, template, context)
    except Exception as err:
        logger.error(err)
        raise err

def mapa_artesp_view(request):
    return redirect('https://dadosabertos.artesp.sp.gov.br')

def show_ocorrencia_detail_view(request, slug):
    try:
        ocorrencia = Ocorrencia.objects.prefetch_related(
            Prefetch('concessionarias', queryset=Concessionaria.objects.all().only('nome')),
            Prefetch('fotos', 
                     queryset=Foto.objects.filter(validada_por__isnull=False)\
                        .prefetch_related('adicionada_por')
            ),
            'municipios','congestionamento', 'interdicao', 'fim', 'veiculos',
            Prefetch('atualizacao', queryset=OcorrenciaAtualizacao.objects.filter(atualizacao_apagada_em__isnull=True).prefetch_related('oc_atualizada_por')))\
            .select_related('rodovia').get(slug=slug)
        if not ocorrencia.publicada:
            raise Ocorrencia.DoesNotExist()

        vitimas_template = ocorrencia.generate_vitimas_str_template(ocorrencia.fim.all())
        context = {
            'ocorrencia': ocorrencia,
            'vitimas_template': vitimas_template,
            # 'atualizacoes': atualizacoes
        }
        return render(request, 'home/ocorrencia.html', context)
    except Ocorrencia.DoesNotExist as err:
        # logger.error(f'slug:{slug}|{err}')
        raise Http404()
    except Exception as err:
        logger.error(err)
        raise err


def show_obra_detail_view(request, slug):
    try:
        obra = Obra.objects.prefetch_related(
            'concessionarias', 'municipios', 
            Prefetch('obra_fotos_set',
                queryset=ObraFoto.objects.select_related(
                    'adicionada_por',
                )
            ),
            Prefetch('obra_atualizacao_set',
                queryset=ObraAtualizacao.objects.select_related(
                    'atualizacao_criado_por',
                    'obra_atualizada_por',
                    'atualizacao_apagada_por',
                )
            ),
            Prefetch('obra_interdicao_set',
                queryset=ObraInterdicao.objects.order_by('-dt_hora_inicio').select_related(
                    'rodovia',
                )
            ),
        ).select_related('categoria', 'rodovia', 'criado_por', 'atualizado_por').get(slug=slug)

        obra_interdicao = obra.obra_interdicao_set.all()
        obra_interdicao_contador = obra_interdicao.count()
        obra_interdicao_nro = 10 if obra_interdicao_contador > 10 else obra_interdicao_contador
        obra_interdicao_set = obra_interdicao[:obra_interdicao_nro]

        context = {
            'obra': obra,
            'obra_interdicao_set': obra_interdicao_set,
            'obra_interdicao_contador': obra_interdicao_contador,
            'obra_interdicao_nro': obra_interdicao_nro,
            'is_cci': request.user.groups.filter(name='cci').exists()
        }
        return render(request, 'home/obra.html', context)
    except Ocorrencia.DoesNotExist as err:
        # logger.error(f'slug:{slug}|{err}')
        raise Http404()
    except Exception as err:
        logger.error(err)
        raise err


def show_ocorrencia_print_view(request, slug):
    try:
        ocorrencia = Ocorrencia.objects.prefetch_related(
            Prefetch('concessionarias', queryset=Concessionaria.objects.all().only('nome')),
            Prefetch('fotos', 
                     queryset=Foto.objects.filter(validada_por__isnull=False)\
                        .prefetch_related('adicionada_por')
            ),
            'municipios','congestionamento', 'interdicao', 'fim', 'veiculos',
            Prefetch('atualizacao', queryset=OcorrenciaAtualizacao.objects.filter(atualizacao_apagada_em__isnull=True).prefetch_related('oc_atualizada_por')))\
            .select_related('rodovia').get(slug=slug)
        if not ocorrencia.publicada:
            raise Ocorrencia.DoesNotExist()
        vitimas_template = ocorrencia.generate_vitimas_str_template(ocorrencia.fim.all())
        context = {
            'ocorrencia': ocorrencia,
            'vitimas_template': vitimas_template,
            # 'atualizacoes': atualizacoes
        }
        return render(request, 'home/ocorrencia_print.html', context)
    except Ocorrencia.DoesNotExist as err:
        # logger.error(f'slug:{slug}|{err}')
        raise Http404()
    except Exception as err:
        logger.error(err)
        raise err


@login_required
@user_passes_test(is_member)
def dashboard(request):
    try:
        # instance = ColetaInvertidaAdmin.objects.first()

        # if request.method == 'POST':
        #     form = ColetaInvertidaAdminForm(request.POST, instance=instance)
        #     if form.is_valid():
        #         form.save()
        #         messages.add_message(request, messages.SUCCESS, f'Opção salva com sucesso')
        #         return redirect('dashboard')  # replace 'success_page' with your success page URL or name
        # else:
        #     form = ColetaInvertidaAdminForm(instance=instance)
        # context = {
        #     'form': form
        # }
        context = {}
        return render(request, 'dashboard/dashboard.html', context)
    except Exception as err:
        logger.error(err)
        raise err


@login_required
@user_passes_test(is_member)
def waze_alertas(request):
    try:
        waze_alerts = WazeAlert.objects.all().order_by('-cor', 'rodovia')
        waze_ultima_coleta = ColetaWazeAlerta.objects.filter(status=200).last()

        resumo = (waze_alerts
            .values('alerta_subtipo')
            .annotate(total_subtipo=(Count('alerta_subtipo')))
            .order_by('alerta_subtipo')
        )

        context = {
            'alertas': waze_alerts,
            'coleta': waze_ultima_coleta,
            'resumo': resumo,
            'total': waze_alerts.count()
        }

        return render(request, 'dashboard/waze/alertas.html', context)
    except Exception as err:
        logger.error(err)
        raise err


@login_required
@user_passes_test(is_lgpd)
def cadastro_lista_solicitacao(request):
    try:
        solicitacoes = SolicitacaoCadastro.objects\
            .filter(eliminada_em__isnull=True).filter(aprovada_em__isnull=True)
        context = {
            'solicitacoes': solicitacoes,
        }
        return render(request, 'dashboard/accounts/cadastro-lista-solicitacao.html', context)
    except Exception as err:
        logger.error(err)
        raise err


@login_required
@user_passes_test(is_lgpd)
def cadastro_validar_usuario(request, solicitacao_id):
    try:
        solicitacao = get_object_or_404(SolicitacaoCadastro, pk=solicitacao_id)
        if solicitacao.aprovada_em or solicitacao.eliminada_em:
            raise Http404()
        normalized_email = None
        if solicitacao.email:
            normalized_email = solicitacao.email.lower()
        if request.method == 'POST':
            form = CadastroAdminForm(request.POST)
            if form.is_valid():
                data = form.cleaned_data
                motivo_reprovacao = data.get('motivo_reprovacao', None)
                tipo_acao = request.POST.keys()
                if 'apagarHidden' in tipo_acao:
                    solicitacao.delete()
                    send_mail_cadastro_reprovado(
                        nome=solicitacao.first_name,
                        email=solicitacao.email,
                        motivo_reprovacao=motivo_reprovacao
                    )
                    messages.add_message(request, messages.SUCCESS, f'Solicitação eliminada com sucesso')
                    return redirect('cadastro-lista-solicitacao')
                else:
                    novo_usuario = {
                        'email': normalized_email,
                        'first_name': solicitacao.first_name,
                        'cpf': solicitacao.cpf,
                        'dt_nascimento': solicitacao.dt_nascimento,
                        'cnpj': solicitacao.cnpj,
                        'funcao': solicitacao.funcao,
                        'telefone': solicitacao.telefone,
                        'empresa': solicitacao.empresa,
                        'end_web': solicitacao.end_web,
                        'motivacao': solicitacao.motivacao,
                        'aceite_termos_uso_em': solicitacao.criada_em,
                        'aceite_termos_uso_ip': solicitacao.ip,
                    }
                    User = get_user_model()
                    user_exists = User.objects.filter(email=novo_usuario['email'])
                    if user_exists:
                        messages.add_message(request, messages.ERROR, f'E-mail já cadastrado no sistema')
                        return redirect('cadastro-lista-solicitacao')
                    senha = create_user(**novo_usuario, **data)
                    solicitacao.aprovada_em = datetime.datetime.now()
                    solicitacao.aprovada_por = request.user
                    solicitacao.save()
                    send_mail_cadastro_validado(
                        nome=solicitacao.first_name,
                        email=solicitacao.email,
                        senha=senha
                    )
                    messages.add_message(request, messages.SUCCESS, f'Usuário criado com sucesso')
                    return redirect('cadastro-lista-solicitacao')
        context = {
            'solicitacao': solicitacao,
            'form': CadastroAdminForm
        }
        return render(request, 'dashboard/accounts/cadastro-validar-usuario.html', context)
    except Exception as err:
        logger.error(err)
        raise err


def handle_server_error(request):
    return render(request, '500.html', None)

def response_error_handler(request, exception=None):
    response = render(request, '403.html', status=403)
    return response

def permission_denied_view(request):
    raise PermissionDenied

def csrf_failure(request, reason="", template_name='403_csrf.html'):...

def politica_de_privacidade(request):
    return render(request, 'politica-de-privacidade.html')

def termos_de_uso(request):
    return render(request, 'termos-de-uso.html')

class NoticiaListView(ListView):
    model = Noticia
    ordering = ['-publication_date']
    paginate_by = 48
    template_name = 'home/noticias.html'
    context_object_name = 'noticias'

def redirect_news(request, slug):
    try:
        obj = get_object_or_404(Noticia, slug=slug)
        print(obj)
        return redirect(obj.link)

    except Exception as err:
        logger.error(err)
        raise HttpResponseServerError()

class PainelListView(ListView):
    model = Painel
    ordering = ['-dt_hr_atualizacao']
    paginate_by = 50
    template_name = 'home/paineis.html'
    context_object_name = 'paineis'

    def dispatch(self, request, *args, **kwargs):
        return redirect('https://dadosabertos.artesp.sp.gov.br')

    # The methods below will no longer be reached due to the immediate redirect in dispatch()
    def get_queryset(self):
        qs = super(PainelListView, self).get_queryset()
        qs = qs.filter(publicado=True).prefetch_related('atualizado_por')
        psort = self.request.GET.get('psort', None)
        if psort:
            if psort == 'nome':
                qs  = qs.order_by('nome')
            elif psort == 'nro_visual':
                qs = qs.order_by('-click_count')
        return qs

    def get_context_data(self, **kwargs):
        context = super(PainelListView, self).get_context_data(**kwargs)
        psort = self.request.GET.get('psort', None)
        if psort:
            if psort != 'nome' and psort != 'nro_visual':
                psort = None
        paineis_auth =  self.get_queryset().filter(apenas_usr_autenticado=True).exclude(apenas_cci=True)
        plural = True if paineis_auth.count() > 1 else False
        paineis_str = []
        for painel in paineis_auth:
            paineis_str.append(f'<span class="font-normal">{painel.get_tipo()}<span>&nbsp;' \
                               f'<span class="font-semibold">"{str(painel).upper()}"</span>')
        paineis = ', '.join(paineis_str)
        msg_paineis = f'{paineis} está disponível apenas para usuários autenticados.'
        if plural:
            msg_paineis = f'{paineis} estão disponíveis apenas para usuários autenticados.'
        context['msg_paineis'] = msg_paineis
        context['psort'] = psort
        context['user_groups'] = self.request.user.groups.all()
        context['painel_groups'] = {painel.id: painel.grupos_acesso.all() for painel in context['paineis']}
        return context

def generate_ocs_pdf(request):
    try:
        ocorrencias = Ocorrencia.objects.all()\
            .filter(dt_hr_termino__isnull=True)\
            .filter(publicada=True)\
            .prefetch_related(
            Prefetch('rodovia', queryset=Rodovia.objects.all().only('codigo')), 
            # 'rodovia',
            Prefetch('concessionarias', queryset=Concessionaria.objects.all().only('lote', 'nome')),
            # 'concessionarias',
            Prefetch('municipios', queryset=Municipio.objects.all().only('id', 'nome')),
            # 'municipios',
            'atualizado_por', 'interdicao', 'fim', 'veiculos')\
            .order_by('-dt_hr_termino', '-atualizado_em')
        
        ocs = ocorrencias.filter(Q(classe='Ocorrência') | Q(classe='Obra') | Q(classe='Evento natural'))
        acidentes = ocorrencias.filter(classe='Acidente')
        ocs = ocs\
            .values('classe', 'subclasse_oc')\
            .order_by().annotate(qtd=Count('subclasse_oc'))
        acidentes = acidentes\
            .values('classe', 'subclasse_ac')\
            .order_by().annotate(qtd=Count('subclasse_ac'))
        resumo = ocs.union(acidentes)

        context = {
            'ocorrencias': ocorrencias,
            'resumo': resumo,
        }
        
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=OcorrenciasRelevantes.pdf'

        html = render_to_string('home/relevantes_pdf.html', context,
                request=request)

        pisa_status = pisa.CreatePDF(
            html, dest=response, link_callback=None)

        if pisa_status.err:
            raise HttpResponseServerError('Erro ao criar o arquivo PDF')

        return response

    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR,
                             "Ocorreu um erro e não foi possível gerar o PDF.")
        return redirect('relevantes')


def generate_ocs_test_pdf(request):
    try:
        ocorrencias = Ocorrencia.objects.all()\
            .filter(dt_hr_termino__isnull=True)\
            .order_by('-atualizado_em')
        context = {
            'ocorrencias': ocorrencias
        }

        return render(request, 'home/relevantes_pdf.html', context)

    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR,
                             "Ocorreu um erro e não foi possível gerar o PDF.")
        return redirect('relevantes')


def trafego_imprimir(request):
    # FETCH DATA
    # try:
        context = {}
        trafego_rodovias = TrafegoRodovia.objects\
            .filter(publicada=True)\
            .prefetch_related(
                'concessionaria', 'rodovia', 'operacao',
                Prefetch(
                    'itens', queryset=Trafego.objects.filter(encerrado_em__isnull=True)
                    .prefetch_related('fotos', 'ocorrencia', 'atualizado_por'),
                    to_attr='trafegos')
            ).order_by('concessionaria__lote', 'rodovia__codigo')

        cod_rodovias = trafego_rodovias.order_by('rodovia__codigo')\
            .values_list("concessionaria__nome", "rodovia__codigo", "nome")\
            .distinct("concessionaria__nome", "rodovia__codigo", "nome")
        cod_rod_context = {}
        for tp in cod_rodovias:
            conc = tp[0].upper()
            rodovia_cod = tp[1].upper()
            rodovia_nome = tp[2].upper()
            key = f'{rodovia_cod} - {rodovia_nome}'
            if not key in cod_rod_context:
                cod_rod_context[key] = f'{conc}'
            else:
                cod_rod_context[key] += f',{conc}'
        context["cod_rodovias"] = cod_rod_context

        trafego_rodovias, trafego_contador, _ = TrafegoRodovia.get_contador()

        trafego_normal = True
        for v in trafego_contador.values():
            if "km" in v.keys():
                if v["km"] > 0:
                    trafego_normal = False
                    break

        context['total_km_monitorado'] = TrafegoRodovia.get_total_km_trafego_rodovias(
            trafego_rodovias)
        context['trafego_contador'] = trafego_contador
        context['trafego_rodovias'] = trafego_rodovias
        context['trafego_normal'] = trafego_normal
        return render(request, 'dashboard/trafego/pdf.html', context)

    # except Exception as err:
    #     messages.add_message(request, messages.ERROR,
    #                          "Ocorreu um erro e não foi possível gerar a página para impressão.")
    #     logger.error(err)
    #     return redirect('home')


def generate_trafego_pdf(request):
    # FETCH DATA
    try:
        context = {}
        trafego_rodovias = TrafegoRodovia.objects\
            .filter(publicada=True)\
            .prefetch_related(
                'concessionaria', 'rodovia', 'operacao',
                Prefetch(
                    'itens', queryset=Trafego.objects.filter(encerrado_em__isnull=True)
                    .prefetch_related('fotos', 'ocorrencia', 'atualizado_por'),
                    to_attr='trafegos')
            ).order_by('concessionaria__lote', 'rodovia__codigo')

        cod_rodovias = trafego_rodovias.order_by('rodovia__codigo')\
            .values_list("concessionaria__nome", "rodovia__codigo", "nome")\
            .distinct("concessionaria__nome", "rodovia__codigo", "nome")
        cod_rod_context = {}
        for tp in cod_rodovias:
            conc = tp[0].upper()
            rodovia_cod = tp[1].upper()
            rodovia_nome = tp[2].upper()
            key = f'{rodovia_cod} - {rodovia_nome}'
            if not key in cod_rod_context:
                cod_rod_context[key] = f'{conc}'
            else:
                cod_rod_context[key] += f',{conc}'
        context["cod_rodovias"] = cod_rod_context

        trafego_rodovias, trafego_contador, _ = TrafegoRodovia.get_contador()

        trafego_normal = True
        for v in trafego_contador.values():
            if v["km"] > 0:
                trafego_normal = False
                break

        context['total_km_monitorado'] = TrafegoRodovia.get_total_km_trafego_rodovias(
            trafego_rodovias)
        context['trafego_contador'] = trafego_contador
        context['trafego_rodovias'] = trafego_rodovias
        context['trafego_normal'] = trafego_normal

        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=Tráfego.pdf'

        html = render_to_string('dashboard/trafego/pdf.html', context)

        pisa_status = pisa.CreatePDF(
            html, dest=response, link_callback=None)
        # if error then show some funy view
        if pisa_status.err:
            return HttpResponse('We had some errors <pre>' + html + '</pre>')

        return response

    except Exception as err:
        messages.add_message(request, messages.ERROR,
                             "Ocorreu um erro e não foi possível gerar o PDF.")
        logger.error(err)
        return redirect('home')


# def generate_ocs_filtradas_pdf(request):
#     try:
#         str_timedelta = request.GET.get('dias', 7)
#         try:
#             int_timedelta = int(str_timedelta)
#             if not request.user.is_authenticated and int_timedelta > 14:
#                 int_timedelta = 14
#         except:
#             int_timedelta = 14
#         _now = datetime.datetime.now()
#         _timedelta = datetime.timedelta(days=int_timedelta)
#         n_dias_atras = _now - _timedelta

#         filtro_nome = request.GET.get("filtro_nome", None)
#         if filtro_nome:
#             ocorrencias_qs = Ocorrencia.objects.filter(
#                 slug__icontains=filtro_nome)
#         else:
#             ocorrencias_qs = Ocorrencia.objects.filter(
#                 Q(dt_hr_termino__gt=n_dias_atras) | Q(dt_hr_termino__isnull=True))

#         ocorrencias_qs = ocorrencias_qs.filter(publicada=True).prefetch_related(
#             Prefetch('rodovia', queryset=Rodovia.objects.all().only('codigo')), 
#             # 'rodovia',
#             Prefetch('concessionarias', queryset=Concessionaria.objects.all().only('lote', 'nome')),
#             # 'concessionarias',
#             Prefetch('municipios', queryset=Municipio.objects.all().only('id', 'nome')),
#             # 'municipios',
#             'atualizado_por', 'interdicao', 'fim', 'veiculos')\
#             .order_by('-dt_hr_termino', '-atualizado_em')

#         filtro_concessionaria = request.GET.get("filtro_concessionaria", None)
#         filtro_rodovia = request.GET.get("filtro_rodovia", None)
#         filtro_municipio = request.GET.get("filtro_municipio", None)
#         filtro_classe = request.GET.get("filtro_classe", None)
#         filtro_subclasse = request.GET.get("filtro_subclasse", None)
#         filtro_finalizada = request.GET.get("filtro_finalizada", None)
#         if filtro_concessionaria:
#             filtro_concessionaria = int(filtro_concessionaria)
#             ocorrencias_qs = ocorrencias_qs.filter(
#                 concessionarias__pk=filtro_concessionaria)
#         if filtro_rodovia:
#             filtro_rodovia = int(filtro_rodovia)
#             ocorrencias_qs = ocorrencias_qs.filter(rodovia=filtro_rodovia)
#         if filtro_municipio:
#             filtro_municipio = int(filtro_municipio)
#             ocorrencias_qs = ocorrencias_qs.filter(
#                 municipios__pk=filtro_municipio)
#         if filtro_classe:
#             ocorrencias_qs = ocorrencias_qs.filter(classe=filtro_classe)
#         if filtro_subclasse:
#             ocorrencias_qs = ocorrencias_qs.filter(
#                 Q(subclasse_oc__exact=filtro_subclasse) | Q(subclasse_ac__exact=filtro_subclasse))
#         if filtro_finalizada:
#             filtro_finalizada = int(filtro_finalizada)
#             ocorrencias_qs = ocorrencias_qs.filter(
#                 dt_hr_termino__isnull=filtro_finalizada)

#         # resumo
#         # resumo = ocorrencias_qs.filter(dt_hr_termino__isnull=True)
#         # context['nro_ocorrencias_ativas'] = resumo.count()
#         # context['nro_ocorrencias_finalizadas'] = ocorrencias_qs.count() - context['nro_ocorrencias_ativas']

#         ocs = ocorrencias_qs.filter(Q(classe='Ocorrência') | Q(classe='Obra') | Q(classe='Evento natural'))
#         acidentes = ocorrencias_qs.filter(classe='Acidente')
#         ocs = ocs\
#             .values('classe', 'subclasse_oc')\
#             .order_by().annotate(qtd=Count('subclasse_oc'))
#         acidentes = acidentes\
#             .values('classe', 'subclasse_ac')\
#             .order_by().annotate(qtd=Count('subclasse_ac'))
#         resumo = ocs.union(acidentes)

#         context = {
#             'ocorrencias': ocorrencias_qs,
#             'dias': int_timedelta,
#             'resumo': resumo,
#         }
#         response = HttpResponse(content_type='application/pdf')
#         response['Content-Disposition'] = 'attachment; filename=OcorrenciasRelevantesComFiltro.pdf'

#         html = render_to_string('home/relevantes_filtradas_pdf.html', context)

#         pisa_status = pisa.CreatePDF(
#             html, dest=response, link_callback=None)
#         # if error then show some funy view
#         if pisa_status.err:
#             return HttpResponse('We had some errors <pre>' + html + '</pre>')
#         return response
       
#         return render(request, 'home/relevantes_filtradas_pdf.html', context)

#     except Exception as err:
#         logger.error(err)
#         messages.add_message(request, messages.ERROR,
#                              "Ocorreu um erro e não foi possível gerar o PDF.")
#         return redirect('relevantes')
    

def download_excel_ocs_filtradas(request):
    try:
        filtro_nome = request.GET.get("filtro_nome", None)
        filtro_data_inicio = request.GET.get("filtro_data_inicio", None)
        filtro_data_fim = request.GET.get("filtro_data_fim", None)
        filtro_concessionaria = request.GET.get("filtro_concessionaria", None)
        frf = request.GET.get("frf", None)
        filtro_rodovia = request.GET.get("filtro_rodovia", None)
        filtro_municipio = request.GET.get("filtro_municipio", None)
        filtro_classe = request.GET.get("filtro_classe", None)
        filtro_subclasse = request.GET.get("filtro_subclasse", "")
        if filtro_subclasse:
            filtro_subclasse = filtro_subclasse.split(",")
        else:
            filtro_subclasse = None
        filtro_finalizada = request.GET.get("filtro_finalizada", None)

        filters = [
            filtro_nome,
            filtro_data_inicio,
            filtro_data_fim,
            filtro_concessionaria,
            frf,
            filtro_rodovia,
            filtro_municipio,
            filtro_classe,
            filtro_subclasse,
            filtro_finalizada,
        ]

        has_filters = sum([1 if f is not None else 0 for f in filters])

        ocorrencias_qs = Ocorrencia.objects.filter(publicada=True)
        n_rows = ocorrencias_qs.count()

        if has_filters:
            ocorrencias_qs, n_rows, filtro_data_inicio, filtro_data_fim = get_filtered_queryset(
                    ocorrencias_qs,
                    filtro_nome, 
                    filtro_data_inicio,
                    filtro_data_fim,
                    filtro_concessionaria,
                    frf,
                    filtro_rodovia,
                    filtro_municipio,
                    filtro_classe,
                    filtro_subclasse,
                    filtro_finalizada,
                )

        # resumo
        ocs = ocorrencias_qs.filter(
            Q(classe='Ocorrência') | Q(classe='Obra') | Q(classe='Evento natural'))
        acidentes = ocorrencias_qs.filter(classe='Acidente')
        ocs = ocs\
            .values('classe', 'subclasse_oc')\
            .order_by().annotate(qtd=Count('subclasse_oc'))
        acidentes = acidentes\
            .values('classe', 'subclasse_ac')\
            .order_by().annotate(qtd=Count('subclasse_ac'))
        resumo = ocs.union(acidentes)


        df_resumo = pd.DataFrame(resumo)
        df_resumo.columns = [
            'CLASSE',
            'SUBCLASSE',
            'QTD.',
        ]

        #atualizacoes
        qs_atualizacoes = OcorrenciaAtualizacao.objects\
            .filter(ocorrencia_id__in=Subquery(ocorrencias_qs.values('pk')))\
            .filter(atualizacao_apagada_por__isnull=True)
        df_atualizacoes = get_df_atualizacoes(qs_atualizacoes)

        #fim
        qs_fim = OcorrenciaFim.objects.filter(
            ocorrencia_id__in=Subquery(ocorrencias_qs.values('pk')))
        df_fim = get_df_fim(qs_fim)
        
        #veiculos
        qs_veiculos = OcorrenciaVeiculo.objects.filter(
            ocorrencia_id__in=Subquery(ocorrencias_qs.values('pk')))
        df_veiculos = get_df_veiculos(qs_veiculos)

        #interdicao
        qs_interdicao = Interdicao.objects.filter(
            ocorrencia_id__in=Subquery(ocorrencias_qs.values('pk')))
        df_interdicoes = get_df_interdicoes(qs_interdicao)

        df: pd.DataFrame = pd.DataFrame.from_records(ocorrencias_qs.values(
            'pk',
            'dt_hr_oc',
            'dt_hr_termino',
            'concessionarias__nome',
            'rodovia__codigo',
            'km_inicial',
            'km_final',
            'lat',
            'lng',
            'municipios__nome',
            'classe',
            'subclasse_oc',
            'subclasse_ac',
            'tipo_ac',
            'observacoes_cci',
            'atualizado_em',
            'atualizado_por__nome_publico'
        ))
        df.columns = [
            'CÓDIGO',
            'DATA HORA INÍCIO',
            'DATA HORA TÉRMINO',
            'CONCESSIONARIA',
            'RODOVIA',
            'KM INICIAL',
            'KM FINAL',
            'LATITUDE',
            'LONGITUDE',
            'MUNICÍPIO',
            'CLASSE',
            'SUBCLASSE OC.',
            'SUBCLASSE AC.',
            'TIPO AC.',
            'OBS.',
            'ATUALIZADO EM',
            'ATUALIZADO POR',
        ]

        df['DETALHES'] = settings.BASE_URL_VIEWS + 'oc' + df['CÓDIGO'].astype(str)

        dfs_to_merge = [df_atualizacoes, df_fim, df_veiculos, df_interdicoes]
        for df_to_merge in dfs_to_merge:
            if df_to_merge is None:
                continue
            df = df.merge(df_to_merge, how='left')
        
        
        df.sort_values('CÓDIGO', ascending=False, inplace=True)
        df.drop_duplicates(inplace=True)
        df.reset_index(drop=True, inplace=True)
        df.index += 1
        b = BytesIO()
        writer = pd.ExcelWriter(b, engine='xlsxwriter')
        df.to_excel(writer, sheet_name='OCS', index=True, index_label='#')
        df_resumo.to_excel(writer, sheet_name='RESUMO', index=False)
        writer.close()
        # Set up the Http response.
        filename = 'ocorrencias_filtradas.xlsx'
        b.seek(0)
        response = FileResponse(
            b,
            as_attachment=True,
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            filename=filename
        )
        # response['Content-Disposition'] = 'attachment; filename=%s' % filename
        return response

    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR,
                             "Ocorreu um erro e não foi possível gerar a planilha eletrônica.")
        return redirect('relevantes')

def get_df_interdicoes(qs_interdicao):
    df_interdicao = pd.DataFrame.from_records(qs_interdicao.values(
            'ocorrencia_id',
            'rodovia__codigo',
            'sentido',
            'faixa',
            'dt_hora_inicio',
            'dt_hora_termino',
            'obs',
        ))

    df_interdicao['resumo'] = \
        df_interdicao['rodovia__codigo'].astype(str) + ' ' +\
            df_interdicao['sentido'].astype(str) + '/' + df_interdicao['faixa'].astype(str) +\
             ' [Bloqueado em ' + df_interdicao['dt_hora_inicio'].astype(str) + ']' +\
                ' [Liberado em ' + df_interdicao['dt_hora_termino'].astype(str) + '] (' +\
                    df_interdicao['obs'].astype(str) + ')'

    df_interdicao = df_interdicao[['ocorrencia_id', 'resumo']]\
            .groupby('ocorrencia_id')['resumo']\
            .apply(lambda x: ' | '.join(x))\
            .reset_index()
    df_interdicao['resumo'] = df_interdicao['resumo'].str.replace('(None)', '-', regex=False)
    df_interdicao['resumo'] = df_interdicao['resumo'].str.replace('None', '-', regex=False)
    df_interdicao['resumo'] = df_interdicao['resumo'].str.replace('NaT', '-', regex=False)
    df_interdicao['resumo'] = df_interdicao['resumo'].str.replace('NaN', '-', regex=False)
    
    df_interdicao.columns = ['CÓDIGO',  'INTERDIÇÕES']
    return df_interdicao

def get_df_veiculos(qs_veiculos):
    if not qs_veiculos:
        return None
    df_veiculos = pd.DataFrame.from_records(qs_veiculos.values(
            'ocorrencia_id',
            'tipo',
        ))

    df_veiculos = df_veiculos[['ocorrencia_id', 'tipo']]
    df_veiculos = df_veiculos.groupby('ocorrencia_id')['tipo']\
                .apply(' | '.join).reset_index()
    df_veiculos.columns = ['CÓDIGO', 'VEÍCULOS']
    df_veiculos['VEÍCULOS'] = df_veiculos['VEÍCULOS'].apply(vehicles_counter)
    return df_veiculos

def get_df_fim(qs_fim):
    if not qs_fim:
        return None
    df_fim = pd.DataFrame.from_records(qs_fim.values(
            'ocorrencia_id',
            'tipo',
            'qtd',
            'obs'
        ))

    df_fim = df_fim[['ocorrencia_id', 'tipo', 'qtd', 'obs']]
    df_fim['resumo'] = df_fim['tipo'].astype(str) + ': ' + df_fim['qtd'].astype(str) +\
             ' (' + df_fim['obs'].astype(str) + ')'
    df_fim = df_fim.groupby('ocorrencia_id')['resumo']\
            .apply(' | '.join).reset_index()
    df_fim['resumo'] = df_fim['resumo'].str.replace('(None)', '', regex=False)
    df_fim['resumo'] = df_fim['resumo'].str.replace('None', '', regex=False)
    df_fim = df_fim[['ocorrencia_id', 'resumo']]
    df_fim.columns = ['CÓDIGO', 'VÍTIMAS']
    return df_fim


def get_df_atualizacoes(qs_atualizacoes):
    if not qs_atualizacoes:
        return None
    df_atualizacoes = pd.DataFrame.from_records(qs_atualizacoes.values(
        'ocorrencia_id',
        'atualizacao',
        'atualizacao_criado_em',
        'atualizacao_criado_por__nome_publico',
        'oc_atualizada_em',
        'oc_atualizada_por__nome_publico',
    ))

    df_atualizacoes = df_atualizacoes[['ocorrencia_id', 'oc_atualizada_em', 'atualizacao']]
    df_atualizacoes['resumo'] = '(' + df_atualizacoes['oc_atualizada_em'].astype(str) + \
        ') ' + df_atualizacoes['atualizacao'].astype(str)
    df_atualizacoes = df_atualizacoes\
        .groupby('ocorrencia_id')['resumo']\
        .apply(lambda x: ' | '.join(x))\
        .reset_index()
    df_atualizacoes.columns = ['CÓDIGO',  'ATUALIZAÇÕES']
    return df_atualizacoes


def vehicles_counter(value):
    lst = value.strip().split(' | ')
    result = str(dict(Counter(lst)))
    return result.replace('{', '').replace('}', '').replace("'", "").replace(',', ' | ')

def get_fim_acidentes(eventos_acidente):
    fim_acidentes = OcorrenciaFim.objects.filter(
            Q(ocorrencia_id__in=Subquery(eventos_acidente.values('pk'))))\
            .values('tipo')\
            .annotate(total=Sum('qtd'))\
            .order_by()
    fim_acidentes_dict = {}
    for dic in fim_acidentes:
        key = dic['tipo']
        value = dic['total']
        if key in fim_acidentes_dict.keys():
            fim_acidentes_dict[key] += value
        else:
            fim_acidentes_dict[key] = value

    total_ilesos = fim_acidentes_dict.get('ILESA', 0) + fim_acidentes_dict.get('ILESO', 0)
    total_leves = fim_acidentes_dict.get('LEVE', 0)
    total_moderados = fim_acidentes_dict.get('MODERADA', 0) + fim_acidentes_dict.get('FERIDO', 0)
    total_graves = fim_acidentes_dict.get('GRAVE', 0)
    total_obitos = fim_acidentes_dict.get('MORTO', 0) + fim_acidentes_dict.get('FATAL', 0)
    total_acidentados = sum(
            [total_ilesos, total_leves, total_moderados, total_graves, total_obitos])
        # Make sure that total is not zero
    if total_acidentados > 0:
        pct_ilesos = (total_ilesos / total_acidentados) * 100
        pct_leves = (total_leves / total_acidentados) * 100
        pct_moderados = (total_moderados / total_acidentados) * 100
        pct_graves = (total_graves / total_acidentados) * 100
        pct_obitos = (total_obitos / total_acidentados) * 100
    else:
        pct_ilesos = 0
        pct_leves = 0
        pct_moderados = 0
        pct_graves = 0
        pct_obitos = 0
    return total_ilesos,total_leves,total_moderados,total_graves,total_obitos,pct_ilesos,pct_leves,pct_moderados,pct_graves,pct_obitos


def get_fim_ocorrencias(eventos_ocorrencias):
    fim_acidentes = OcorrenciaFim.objects.filter(
            Q(ocorrencia_id__in=Subquery(eventos_ocorrencias.values('pk'))))\
            .values('tipo')\
            .annotate(total=Sum('qtd'))\
            .order_by()
    fim_acidentes_dict = {}
    for dic in fim_acidentes:
        key = dic['tipo']
        value = dic['total']
        if key in fim_acidentes_dict.keys():
            fim_acidentes_dict[key] += value
        else:
            fim_acidentes_dict[key] = value

    total_ilesos = fim_acidentes_dict.get('ILESA', 0) + fim_acidentes_dict.get('ILESO', 0)
    total_leves = fim_acidentes_dict.get('LEVE', 0)
    total_moderados = fim_acidentes_dict.get('MODERADA', 0) + fim_acidentes_dict.get('FERIDO', 0)
    total_graves = fim_acidentes_dict.get('GRAVE', 0)
    total_obitos = fim_acidentes_dict.get('MORTO', 0) + fim_acidentes_dict.get('FATAL', 0)
    total_acidentados = sum(
            [total_ilesos, total_leves, total_moderados, total_graves, total_obitos])
        # Make sure that total is not zero
    if total_acidentados > 0:
        pct_ilesos = (total_ilesos / total_acidentados) * 100
        pct_leves = (total_leves / total_acidentados) * 100
        pct_moderados = (total_moderados / total_acidentados) * 100
        pct_graves = (total_graves / total_acidentados) * 100
        pct_obitos = (total_obitos / total_acidentados) * 100
    else:
        pct_ilesos = 0
        pct_leves = 0
        pct_moderados = 0
        pct_graves = 0
        pct_obitos = 0
    return total_ilesos,total_leves,total_moderados,total_graves,total_obitos,pct_ilesos,pct_leves,pct_moderados,pct_graves,pct_obitos

def painel_view_and_count(request, slug):
    try:
        return redirect('https://dadosabertos.artesp.sp.gov.br')
    except Exception as err:
        logger.error(err)
        messages.error(request, "Ocorreu um erro.")
        return redirect('home')

def get_tipos_obras(request):
    try:
        query = request.GET.get('tipo', 'Todos')
        tipos = []
        if query == 'Todos':
            tipos = Obra.objects.all().values_list('tipo_obra', flat=True)
        else:
            tipos = Obra.objects.filter(tipo_obra__icontains=query).values_list('tipo_obra', flat=True)
        entity_json =  {}
        tipos_upper = list(set([ v.upper() if isinstance(v, str) else v for v in tipos ]))
        entity_json['tipos'] = tipos_upper
        json_response = {
                            'success': True,
                            'obj': entity_json
                        }

        return JsonResponse(json_response, status=200)

    except Exception as err:
        logger.error(err)
        return JsonResponse({'success': False, 'message': str(err)}, safe=False, status=500)


def get_rodovias(request):
    try:
        query = request.GET.get('q', 'Todos')
        rodovias = []

        rodovias = Rodovia.objects.filter(
            Q(codigo__icontains=query) | Q(slug__icontains=query)
        ).order_by('codigo')[:10]\
                        .values_list('codigo', 'pk')
        if rodovias:
            entity_json =  {}
            rodovias_upper = list(set([ v.upper() if isinstance(v, str) else v for v in rodovias ]))
            entity_json['rodovias'] = rodovias_upper
            json_response = {
                                'success': True,
                                'obj': entity_json
                            }
            return JsonResponse(json_response, status=200)
        else:
            return JsonResponse(
                {
                    'success': True,
                    'obj': {}
                }
                , status=200)

    except Exception as err:
        logger.error(err)
        return JsonResponse({'success': False, 'message': str(err)}, safe=False, status=500)


from obras.views import get_excel_obras
def obras_download_excel(request):
    try:
        excel_file = get_excel_obras()
        if not excel_file:
            raise ValueError('não foram encontrados registros de obras')
        response = HttpResponse(
            excel_file, 
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="%s"' % 'intervencoes_viarias.xlsx'
        return response
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
    return redirect('obras')


def new_dashboard(request):
    context = {}

    return render(request, 'dashboard/nova-dashboard.html', context)

def show_photo_hr(request, oc_slug, pk):
    try:
        foto = get_object_or_404(Foto, pk=pk)
        if not foto.liberada_nao_autenticados and not request.user.is_authenticated:
            messages.add_message(request, messages.ERROR, "Foto disponível apenas para usuários autenticados")
            return redirect('home-ocorrencia-detail', oc_slug)

        ocorrencia_da_foto = foto.ocorrencia
        ocorrencia = get_object_or_404(Ocorrencia, slug=oc_slug)
        if ocorrencia_da_foto.pk != ocorrencia.pk:
            raise Http404()

        context = {
            'ocorrencia': ocorrencia,
            'foto': foto
        }
        return render(request, 'dashboard/foto.html', context)

    except Ocorrencia.DoesNotExist:
        raise Http404("Ocorrência inexistente")

    except Foto.DoesNotExist:
        raise Http404("Foto inexistente")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        raise Http404()


def show_trafego_photo_hr(request, pk, pk_foto):
    try:
        foto = get_object_or_404(TrafegoFoto, pk=pk_foto)
        trafego = foto.trafego
        if pk != trafego.pk:
            raise Http404("Foto inexistente ou indisponível") 
        traf_rod = trafego.trafego_rodovia
        context = {
            'traf_rod': traf_rod,
            'trafego': trafego,
            'foto': foto,
        }
        return render(request, 'dashboard/foto_trafego.html', context)

    except Foto.DoesNotExist:
        raise Http404("Foto inexistente ou indisponível")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        raise Http404()


def show_trafego(request, pk):
    try:
        trafego = get_object_or_404(Trafego, pk=pk)
        fotos = trafego.fotos.all()
        traf_rod = trafego.trafego_rodovia
        context = {
            'traf_rod': traf_rod,
            'trafego': trafego,
            'fotos': fotos,
        }
        return render(request, 'dashboard/trafego.html', context)

    except Trafego.DoesNotExist:
        raise Http404("Foto inexistente")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        raise Http404()


def show_obra_photo_hr(request, oc_slug, pk):
    try:
        foto = get_object_or_404(ObraFoto, pk=pk)
        obra_da_foto = foto.obra
        obra = get_object_or_404(Obra, slug=oc_slug)

        if obra_da_foto.pk != obra.pk:
            raise Http404()

        context = {
            'obra': obra,
            'foto': foto
        }
        return render(request, 'dashboard/obras/foto.html', context)

    except Obra.DoesNotExist:
        raise Http404("Obra inexistente")

    except ObraFoto.DoesNotExist:
        raise Http404("Foto inexistente")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        raise err
