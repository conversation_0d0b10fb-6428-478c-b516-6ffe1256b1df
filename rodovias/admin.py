from django.contrib import admin
from django.utils import timezone
from .models import Operacao, Rodovia, Trecho, Ponto


class RodoviaAdmin(admin.ModelAdmin):
    readonly_fields = ('criado_por', 'atualizado_por', 'slug')
    search_fields = ('codigo',)

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def save_model(self, request, obj, form, change):
        if not obj.pk:
            obj.criado_em = timezone.now()
            obj.criado_por = request.user
        else:
            obj.atualizado_em = timezone.now()
            obj.atualizado_por = request.user
        obj.save()


class TrechoAdmin(admin.ModelAdmin):
    readonly_fields = ('criado_por', 'atualizado_por')
    search_fields = ('rodovia__codigo', 'concessionaria__nome', 'municipio__nome',)
    list_filter = ('concessionaria', 'regional',)
    
    def get_queryset(self, request):
        # Use all_objects instead of the default objects manager
        return Trecho.all_objects.all()

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_view_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def save_model(self, request, obj, form, change):
        if not obj.pk:
            obj.criado_em = timezone.now()
            obj.criado_por = request.user
        else:
            obj.atualizado_em = timezone.now()
            obj.atualizado_por = request.user
        obj.save()


class OperacaoAdmin(admin.ModelAdmin):

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def has_view_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        else:
            return False

    def save_model(self, request, obj, form, change):
        if not obj.pk:
            obj.criado_em = timezone.now()
            obj.criado_por = request.user
        else:
            obj.atualizado_em = timezone.now()
            obj.atualizado_por = request.user
        obj.save()


class PontoAdmin(admin.ModelAdmin):
    pass


admin.site.register(Rodovia, RodoviaAdmin)
admin.site.register(Trecho, TrechoAdmin)
admin.site.register(Ponto, PontoAdmin)
admin.site.register(Operacao, OperacaoAdmin)