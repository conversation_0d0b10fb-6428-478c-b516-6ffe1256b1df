import datetime
import csv
import logging
from django.shortcuts import render, redirect
from django.urls import reverse
from django.http import Http404, HttpResponse
from django.core.exceptions import PermissionDenied
from django.views.generic import ListView, DetailView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.decorators import login_required, user_passes_test
from django.db.models import Q
from django.contrib import messages
# from django.core.exceptions import ValidationError

import pandas as pd

from .models import Trecho
from municipios.models import Municipio
from concessionarias.models import Concessionaria
from rodovias.models import Rodovia
from regionais.models import Regional
from .forms import TrechoUpdateForm

logger = logging.getLogger("django_file")

def is_member(user):
    if user.groups.filter(name='cci').exists():
        return True
    else:
        raise PermissionDenied()

def handle_qs(request, qs=None):
    if qs is None:
        qs = Trecho.objects.all()
        qs = qs.prefetch_related('rodovia').prefetch_related('municipio').prefetch_related('regional')\
                .prefetch_related('concessionaria')

    filtro_nome = request.GET.get('filtro_nome', '')
    if filtro_nome:
        qs = qs.filter(
            Q(nome__icontains=filtro_nome) |
            Q(rodovia__codigo__icontains=filtro_nome) |
            Q(rodovia__nome_principal__icontains=filtro_nome) |
            Q(rodovia__nome_secundario__icontains=filtro_nome)
        )

    filtro_km_inicial = request.GET.get('filtro_km_inicial', '')
    if filtro_km_inicial:
        filtro_km_inicial = filtro_km_inicial.strip().replace(',', '.').replace('+', '.')
        try:
            filtro_km_inicial = float(filtro_km_inicial)
            qs = qs.filter(km_final__gte=filtro_km_inicial)
        except:
            pass
    filtro_km_final = request.GET.get('filtro_km_final', '')
    if filtro_km_final:
        filtro_km_final = filtro_km_final.strip().replace(',', '.').replace('+', '.')
        try:
            filtro_km_final = float(filtro_km_final)
            qs = qs.filter(km_inicial__lte=filtro_km_final)
        except:
            pass

    filtro_concessionaria = request.GET.get('filtro_concessionaria', '')
    if filtro_concessionaria:
        filtro_concessionaria = int(filtro_concessionaria)
        qs = qs.filter(concessionaria__pk=filtro_concessionaria)

    filtro_regional = request.GET.get('filtro_regional', '')
    if filtro_regional:
        filtro_regional = int(filtro_regional)
        qs = qs.filter(regional__pk=filtro_regional)

    filtro_tipo = request.GET.get('filtro_tipo', '')
    if filtro_tipo:
        qs = qs.filter(tipo__iexact=filtro_tipo)

    filtro_jurisdicao = request.GET.get('filtro_jurisdicao', '')
    if filtro_jurisdicao:
        qs = qs.filter(jurisdicao__iexact=filtro_jurisdicao)

    filtro_municipio = request.GET.get('filtro_municipio', '')
    if filtro_municipio:
        filtro_municipio = int(filtro_municipio)
        qs = qs.filter(municipio__pk=filtro_municipio)

    return qs


class TrechoListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Trecho
    template_name = "dashboard/rodovias/list.html"
    context_object_name = 'trechos'
    paginate_by = 10

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_queryset(self):
        qs = super().get_queryset().prefetch_related('rodovia').prefetch_related('municipio')\
            .prefetch_related('regional').prefetch_related('concessionaria')
        qs = handle_qs(self.request, qs)
        return qs

    def get_context_data(self, **kwargs):
        context = super(TrechoListView,self).get_context_data(**kwargs)

        filtro_nome = self.request.GET.get('filtro_nome', '')
        if filtro_nome:
            context['filtro_nome'] = filtro_nome

        filtro_km_inicial = self.request.GET.get('filtro_km_inicial', '')
        if filtro_km_inicial:
            context['filtro_km_inicial'] = filtro_km_inicial

        filtro_km_final = self.request.GET.get('filtro_km_final', '')
        if filtro_km_final:
            context['filtro_km_final'] = filtro_km_final

        filtro_concessionaria = self.request.GET.get('filtro_concessionaria', '')
        if filtro_concessionaria:
            context['filtro_concessionaria'] = int(filtro_concessionaria)

        filtro_regional = self.request.GET.get('filtro_regional', '')
        if filtro_regional:
            context['filtro_regional'] = int(filtro_regional)

        filtro_tipo = self.request.GET.get('filtro_tipo', '')
        if filtro_tipo:
            context['filtro_tipo'] = filtro_tipo

        filtro_jurisdicao = self.request.GET.get('filtro_jurisdicao', '')
        if filtro_jurisdicao:
            context['filtro_jurisdicao'] = filtro_jurisdicao

        filtro_municipio = self.request.GET.get('filtro_municipio', '')
        if filtro_municipio:
            context['filtro_municipio'] = int(filtro_municipio)

        context['municipios'] = Municipio.objects.all()

        concs = Concessionaria.objects.all()
        context['concessionarias'] = sorted(concs, key=lambda c: str(c), reverse=False)

        context['regionais'] = Regional.objects.all()

        tipos = [tipo[1] for tipo in Trecho.TIPO_CHOICES]
        context['tipos'] = tipos

        jurisdicoes = [jurisdicao[1] for jurisdicao in Trecho.JURISDICAO_CHOICES]
        context['jurisdicoes'] = jurisdicoes

        return context


class TrechoDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    model = Trecho
    template_name = "dashboard/rodovias/detail.html"
    context_object_name = 'trecho'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


class TrechoUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Trecho
    template_name = "dashboard/rodovias/update.html"
    context_object_name = 'trecho'
    form_class = TrechoUpdateForm

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.criado_por = self.request.user
        self.object.atualizado_por = self.request.user
        self.object.atualizado_em = datetime.datetime.now()
        self.object.save()
        return redirect(self.get_success_url())

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('rodovias:detail', kwargs={'pk': self.kwargs.get('pk')})


@login_required
@user_passes_test(is_member)
def download_csv(request):
    qs = Trecho.objects.all().prefetch_related('rodovia').prefetch_related('municipio').prefetch_related('regional')\
        .prefetch_related('concessionaria')

    qs = handle_qs(request, qs)

    df = pd.DataFrame.from_records(
        qs.values(
            'pk',
            'concessionaria__nome',
            'rodovia__codigo',
            'municipio__nome',
            'regional__nome',
            'tipo',
            'km_inicial',
            'km_final',
            'nome',
            'nome_segmento',
            'vdm',
            'jurisdicao',
            'pista',
            'sentido',
            'superficie',
            'faixas',
            'dt_inicio',
            'criado_em',
            'criado_por__nome_publico',
            'atualizado_em',
            'atualizado_por__nome_publico',
            'ramo_dispositivo',
            'sentido_ordem',
            'observacao'
        )
    )

    response = HttpResponse(
        content_type='text/csv',
        headers={'Content-Disposition': 'attachment; filename="rodovias.csv"'},
    )

    df.to_csv(
        path_or_buf=response,
        index=False,
        quoting=csv.QUOTE_NONNUMERIC,
        sep="|",
        header=[
            'ID',
            'CONCESSIONÁRIA',
            'CÓDIGO DA RODOVIA',
            'MUNICÍPIO',
            'REGIONAL',
            'TIPO',
            'KM INICIAL',
            'KM FINAL',
            'NOME',
            'NOME DO SEGMENTO',
            'VDM',
            'JURISDIÇÃO',
            'PISTA',
            'SENTIDO',
            'SUPERFÍCIE',
            'FAIXAS',
            'DATA DE INÍCIO',
            'CRIADO EM',
            'CRIADO POR',
            'ATUALIZADO EM',
            'ATUALIZADO POR',
            'RAMO DE DISPOSITIVO',
            'ORDEM DO SENTIDO',
            'OBSERVAÇÃO'
            ]
    )
    return response


from dal import autocomplete


class RodoviaAutocompleteView(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        # Don't forget to filter out results depending on the visitor !
        if not self.request.user.is_authenticated:
            return Rodovia.objects.none()

        qs = Rodovia.objects.all()

        if self.q:
            qs = qs.filter(codigo__istartswith=self.q)

        return qs