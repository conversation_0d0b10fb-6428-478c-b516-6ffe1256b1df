# Generated by Django 3.2 on 2023-08-21 15:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('trafego', '0028_trafego_obra'),
        ('rodovias', '0008_operacao_sistema'),
    ]

    operations = [
        migrations.CreateModel(
            name='OperacaoAtivacoes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('criada_em', models.DateTimeField(auto_now_add=True, verbose_name='Criada em')),
                ('encerrada_em', models.DateTimeField(blank=True, null=True, verbose_name='Encerrada em')),
                ('criada_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='op_criada_por', to=settings.AUTH_USER_MODEL, verbose_name='Criada por')),
                ('encerrada_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='op_encerrada_por', to=settings.AUTH_USER_MODEL, verbose_name='Encerrada por')),
                ('op', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ativacoes', to='rodovias.operacao', verbose_name='Operação')),
                ('trafego', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='traf_ativacoes', to='trafego.trafegorodovia', verbose_name='Tráfego')),
            ],
            options={
                'verbose_name': 'OperaçãoAtivação',
                'verbose_name_plural': 'OperaçãoAtivações',
                'ordering': ('-criada_em', '-encerrada_em'),
            },
        ),
    ]
