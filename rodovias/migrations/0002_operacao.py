# Generated by Django 3.2 on 2021-11-30 15:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('rodovias', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Operacao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(blank=True, max_length=50, null=True, verbose_name='Nome')),
                ('km_inicial', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='KM Inicial')),
                ('km_final', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='KM Final')),
                ('croqui', models.ImageField(blank=True, null=True, upload_to='trafego/operacoes', verbose_name='Croqui')),
                ('rodovia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operacoes', to='rodovias.rodovia', verbose_name='Rodovia')),
            ],
            options={
                'verbose_name': 'Operação',
                'verbose_name_plural': 'Operações',
                'ordering': ('nome',),
            },
        ),
    ]
