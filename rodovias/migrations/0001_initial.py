# Generated by Django 3.2 on 2021-08-29 13:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('regionais', '0001_initial'),
        ('concessionarias', '0001_initial'),
        ('municipios', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Ponto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lat', models.DecimalField(decimal_places=7, max_digits=12, verbose_name='Latitude')),
                ('lng', models.DecimalField(decimal_places=7, max_digits=12, verbose_name='Longitude')),
                ('desc', models.CharField(max_length=50, verbose_name='Descrição')),
            ],
        ),
        migrations.CreateModel(
            name='Rodovia',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codigo', models.CharField(db_index=True, max_length=20, verbose_name='Código')),
                ('nome_principal', models.CharField(blank=True, max_length=100, null=True, verbose_name='Nome Principal')),
                ('nome_secundario', models.CharField(blank=True, max_length=100, null=True, verbose_name='Nome Secundário')),
                ('slug', models.SlugField(blank=True, max_length=250)),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
                ('atualizado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rodovia_atualizada_por', to=settings.AUTH_USER_MODEL, verbose_name='Atualizado por')),
                ('criado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rodovia_criada_por', to=settings.AUTH_USER_MODEL, verbose_name='Criado por')),
            ],
            options={
                'verbose_name': 'Rodovia',
                'verbose_name_plural': 'Rodovias',
                'ordering': ('codigo',),
            },
        ),
        migrations.CreateModel(
            name='Trecho',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(choices=[('ACESSO', 'Acesso'), ('DISPOSITIVO', 'Dispositivo'), ('EIXO', 'Eixo'), ('FEDERAL', 'Federal'), ('INTERLIGAÇÃO', 'Interligação'), ('MARGINAL', 'Marginal'), ('VICINAL', 'Vicinal'), ('OUTRO', 'Outro')], max_length=50, verbose_name='Tipo')),
                ('km_inicial', models.DecimalField(decimal_places=3, max_digits=8, verbose_name='KM Inicial')),
                ('km_final', models.DecimalField(decimal_places=3, max_digits=8, verbose_name='KM Final')),
                ('nome', models.CharField(blank=True, max_length=100, null=True, verbose_name='Nome Trecho')),
                ('nome_segmento', models.CharField(blank=True, max_length=100, null=True, verbose_name='Nome Segmento')),
                ('vdm', models.DecimalField(blank=True, decimal_places=3, max_digits=13, null=True, verbose_name='VDM')),
                ('jurisdicao', models.CharField(choices=[('MUNICIPAL', 'Municipal'), ('ESTADUAL', 'Estadual'), ('FEDERAL', 'Federal')], default='ESTADUAL', max_length=20, verbose_name='Jurisdição')),
                ('pista', models.CharField(blank=True, choices=[('Simples', 'Simples'), ('Dupla', 'Dupla'), ('Expressa', 'Expressa'), ('Interna', 'Interna'), ('Externa', 'Externa'), ('Marginal', 'Marginal'), ('Dupla com canteiro central', 'Dupla com canteiro central'), ('Dupla com barreira central', 'Dupla com barreira central'), ('Dupla com faixa central', 'Dupla com faixa central'), ('Simples de mão dupla', 'Simples de mão dupla'), ('Simples com mão única', 'Simples com mão única'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], max_length=50, null=True, verbose_name='Pista')),
                ('sentido', models.CharField(blank=True, choices=[('Norte', 'Norte'), ('Sul', 'Sul'), ('Leste', 'Leste'), ('Oeste', 'Oeste'), ('Interno', 'Interno'), ('Externo', 'Externo'), ('Norte-Sul', 'Norte-Sul'), ('Leste-Oeste', 'Leste-Oeste'), ('Noroeste-Sudeste', 'Noroeste-Sudeste'), ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'), ('Sudeste', 'Sudeste'), ('Sudoeste', 'Sudoeste'), ('Nordeste', 'Nordeste'), ('Noroeste', 'Noroeste'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], max_length=50, null=True, verbose_name='Sentido')),
                ('superficie', models.CharField(blank=True, choices=[('DUP-Duplicada', 'DUP-Duplicada'), ('IMP-Terra', 'IMP-Terra'), ('PAV-Pavimentada', 'PAV-Pavimentada'), ('PLAN-Planejada', 'PLAN-Planejada'), ('OUTRO', 'Outro')], max_length=50, null=True, verbose_name='Superfície')),
                ('faixas', models.PositiveSmallIntegerField(blank=True, null=True, verbose_name='Faixas')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
                ('atualizado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trecho_atualizado_por', to=settings.AUTH_USER_MODEL, verbose_name='Atualizado por')),
                ('concessionaria', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trechos', to='concessionarias.concessionaria', verbose_name='Concessionária')),
                ('criado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trecho_criado_por', to=settings.AUTH_USER_MODEL, verbose_name='Criado por')),
                ('municipio', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trechos', to='municipios.municipio', verbose_name='Municipio')),
                ('pontos', models.ManyToManyField(blank=True, to='rodovias.Ponto', verbose_name='Pontos')),
                ('regional', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trechos', to='regionais.regional', verbose_name='Regional')),
                ('rodovia', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='trechos', to='rodovias.rodovia', verbose_name='Rodovia')),
            ],
            options={
                'verbose_name': 'Trecho',
                'verbose_name_plural': 'Trechos',
                'ordering': ('rodovia__codigo', 'km_inicial'),
            },
        ),
    ]
