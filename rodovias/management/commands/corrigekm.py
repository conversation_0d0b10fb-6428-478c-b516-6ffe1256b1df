import pathlib
from django.core import management

from django.db import transaction

from rodovias.models import Trecho

from django.core.management.base import BaseCommand, CommandError


class Command(BaseCommand):
    help = 'Carrega dados iniciais'

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        try:
            with transaction.atomic():
                self.stdout.write(self.style.WARNING('GETTING ALL data...'))
                counter = 0
                for trecho in Trecho.objects.all().iterator():
                    if trecho.sentido_ordem == 'DECRESCENTE':
                        if trecho.km_inicial < trecho.km_final:
                            trecho.km_inicial, trecho.km_final = trecho.km_final, trecho.km_inicial
                            trecho.save()
                            counter += 1
                
                # END
                self.stdout.write(self.style.SUCCESS(f'{counter} trechos foram alterados...'))

        except Exception as err:
            raise CommandError('Error: ' % err)

    def remove_all_from_rodovia_trechos_table(self):
        Trecho.objects.all().delete()

