from django.core.management.base import BaseCommand, CommandError
from rodovias.models import Trecho  # Adjust this import path if needed
import csv
import os
from decimal import Decimal


class Command(BaseCommand):
    help = 'Verifies if all Trecho objects from the CSV file exist in the database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--csv_file', 
            type=str,
            default='/home/<USER>/Projects/python/cciapp/cciapp/bkp/cadastro_rs_nv_portal-cci.csv',
            help='Path to the CSV file containing Trecho data'
        )

    def handle(self, *args, **options):
        csv_file_path = options['csv_file']
        
        if not os.path.exists(csv_file_path):
            raise CommandError(f'File {csv_file_path} does not exist')
        
        self.stdout.write(f'Checking Trecho objects in {csv_file_path}...')
        
        missing_trechos = []
        total_rows = 0
        
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            csv_reader = csv.reader(file)
            
            # Skip header row if it exists
            header = next(csv_reader, None)
            
            for i, row in enumerate(csv_reader, 1):
                if not row:  # Skip empty rows
                    continue
                
                total_rows += 1
                
                # If the row is a single element, split it by pipe
                if len(row) == 1:
                    fields = row[0].split('|')
                else:
                    fields = row
                
                try:
                    # Extract the required fields for new CSV format
                    # concessionaria_id|rodovia_codigo|km_inicial|km_final|pista|sentido|sentido_ordem
                    rodovia_codigo = fields[1]
                    km_inicial = Decimal(fields[2])
                    km_final = Decimal(fields[3])
                    sentido = fields[5]
                    sentido_ordem = fields[6]
                    
                    # Validate km_inicial and km_final relationship with sentido
                    sentido_lower = sentido_ordem.lower() if sentido_ordem else ""
                    
                    # Check for inconsistencies between km values and sentido
                    if sentido_lower == "decrescente" and km_inicial < km_final:
                        error_msg = (
                            f'Row {i}: Inconsistent data - km_inicial({km_inicial}) < km_final({km_final}) '
                            f'but sentido_ordem is "decrescente"'
                        )
                        missing_trechos.append(error_msg)
                        self.stdout.write(self.style.ERROR(error_msg))
                        continue
                        
                    if sentido_lower == "crescente" and km_inicial > km_final:
                        error_msg = (
                            f'Row {i}: Inconsistent data - km_inicial({km_inicial}) > km_final({km_final}) '
                            f'but sentido_ordem is "crescente"'
                        )
                        missing_trechos.append(error_msg)
                        self.stdout.write(self.style.ERROR(error_msg))
                        continue
                    
                    # Check if a matching Trecho exists in the database
                    matching_trechos = Trecho.objects.filter(
                        rodovia__codigo=rodovia_codigo,
                        km_inicial=km_inicial,
                        km_final=km_final,
                        sentido=sentido,
                        sentido_ordem=sentido_ordem
                    )
                    
                    if not matching_trechos.exists():
                        error_msg = (
                            f'Row {i}: No matching Trecho found with '
                            f'rodovia_codigo="{rodovia_codigo}", km_inicial={km_inicial}, km_final={km_final}, '
                            f'sentido="{sentido}", sentido_ordem="{sentido_ordem}"'
                        )
                        missing_trechos.append(error_msg)
                        self.stdout.write(self.style.ERROR(error_msg))
                    else:
                        self.stdout.write(
                            self.style.SUCCESS(f'Row {i}: Matching Trecho found ✓')
                        )
                    
                except IndexError:
                    raise CommandError(f'Invalid data format at row {i}: {row}')
                except ValueError as e:
                    raise CommandError(f'Invalid numeric value at row {i}: {str(e)}')
            
            # Final report
            if missing_trechos:
                self.stdout.write(
                    self.style.ERROR(
                        f'Verification failed: {len(missing_trechos)} out of {total_rows} '
                        f'Trecho objects were not found in the database.'
                    )
                )
                raise CommandError('Some Trechos from the CSV do not exist in the database')
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'All {total_rows} Trecho objects from the CSV file exist in the database!'
                    )
                )