import logging

from django.core.management.base import BaseCommand, CommandError

from rodovias.models import Trecho
from concessionarias.models import Concessionaria
from trafego.models import TrafegoRodovia

logger = logging.getLogger("django_file")

class Command(BaseCommand):
    help = 'Atualiza os trechos da trisol para a econoroeste'

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):

        try:
            # START...
            self.stdout.write(self.style.WARNING('starting script...'))

            self.update_trechos_from_lote9_to_lote31()
            self.update_trafegos_from_lote9_to_lote31()
            
            # END
            self.stdout.write(self.style.SUCCESS('Done'))

        except Exception as err:
            logger.error(err)
            raise CommandError('Error: ' % err)

    def update_trechos_from_lote9_to_lote31(self):
        concessionaria = Concessionaria.objects.filter(lote=31).first()
        Trecho.objects\
            .filter(concessionaria__lote=9)\
            .update(concessionaria=concessionaria)

    def update_trafegos_from_lote9_to_lote31(self):
        concessionaria = Concessionaria.objects.filter(lote=31).first()
        TrafegoRodovia.objects\
            .filter(concessionaria__lote=9)\
            .update(concessionaria=concessionaria)

