import pathlib
from django.core import management


from rodovias.models import Trecho

from django.core.management.base import BaseCommand, CommandError

    
def file_exists(path: str):
    cwd = pathlib.Path(__file__)
    full_path = f'fixtures/{path}'
    _path = cwd.parent.parent.parent / full_path
    return _path.is_file()

class Command(BaseCommand):
    help = 'Carrega dados iniciais'

    def add_arguments(self, parser):
        parser.add_argument('fixture', type=str)

    def handle(self, *args, **options):
        fixture_path = options['fixture']

        try:

            if file_exists(fixture_path):
                # START...
                self.stdout.write(self.style.WARNING('REMOVING ALL data...'))

                self.remove_all_from_rodovia_trechos_table()

                self.stdout.write(self.style.WARNING('INSERTING data...'))
                # DADOS INICIAIS
                management.call_command('loaddata', f'rodovias/fixtures/{fixture_path}', verbosity=0)
                
                # END
                self.stdout.write(self.style.SUCCESS('Data loaded'))

        except Exception as err:
            raise CommandError('Error: ' % err)

    def remove_all_from_rodovia_trechos_table(self):
        Trecho.objects.all().delete()

