from zoneinfo import ZoneInfo
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
import datetime
import csv
from decimal import Decimal
from rodovias.models import Trecho, Rodovia


class Command(BaseCommand):
    help = 'Imports trechos from CSV and updates concessionaria relationships'

    def add_arguments(self, parser):
        parser.add_argument('csv_file', type=str, help='Path to the CSV file')

    def handle(self, *args, **options):
        csv_file = options['csv_file']
        self.stdout.write(self.style.SUCCESS(f'Starting import from {csv_file}'))
        
        # Set the dates for original trechos
        start_date = datetime.datetime(2025, 3, 31, 0, 0, 0, tzinfo=timezone.get_current_timezone())

        end_date_day = datetime.date(2025, 3, 30)
        end_date_time = datetime.time(23, 59, 59, 999999)
        tz = ZoneInfo("America/Sao_Paulo")
        end_date = datetime.datetime.combine(end_date_day, end_date_time)
        end_date_w_tz = end_date.replace(tzinfo=tz)
        
        
        # Counter for statistics
        counter = {
            'processed': 0,
            'updated': 0,
            'created': 0,
            'errors': 0
        }
        
        try:
            with open(csv_file, 'r', encoding='utf-8-sig') as file:
                reader = csv.reader(file, delimiter='|')
                
                # Skip header row if present
                header_row = next(reader, None)
                if header_row and any('concessionaria_id' in col for col in header_row):
                    self.stdout.write(self.style.SUCCESS("Skipped header row"))
                
                # Process each row
                for row in reader:
                    if not row or len(row) < 6:
                        self.stdout.write(self.style.WARNING(f'Skipping invalid row: {row}'))
                        continue
                    
                    # Parse the row
                    try:
                        counter['processed'] += 1
                        
                        # Extract data from CSV
                        concessionaria_id = int(row[0])
                        rodovia_codigo = row[1]
                        km_inicial = Decimal(row[2])
                        km_final = Decimal(row[3])
                        pista = row[4]
                        sentido = row[5]
                        sentido_ordem = row[6] if len(row) > 6 else None
                        
                        # Find the matching Trecho
                        filters = {
                            'rodovia__codigo': rodovia_codigo,
                            'km_inicial': km_inicial,
                            'km_final': km_final,
                            'sentido': sentido
                        }
                        
                        if sentido_ordem:
                            filters['sentido_ordem'] = sentido_ordem
                            
                        # Use all_objects to ensure we get all trechos regardless of concessionaria status
                        matching_trechos = Trecho.all_objects.filter(**filters)
                        
                        if not matching_trechos.exists():
                            error_msg = f'No match found for: {rodovia_codigo}, KM {km_inicial}-{km_final}, {sentido}, {sentido_ordem}'
                            self.stdout.write(self.style.ERROR(error_msg))
                            raise CommandError(error_msg)
                        
                        # Ensure exactly one match
                        if matching_trechos.count() > 1:
                            error_msg = f'Multiple matches found for: {rodovia_codigo}, KM {km_inicial}-{km_final}, {sentido}, {sentido_ordem}. Found {matching_trechos.count()} matches.'
                            self.stdout.write(self.style.ERROR(error_msg))
                            raise CommandError(error_msg)
                        
                        # Get the single matching trecho
                        original_trecho = matching_trechos.first()
                        
                        # Create a copy with the new concessionaria
                        new_trecho = Trecho.all_objects.get(pk=original_trecho.pk)
                        new_trecho.pk = None  # This will create a new instance
                        new_trecho.concessionaria_id = concessionaria_id
                        new_trecho.dt_inicio = start_date
                        new_trecho.dt_fim = None
                        new_trecho.save()
                        
                        # Copy many-to-many relationships
                        for ponto in original_trecho.pontos.all():
                            new_trecho.pontos.add(ponto)
                        
                        # Update the original trecho with end date
                        original_trecho.dt_fim = end_date_w_tz
                        original_trecho.save()
                        
                        counter['created'] += 1
                        counter['updated'] += 1
                        
                        self.stdout.write(self.style.SUCCESS(
                            f'Updated trecho: {rodovia_codigo}, KM {km_inicial}-{km_final}, {sentido}'
                        ))
                    
                    except Exception as e:
                        counter['errors'] += 1
                        self.stdout.write(self.style.ERROR(f'Error processing row: {e}'))
                        raise CommandError(f'Error processing row: {e}')
        
        except FileNotFoundError:
            raise CommandError(f'File not found: {csv_file}')
        except Exception as e:
            raise CommandError(f'Error reading file: {e}')
        
        # Print summary
        self.stdout.write(self.style.SUCCESS(
            f'Import completed: {counter["processed"]} rows processed, '
            f'{counter["created"]} trechos created, {counter["updated"]} original trechos updated, '
            f'{counter["errors"]} errors encountered.'
        ))