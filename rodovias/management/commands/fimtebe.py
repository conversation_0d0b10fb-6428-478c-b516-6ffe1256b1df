from django.core.management.base import BaseCommand
from django.db.models import Q
from rodovias.models import Trecho, Concessionaria
from django.utils import timezone


class Command(BaseCommand):
    help = 'Duplicates Trecho objects from TEBE concessionaria to ECONOROESTE with dt_inicio set to 2025-03-04'

    def handle(self, *args, **options):
        # Find the TEBE concessionaria
        tebe_filter = Q(concessionaria__nome__icontains='tebe')
        trechos_tebe = Trecho.objects.filter(tebe_filter)
        
        if not trechos_tebe.exists():
            self.stdout.write(self.style.WARNING('No Trecho objects found with concessionaria containing "tebe"'))
            return
        
        # Find the ECONOROESTE concessionaria
        try:
            econoroeste = Concessionaria.objects.filter(nome__icontains='econoroeste').first()
            if not econoroeste:
                self.stdout.write(self.style.ERROR('No concessionaria found with name containing "econoroeste"'))
                return
        except Concessionaria.DoesNotExist:
            self.stdout.write(self.style.ERROR('No concessionaria found with name containing "econoroeste"'))
            return
        
        # Set the dt_inicio date
        dt_inicio = timezone.datetime(2025, 3, 4, 0, 0, 0, tzinfo=timezone.get_current_timezone())
        
        duplicated_count = 0
        
        # Duplicate Trecho objects
        for trecho in trechos_tebe:
            # Create a new instance without saving it to the database yet
            new_trecho = Trecho(
                # Copy all fields except pk, concessionaria, and dt_inicio
                tipo=trecho.tipo,
                km_inicial=trecho.km_inicial,
                km_final=trecho.km_final,
                nome=trecho.nome,
                nome_segmento=trecho.nome_segmento,
                vdm=trecho.vdm,
                jurisdicao=trecho.jurisdicao,
                pista=trecho.pista,
                sentido=trecho.sentido,
                superficie=trecho.superficie,
                faixas=trecho.faixas,
                rodovia=trecho.rodovia,
                municipio=trecho.municipio,
                regional=trecho.regional,
                ramo_dispositivo=trecho.ramo_dispositivo,
                sentido_ordem=trecho.sentido_ordem,
                observacao=trecho.observacao,
                
                # Set the new concessionaria and dt_inicio
                concessionaria=econoroeste,
                dt_inicio=dt_inicio,
                
                # Set creation metadata
                criado_em=timezone.now(),
                atualizado_em=timezone.now(),
                # Keep the user reference if needed, otherwise it will be NULL
                criado_por=trecho.criado_por,
                atualizado_por=trecho.atualizado_por,
            )
            
            # Save the new Trecho
            new_trecho.save()
            
            # Add many-to-many relationships after saving
            for ponto in trecho.pontos.all():
                new_trecho.pontos.add(ponto)
            
            duplicated_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully duplicated {duplicated_count} Trecho objects from TEBE to ECONOROESTE')
        )