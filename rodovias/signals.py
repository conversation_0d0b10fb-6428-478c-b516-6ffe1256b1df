from django.db.models.signals import pre_save, post_save
from django.utils import timezone
from django.dispatch import receiver
from django.utils.text import slugify
from django.db.models import Q, F

from .models import Rodovia, Trecho
from ocorrencias.models import Ocorrencia


def slug(sender, instance, *args, **kwargs):
    codigo_sem_barra = instance.codigo.replace("/", "-")
    slug = slugify(codigo_sem_barra)
    exists = Rodovia.objects.filter(slug=slug).exists()
    if exists:
        slug = f'{slug}-{instance.id}'
    instance.slug = slug


def criado_em(sender, instance, *args, **kwargs):
    now = timezone.now()
    if not instance.criado_em:
        instance.criado_em = now


def atualizado_em(sender, instance, created, **kwargs):
    if not created:
        sentido = instance.sentido \
            if instance.sentido != 'SEM INFORMAÇÃO' and instance.sentido != 'NÃO SE APLICA' \
            else None
        ocs = Ocorrencia.objects.filter(rodovia=instance.rodovia.pk)\
            .filter(sentido=sentido)\
            .annotate(diff=F('km_final')-F('km_inicial'))\
            .filter(
                (
                    Q(diff__gte=0) & 
                    Q(km_final__gte=instance.km_inicial) &
                    Q(km_inicial__lte=instance.km_final)
                ) | (
                    Q(diff__lt=0) & 
                    Q(km_inicial__gte=instance.km_inicial) &
                    Q(km_final__lte=instance.km_final)
                )
            )
        for oc in ocs:
            oc.concessionarias.clear()
            concessionarias = oc.get_concessionarias()
            oc.concessionarias.add(*concessionarias)


pre_save.connect(slug, sender=Rodovia)
pre_save.connect(criado_em, sender=Rodovia)
pre_save.connect(criado_em, sender=Trecho)

post_save.connect(atualizado_em, sender=Trecho)