from django.urls import path
from django.views.generic import TemplateView

from .views import TrechoListView, TrechoDetailView, download_csv, TrechoUpdateView, RodoviaAutocompleteView
from .models import Rodovia


from dal import autocomplete

app_name = 'rodovias'

urlpatterns = [
    path('', TrechoListView.as_view(), name='list'),
    path('csv-download/', download_csv, name='csv-download'),
    path('rodovia-autocomplete/', RodoviaAutocompleteView.as_view(), name='rodovia-autocomplete'),
    # path('rodovia-autocomplete/', autocomplete.Select2QuerySetView.as_view(model=Rodovia), name='rodovia-autocomplete'),
    path('<int:pk>/', TrechoDetailView.as_view(), name='detail'),
    path('<int:pk>/edit', TrechoUpdateView.as_view(), name='edit'),
]
