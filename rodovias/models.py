from django.db import models
from django.db.models import Max, Min
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.utils.functional import cached_property
from concessionarias.models import Concessionaria
from municipios.models import Municipio
from regionais.models import Regional

class Rodovia(models.Model):
    codigo = models.CharField(max_length=20, verbose_name='Código', db_index=True,)
    nome_principal = models.CharField(max_length=100, blank=True, null=True, verbose_name='Nome Principal')
    nome_secundario = models.CharField(max_length=100, blank=True, null=True, verbose_name='Nome Secundário')
    slug = models.SlugField(max_length=250, blank=True)
    criado_em = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='rodovia_criada_por'
    )
    atualizado_em = models.DateTimeField(auto_now=True, null=True, blank=True, verbose_name='Atualizado em')
    atualizado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Atualizado por', related_name='rodovia_atualizada_por'
    )

    class Meta:
        ordering = ('codigo',)
        verbose_name = 'Rodovia'
        verbose_name_plural  = 'Rodovias'

    def __str__(self):
        return f'{self.codigo}'


    @cached_property
    def lista_concessionarias_da_rodovia(self):
        # todos os trechos da rodovia
        trechos = self.trechos.all()
        concessionarias = set([trecho.concessionaria.nome for trecho in trechos])
        return ', '.join(concessionarias)


    def get_concessionarias_tuple(self):
        # todos os trechos da rodovia
        trechos = self.trechos.all()
        concessionarias = Concessionaria.objects\
            .filter(ativa=True)\
            .filter(trechos__in=trechos)\
            .distinct().order_by('lote')
        return [(conc.pk, conc) for conc in concessionarias]
    
    def get_pistas(self):
        pistas = self.trechos.values_list('pista', flat=True).distinct()
        pistas_set = list(set(pistas))
        return [(pista, pista,) for pista in pistas_set]
    
    def get_sentidos(self):
        sentidos = self.trechos.values_list('sentido', flat=True).distinct()
        sentidos_set = list(set(sentidos))
        return [(sentido, sentido,) for sentido in sentidos_set]

    def get_concessionarias_km_inicial_final(self, concessionaria__nome = None, sentido = None):
        # todos os trechos da rodovia
        trechos = self.trechos\
        .filter(concessionaria__ativa=True)\
        .filter(concessionaria__fiscalizacao_artesp=True)\
        .values(
            'pk',
            'concessionaria__nome',
            'sentido', 'km_inicial' , 'km_final', 'sentido_ordem', 'nome', 'pista')\
        .order_by('sentido', 'km_inicial')
        if concessionaria__nome:
            trechos = list(filter(lambda item: item["concessionaria__nome"] == concessionaria__nome, trechos))
        if sentido:
            trechos = list(filter(lambda item: item["sentido"] == sentido, trechos))
        # result = {}
        # for _dict in trechos:
        #     pk = _dict['pk']
        #     conc = _dict['concessionaria__nome']
        #     sentido = _dict['sentido']
        #     sentido_ordem = _dict['sentido_ordem']
        #     # key = f'{pk}/{conc}/{sentido}'
        #     km_inicial = _dict['km_inicial']
        #     km_final = _dict['km_final']
        #     nome = _dict['nome']
        #     if sentido_ordem == 'DECRESCENTE':
        #         km_inicial, km_final = km_final, km_inicial
        #     result[pk] = [conc,sentido, km_inicial, km_final, sentido_ordem, nome]
        # final = {k: v for k, v in sorted(result.items(), key=lambda item: item[1][2])}
        return trechos

        

class Ponto(models.Model):
    lat = models.DecimalField(_("Latitude"), max_digits=12, decimal_places=7)
    lng = models.DecimalField(_("Longitude"), max_digits=12, decimal_places=7)
    desc = models.CharField(_("Descrição"), max_length=50)


# Custom Manager for Trecho model
class TrechoAtivoManager(models.Manager):
    def get_queryset(self):
        # Return only trechos with concessionaria.ativa = True
        return super().get_queryset().filter(concessionaria__ativa=True)


class Trecho(models.Model):
    SUPERFICIE_CHOICES = [
        ('DUP-Duplicada', 'DUP-Duplicada'),
        ('IMP-Terra', 'IMP-Terra'),
        ('PAV-Pavimentada', 'PAV-Pavimentada'),
        ('PLAN-Planejada', 'PLAN-Planejada'),
        ('OUTRO', 'Outro'),
    ]
    TIPO_CHOICES = [
        ('ACESSO', 'Acesso'),
        ('DISPOSITIVO', 'Dispositivo'),
        ('EIXO', 'Eixo'),
        ('FEDERAL', 'Federal'),
        ('INTERLIGAÇÃO', 'Interligação'),
        ('MARGINAL', 'Marginal'),
        ('VICINAL', 'Vicinal'),
        ('OUTRO', 'Outro'),
    ]
    JURISDICAO_CHOICES = [
        ('MUNICIPAL', 'Municipal'),
        ('ESTADUAL', 'Estadual'),
        ('FEDERAL', 'Federal'),
    ]
    PISTA_CHOICES = [
        ('DUPLA','DUPLA'),
        ('SIMPLES','SIMPLES'),
        ('PLANEJADA','PLANEJADA'),
        ('DISPOSITIVO','DISPOSITIVO'),
        ('SEM INFORMAÇÃO','SEM INFORMAÇÃO'),
    ]
    SENTIDO_CHOICES = [
        ('SEM INFORMAÇÃO','SEM INFORMAÇÃO'),
        ('NORTE','NORTE'),
        ('SUL','SUL'),
        ('LESTE','LESTE'),
        ('OESTE','OESTE'),
        ('NORTE/SUL','NORTE/SUL'),
        ('LESTE/OESTE','LESTE/OESTE'),
        ('EXTERNO','EXTERNO'),
        ('INTERNO','INTERNO'),
        ('NÃO SE APLICA','NÃO SE APLICA'),
    ]
    tipo = models.CharField(_("Tipo"), choices=TIPO_CHOICES, max_length=50)
    km_inicial = models.DecimalField(_("KM Inicial"), max_digits=8, decimal_places=3)
    km_final = models.DecimalField(_("KM Final"), max_digits=8, decimal_places=3)
    pontos = models.ManyToManyField(Ponto, verbose_name=_("Pontos"), blank=True)
    nome = models.CharField(max_length=100, blank=True, null=True, verbose_name='Nome Trecho')
    nome_segmento = models.CharField(max_length=100, blank=True, null=True, verbose_name='Nome Segmento')
    vdm = models.DecimalField(_("VDM"), max_digits=13, blank=True, null=True, decimal_places=3)
    jurisdicao = models.CharField(
        max_length=20,
        choices=JURISDICAO_CHOICES,
        default='ESTADUAL',
        verbose_name='Jurisdição'
    )
    pista = models.CharField(
        max_length=50,
        choices=PISTA_CHOICES,
        null=True,
        blank=True,
        verbose_name='Pista'
    )
    sentido = models.CharField(
        max_length=50,
        choices=SENTIDO_CHOICES,
        null=True,
        blank=True,
        verbose_name='Sentido'
    )
    superficie = models.CharField(_("Superfície"), choices=SUPERFICIE_CHOICES, max_length=50, null=True, blank=True)
    faixas = models.PositiveSmallIntegerField(_("Faixas"), null=True, blank=True)
    rodovia = models.ForeignKey(
        Rodovia, null=True, on_delete=models.SET_NULL,
        verbose_name='Rodovia', related_name='trechos'
    )
    municipio = models.ForeignKey(
        Municipio, null=True, on_delete=models.SET_NULL,
        verbose_name='Municipio', related_name='trechos'
    )
    regional = models.ForeignKey(
        Regional, null=True, on_delete=models.SET_NULL,
        verbose_name='Regional', related_name='trechos'
    )
    concessionaria = models.ForeignKey(
        Concessionaria, null=True, on_delete=models.SET_NULL,
        verbose_name='Concessionária', related_name='trechos'
    )
    dt_inicio = models.DateTimeField(auto_now=False, null=True, blank=True, verbose_name='Início em')
    dt_fim = models.DateTimeField(auto_now=False, null=True, blank=True, verbose_name='Término em')
    criado_em = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='trecho_criado_por'
    )
    atualizado_em = models.DateTimeField(auto_now=True, null=True, blank=True, verbose_name='Atualizado em')
    atualizado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Atualizado por', related_name='trecho_atualizado_por'
    )
    ramo_dispositivo = models.CharField(max_length=100, blank=True, null=True, verbose_name='Ramo Disp.')
    sentido_ordem = models.CharField(max_length=100, blank=True, null=True, verbose_name='Sentido ordem')
    observacao = models.TextField(blank=True, null=True, verbose_name='Observação')
    
    # Add the custom manager
    objects = TrechoAtivoManager()
    # Keep the original manager as all_objects to access all records when needed
    all_objects = models.Manager()

    class Meta:
        ordering = ('rodovia__codigo', 'sentido', 'km_inicial')
        verbose_name = 'Trecho'
        verbose_name_plural  = 'Trechos'

    def __str__(self):
        try:
            return f'{self.rodovia.codigo} {self.sentido} | KMi {self.km_inicial}-KMf {self.km_final} | ID:{self.pk}'
        except:
            pass
        return f'KMi {self.km_inicial}-KMf {self.km_final} | ID:{self.pk}'

    def get_extensao(self):
        return self.km_final - self.km_inicial


class Operacao(models.Model):
    OPERACAO_CHOICES = [
        ('Normal', 'Normal'),
        ('Especial', 'Especial'),
    ]
    rodovia = models.ForeignKey(Rodovia, verbose_name=_("Rodovia"), on_delete=models.CASCADE, related_name='operacoes')
    nome = models.CharField(_("Nome da operação"), max_length=50, null=True, blank=True)
    sistema = models.CharField(_("Nome do sistema"), max_length=50, null=True, blank=True)
    km_inicial = models.DecimalField(_("KM Inicial"), max_digits=8, decimal_places=3, null=True, blank=True)
    km_final = models.DecimalField(_("KM Final"), max_digits=8, decimal_places=3, null=True, blank=True)
    croqui = models.ImageField(
        _("Croqui"), upload_to='trafego/operacoes', 
        height_field=None, 
        width_field=None, 
        max_length=None,
        null=True,
        blank=True
    )
    tipo = models.CharField(_("Tipo"), max_length=50, choices=OPERACAO_CHOICES, default='Especial')

    class Meta:
        verbose_name = 'Operação'
        verbose_name_plural = 'Operações'
        ordering = ('tipo', 'nome',)

    def __str__(self):
        if self.sistema:
            return f'{self.rodovia} | {self.tipo} | '\
            f'Operação {self.nome} | Sistema {self.sistema}'
        return f'{self.rodovia} | {self.tipo} | '\
            f'Operação {self.nome}'


class OperacaoAtivacoes(models.Model):
    op = models.ForeignKey(Operacao, verbose_name=_("Operação"), on_delete=models.CASCADE, related_name='ativacoes')
    trafego = models.ForeignKey(
        "trafego.TrafegoRodovia", verbose_name=_("Tráfego"), on_delete=models.CASCADE, related_name='traf_ativacoes')
    criada_em = models.DateTimeField(auto_now_add=True, verbose_name='Criada em')
    criada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criada por', related_name='op_criada_por'
    )
    encerrada_em = models.DateTimeField(verbose_name='Encerrada em', blank=True, null=True)
    encerrada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Encerrada por', related_name='op_encerrada_por'
    )

    class Meta:
        verbose_name = 'OperaçãoAtivação'
        verbose_name_plural = 'OperaçãoAtivações'
        ordering = ('-criada_em', '-encerrada_em',)

    def __str__(self):
        # if self.sistema:
        #     return f'{self.rodovia} | {self.tipo} | '\
        #     f'Operação {self.nome} | Sistema {self.sistema}'
        # return f'{self.rodovia} | {self.tipo} | '\
        #     f'Operação {self.nome}'
        return f'{self.criada_em}'