from datetime import datetime
import pytz
from concessionarias.models import Concessionaria
from celery import shared_task

@shared_task
def update_concs_rs_nr():
    # Use the local timezone for São Paulo
    local_tz = pytz.timezone("America/Sao_Paulo")

    # Get the current local time in SP
    now_local = datetime.now(tz=local_tz)
    
    # Check if local date is 2025-03-30
    if now_local.year == 2025 and now_local.month == 3 and now_local.day == 30:
        # 1) VIAOESTE -> ativa=False
        count_viaoeste = Concessionaria.objects.filter(
            nome__icontains="viaoeste"
        ).update(ativa=False)

        # 2) NOVA RAPOSO -> ativa=True
        count_nova_raposo = Concessionaria.objects.filter(
            nome__icontains="nova raposo"
        ).update(ativa=True)

        # 3) ROTA SOROCABANA -> ativa=True
        count_rota_sorocabana = Concessionaria.objects.filter(
            nome__icontains="rota sorocabana"
        ).update(ativa=True)

        # Build a summary message
        _str = f"Updated {count_viaoeste} Concessionaria(s) for 'viaoeste' (ativa=False). "
        _str += f"Updated {count_nova_raposo} Concessionaria(s) for 'nova raposo' (ativa=True). "
        _str += f"Updated {count_rota_sorocabana} Concessionaria(s) for 'rota sorocabana' (ativa=True)."

        return _str
    else:
        return (
            f"Skipped updates: Current local time in America/Sao_Paulo is "
            f"{now_local.strftime('%Y-%m-%d %H:%M:%S %Z')} (not 2025-03-30)."
        )
