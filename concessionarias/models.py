from django.db import models
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _




class ConcessionariaArtespManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(publica=False).filter(fiscalizacao_artesp=True)


class ConcessionariaManager(models.Manager):
    pass


class AtivoManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(ativo=True)


class ModoTransporte(models.Model):
    description = models.CharField(max_length=100, unique=True, verbose_name="Modo de Transporte")
    ativo = models.BooleanField("Ativo", default=True, help_text='Opção ativa')
    
    objects = AtivoManager()  # Default manager - will only return active records
    all_objects = models.Manager()  # Secondary manager - will return all records

    def __str__(self):
        return self.description

    class Meta:
        ordering: ['description']

class Concessionaria(models.Model):

    TIPOS_CHOICES = (
        ('DER', 'DER'),
        ('DNIT', 'DNIT'),
        ('CONCESSIONÁRIA', 'CONCESSIONÁRIA'),
        ('PREFEITURA', 'PREFEITURA'),
        ('OUTRO', 'OUTRO'),
    )
    nome = models.CharField(max_length=100, unique=True, verbose_name='Nome')
    slug = models.SlugField(max_length=250, blank=True)
    tipo = models.CharField(
        max_length=20,
        choices=TIPOS_CHOICES,
        null=True,
        blank=True,
        verbose_name='Tipo'
    )
    publica = models.BooleanField(_("Pública"))
    fiscalizacao_artesp = models.BooleanField(_("Fiscalização ARTESP"), default=False)
    nome_fantasia = models.CharField(max_length=100, null=True, blank=True, default=None, verbose_name='Nome Fantasia')
    razao_social = models.CharField(max_length=255, null=True, blank=True, default=None, verbose_name='Razão Social')
    logo = models.ImageField(
        _("Logo"), upload_to="images/concessionarias/logos", null=True, blank=True,
         max_length=100
    )
    telefone_adm = models.CharField(max_length=20, null=True, blank=True)
    telefone_cco = models.CharField(max_length=20, null=True, blank=True)
    logradouro = models.CharField(max_length=255, null=True, blank=True)
    numero = models.CharField(max_length=20, null=True, blank=True)
    bairro = models.CharField(max_length=100, null=True, blank=True)
    cidade = models.CharField(max_length=100, null=True, blank=True)
    cep = models.CharField(max_length=10, null=True, blank=True)
    uf = models.CharField(max_length=2, null=True, blank=True)
    ativa = models.BooleanField(default=1)
    desc = models.TextField(null=True, blank=True, default=None)
    lote = models.PositiveIntegerField(default=99)
    etapa = models.PositiveIntegerField(null=True, blank=True)
    contrato = models.CharField(max_length=100, null=True, blank=True)
    contrato_dt_ass = models.DateField(null=True, blank=True)
    inicio = models.DateTimeField(null=True, blank=True)
    termino = models.DateTimeField(null=True, blank=True)
    edital = models.CharField(max_length=100, null=True, blank=True)
    grupo = models.CharField(max_length=50, null=True, blank=True)
    link = models.URLField(_("Link"), max_length=200, null=True, blank=True)
    senha = models.CharField(_("Senha"), max_length=50, null=True, blank=True)
    web = models.URLField(_("End. WEB"), max_length=200, null=True, blank=True)
    link_mapa = models.URLField(_("Link Mapa"), max_length=200, null=True, blank=True)
    criado_em = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    atualizado_em = models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')
    
    # added later
    modo_transporte = models.ForeignKey(
        ModoTransporte, 
        verbose_name=_("Modo de transporte"),
        blank=True,
        null=True,
        on_delete=models.SET_NULL, 
        related_name='transp_concs'
    )

    objects = ConcessionariaManager()
    artesp_obj = ConcessionariaArtespManager()

    class Meta:
        ordering = ('-lote',)
        verbose_name = 'Concessionária'
        verbose_name_plural  = 'Concessionárias'

    def __str__(self):
        if self.lote:
            return f'L{str(self.lote).zfill(2)}-{self.nome}'
        else:
            return f'L99-{self.nome}'

    def get_lote(self):
        if self.lote:
            return f'L{str(self.lote).zfill(2)}-{self.nome}'
        else:
            return f'L99-{self.nome}'

    def get_prazo(self):
        if not self.termino:
            return
        dias = (self.termino - timezone.now()).days
        return f'{dias} dias'

    # def save(self, *args, **kwargs):
    #     ''' On save, ajusta logotipo '''
    #     if self.logo:

    #     return super(Concessionaria, self).save(*args, **kwargs)

class Equipe(models.Model):
    nome = models.CharField(max_length=100, unique=True)
    telefone = models.CharField(max_length=20, null=True, blank=True)
    desc = models.TextField(null=True, blank=True, default=None)

    def __str__(self):
        return self.nome


class ConcessionariaEquipe(models.Model):

    class Meta:
        ordering = ('concessionaria', 'equipe')
        unique_together = ('concessionaria','equipe',)

    concessionaria = models.ForeignKey(
        Concessionaria, verbose_name=_("Concessionária"), on_delete=models.CASCADE, related_name='ce_conc'
    )
    equipe = models.ForeignKey(Equipe, verbose_name=_("Equipe"), on_delete=models.CASCADE, related_name='ce_equipe')

    def __str__(self):
        return f'{self.concessionaria.nome} - {self.equipe.nome}'


class Funcionario(models.Model):
    nome =  models.CharField(max_length=100)
    email = models.EmailField()
    ocupacao =  models.CharField(max_length=100, null=True, blank=True)
    telefone = models.CharField(max_length=20, null=True, blank=True)
    celular = models.CharField(max_length=20, null=True, blank=True)
    dt_nascimento = models.DateField(null=True, blank=True)
    obs = models.CharField(null=True, max_length=255, blank=True, default=None)
    conc_equipe = models.ManyToManyField(ConcessionariaEquipe, related_name='funcionarios')

    class Meta:
        ordering = ('nome', 'email')

    def get_equipes(self, conc_id):
        conc_equipes = self.conc_equipe.all().filter(concessionaria__pk=conc_id)
        return ', '.join([ce.equipe.nome for ce in conc_equipes])

    def __str__(self):
        return f'{self.nome} <{self.email}>'