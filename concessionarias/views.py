import logging
import csv
from django.http import HttpResponse
from django.core.exceptions import PermissionDenied
from django.urls import reverse
from django.contrib import messages
from django.shortcuts import render,  get_object_or_404, redirect
from django.views.generic import ListView, DetailView, DeleteView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.decorators import login_required, user_passes_test
from concessionarias.models import Concessionaria, Funcionario, Equipe, ConcessionariaEquipe

import pandas as pd

from .forms import CadFuncForm, ConcessionariaForm

logger = logging.getLogger("django_file")

def is_member(user):
    if user.groups.filter(name='cci').exists():
        return True
    else:
        raise PermissionDenied()


class ConcessionariaUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Concessionaria
    template_name = 'dashboard/concessionarias/update.html'
    context_object_name = 'concessionaria'
    form_class = ConcessionariaForm

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('concessionarias:detail', kwargs={'slug': self.kwargs.get('slug')})


class ConcessionariaListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Concessionaria
    template_name = 'dashboard/concessionarias/list.html'
    context_object_name = 'concessionarias'
    paginate_by = 10

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


class ConcessionariaDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    model = Concessionaria
    template_name = 'dashboard/concessionarias/detail.html'
    context_object_name = 'conc'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


    def get_object(self, queryset=None):
        return Concessionaria.objects.get(slug=self.kwargs.get('slug'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        slug = self.kwargs.get('slug', '')
        context['funcionarios'] = Funcionario.objects.filter(conc_equipe__concessionaria__slug=slug).distinct()
        return context

@login_required
@user_passes_test(is_member)
def cadastrar_funcionario_view(request, slug):
    conc = get_object_or_404(Concessionaria, slug=slug)
    initial_data = {
        'concessionaria': conc
    }
    form = CadFuncForm(conc, request.POST or None)
    if form.is_valid():
        form.save()
        messages.add_message(request, messages.SUCCESS, 'Funcionário adicionado com sucesso.')
        return redirect('concessionarias:detail', conc.slug)

    context = {
        'form': form,
        'conc': conc
    }

    return render(request, 'dashboard/concessionarias/create-func.html', context)

@login_required
@user_passes_test(is_member)
def editar_funcionario_view(request, slug, func_id):
    conc = get_object_or_404(Concessionaria, slug=slug)
    func = get_object_or_404(Funcionario, pk=func_id)
    initial_data = {
        'concessionaria': conc
    }
    form = CadFuncForm(conc, request.POST or None, instance=func)
    if form.is_valid():
        form.save()
        messages.add_message(request, messages.SUCCESS, 'Funcionário editado com sucesso.')
        return redirect('concessionarias:detail', conc.slug)

    context = {
        'form': form,
        'conc': conc,
        'func': func,
    }

    return render(request, 'dashboard/concessionarias/edit-func.html', context)


class FuncDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    model = Funcionario
    pk_url_kwarg = 'func_id'
    template_name = 'dashboard/concessionarias/delete-func.html'
    context_object_name = 'func'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_object(self, queryset=None):
        return Funcionario.objects.get(pk=self.kwargs.get('func_id'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["conc"] = Concessionaria.objects.get(slug=self.kwargs.get('slug'))
        return context
    
    def delete(self, request, *args, **kwargs):
        messages.add_message(request, messages.SUCCESS, 'Funcionário excluído com sucesso.')
        return super(FuncDeleteView, self).delete(request, *args, **kwargs)

    def get_success_url(self):
        return reverse('concessionarias:detail', kwargs={'slug': self.kwargs.get('slug')})

@login_required
@user_passes_test(is_member)
def download_csv(request):
    qs = Concessionaria.objects.all()

    df = pd.DataFrame.from_records(
        qs.values(
            'pk',
            'nome',
            'tipo',
            'publica',
            'fiscalizacao_artesp',
            'nome_fantasia',
            'razao_social',
            'telefone_adm',
            'telefone_cco',
            'logradouro',
            'numero',
            'bairro',
            'cidade',
            'cep',
            'uf',
            'ativa',
            'desc',
            'lote',
            'etapa',
            'contrato',
            'contrato_dt_ass',
            'inicio',
            'termino',
            'edital',
            'grupo',
            'web',
        )
    )

    response = HttpResponse(
        content_type='text/csv',
        headers={'Content-Disposition': 'attachment; filename="concessionarias.csv"'},
    )

    df.to_csv(
        path_or_buf=response,
        index=False,
        quoting=csv.QUOTE_NONNUMERIC,
        sep="|",
        header=[
            'id',
            'nome',
            'tipo',
            'publica',
            'fiscalizacao_artesp',
            'nome_fantasia',
            'razao_social',
            'telefone_adm',
            'telefone_cco',
            'logradouro',
            'numero',
            'bairro',
            'cidade',
            'cep',
            'uf',
            'ativa',
            'desc',
            'lote',
            'etapa',
            'contrato',
            'contrato_dt_ass',
            'inicio',
            'termino',
            'edital',
            'grupo',
            'web',
        ]
    )

    return response