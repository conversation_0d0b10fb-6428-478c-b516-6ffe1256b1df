from django.urls import path
from django.views.generic import TemplateView

from .views import ConcessionariaListView, ConcessionariaDetailView, cadastrar_funcionario_view,\
    editar_funcionario_view, FuncDeleteView, download_csv, ConcessionariaUpdateView

app_name = 'concessionarias'

urlpatterns = [
    path('', ConcessionariaListView.as_view(), name='list'),
    path('download_csv', download_csv, name='download-csv'),
    path('<str:slug>/<int:func_id>/editar-funcionario', editar_funcionario_view, name='edit-func'),
    path('<str:slug>/<int:func_id>/excluir-funcionario', FuncDeleteView.as_view(), name='delete-func'),
    path('<str:slug>/cadastrar-funcionario', cadastrar_funcionario_view, name='create-func'),
    path('<str:slug>/update', ConcessionariaUpdateView.as_view(), name='update'),
    path('<str:slug>/', ConcessionariaDetailView.as_view(), name='detail'),
]
