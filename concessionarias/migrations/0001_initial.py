# Generated by Django 3.2 on 2021-08-29 13:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Concessionaria',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=100, unique=True, verbose_name='Nome')),
                ('slug', models.SlugField(blank=True, max_length=250)),
                ('tipo', models.CharField(blank=True, choices=[('DER', 'DER'), ('DNIT', 'DNIT'), ('CONCESSIONÁRIA', 'CONCESSIONÁRIA'), ('PREFEITURA', 'PREFEITURA'), ('OUTRO', 'OUTRO')], max_length=20, null=True, verbose_name='Tipo')),
                ('publica', models.BooleanField(verbose_name='Pública')),
                ('fiscalizacao_artesp', models.BooleanField(default=False, verbose_name='Fiscalização ARTESP')),
                ('nome_fantasia', models.CharField(blank=True, default=None, max_length=100, null=True, verbose_name='Nome Fantasia')),
                ('razao_social', models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='Razão Social')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='media/images/concessionarias/logos', verbose_name='Logo')),
                ('telefone_adm', models.CharField(blank=True, max_length=20, null=True)),
                ('telefone_cco', models.CharField(blank=True, max_length=20, null=True)),
                ('logradouro', models.CharField(blank=True, max_length=255, null=True)),
                ('numero', models.CharField(blank=True, max_length=20, null=True)),
                ('bairro', models.CharField(blank=True, max_length=100, null=True)),
                ('cidade', models.CharField(blank=True, max_length=100, null=True)),
                ('cep', models.CharField(blank=True, max_length=10, null=True)),
                ('uf', models.CharField(blank=True, max_length=2, null=True)),
                ('ativa', models.BooleanField(default=1)),
                ('desc', models.TextField(blank=True, default=None, null=True)),
                ('lote', models.PositiveIntegerField(blank=True, default=99, null=True)),
                ('etapa', models.PositiveIntegerField(blank=True, null=True)),
                ('contrato', models.CharField(blank=True, max_length=100, null=True)),
                ('contrato_dt_ass', models.DateField(blank=True, null=True)),
                ('inicio', models.DateTimeField(blank=True, null=True)),
                ('termino', models.DateTimeField(blank=True, null=True)),
                ('edital', models.CharField(blank=True, max_length=100, null=True)),
                ('grupo', models.CharField(blank=True, max_length=50, null=True)),
                ('link', models.URLField(blank=True, null=True, verbose_name='Link')),
                ('senha', models.CharField(blank=True, max_length=50, null=True, verbose_name='Senha')),
                ('web', models.URLField(blank=True, null=True, verbose_name='End. WEB')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Concessionária',
                'verbose_name_plural': 'Concessionárias',
                'ordering': ('-lote',),
            },
        ),
        migrations.CreateModel(
            name='ConcessionariaEquipe',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('concessionaria', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ce_conc', to='concessionarias.concessionaria', verbose_name='Concessionária')),
            ],
            options={
                'ordering': ('concessionaria', 'equipe'),
            },
        ),
        migrations.CreateModel(
            name='Equipe',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=100, unique=True)),
                ('telefone', models.CharField(blank=True, max_length=20, null=True)),
                ('desc', models.TextField(blank=True, default=None, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Funcionario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('ocupacao', models.CharField(blank=True, max_length=100, null=True)),
                ('telefone', models.CharField(blank=True, max_length=20, null=True)),
                ('celular', models.CharField(blank=True, max_length=20, null=True)),
                ('dt_nascimento', models.DateField(blank=True, null=True)),
                ('obs', models.CharField(blank=True, default=None, max_length=255, null=True)),
                ('conc_equipe', models.ManyToManyField(related_name='funcionarios', to='concessionarias.ConcessionariaEquipe')),
            ],
            options={
                'ordering': ('nome', 'email'),
            },
        ),
        migrations.AddField(
            model_name='concessionariaequipe',
            name='equipe',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ce_equipe', to='concessionarias.equipe', verbose_name='Equipe'),
        ),
        migrations.AlterUniqueTogether(
            name='concessionariaequipe',
            unique_together={('concessionaria', 'equipe')},
        ),
    ]
