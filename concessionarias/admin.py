from django.utils import timezone
from django.contrib import admin
from .models import Concessionaria, Equipe, \
    Funcionario, ConcessionariaEquipe, ModoTransporte


class ConcessionariaAdmin(admin.ModelAdmin):
    readonly_fields = ('slug',)
    search_fields = ('nome',)
    list_filter = ('grupo', 'tipo',)
    list_display = ('lote', 'nome', 'fiscalizacao_artesp', 'ativa', 'modo_transporte' )

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False

    def has_view_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False

    def save_model(self, request, obj, form, change):
        if not obj.pk:
            obj.criado_em = timezone.now()
            obj.criado_por = request.user
        else:
            obj.atualizado_em = timezone.now()
            obj.atualizado_por = request.user
        obj.save()


class EquipeAdmin(admin.ModelAdmin):
    pass


class FuncionarioAdmin(admin.ModelAdmin):
    pass



class ModoTransporteAdmin(admin.ModelAdmin):
    list_display = ('description' , 'ativo',)

    def get_queryset(self, request):
        return ModoTransporte.all_objects.all()


class ConcessionariaEquipeAdmin(admin.ModelAdmin):
    readonly_fields = ('pk',)
    search_fields = ('concessionaria', 'equipe',)
    list_filter = ('concessionaria', 'equipe',)
    list_per_page = 10

admin.site.register(Concessionaria, ConcessionariaAdmin)
admin.site.register(ModoTransporte, ModoTransporteAdmin)
admin.site.register(Equipe, EquipeAdmin)
admin.site.register(Funcionario, FuncionarioAdmin)
admin.site.register(ConcessionariaEquipe, ConcessionariaEquipeAdmin)
