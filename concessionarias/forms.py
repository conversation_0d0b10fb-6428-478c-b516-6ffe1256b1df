from django import forms
from .models import Equipe, Concessionaria, Funcionario, ConcessionariaEquipe

class CadFuncForm(forms.ModelForm):
    class Meta:
        model = Funcionario
        fields = [
            'nome', 'email', 'ocupacao', 'telefone', 'celular', 'dt_nascimento','obs', 'conc_equipe'
        ]
        widgets = {
            'dt_nascimento': forms.DateTimeInput(
                format='%Y-%m-%d',
                attrs={'type': 'date'}
            ),
            'conc_equipe': forms.CheckboxSelectMultiple()
        }
    # nome = forms.CharField(label="Nome", max_length=100, required=False)
    # email = forms.EmailField(label="Email", required=True)
    # ocupacao = forms.CharField(label="Ocupação", max_length=100, required=False)
    # telefone = forms.CharField(label="Telefone", max_length=20, required=False)
    # celular = forms.CharField(label="Celular", max_length=20, required=False)
    # dt_nascimento = forms.DateField(
    #     label="Data de nascimento", required=False,
    #     widget=forms.DateInput(attrs={'type': 'date'})
    # )
    # obs = forms.CharField(label="Observações", max_length=255, required=False)
    # equipes = forms.ModelChoiceField(
    #     queryset=ConcessionariaEquipe.objects.all(),
    #     widget=forms.CheckboxSelectMultiple(),
    #     required=False,
    # )

    def __init__(self, conc, *args, **kwargs):
        super(CadFuncForm, self).__init__(*args, **kwargs)
        self.fields['conc_equipe'].queryset = ConcessionariaEquipe.objects.filter(concessionaria__pk=conc.pk)


    def clean_equipes(self):
        print('clean_equipes BEFORE ==> ', equipes)
        equipes = self.cleaned_data["equipes"]
        print('clean_equipes AFTER ==> ', equipes)
        if len(equipes) == 0:
            raise forms.ValidationError(f'Necessário associar ao menos uma equipe para o funcionário.')
        return equipes


class ConcessionariaForm(forms.ModelForm):

    class Meta:
        model = Concessionaria
        fields = '__all__'
        widgets = {
            'contrato_dt_ass': forms.DateTimeInput(
                format='%Y-%m-%d',
                attrs={'type': 'date'}
            ),
            'inicio': forms.DateTimeInput(
                format='%Y-%m-%d',
                attrs={'type': 'date'}
            ),
            'termino': forms.DateTimeInput(
                format='%Y-%m-%d',
                attrs={'type': 'date'}
            ),
        }