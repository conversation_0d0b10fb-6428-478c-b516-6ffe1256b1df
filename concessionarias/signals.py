from django.db.models.signals import pre_save, post_save
from django.utils import timezone
from django.dispatch import receiver
from .models import Concessionaria, Equipe, ConcessionariaEquipe
from django.utils.text import slugify


def slug(sender, instance, *args, **kwargs):
    slug = slugify(instance.nome)
    instance.slug = slug


def criado_em(sender, instance, *args, **kwargs):
    now = timezone.now()
    if not instance.criado_em:
        instance.criado_em = now


def cria_equipes(sender, instance, created,*args, **kwargs):
    if created:
        concs = Concessionaria.artesp_obj.all()
        for conc in concs:
            conc_equip =  ConcessionariaEquipe(concessionaria=conc, equipe=instance)
            conc_equip.save()


pre_save.connect(slug, sender=Concessionaria)
pre_save.connect(criado_em, sender=Concessionaria)

post_save.connect(cria_equipes, sender=Equipe)