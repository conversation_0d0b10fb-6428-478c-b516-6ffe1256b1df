from django.core import management
from django.core.management.commands import loaddata
from django.contrib.auth.models import Group, Permission

from django.core.management.base import BaseCommand, CommandError
from django.db.models import Q

from concessionarias.models import Concessionaria, ModoTransporte

class Command(BaseCommand):
    help = 'Carrega dados iniciais do app PROTOCOLO'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Loading data...'))

        # DADOS INICIAIS
        management.call_command('loaddata', 'modo-transporte', verbosity=1)
        
        concs = Concessionaria.objects\
            .filter(fiscalizacao_artesp=True)\
            .filter(~Q(lote=99))
        modo_transporte = ModoTransporte.objects.filter(pk=1).first()
        for conc in concs:
            conc.modo_transporte = modo_transporte
            conc.save()
        
        # END
        self.stdout.write(self.style.SUCCESS('Data loaded'))

