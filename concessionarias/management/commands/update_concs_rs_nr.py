from django.core.management.base import BaseCommand
from concessionarias.models import Concessionaria

class Command(BaseCommand):
    help = (
        "Updates Concessionaria objects:\n"
        "1) If 'nome' icontains 'VIAOESTE', set 'ativa' = False.\n"
        "2) If 'nome' icontains 'NOVA RAPOSO', set 'ativa' = True.\n"
        "3) If 'nome' icontains 'ROTA SOROCABANA', set 'ativa' = True.\n"
    )

    def handle(self, *args, **options):
        # 1) VIAOESTE -> ativa=False
        count_viaoeste = Concessionaria.objects.filter(
            nome__icontains="viaoeste"
        ).update(ativa=False)

        # 2) NOVA RAPOSO -> ativa=True
        count_nova_raposo = Concessionaria.objects.filter(
            nome__icontains="nova raposo"
        ).update(ativa=True)

        # 3) ROTA SOROCABANA -> ativa=True
        count_rota_sorocabana = Concessionaria.objects.filter(
            nome__icontains="rota sorocabana"
        ).update(ativa=True)

        # Print results to the console
        self.stdout.write(self.style.SUCCESS(
            f"Updated {count_viaoeste} Concessionaria(s) matching 'viaoeste' (ativa=False)."
        ))
        self.stdout.write(self.style.SUCCESS(
            f"Updated {count_nova_raposo} Concessionaria(s) matching 'nova raposo' (ativa=True)."
        ))
        self.stdout.write(self.style.SUCCESS(
            f"Updated {count_rota_sorocabana} Concessionaria(s) matching 'rota sorocabana' (ativa=True)."
        ))
