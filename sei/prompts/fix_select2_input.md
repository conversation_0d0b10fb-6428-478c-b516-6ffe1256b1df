Fix the Select2 dropdown positioning and width issues in the SEI form. When users click on any Select2 dropdown (Unidade, Interessado, Localização, Assunto, Local, or Disciplina), the dropdown options list extends beyond the boundaries of its parent container, creating a visual overflow problem.

Specifically:
1. The dropdown options list appears to have a width that exceeds the parent container's boundaries
2. The dropdown may be using an element with `aria-controls="select2-[fieldname]_select-container"` or similar attribute that needs proper CSS styling
3. The issue affects all Select2 dropdowns across all form steps

Requirements:
- Ensure the dropdown options list stays within the bounds of its parent container
- Maintain the full-width styling we previously implemented for the Select2 inputs
- Fix the positioning so the dropdown doesn't overflow horizontally or vertically
- Apply the fix to all Select2 dropdowns in the form (not just one specific field)
- Preserve the existing functionality and styling of the Select2 components
- Test that the dropdown works properly on different screen sizes

The solution should involve CSS modifications to properly constrain the Select2 dropdown positioning and dimensions.