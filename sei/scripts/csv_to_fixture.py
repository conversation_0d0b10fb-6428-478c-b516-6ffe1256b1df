import argparse
import csv
import json
from pathlib import Path
from datetime import datetime, timezone

def csv_to_django_fixture(
    csv_path: Path,
    app_name: str,
    model_name: str,
    output_path: Path,
    delimiter: str
) -> None:
    """
    Converts a CSV file to a Django JSON fixture, assuming the header
    is always the first line.

    Args:
        csv_path: Path to the input CSV file.
        app_name: Name of the Django app.
        model_name: Name of the model.
        output_path: Path to save the output JSON fixture file.
        delimiter: The character used to separate columns in the CSV.
    """
    fixtures = []
    pk_counter = 1
    full_model_string = f"{app_name}.{model_name}"
    now_utc = datetime.now(timezone.utc).isoformat()

    print(f"--- Starting Conversion ---")
    print(f"🔄 Input File: {csv_path.name}")
    print(f"📦 Model: {full_model_string}")
    print(f"🎯 Delimiter: '{delimiter}'")

    try:
        # Use 'utf-8-sig' to handle potential BOM characters at the file start
        with csv_path.open(mode='r', encoding='utf-8-sig', newline='') as csv_file:
            
            # Use a standard, robust CSV reading strategy.
            # QUOTE_MINIMAL is the most common and safest option.
            reader = csv.DictReader(
                csv_file,
                delimiter=delimiter,
                quotechar='"',
                quoting=csv.QUOTE_MINIMAL
            )
            
            reader.fieldnames = [field.strip() for field in reader.fieldnames]
            print(f"🔍 Found Headers: {reader.fieldnames}")

            for row in reader:
                fields = {}
                for key, value in row.items():
                    if key is None: continue

                    processed_value = value.strip() if isinstance(value, str) else value

                    # Manual type inference for robustness
                    if isinstance(processed_value, str):
                        if processed_value.lower() in ['true', 'false']:
                            fields[key] = processed_value.lower() == 'true'
                        elif processed_value == '':
                            fields[key] = None
                        else:
                            # Try converting to int, then float, then leave as string
                            try:
                                fields[key] = int(processed_value)
                            except ValueError:
                                try:
                                    # Handle decimals with comma or period
                                    fields[key] = float(processed_value.replace(',', '.'))
                                except ValueError:
                                    fields[key] = processed_value # It's a string
                    else:
                        fields[key] = processed_value
                
                # Add required timestamps for Django model fields
                fields['created_at'] = now_utc
                fields['updated_at'] = now_utc

                fixture = {
                    "model": full_model_string,
                    "pk": pk_counter,
                    "fields": fields
                }
                fixtures.append(fixture)
                pk_counter += 1

        output_path.parent.mkdir(parents=True, exist_ok=True)
        with output_path.open(mode='w', encoding='utf-8') as json_file:
            json.dump(fixtures, json_file, indent=2, ensure_ascii=False)

        print(f"\n✅ Successfully created Django fixture with {len(fixtures)} records at: {output_path}")

    except FileNotFoundError:
        print(f"❌ Error: The file was not found at {csv_path}")
    except Exception as e:
        print(f"❌ An unexpected error occurred: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Convert a CSV file to a Django JSON fixture.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("app_name", type=str, help="Name of the Django app.")
    parser.add_argument("model_name", type=str, help="Name of the Model.")
    parser.add_argument("csv_file", type=Path, help="Path to the input CSV file.")
    parser.add_argument("output_file", type=Path, help="Path for the output JSON fixture file.")
    parser.add_argument(
        "--delimiter",
        type=str,
        default=',',
        help="The column delimiter character. Defaults to comma ','."
    )
    
    args = parser.parse_args()
    csv_to_django_fixture(args.csv_file, args.app_name, args.model_name, args.output_file, args.delimiter)