[{"model": "sei.Unidade", "pk": 1, "fields": {"codigo": "NA", "unidade": "NÃO SE APLICA", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 2, "fields": {"codigo": "SUROD", "unidade": "SUPERINTENDÊNCIA DE RODOVIAS", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 3, "fields": {"codigo": "SUMEF", "unidade": "SUPERINTENDÊNCIA METROFERROVIÁRIA", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 4, "fields": {"codigo": "SUCOL", "unidade": "SUPERINTENDÊNCIA DE COLETIVOS", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 5, "fields": {"codigo": "SUAEP", "unidade": "SUPERINTENDÊNCIA AEROPORTUÁRIA", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 6, "fields": {"codigo": "SUHID", "unidade": "SUPERINTENDÊNCIA HIDROVIÁRIA", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 7, "fields": {"codigo": "SUREF", "unidade": "SUPERINTENDÊNCIA ECONÔMICO-FINANCEIRO", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 8, "fields": {"codigo": "SUINV", "unidade": "SUPERINTENDÊNCIA DE NOVOS INVESTIMENTOS", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 9, "fields": {"codigo": "SUSAM", "unidade": "SUPERINTENDÊNCIA SOCIOAMBIENTAL", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 10, "fields": {"codigo": "SUTID", "unidade": "SUPERINTENDÊNCIA DE TI", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 11, "fields": {"codigo": "SUADI", "unidade": "SUPERINTENDÊNCIA ADMINISTRATIVA", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 12, "fields": {"codigo": "DIRDZ", "unidade": "DIRETORIA DIEGO ZANATO", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 13, "fields": {"codigo": "DIRRF", "unidade": "DIRETORIA RAQUEL FRANÇA", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 14, "fields": {"codigo": "DIRSF", "unidade": "DIRETORIA SANTI FERRI", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 15, "fields": {"codigo": "DIRPRES", "unidade": "DIRETOR PRESIDENTE", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}, {"model": "sei.Unidade", "pk": 16, "fields": {"codigo": "GABPRES", "unidade": "GABINETE DA PRESIDÊNCIA", "created_at": "2025-07-16T17:39:31.260043+00:00", "updated_at": "2025-07-16T17:39:31.260043+00:00"}}]