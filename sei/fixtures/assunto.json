[{"model": "sei.<PERSON><PERSON><PERSON>", "pk": 1, "fields": {"codigo": "PF_CONTRAT", "doc": "PF_CONTRAT - <PERSON><PERSON><PERSON><PERSON><PERSON> FUNCIONAL DE INVESTIMENTO/OBRA CONTRATUAL (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": "PF_CONTRAT - PF_CONTRAT - <PERSON>O<PERSON><PERSON><PERSON> FUNCIONAL DE INVESTIMENTO/OBRA CONTRATUAL (PARA TODOS OS MODOS DE TRANSPORTE) (PARA RODOVIAS)", "obs3": null, "sigla_padronizada": "PRJ.PF", "sigla_original": "PF", "descricao": "PROJETO FUNCIONAL", "categoria_tematica": "Projetos", "extra": "PRJ.PF - <PERSON><PERSON><PERSON><PERSON><PERSON> FUNCIONAL (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 2, "fields": {"codigo": "PE_CONTRAT", "doc": "PE_CONTRAT - PR<PERSON><PERSON><PERSON><PERSON> EXECUTIVO DE INVESTIMENTO/OBRA CONTRATUAL (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "PRJ.PE", "sigla_original": "PE", "descricao": "PROJETO EXECUTIVO", "categoria_tematica": "Projetos", "extra": "PRJ.PE - PRO<PERSON><PERSON><PERSON> EXECUTIVO (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 3, "fields": {"codigo": "AB_CONTRAT", "doc": "AB_CONTRAT - DOCUMENTAÇÃO AS BUILT DE INVESTIMENTO/OBRA CONTRATUAL (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "PRJ.AB", "sigla_original": "AB", "descricao": "DOCUMENTAÇÃO AS BUILT", "categoria_tematica": "Projetos", "extra": "PRJ.AB - DOCUMENTAÇÃO AS BUILT (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 4, "fields": {"codigo": "PF_ACESSO", "doc": "PF_ACESSO - <PERSON><PERSON><PERSON><PERSON><PERSON> FUNCIONAL DE ACESSO VIÁRIO À FAIXA DE DOMÍNIO (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 5, "fields": {"codigo": "PE_ACESSO", "doc": "PE_ACESSO - <PERSON><PERSON><PERSON><PERSON><PERSON> EXECUTIVO DE ACESSO VIÁRIO À FAIXA DE DOMÍNIO  (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 6, "fields": {"codigo": "AB_ACESSO", "doc": "AB_ACESSO - DOCUMENTAÇÃO AS BUILT DE ACESSO VIÁRIO À FAIXA DE DOMÍNIO  (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 7, "fields": {"codigo": "PF_FXD", "doc": "PF_FXD - <PERSON><PERSON>J<PERSON><PERSON> FUNCIONAL DE OCUPAÇÃO NA FAIXA DE DOMÍNIO (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 8, "fields": {"codigo": "PE_FXD", "doc": "PE_FXD - PR<PERSON>J<PERSON><PERSON> EXECUTIVO DE OCUPAÇÃO NA FAIXA DE DOMÍNIO (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 9, "fields": {"codigo": "AB_FXD", "doc": "AB_FXD - DOCUMENTAÇÃO AS BUILT DE OCUPAÇÃO NA FAIXA DE DOMÍNIO (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 10, "fields": {"codigo": "PF_NOVO", "doc": "PF_NOVO - <PERSON><PERSON><PERSON><PERSON><PERSON> FUNCIONAL DE NOVO INVESTIMENTO/OBRA NÃO CONTRATUAL (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 11, "fields": {"codigo": "PE_NOVO", "doc": "PE_NOVO - <PERSON><PERSON><PERSON><PERSON><PERSON> EXECUTIVO DE NOVO INVESTIMENTO/OBRA NÃO CONTRATUAL (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 12, "fields": {"codigo": "PL_ACESSO", "doc": "PL_ACESSO - PEDID<PERSON> DE ACESSO NA FAIXA DE DOMÍNIO (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FUN.ACS", "sigla_original": "ACESSO", "descricao": "PEDIDO DE ACESSO NA FAIXA DE DOMÍNIO", "categoria_tematica": "Fundiário", "extra": "FUN.ACS - <PERSON><PERSON><PERSON><PERSON> DE ACESSO NA FAIXA DE DOMÍNIO (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 13, "fields": {"codigo": "PL_FXD", "doc": "PL_FXD - PEDIDO DE OCUPAÇÃO DE FAIXA DE DOMÍNIO (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FUN.FXD", "sigla_original": "FXD", "descricao": "OCUPAÇÃO DE FAIXA DE DOMÍNIO", "categoria_tematica": "Fundiário", "extra": "FUN.FXD - OCUPAÇÃO DE FAIXA DE DOMÍNIO (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 14, "fields": {"codigo": "MONIT_OAES", "doc": "MONIT_OAES - RELATÓRIOS DE MONITORAMENTO DE OAES (PARA RODOVIAS)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "MON.OAE", "sigla_original": "MONITOAES", "descricao": "MONITORAMENTO DE OAES", "categoria_tematica": "Técnico-Operacional", "extra": "MON.OAE - MONITORAM<PERSON><PERSON> DE OAES (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 15, "fields": {"codigo": "MONIT_PAV", "doc": "MONIT_PAV - RELATÓRIOS DE MONITORAMENTO DE PAVIMENTO (PARA RODOVIAS)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "MON.PAV", "sigla_original": "MONITOPAV", "descricao": "MONITORAMENTO DE PAVIMENTO", "categoria_tematica": "Técnico-Operacional", "extra": "MON.PAV - <PERSON><PERSON><PERSON><PERSON>AM<PERSON><PERSON> DE PAVIMENTO (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 16, "fields": {"codigo": "PL_ADEQ_CRONO", "doc": "ADEQ_CRONO - PLEITO DE ADEQUAÇÃO DE CRONOGRAMA DE OBRAS/INVESTIMENTOS CONTRATUAIS (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "PRJ.CRO", "sigla_original": "CRONO", "descricao": "CRONOGRAMA DE OBRAS", "categoria_tematica": "Projetos", "extra": "PRJ.CRO - CRONOGRAMA DE OBRAS (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 17, "fields": {"codigo": "PL_DESEQ", "doc": "PL_DESEQ - PLEITO DESEQUILÍBRIO/REEQUILÍBRIO (EXCETO ADEQUAÇÕES DE CRONOGRAMA) (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "REG.DSQ", "sigla_original": "DESEQ", "descricao": "PLEITO DESEQUILÍBRIO/REEQUILÍBRIO", "categoria_tematica": "Regulação Econômica", "extra": "REG.DSQ - PLEITO DESEQUILÍBRIO/REEQUILÍBRIO (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 18, "fields": {"codigo": "PL_INVEST", "doc": "PL_INVEST - PLEITOS DA SOCIEDADE POR NOVOS INVESTIMENTOS (OBRAS E/OU MELHORIAS) (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "REG.PLB", "sigla_original": "PLOBRAS", "descricao": "PLEITOS DA SOCIEDADE (OBRAS E/OU MELHORIAS)", "categoria_tematica": "Regulação Social", "extra": "REG.PLB - PLEITOS DA SOCIEDADE (OBRAS E/OU MELHORIAS) (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 19, "fields": {"codigo": "RESPOSTA", "doc": "RESPOSTA  - RESPOSTA A SOLICITAÇÕES DA ARTESP (PARA RODOVIAS)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "REG.RSP", "sigla_original": "RESPOSTA", "descricao": "RESPOSTA A SOLICITAÇÕES DA ARTESP", "categoria_tematica": "Regulação Social", "extra": "REG.RSP - RESPOSTA A SOLICITAÇÕES DA ARTESP (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 20, "fields": {"codigo": "DEFPR", "doc": "DEFPR - DEFESAS PRÉVIAS DE SANCIONATÓRIOS (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "SAN.DPS", "sigla_original": "DEFPR", "descricao": "DEFESAS PRÉVIAS DE SANCIONATÓRIOS", "categoria_tematica": "Jurídico-Sancionatório", "extra": "SAN.DPS - DEFESAS PRÉVIAS DE SANCIONATÓRIOS (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 21, "fields": {"codigo": "ALEGFIN", "doc": "ALEGFIN - ALEGAÇÕES <PERSON>IN<PERSON>IS DE SANCIONATÓRIOS (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "SAN.AFS", "sigla_original": "ALEGFIN", "descricao": "ALEGAÇÕES FINAIS DE SANCIONATÓRIOS", "categoria_tematica": "Jurídico-Sancionatório", "extra": "SAN.AFS - ALEGAÇÕES FINAIS DE SANCIONATÓRIOS (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 22, "fields": {"codigo": "RECADM", "doc": "RECADM - R<PERSON><PERSON>SO ADMINISTRATIVO DE SANCIONATÓRIOS (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "SAN.RAD", "sigla_original": "RECADM", "descricao": "RECURSO ADMINISTRATIVO DE SANCIONATÓRIOS", "categoria_tematica": "Jurídico-Sancionatório", "extra": "SAN.RAD - <PERSON><PERSON><PERSON><PERSON> ADMINISTRATIVO DE SANCIONATÓRIOS (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 23, "fields": {"codigo": "RECONS", "doc": "RECONS - P<PERSON>IDO DE RECONSIDERAÇÃO (PARA TODOS OS MODOS DE TRANSPORTE)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "SAN.RCN", "sigla_original": "RECONS", "descricao": "PEDIDO DE RECONSIDERAÇÃO", "categoria_tematica": "Jurídico-Sancionatório", "extra": "SAN.RCN - PEDIDO DE RECONSIDERAÇÃO (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 24, "fields": {"codigo": "RT_REFLET", "doc": "RT_REFLET - R<PERSON><PERSON><PERSON><PERSON><PERSON> DE ÍNDICES DE RETRORREFLETÂNCIA (PARA RODOVIAS)", "sugestao": null, "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": "-  (ÁREA DESCONHECIDA)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 25, "fields": {"codigo": "CSP", "doc": "CSP - COEFICIENTE DE SERVIÇOS PRESTADOS (PARA RODOVIAS)", "sugestao": "SUROD", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "REG.CSP", "sigla_original": "CSP", "descricao": "COEFICIENTE DE SERVIÇOS PRESTADOS", "categoria_tematica": "Regulação Técnica", "extra": "REG.CSP - COEFICIENT<PERSON> DE SERVIÇOS PRESTADOS (PARA RODOVIAS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 26, "fields": {"codigo": "DESAP", "doc": "DESAP - DESAPROPRIAÇÃO (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FUN.DSA", "sigla_original": "DESAP", "descricao": "DESAPROPRIAÇÃO", "categoria_tematica": "Fundiário", "extra": "FUN.DSA - DESAPROPRIAÇÃO (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 27, "fields": {"codigo": "DUP", "doc": "DUP - DECRETO DE UTILIDADE PÚBLICA (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FUN.DUP", "sigla_original": "DUP", "descricao": "DECRETO DE UTILIDADE PÚBLICA", "categoria_tematica": "Fundiário", "extra": "FUN.DUP - DECRETO DE UTILIDADE PÚBLICA (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 28, "fields": {"codigo": "REINTPOS", "doc": "REINTPOS - REINTEGRAÇÃO DE POSSE (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FUN.RTP", "sigla_original": "REINTPOS", "descricao": "REINTEGRAÇÃO DE POSSE", "categoria_tematica": "Fundiário", "extra": "FUN.RTP - REINTEGRAÇÃO DE POSSE (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 29, "fields": {"codigo": "CTAJUD", "doc": "CTAJUD - CARTA DE AJUDICAÇÃO (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FUN.CTJ", "sigla_original": "CTAJUD", "descricao": "CARTA DE AJUDICAÇÃO", "categoria_tematica": "Fundiário", "extra": "FUN.CTJ - CARTA DE AJUDICAÇÃO (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 30, "fields": {"codigo": "RADA", "doc": "RADA - RELATÓRIO ANUAL DE DESEMPENHO AMBIENTAL (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "AMB.RAD", "sigla_original": "RADA", "descricao": "RELATÓRIO ANUAL DE DESEMPENHO AMBIENTAL", "categoria_tematica": "Ambiental", "extra": "AMB.RAD - R<PERSON><PERSON><PERSON>RI<PERSON> ANUAL DE DESEMPENHO AMBIENTAL (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 31, "fields": {"codigo": "RPA", "doc": "RPA - RELATÓRIO DE PASSIVOS AMBIENTAIS (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "AMB.RPA", "sigla_original": "RPA", "descricao": "RELATÓRIO DE PASSIVOS AMBIENTAIS", "categoria_tematica": "Ambiental", "extra": "AMB.RPA - R<PERSON><PERSON><PERSON>RI<PERSON> DE PASSIVOS AMBIENTAIS (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 32, "fields": {"codigo": "ADA", "doc": "ADA - AVALIAÇÃO DE DESEMPENHO AMBIENTAL (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "AMB.ADA", "sigla_original": "ADA", "descricao": "AVALIAÇÃO DE DESEMPENHO AMBIENTAL", "categoria_tematica": "Ambiental", "extra": "AMB.ADA - AVALIAÇÃO DE DESEMPENHO AMBIENTAL (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 33, "fields": {"codigo": "RELAMB", "doc": "RELAMB - RELATÓRIOS AMBIENTAIS CONTRATUAIS (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "AMB.RLB", "sigla_original": "RELAMB", "descricao": "RELATÓRIOS AMBIENTAIS CONTRATUAIS", "categoria_tematica": "Ambiental", "extra": "AMB.RLB - RELATÓRIOS AMBIENTAIS CONTRATUAIS (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 34, "fields": {"codigo": "PDIFC", "doc": "PDIFC - RELATÓRIO DE ATENDIMENTO AOS PADRÕES DE DESEMPENHO DA IFC (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "AMB.PDI", "sigla_original": "PDIFC", "descricao": "REL. ATEND. PADRÕES DESEMPENHO IFC", "categoria_tematica": "Ambiental", "extra": "AMB.PDI - REL. ATEND. PADRÕES DESEMPENHO IFC (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 35, "fields": {"codigo": "GI", "doc": "GI - GESTÃO DE IMÓVEL (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FUN.GI", "sigla_original": "GI", "descricao": "GESTÃO DE IMÓVEL", "categoria_tematica": "Fundiário", "extra": "FUN.GI - GESTÃO DE IMÓVEL (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 36, "fields": {"codigo": "RETMATR", "doc": "RETMATR - RETIFICAÇÃO DE MATRÍCULA (PARA MEIO AMBIENTE)", "sugestao": "SUSAM", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FUN.RTM", "sigla_original": "RETMATR", "descricao": "RETIFICAÇÃO DE MATRÍCULA", "categoria_tematica": "Fundiário", "extra": "FUN.RTM - RETIFICAÇÃO DE MATRÍCULA (PARA MEIO AMBIENTE)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 37, "fields": {"codigo": "SEGUROS", "doc": "SEGUROS - SEGUROS (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "CTR.SEG", "sigla_original": "SEGUROS", "descricao": "SEGUROS", "categoria_tematica": "Jurídico/Contratual", "extra": "CTR.SEG - SEG<PERSON><PERSON> (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 38, "fields": {"codigo": "REIDI", "doc": "REIDI - PEDIDOS DE HABILITAÇÃO AO REIDI (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "TRI.REI", "sigla_original": "REIDI", "descricao": "PEDIDOS DE HABILITAÇÃO AO REIDI", "categoria_tematica": "Fiscal/Tributário", "extra": "TRI.REI - PEDIDOS DE HABILITAÇÃO AO REIDI (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 39, "fields": {"codigo": "PAS", "doc": "PAS - PROCESSO ADMINISTRATIVO SANCIONADOR (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "SAN.PAS", "sigla_original": "PAS", "descricao": "PROCESSO ADMINISTRATIVO SANCIONADOR", "categoria_tematica": "Jurídico-Sancionatório", "extra": "SAN.PAS - <PERSON><PERSON><PERSON><PERSON> ADMINISTRATIVO SANCIONADOR (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 40, "fields": {"codigo": "RACESSORIAS", "doc": "RACESSORIAS - RECEITAS ACESSÓRIAS (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "FIN.RAC", "sigla_original": "RACESSORIAS", "descricao": "RECEITAS ACESSÓRIAS", "categoria_tematica": "Econômico/Financeiro", "extra": "FIN.RAC - RECEITAS ACESSÓRIAS (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 41, "fields": {"codigo": "EVENTOS", "doc": "EVENTOS - EVENTOS - SERVIÇOS ADICIONAIS (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "OPE.EVT", "sigla_original": "EVENTOS", "descricao": "EVENTOS - SERVIÇOS ADICIONAIS", "categoria_tematica": "Técnico-Operacional", "extra": "OPE.EVT - EVENTOS - SERVIÇOS ADICIONAIS (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 42, "fields": {"codigo": "NR", "doc": "NR - NAMING RIGHTS (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "CTR.NR", "sigla_original": "NR", "descricao": "NAMING RIGHTS", "categoria_tematica": "Contratual", "extra": "CTR.NR - NAMING RIGHTS (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 43, "fields": {"codigo": "IN", "doc": "IN - INCIDENTE NOTÁVEL (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "SEG.IN", "sigla_original": "IN", "descricao": "INCIDENTE NOTÁVEL", "categoria_tematica": "Segurança", "extra": "SEG.IN - INCIDENTE NOTÁVEL (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 44, "fields": {"codigo": "COPESE", "doc": "COPESE - COMISSÃO PERMAMENTE DE SEGURANÇA SOBRE ACIDENTES (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "SEG.CPS", "sigla_original": "COPESE", "descricao": "COMISSÃO PERMANTE DE SEGURANÇA SOBRE ACIDENTES", "categoria_tematica": "Segurança", "extra": "SEG.CPS - COMISSÃO PERMANTE DE SEGURANÇA SOBRE ACIDENTES (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 45, "fields": {"codigo": "IND", "doc": "IND - INDICADORES (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "PLN.IND", "sigla_original": "IND", "descricao": "INDICADORES", "categoria_tematica": "Planejamento/Gestão", "extra": "PLN.IND - INDICADORES (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 46, "fields": {"codigo": "ESTRATOP", "doc": "ESTRATOP - ESTRATÉGIAS OPERACIONAIS (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "PLN.OPE", "sigla_original": "ESTRATOP", "descricao": "ESTRATÉGIAS OPERACIONAIS", "categoria_tematica": "Planejamento/Gestão", "extra": "PLN.OPE - ESTRATÉGIAS OPERACIONAIS (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 47, "fields": {"codigo": "MED", "doc": "MED - MEDIÇÕES (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "OPE.MED", "sigla_original": "MED", "descricao": "MEDIÇÕES", "categoria_tematica": "Técnico-Operacional", "extra": "OPE.MED - MEDIÇÕES (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 48, "fields": {"codigo": "ETS", "doc": "ETS - ESPECIFICAÇÕES TÉCNICAS DE SERVIÇOS (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "OPE.ETS", "sigla_original": "ETS", "descricao": "ESPECIFICAÇÕES TÉCNICAS DE SERVIÇOS", "categoria_tematica": "Técnico-Operacional", "extra": "OPE.ETS - ESPECIFICAÇÕES TÉCNICAS DE SERVIÇOS (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 49, "fields": {"codigo": "OUTROS", "doc": "OUTROS - OUTROS TEMAS (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "DIV.OUT", "sigla_original": "OUTROS", "descricao": "OUTROS TEMAS", "categoria_tematica": "Diversos", "extra": "DIV.OUT - OUTROS TEMAS (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 50, "fields": {"codigo": "PROJDIRET", "doc": "PROJDIRET - ESTUDOS PRELIMINARES / PROJETO DIRETRIZ   (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "PRJ.EPL", "sigla_original": 2, "descricao": "ESTUDOS PRELIMINARES / PROJETO DIRETRIZ", "categoria_tematica": "Projetos", "extra": "PRJ.EPL - ESTUDOS PRELIMINARES / PROJETO DIRETRIZ (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 51, "fields": {"codigo": "SISEQUIP", "doc": "SISEQUIP - SISTEMA/EQUIPAMENTO EM FUNCIONAMENTO (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "OPE.SFM", "sigla_original": 3, "descricao": "SISTEMA/EQUIPAMENTO EM FUNCIONAMENTO", "categoria_tematica": "Técnico-Operacional", "extra": "OPE.SFM - SISTEMA/EQUIPAMENTO EM FUNCIONAMENTO (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 52, "fields": {"codigo": "PB", "doc": "PB - PROJETO BÁSICO (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "PRJ.PB", "sigla_original": "PB", "descricao": "PROJETO BÁSICO", "categoria_tematica": "Projetos", "extra": "PRJ.PB - PROJETO BÁSICO (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 53, "fields": {"codigo": "PMANUT", "doc": "PMANUT - <PERSON><PERSON><PERSON><PERSON><PERSON> COM FINALIDADE DE MANUTENÇÃO (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "PRJ.PMN", "sigla_original": 8, "descricao": "PROJETO COM FINALIDADE DE MANUTENÇÃO", "categoria_tematica": "Projetos", "extra": "PRJ.PMN - <PERSON><PERSON><PERSON><PERSON><PERSON> COM FINALIDADE DE MANUTENÇÃO (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 54, "fields": {"codigo": "POPER", "doc": "POPER - PR<PERSON>JETO COM FINALIDADE DE OPERAÇÃO *   (PARA METROFERROVIÁRIO)", "sugestao": "SUMEF", "obs1": "CONFORME NS.GFA/001", "obs2": null, "obs3": null, "sigla_padronizada": "PRJ.OPE", "sigla_original": 9, "descricao": "PROJETO COM FINALIDADE DE OPERAÇÃO", "categoria_tematica": "Projetos", "extra": "PRJ.OPE - <PERSON><PERSON><PERSON><PERSON><PERSON> COM FINALIDADE DE OPERAÇÃO (PARA METROFERROVIÁRIO)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 55, "fields": {"codigo": "MOVIM", "doc": "MOVIM - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DE AERONAVES E PASSAGEIROS MENSAL (PARA AEROPORTOS)", "sugestao": "SUAEP", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "AER.MOV", "sigla_original": "MOVIM", "descricao": "MOVIMENTO DE AERONAVES E PASSAGEIROS MENSAL", "categoria_tematica": "Aeroportuário", "extra": "AER.MOV - <PERSON><PERSON><PERSON>MENTO DE AERONAVES E PASSAGEIROS MENSAL (PARA AEROPORTOS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 56, "fields": {"codigo": "OCAER", "doc": "OCAER - OCOR<PERSON><PERSON><PERSON>S AERONÁUTICAS (PARA AEROPORTOS)", "sugestao": "SUAEP", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "AER.OCA", "sigla_original": "OCAER", "descricao": "OCORRÊNCIAS AERONÁUTICAS", "categoria_tematica": "Aeroportuário", "extra": "AER.OCA - OCORRÊNCIAS AERONÁUTICAS (PARA AEROPORTOS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 57, "fields": {"codigo": "RIA", "doc": "RIA - RELATORIO DE INSPEÇÃO AEROPORTUÁRIA (PARA AEROPORTOS)", "sugestao": "SUAEP", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "AER.RIA", "sigla_original": "RIA", "descricao": "RELATÓRIO DE INSPEÇÃO AEROPORTUÁRIA", "categoria_tematica": "Aeroportuário", "extra": "AER.RIA - RELATÓRIO DE INSPEÇÃO AEROPORTUÁRIA (PARA AEROPORTOS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 58, "fields": {"codigo": "RINV", "doc": "RINV - REL<PERSON>OR<PERSON> DE INVESTIMENTO (PARA AEROPORTOS)", "sugestao": "SUINV", "obs1": null, "obs2": null, "obs3": null, "sigla_padronizada": "FIN.RIV", "sigla_original": "RINV", "descricao": "RELATÓRIO DE INVESTIMENTO", "categoria_tematica": "Econômico/Financeiro", "extra": "FIN.RIV - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DE INVESTIMENTO (PARA INVESTIMENTOS)", "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 59, "fields": {"codigo": "PL", "doc": "PL - PLANILHA (PARA TRANSPORTE COLETIVO)", "sugestao": null, "obs1": "CONFORME IT-GPS-065-2025", "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 60, "fields": {"codigo": "CM", "doc": "CM - CRITÉRIOS DE MEDIÇÃO (PARA TRANSPORTE COLETIVO)", "sugestao": null, "obs1": "CONFORME IT-GPS-065-2025", "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}, {"model": "sei.<PERSON><PERSON><PERSON>", "pk": 61, "fields": {"codigo": "AT", "doc": "AT - ATA DE REUNIÃO (PARA TRANSPORTE COLETIVO)", "sugestao": null, "obs1": "CONFORME IT-GPS-065-2025", "obs2": null, "obs3": null, "sigla_padronizada": null, "sigla_original": null, "descricao": null, "categoria_tematica": null, "extra": null, "created_at": "2025-07-16T17:40:42.785382+00:00", "updated_at": "2025-07-16T17:40:42.785382+00:00"}}]