"""
Utility functions for the SEI application.
"""

from django.http import HttpResponse
from django.template.loader import get_template
from django.template import Context
from io import BytesIO
import logging

logger = logging.getLogger(__name__)


def render_to_pdf(template_src, context_dict=None, filename=None):
    """
    Render a template to PDF using xhtml2pdf (pisa).

    Args:
        template_src (str): Path to the template file
        context_dict (dict): Context data for the template
        filename (str): Optional filename for the PDF download

    Returns:
        HttpResponse: PDF response or error response
    """
    if context_dict is None:
        context_dict = {}

    try:
        # Try to import xhtml2pdf
        try:
            from xhtml2pdf import pisa
        except ImportError:
            logger.error("xhtml2pdf is not installed. Install it with: pip install xhtml2pdf")
            return HttpResponse(
                "PDF generation not available. xhtml2pdf is not installed.",
                status=500,
                content_type="text/plain"
            )

        # Render the template
        template = get_template(template_src)
        html = template.render(context_dict)

        # Generate PDF using pisa
        result = BytesIO()
        pdf = pisa.pisaDocument(BytesIO(html.encode("utf-8")), result)

        if pdf.err:
            logger.error(f"Error generating PDF: {pdf.err}")
            return HttpResponse(
                "Error generating PDF document.",
                status=500,
                content_type="text/plain"
            )

        # Create response
        pdf_content = result.getvalue()
        response = HttpResponse(pdf_content, content_type='application/pdf')

        # Set filename if provided
        if filename:
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
        else:
            response['Content-Disposition'] = 'attachment; filename="protocolo.pdf"'

        return response

    except Exception as e:
        logger.error(f"Error generating PDF: {str(e)}")
        return HttpResponse(
            f"Error generating PDF: {str(e)}",
            status=500,
            content_type="text/plain"
        )


def render_to_pdf_response(template_src, context_dict=None, filename=None):
    """
    Alternative PDF rendering function that returns the PDF as inline content.

    Args:
        template_src (str): Path to the template file
        context_dict (dict): Context data for the template
        filename (str): Optional filename for the PDF

    Returns:
        HttpResponse: PDF response for inline viewing
    """
    if context_dict is None:
        context_dict = {}

    try:
        # Try to import xhtml2pdf
        try:
            from xhtml2pdf import pisa
        except ImportError:
            logger.error("xhtml2pdf is not installed. Install it with: pip install xhtml2pdf")
            return HttpResponse(
                "PDF generation not available. xhtml2pdf is not installed.",
                status=500,
                content_type="text/plain"
            )

        # Render the template
        template = get_template(template_src)
        html = template.render(context_dict)

        # Generate PDF using pisa
        result = BytesIO()
        pdf = pisa.pisaDocument(BytesIO(html.encode("utf-8")), result)

        if pdf.err:
            logger.error(f"Error generating PDF: {pdf.err}")
            return HttpResponse(
                "Error generating PDF document.",
                status=500,
                content_type="text/plain"
            )

        # Create response for inline viewing
        pdf_content = result.getvalue()
        response = HttpResponse(pdf_content, content_type='application/pdf')

        # Set filename for inline viewing
        if filename:
            response['Content-Disposition'] = f'inline; filename="{filename}"'
        else:
            response['Content-Disposition'] = 'inline; filename="protocolo.pdf"'

        return response

    except Exception as e:
        logger.error(f"Error generating PDF: {str(e)}")
        return HttpResponse(
            f"Error generating PDF: {str(e)}",
            status=500,
            content_type="text/plain"
        )


def sanitize_filename(filename):
    """
    Sanitize a filename for safe use in file downloads.
    
    Args:
        filename (str): The original filename
        
    Returns:
        str: Sanitized filename safe for downloads
    """
    import re
    
    # Remove or replace unsafe characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove multiple consecutive underscores
    filename = re.sub(r'_+', '_', filename)
    
    # Remove leading/trailing underscores and spaces
    filename = filename.strip('_ ')
    
    # Ensure filename is not empty
    if not filename:
        filename = 'protocolo'
    
    return filename


def generate_pdf_filename(protocolo):
    """
    Generate a safe PDF filename based on protocol data.
    
    Args:
        protocolo: The Protocolo model instance
        
    Returns:
        str: Safe filename for the PDF
    """
    # Use doc_cod as base filename
    base_name = protocolo.doc_cod if protocolo.doc_cod else f"protocolo_{protocolo.id}"
    
    # Sanitize the filename
    safe_name = sanitize_filename(base_name)
    
    # Add PDF extension
    return f"{safe_name}.pdf"


def validate_protocolo_access(protocolo_id, user=None):
    """
    Validate if a user can access a specific protocol.
    Currently allows access to all protocols, but can be extended for user-specific access control.
    
    Args:
        protocolo_id (str): UUID of the protocol
        user: Django user object (optional, for future access control)
        
    Returns:
        bool: True if access is allowed, False otherwise
    """
    # For now, allow access to all protocols
    # In the future, this could check if the user is the protocol owner
    # or has appropriate permissions
    return True


def get_pdf_context(protocolo):
    """
    Prepare context data for PDF generation.
    
    Args:
        protocolo: The Protocolo model instance with related objects loaded
        
    Returns:
        dict: Context dictionary for template rendering
    """
    return {
        'protocolo': protocolo,
        'title': f'Protocolo {protocolo.doc_cod}',
        'generated_at': protocolo.created_at,
    }
