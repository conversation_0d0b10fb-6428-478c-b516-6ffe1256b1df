# Generated by Django 4.2.5 on 2025-07-16 15:10

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import sei.models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Assunto',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('codigo', models.CharField(blank=True, db_index=True, help_text='Código do assunto', max_length=255, null=True, verbose_name='<PERSON>ódigo')),
                ('doc', models.CharField(blank=True, max_length=255, null=True, verbose_name='Documento')),
                ('sugestao', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sugestão')),
                ('obs1', models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='Observação 1')),
                ('obs2', models.CharField(blank=True, max_length=255, null=True, verbose_name='Observação 2')),
                ('obs3', models.CharField(blank=True, max_length=255, null=True, verbose_name='Observação 3')),
                ('sigla_padronizada', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sigla Padronizada')),
                ('sigla_original', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sigla Original')),
                ('descricao', models.CharField(blank=True, max_length=255, null=True, verbose_name='Descrição')),
                ('categoria_tematica', models.CharField(blank=True, max_length=255, null=True, verbose_name='Categoria Temática')),
                ('sugestao1', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sugestão 1')),
                ('extra', models.CharField(blank=True, max_length=255, null=True, verbose_name='Extra')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Assunto',
                'verbose_name_plural': 'Assuntos',
                'ordering': ['descricao'],
            },
        ),
        migrations.CreateModel(
            name='Disciplina',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('codigo', models.CharField(blank=True, db_index=True, help_text='Código da disciplina', max_length=255, null=True, verbose_name='Código')),
                ('disciplina', models.CharField(blank=True, help_text='Descrição da disciplina', max_length=255, null=True, verbose_name='Disciplina')),
                ('sugestao', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sugestão')),
                ('comentarios', models.CharField(blank=True, max_length=255, null=True, verbose_name='Comentários')),
                ('extra', models.CharField(blank=True, max_length=255, null=True, verbose_name='Extra')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Disciplina',
                'verbose_name_plural': 'Disciplinas',
                'ordering': ['disciplina'],
            },
        ),
        migrations.CreateModel(
            name='Interessado',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('codigo', models.CharField(blank=True, db_index=True, help_text='Código do interessado', max_length=255, null=True, verbose_name='Código')),
                ('concessao', models.CharField(blank=True, help_text='Informações sobre a concessão', max_length=255, null=True, verbose_name='Concessão')),
                ('sugestao', models.TextField(blank=True, null=True, verbose_name='Sugestão')),
                ('comentarios', models.TextField(blank=True, null=True, verbose_name='Comentários')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Interessado',
                'verbose_name_plural': 'Interessados',
                'ordering': ['codigo'],
            },
        ),
        migrations.CreateModel(
            name='Local',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('codigo', models.CharField(blank=True, db_index=True, help_text='Código do local', max_length=255, null=True, verbose_name='Código')),
                ('local', models.CharField(blank=True, help_text='Descrição da localização', max_length=255, null=True, verbose_name='Localização')),
                ('sugestao', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sugestão')),
                ('comentarios', models.CharField(blank=True, max_length=255, null=True, verbose_name='Comentários')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Local',
                'verbose_name_plural': 'Locais',
                'ordering': ['local'],
            },
        ),
        migrations.CreateModel(
            name='Localizacao',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('codigo', models.CharField(blank=True, db_index=True, help_text='Código da localização', max_length=255, null=True, verbose_name='Código')),
                ('localizacao', models.CharField(blank=True, help_text='Descrição da localização', max_length=255, null=True, verbose_name='Localização')),
                ('obs', models.CharField(blank=True, max_length=255, null=True, verbose_name='Observação')),
                ('sugestao', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sugestão')),
                ('comentarios', models.CharField(blank=True, max_length=255, null=True, verbose_name='Comentários')),
                ('codificacao', models.CharField(blank=True, max_length=255, null=True, verbose_name='Codificação')),
                ('legenda', models.CharField(blank=True, max_length=255, null=True, verbose_name='Legenda')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Localização',
                'verbose_name_plural': 'Localizações',
                'ordering': ['localizacao'],
            },
        ),
        migrations.CreateModel(
            name='Requisitante',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(help_text='Nome completo do requisitante.', max_length=150, verbose_name='Nome')),
                ('email', models.EmailField(help_text='Endereço de e-mail do requisitante.', max_length=254, validators=[django.core.validators.EmailValidator(message='Entre com um endereço de e-mail válido.')], verbose_name='E-mail')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Endereço IP')),
            ],
            options={
                'verbose_name': 'Requisitante',
                'verbose_name_plural': 'Requisitantes',
                'ordering': ['nome'],
            },
        ),
        migrations.CreateModel(
            name='Unidade',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('codigo', models.CharField(blank=True, db_index=True, help_text='Código da unidade', max_length=255, null=True, verbose_name='Código')),
                ('unidade', models.CharField(blank=True, help_text='Nome da unidade', max_length=255, null=True, verbose_name='Unidade')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Unidade',
                'verbose_name_plural': 'Unidades',
                'ordering': ['unidade'],
            },
        ),
        migrations.CreateModel(
            name='Protocolo',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('servico_codigo', models.CharField(blank=True, db_index=True, help_text='Código do serviço (apenas números)', max_length=255, null=True, validators=[sei.models.validate_numeric_string], verbose_name='Código do Serviço')),
                ('servico_tipo', models.CharField(blank=True, help_text='Descrição do tipo de serviço', max_length=255, null=True, verbose_name='Tipo de Serviço')),
                ('doc_cod', models.CharField(blank=True, db_index=True, help_text='Código do documento', max_length=255, null=True, verbose_name='Código do Documento')),
                ('doc_url_cod', models.CharField(blank=True, db_index=True, help_text='Código da URL', max_length=255, null=True, verbose_name='Código da URL')),
                ('doc_revisao', models.CharField(blank=True, db_index=True, help_text='Revisão do documento', max_length=255, null=True, verbose_name='Revisão do Documento')),
                ('doc_sei_num', models.CharField(blank=True, db_index=True, help_text='Número do processo SEI', max_length=255, null=True, verbose_name='Nro. Processo SEI')),
                ('url', models.URLField(blank=True, null=True, verbose_name='URL')),
                ('hash', models.CharField(blank=True, max_length=64, null=True, verbose_name='Hash de Verificação')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('assunto', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sei.assunto', verbose_name='Assunto')),
                ('disciplina', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sei.disciplina', verbose_name='Disciplina')),
                ('interessado', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sei.interessado', verbose_name='Interessado')),
                ('local', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sei.local', verbose_name='Local')),
                ('localizacao', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sei.localizacao', verbose_name='Localização')),
                ('unidade', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sei.unidade', verbose_name='Unidade')),
                ('usuario', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sei.requisitante', verbose_name='Requisitante')),
            ],
        ),
    ]
