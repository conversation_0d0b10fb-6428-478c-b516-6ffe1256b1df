"""
Test script to verify the Protocolo edit functionality.
"""

from django.test import Test<PERSON>ase, Client
from django.urls import reverse
from django.contrib.auth.models import User
import json
import uuid
from .models import Protocolo, Requisitante


class ProtocoloEditTest(TestCase):
    """Test cases for Protocolo edit functionality"""
    
    def setUp(self):
        self.client = Client()
        self.submit_url = reverse('sei:form_submit')
        
        # Valid form data for creating test protocols
        self.valid_data = {
            'requisitante': {
                'nome': '<PERSON>',
                'email': '<EMAIL>'
            },
            'unidade_id': None,
            'interessado_id': None,
            'localizacao_id': None,
            'assunto_id': None,
            'servico_codigo': '123',
            'servico_tipo': 'Análise Técnica',
            'local_id': None,
            'disciplina_id': None,
            'doc_revisao': '',
            'doc_sei_num': ''
        }
    
    def create_test_protocol(self, doc_sei_num=''):
        """Helper method to create a test protocol"""
        data = self.valid_data.copy()
        data['doc_sei_num'] = doc_sei_num
        
        response = self.client.post(
            self.submit_url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        result = response.json()
        return result['protocolo_id']
    
    def test_edit_view_get_request(self):
        """Test that the edit view loads correctly"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test edit view
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Editar Protocolo')
        self.assertContains(response, 'Campo Editável')
        self.assertContains(response, 'Número SEI')
        self.assertContains(response, 'Salvar Alterações')
    
    def test_edit_view_displays_readonly_fields(self):
        """Test that read-only fields are displayed correctly"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test edit view
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check for read-only field indicators
        self.assertContains(response, 'somente leitura')
        self.assertContains(response, 'cursor-not-allowed')
        self.assertContains(response, 'João Silva Santos')
        self.assertContains(response, '<EMAIL>')
        self.assertContains(response, 'Análise Técnica')
    
    def test_edit_doc_sei_num_success(self):
        """Test successful editing of doc_sei_num field"""
        # Create a test protocol with empty doc_sei_num
        protocolo_id = self.create_test_protocol('')
        
        # Edit the doc_sei_num
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.post(edit_url, {
            'doc_sei_num': '12345-67890'
        })
        
        # Should redirect to detail view
        self.assertEqual(response.status_code, 302)
        detail_url = reverse('sei:protocolo_detail', kwargs={'protocolo_id': protocolo_id})
        self.assertRedirects(response, detail_url)
        
        # Check that the field was updated
        protocolo = Protocolo.objects.get(id=protocolo_id)
        self.assertEqual(protocolo.doc_sei_num, '12345-67890')
    
    def test_edit_doc_sei_num_clear_value(self):
        """Test clearing the doc_sei_num field"""
        # Create a test protocol with doc_sei_num
        protocolo_id = self.create_test_protocol('INITIAL-VALUE')
        
        # Clear the doc_sei_num
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.post(edit_url, {
            'doc_sei_num': ''
        })
        
        # Should redirect to detail view
        self.assertEqual(response.status_code, 302)
        
        # Check that the field was cleared
        protocolo = Protocolo.objects.get(id=protocolo_id)
        self.assertEqual(protocolo.doc_sei_num, '')
    
    def test_edit_doc_sei_num_validation_max_length(self):
        """Test validation for doc_sei_num max length"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Try to set a value that's too long (over 50 characters)
        long_value = 'A' * 51
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.post(edit_url, {
            'doc_sei_num': long_value
        })
        
        # Should stay on edit page with error
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'deve ter no máximo 50 caracteres')
        
        # Check that the field was not updated
        protocolo = Protocolo.objects.get(id=protocolo_id)
        self.assertEqual(protocolo.doc_sei_num, '')
    
    def test_edit_doc_sei_num_no_change(self):
        """Test editing with no actual change"""
        # Create a test protocol with doc_sei_num
        initial_value = 'TEST-123'
        protocolo_id = self.create_test_protocol(initial_value)
        
        # Submit the same value
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.post(edit_url, {
            'doc_sei_num': initial_value
        })
        
        # Should redirect to detail view
        self.assertEqual(response.status_code, 302)
        
        # Check that the field remains the same
        protocolo = Protocolo.objects.get(id=protocolo_id)
        self.assertEqual(protocolo.doc_sei_num, initial_value)
    
    def test_edit_only_updates_doc_sei_num(self):
        """Test that only doc_sei_num field is updated"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Get original values
        original_protocolo = Protocolo.objects.get(id=protocolo_id)
        original_nome = original_protocolo.usuario.nome
        original_email = original_protocolo.usuario.email
        original_servico_tipo = original_protocolo.servico_tipo
        original_updated_at = original_protocolo.updated_at
        
        # Edit the doc_sei_num
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.post(edit_url, {
            'doc_sei_num': 'NEW-VALUE'
        })
        
        # Check that only doc_sei_num and updated_at changed
        updated_protocolo = Protocolo.objects.get(id=protocolo_id)
        self.assertEqual(updated_protocolo.doc_sei_num, 'NEW-VALUE')
        self.assertEqual(updated_protocolo.usuario.nome, original_nome)
        self.assertEqual(updated_protocolo.usuario.email, original_email)
        self.assertEqual(updated_protocolo.servico_tipo, original_servico_tipo)
        self.assertNotEqual(updated_protocolo.updated_at, original_updated_at)
    
    def test_edit_view_with_invalid_protocolo_id(self):
        """Test edit view with non-existent protocol ID"""
        fake_id = str(uuid.uuid4())
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': fake_id})
        
        response = self.client.get(edit_url)
        
        # Should redirect to list page
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('sei:protocolo_list'))
    
    def test_edit_view_csrf_protection(self):
        """Test that CSRF protection is enabled"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test edit view contains CSRF token
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'csrfmiddlewaretoken')
    
    def test_edit_form_pre_population(self):
        """Test that the form is pre-populated with current values"""
        # Create a test protocol with doc_sei_num
        initial_value = 'PRE-POPULATED-123'
        protocolo_id = self.create_test_protocol(initial_value)
        
        # Test edit view
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'value="{initial_value}"')
        self.assertContains(response, f'Valor atual: <span class="font-mono">{initial_value}</span>')
    
    def test_edit_form_empty_value_display(self):
        """Test that empty values are displayed correctly"""
        # Create a test protocol with empty doc_sei_num
        protocolo_id = self.create_test_protocol('')
        
        # Test edit view
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'value=""')
        self.assertContains(response, 'Valor atual: <span class="font-mono">(vazio)</span>')
    
    def test_edit_view_navigation_links(self):
        """Test that navigation links are present and correct"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test edit view
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check for navigation links
        detail_url = reverse('sei:protocolo_detail', kwargs={'protocolo_id': protocolo_id})
        pdf_url = reverse('sei:protocolo_pdf', kwargs={'protocolo_id': protocolo_id})
        list_url = reverse('sei:protocolo_list')
        
        self.assertContains(response, detail_url)
        self.assertContains(response, pdf_url)
        self.assertContains(response, list_url)
        
        # Check for button texts
        self.assertContains(response, 'Ver Detalhes Completos')
        self.assertContains(response, 'Download PDF')
        self.assertContains(response, 'Voltar à Lista')
        self.assertContains(response, 'Cancelar')


class ProtocoloEditUITest(TestCase):
    """Test cases for UI elements and styling"""
    
    def setUp(self):
        self.client = Client()
        self.submit_url = reverse('sei:form_submit')
        
        # Valid form data for creating test protocols
        self.valid_data = {
            'requisitante': {
                'nome': 'João Silva Santos',
                'email': '<EMAIL>'
            },
            'unidade_id': None,
            'interessado_id': None,
            'localizacao_id': None,
            'assunto_id': None,
            'servico_codigo': '123',
            'servico_tipo': 'Análise Técnica',
            'local_id': None,
            'disciplina_id': None,
            'doc_revisao': '',
            'doc_sei_num': ''
        }
    
    def create_test_protocol(self):
        """Helper method to create a test protocol"""
        response = self.client.post(
            self.submit_url,
            data=json.dumps(self.valid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        result = response.json()
        return result['protocolo_id']
    
    def test_readonly_field_styling(self):
        """Test that read-only fields have proper styling"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test edit view
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check for read-only styling classes
        self.assertContains(response, 'bg-gray-50')
        self.assertContains(response, 'cursor-not-allowed')
        self.assertContains(response, 'text-gray-500')  # Label styling
        self.assertContains(response, 'text-gray-700')  # Value styling
    
    def test_editable_field_styling(self):
        """Test that the editable field has proper styling"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test edit view
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check for editable field styling
        self.assertContains(response, 'focus:ring-primary')
        self.assertContains(response, 'border-gray-300')
        self.assertContains(response, 'text-green-400')  # Success icon
        self.assertContains(response, 'bg-green-50')     # Editable section background
    
    def test_visual_distinction_between_sections(self):
        """Test that there's clear visual distinction between read-only and editable sections"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test edit view
        edit_url = reverse('sei:protocolo_edit', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(edit_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check for section headers with different styling
        self.assertContains(response, 'Campos somente leitura')
        self.assertContains(response, 'Campo Editável')
        self.assertContains(response, 'bg-green-50')  # Editable section
        self.assertContains(response, 'bg-gray-50')   # Read-only section


if __name__ == '__main__':
    print("Testing Protocolo edit functionality...")
    print("Run with: python manage.py test sei.test_protocolo_edit")
