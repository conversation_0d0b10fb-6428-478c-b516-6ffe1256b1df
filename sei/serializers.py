from rest_framework import serializers
from .models import <PERSON><PERSON>ita<PERSON>, Protocolo

class RequisitanteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Requisitante
        fields = ['nome', 'email', 'ip_address']

    def validate_nome(self, value):
        """
        Validate nome field: allow spaces, enforce length limits, apply formatting.
        """
        # Trim leading/trailing whitespace but preserve internal spaces
        trimmed_value = value.strip()

        # Check if empty after trimming
        if not trimmed_value:
            raise serializers.ValidationError("Nome é obrigatório")

        # Check minimum length
        if len(trimmed_value) < 3:
            raise serializers.ValidationError("Nome deve ter pelo menos 3 caracteres")

        # Check maximum length
        if len(trimmed_value) > 150:
            raise serializers.ValidationError("Nome deve ter no máximo 150 caracteres")

        # Return uppercase formatted value
        return trimmed_value.upper()

    def validate_email(self, value):
        """
        Validate email field: trim whitespace, convert to lowercase, validate format.
        """
        # Trim and convert to lowercase
        cleaned_email = value.strip().lower()

        # Check for spaces in email (should not contain any)
        if ' ' in cleaned_email:
            raise serializers.ValidationError("E-mail não pode conter espaços")

        # Django's EmailField already validates format, but we ensure it's clean
        return cleaned_email



class SeiFormSubmissionSerializer(serializers.Serializer):
    """
    Serializer for the complete SEI form submission
    """
    requisitante = RequisitanteSerializer()
    unidade_id = serializers.UUIDField(required=False, allow_null=True)
    interessado_id = serializers.UUIDField(required=False, allow_null=True)
    localizacao_id = serializers.UUIDField(required=False, allow_null=True)
    assunto_id = serializers.UUIDField(required=False, allow_null=True)
    servico_codigo = serializers.CharField(max_length=255, required=False, allow_blank=True)
    servico_tipo = serializers.CharField(max_length=255, required=False, allow_blank=True)
    local_id = serializers.UUIDField(required=False, allow_null=True)
    disciplina_id = serializers.UUIDField(required=False, allow_null=True)
    doc_revisao = serializers.CharField(max_length=255, required=False, allow_blank=True)
    doc_sei_num = serializers.CharField(max_length=255, required=False, allow_blank=True)

    def validate_servico_codigo(self, value):
        """
        Validate that servico_codigo contains only numeric characters if provided.
        """
        if value and not value.isdigit():
            raise serializers.ValidationError(
                "O código do serviço deve conter apenas números."
            )
        return value

    def validate_servico_tipo(self, value):
        """
        Validate servico_tipo field: allow spaces, enforce length limits when provided, apply formatting.
        """
        # If empty or None, it's valid (optional field)
        if not value:
            return value

        # Trim leading/trailing whitespace but preserve internal spaces
        trimmed_value = value.strip()

        # If empty after trimming, treat as empty (valid for optional field)
        if not trimmed_value:
            return ""

        # Check minimum length (only when field is provided)
        if len(trimmed_value) < 3:
            raise serializers.ValidationError("Tipo de serviço deve ter pelo menos 3 caracteres")

        # Check maximum length
        if len(trimmed_value) > 255:
            raise serializers.ValidationError("Tipo de serviço deve ter no máximo 255 caracteres")

        # Return uppercase formatted value
        return trimmed_value.upper()

    def validate_doc_revisao(self, value):
        """
        Validate that doc_revisao follows the R## format if provided.
        """
        if not value:  # Allow empty values
            return value

        if not value.startswith('R') or len(value) != 3:
            raise serializers.ValidationError(
                "Revisão deve estar no formato R## (ex: R01, R15)."
            )
        try:
            num = int(value[1:])
            if num < 1 or num > 99:
                raise ValueError()
        except ValueError:
            raise serializers.ValidationError(
                "Revisão deve ser um número entre 01 e 99."
            )
        return value

    def validate_doc_sei_num(self, value):
        """
        Validate that doc_sei_num contains only numeric characters if provided.
        """
        if not value:  # Allow empty values
            return value

        if not value.isdigit():
            raise serializers.ValidationError(
                "Número SEI deve conter apenas números."
            )
        return value

class ProtocoloSerializer(serializers.ModelSerializer):
    class Meta:
        model = Protocolo
        fields = ['id', 'doc_cod', 'doc_revisao', 'doc_sei_num', 'created_at']