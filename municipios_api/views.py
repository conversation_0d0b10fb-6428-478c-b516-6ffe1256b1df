
from rest_framework.permissions import BasePermission, SAFE_METHODS
from rest_framework import viewsets
from rest_framework import pagination
from municipios.models import Municipio
from municipios_api.serializers import MunicipioSerializer

class ReadOnly(BasePermission):
    def has_permission(self, request, view):
        return request.method in SAFE_METHODS


class LargeResultsSetPagination(pagination.LimitOffsetPagination):
    default_limit = 10_000
    limit_query_param = 'limit'
    offset_query_param = 'offset'
    max_limit = 10_000


class MunicipioViewSet(viewsets.ModelViewSet):
    queryset = Municipio.objects.all()
    serializer_class = MunicipioSerializer


class MunicipioPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    pagination_class = LargeResultsSetPagination
    serializer_class = MunicipioSerializer

    def get_queryset(self):
        qs = Municipio.objects.all()
        return qs