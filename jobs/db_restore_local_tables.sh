#!/bin/bash

# Database credentials
DB_USER="doretto"
DB_NAME="portalcci"

# Backup files
BACKUP_DIR="/home/<USER>/Projects/python/cciapp/cciapp/_local_backups"
LARGE_TABLES_FILE="$BACKUP_DIR/YYYYMMDD_large_tables.sql"
REST_FILE="$BACKUP_DIR/YYYYMMDD_rest.sql"

# Uncompress the backup files
# gunzip -k $LARGE_TABLES_FILE
# gunzip -k $REST_FILE

# Restore the large tables
psql -U $DB_USER -d $DB_NAME -f "${LARGE_TABLES_FILE}"

# Restore the rest of the database
psql -U $DB_USER -d $DB_NAME -f "${REST_FILE}"

# Clean up uncompressed files
# rm "${LARGE_TABLES_FILE}"
# rm "${REST_FILE}"
