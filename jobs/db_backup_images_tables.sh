#!/bin/bash

# Database credentials
DB_USER="doretto"
DB_NAME="portalcci"

# Backup destination
BACKUP_DIR="/home/<USER>/cciapp/cciapp/backups"
CURRENT_DATE=$(date +\%Y\%m\%d)
BACKUP_FILE_LARGE_TABLES="$BACKUP_DIR/${CURRENT_DATE}_large_tables.sql"
LOG_FILE="$BACKUP_DIR/backup_log_${CURRENT_DATE}.log"

# Ensure backup directory exists
mkdir -p "$BACKUP_DIR"

# Get the estimated table size
TABLE_SIZE=$(psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT pg_total_relation_size('ocorrencias_foto');" | xargs)

# Start logging
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting backup of table 'ocorrencias_foto' (Estimated size: $TABLE_SIZE bytes)" >> "$LOG_FILE"

# Start the backup in the background
nice -n 19 ionice -c2 -n7 pg_dump -U "$DB_USER" -d "$DB_NAME" --table=ocorrencias_foto > "$BACKUP_FILE_LARGE_TABLES" 2>> "$LOG_FILE" &
BACKUP_PID=$!

# Monitor progress
while kill -0 $BACKUP_PID 2> /dev/null; do
    if [ -f "$BACKUP_FILE_LARGE_TABLES" ]; then
        BACKUP_SIZE=$(stat -c%s "$BACKUP_FILE_LARGE_TABLES")
        PROGRESS=$(echo "scale=2; $BACKUP_SIZE/$TABLE_SIZE*100" | bc)

        # Cap progress at 100%
        if (( $(echo "$PROGRESS > 100" | bc -l) )); then
            PROGRESS=100
        fi

        echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup progress: $PROGRESS%"
    fi
    sleep 10
done


# Check if the backup was successful
wait $BACKUP_PID
if [ $? -eq 0 ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup completed successfully" >> "$LOG_FILE"
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup failed" >> "$LOG_FILE"
    exit 1
fi

# Remove backups older than 7 days
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Removing backups older than 7 days" >> "$LOG_FILE"
find "$BACKUP_DIR" -type f -name "*_large_tables.sql" -mtime +7 -exec rm {} \; -exec echo "[$(date '+%Y-%m-%d %H:%M:%S')] Removed {}" >> "$LOG_FILE" \;

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup script finished" >> "$LOG_FILE"

