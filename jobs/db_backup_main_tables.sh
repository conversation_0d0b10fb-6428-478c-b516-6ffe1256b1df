#!/bin/bash

# Database credentials
DB_USER="doretto"
DB_NAME="portalcci"

# Backup destination
BACKUP_DIR="/home/<USER>/cciapp/cciapp/backups"
BACKUP_FILE_REST="$BACKUP_DIR/$(date +\%Y\%m\%d)_main.sql"

# Perform the backup for the rest of the database
nice -n 19 ionice -c2 -n7 pg_dump -U $DB_USER -d $DB_NAME --exclude-table-data=ocorrencias_foto --exclude-table-data=trafego_trafegofoto > $BACKUP_FILE_REST

# Remove backups older than 7 days
find $BACKUP_DIR -type f -name "*_main.sql" -mtime +1 -exec rm {} \;
