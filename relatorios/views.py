import logging
from xhtml2pdf import pisa
from django.contrib.sites.shortcuts import get_current_site
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.http import Http404, HttpResponse, HttpResponseServerError
from django.core.exceptions import PermissionDenied
from django.urls import reverse
from django.shortcuts import render, redirect
from django.views import generic
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.utils import timezone
from django.utils.text import slugify
from django.forms import inlineformset_factory
from django.forms import modelformset_factory


logger = logging.getLogger("django_file")

def is_member(user):
    if user.groups.filter(name='cci').exists():
        return True
    else:
        raise PermissionDenied()


from .models import Relatorio, RelatorioModelo, RelatorioIndex, RelatorioFoto, RelatorioDestinatarios,\
    RelatorioModeloItens
from .forms import RelatorioForm, RelatorioFotoForm, RelatorioEditForm, RelatorioUnitarioForm,\
    RelatorioModeloForm, RelatorioModeloItemForm

from rodovias.models import Rodovia
from ocorrencias.models import Ocorrencia


class RelatorioModeloSelectView(LoginRequiredMixin, UserPassesTestMixin, generic.ListView):
    model = RelatorioModelo
    template_name = "dashboard/relatorios/select.html"
    context_object_name = 'templates'
    paginate_by = 10

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


@login_required
@user_passes_test(is_member)
def create_relatorio_view(request, rel_id):
    try:
        rel_modelo = RelatorioModelo.objects.get(pk=rel_id)
        rel_modelo_nome = rel_modelo.nome
        rel_modelo_itens = rel_modelo.itens.all().order_by('concessionaria__nome', 'rodovia__codigo')
        num_forms = len(rel_modelo_itens)
        initial_data = []
        ocorrencias = Ocorrencia.objects.all().filter(dt_hr_termino__isnull=True)
        base_url = get_current_site(request)
        for item in rel_modelo_itens:
            item_km_inicial = item.km_inicial
            item_km_final = item.km_final
            if item_km_inicial > item_km_final:
                item_km_inicial, item_km_final = item_km_final, item_km_inicial
            ocs = ocorrencias\
                .filter(rodovia__pk=item.rodovia.pk).filter(km_inicial__gte=item_km_inicial)\
                .filter(km_final__lte=item_km_final).filter(sentido=item.sentido)
            oc_str = "Tráfego Normal."
            oc_cor = "#D1FAE5"
            if ocs:
                oc_str = ""
                for oc in ocs:
                    oc_cor = "#FEF3C7"
                    oc_str += f"Ocorrência {oc.slug.upper()}, tipo '{oc.classe}'."\
                        f" KM inicial: {oc.km_inicial}."\
                        f" KM final: {oc.km_final}. Municípo(s): {oc.get_municipios_names()}."\
                        f" Info: {base_url}/{oc.slug}\n"
            initial_data.append(
                {
                    'concessionaria': item.concessionaria,
                    'rodovia': item.rodovia,
                    'nome': item.nome,
                    'sentido': item.sentido,
                    'destino': item.destino,
                    'km_inicial': item.km_inicial,
                    'km_final': item.km_final,
                    'status': oc_str,
                    'cor': oc_cor
                }
            )
        RelatorioFormSet = modelformset_factory(
            Relatorio, form=RelatorioForm, min_num=num_forms, max_num=num_forms
        )

        if request.method == 'POST':
            formset = RelatorioFormSet(request.POST, form_kwargs={'rel_modelo_itens': rel_modelo_itens}, prefix='relatorio-itens')
            if formset.is_valid():
                date = (timezone.now() - timezone.timedelta(hours=3)).strftime("%Y-%m-%d_%H-%M-%S")
                slug = slugify(f'rel--{rel_modelo_nome}--{date}')
                relatorio_obj = RelatorioIndex()
                relatorio_obj.template = rel_modelo
                relatorio_obj.slug = slug
                relatorio_obj.criado_por = request.user
                relatorio_obj.atualizado_por = request.user
                relatorio_obj.save()
                formset_obj = formset.save(commit=False)
                for form_obj in formset_obj:
                    form_obj.relatorio_index = relatorio_obj

                formset.save()
                messages.add_message(request, messages.SUCCESS, "Relatório criado com sucesso.")
                return redirect('relatorios:detail', relatorio_obj.pk)
            else:
                messages.add_message(request, messages.ERROR, "Ocorreu um erro ao tentar salvar o formulário.")
        else:
            formset = RelatorioFormSet(form_kwargs={'rel_modelo_itens': rel_modelo_itens},
                queryset=Relatorio.objects.none(), prefix='relatorio-itens', initial=initial_data)
        context = {
            'rel_modelo': rel_modelo,
            'formset': formset,
            'itens': rel_modelo_itens
        }
        return render(
            request, 'dashboard/relatorios/create.html',
            context
        )
    except RelatorioModelo.DoesNotExist:
        raise Http404("Relatório Modelo inexistente")
                    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:list')


@login_required
@user_passes_test(is_member)
def edit_relatorio_view(request, rel_id):
    try:
        relatorio = RelatorioIndex.objects.get(pk=rel_id)
        rel_modelo = relatorio.template

        RelatorioEditFormSet = modelformset_factory(
            Relatorio, form=RelatorioEditForm, extra=0, can_delete=True
        )

        if request.method == 'POST':
            formset = RelatorioEditFormSet(
                request.POST
            )
            if formset.is_valid():
                formset.save()
                messages.add_message(request, messages.SUCCESS, 'Relatório atualizado com sucesso.')
                return redirect('relatorios:detail', rel_id)
            else:
                messages.add_message(request, messages.ERROR, "Ocorreu um erro ao tentar salvar o formulário.")
        else:
            formset = RelatorioEditFormSet(
                queryset=relatorio.itens.all().order_by('concessionaria__nome', 'rodovia__codigo'),
            )
        context = {
            'relatorio': relatorio,
            'rel_modelo': rel_modelo,
            'formset': formset,
        }
        return render(
            request, 'dashboard/relatorios/edit.html',
            context
        )
    except RelatorioIndex.DoesNotExist:
        raise Http404("Relatório inexistente")
                        
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:detail', rel_id)


class SelectRodoviaListView(LoginRequiredMixin, UserPassesTestMixin, generic.ListView):
    model = Rodovia
    paginate_by = 10
    template_name = 'dashboard/relatorios/select-rodovia.html'
    context_object_name = 'rodovias'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


    def get_queryset(self):
        qs = super(SelectRodoviaListView, self).get_queryset()
        search = self.request.GET.get('search', '')
        if search:
            qs = qs.filter(codigo__icontains=search)
        return qs

    def get_context_data(self, **kwargs):
        context = super(SelectRodoviaListView, self).get_context_data(**kwargs)
        context['rel_id'] = None
        rel_id = self.kwargs.get('rel_id', None)
        if rel_id:
            context['rel_id'] = rel_id
        return context


class SelectRodoviaListModeloView(LoginRequiredMixin, UserPassesTestMixin, generic.ListView):
    model = Rodovia
    paginate_by = 10
    template_name = 'dashboard/relatorios/select-rodovia-modelo.html'
    context_object_name = 'rodovias'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


    def get_queryset(self):
        qs = super(SelectRodoviaListModeloView, self).get_queryset()
        search = self.request.GET.get('search', '')
        if search:
            qs = qs.filter(codigo__icontains=search)
        return qs

    def get_context_data(self, **kwargs):
        context = super(SelectRodoviaListModeloView, self).get_context_data(**kwargs)
        context['rel_modelo_id'] = None
        rel_modelo_id = self.kwargs.get('rel_modelo_id', None)
        if rel_modelo_id:
            context['rel_modelo_id'] = rel_modelo_id
        return context



@login_required
@user_passes_test(is_member)
def add_ocs_to_relatorio_view(request, rel_id):
    try:
        base_url = get_current_site(request)
        relatorio = RelatorioIndex.objects.get(pk=rel_id)
        itens = relatorio.itens.all()
        initial_data = []
        try:
            ocorrencias = Ocorrencia.objects.all().filter(dt_hr_termino__isnull=True)
            if not ocorrencias:
                raise HttpResponseServerError()
            ocs_modelo_ids = []
            for item in itens:
                item_km_inicial = item.km_inicial
                item_km_final = item.km_final
                if item_km_inicial > item_km_final:
                    item_km_inicial, item_km_final = item_km_final, item_km_inicial
                ocs = ocorrencias\
                    .filter(rodovia__pk=item.rodovia.pk).filter(km_inicial__gte=item_km_inicial)\
                    .filter(km_final__lte=item_km_final).filter(sentido=item.sentido)
                if ocs:
                    ids = ocs.values_list('pk', flat=True)
                    ocs_modelo_ids.append(ids)
            ocs_fm = ocorrencias.exclude(pk__in=ocs_modelo_ids)
            if not len(ocs_fm):
                raise HttpResponseServerError()
            for oc_fm in ocs_fm:
                oc_fm_str = ""
                oc_fm_nome = ""
                oc_fm_destino = ""
                oc_fm_cor = "#FEF3C7"
                oc_fm_str += f"Ocorrência {oc_fm.slug.upper()}, tipo '{oc_fm.classe}'."\
                    f" KM inicial: {oc_fm.km_inicial}."\
                    f" KM final: {oc_fm.km_final}. Município(s): {oc_fm.get_municipios_names()}."\
                    f" Info: {base_url}/{oc_fm.slug}\n"
                if oc_fm.concessionarias.all().count() > 1:
                    for conc in oc_fm.concessionarias.all():
                        initial_data.append(
                            {
                                'relatorio_index': rel_id,
                                'concessionaria': conc,
                                'rodovia': oc_fm.rodovia,
                                'nome': oc_fm_nome,
                                'sentido': oc_fm.sentido,
                                'destino': oc_fm_destino,
                                'km_inicial': oc_fm.km_inicial,
                                'km_final': oc_fm.km_final,
                                'status': oc_fm_str,
                                'cor': oc_fm_cor
                            }
                        )
                else:
                    initial_data.append(
                            {
                                'relatorio_index': rel_id,
                                'concessionaria': oc_fm.concessionarias.first(),
                                'rodovia': oc_fm.rodovia,
                                'nome': oc_fm_nome,
                                'sentido': oc_fm.sentido,
                                'destino': oc_fm_destino,
                                'km_inicial': oc_fm.km_inicial,
                                'km_final': oc_fm.km_final,
                                'status': oc_fm_str,
                                'cor': oc_fm_cor
                            }
                        )
            num_forms = len(initial_data)
            RelatorioFormSet = modelformset_factory(Relatorio,
                fields = (
                    'relatorio_index', 'concessionaria', 'rodovia', 'nome', 'sentido', 'destino', 'km_inicial', 'km_final', 'status',
                    'cond_climaticas', 'cor', 'obs',
                ),
                min_num=num_forms, extra=0
            )
            formset = RelatorioFormSet(request.POST or None, queryset=Relatorio.objects.none(), initial=initial_data)
            if request.method == "POST":
                if formset.is_valid():
                    formset.save()
                    messages.add_message(request, messages.SUCCESS, 'Relatório editado com sucesso.')
                    return redirect('relatorios:detail', rel_id)
                else:
                    messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")
            context = {
                'relatorio': relatorio,
                'formset': formset,
            }
            return render(request, 'dashboard/relatorios/add-ocs-to.html', context)
        except:
            messages.add_message(
                request, messages.ERROR,
                "Os trechos adicionais com OCs já foram adicionados ou não existem OCs fora do relatório.")
            return redirect("relatorios:detail", rel_id)

    except RelatorioIndex.DoesNotExist:
        raise Http404("Relatório inexistente")
                    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:detail', rel_id)


@login_required
@user_passes_test(is_member)
def add_to_relatorio_view(request, rel_id, rod_id):
    try:
        relatorio = RelatorioIndex.objects.get(pk=rel_id)
        form = RelatorioUnitarioForm(rel_id, rod_id, request.POST or None)
        if form.is_valid():
            form.save()
            messages.add_message(request, messages.SUCCESS, 'Relatório editado com sucesso.')
            return redirect('relatorios:detail', rel_id)
        if request.POST and not form.is_valid():
            messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")
        context = {
            'relatorio': relatorio,
            'form': form,
        }
        return render(request, 'dashboard/relatorios/add-to.html', context)

    except RelatorioIndex.DoesNotExist:
        raise Http404("Relatório inexistente")
                
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:detail', rel_id)


class RelatorioModeloListView(LoginRequiredMixin, UserPassesTestMixin, generic.ListView):
    model = RelatorioModelo
    template_name = "dashboard/relatorios/list-modelos.html"
    context_object_name = "modelos"
    paginate_by = 10

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


class RelatorioModeloDetailView(LoginRequiredMixin, UserPassesTestMixin, generic.DetailView):
    model = RelatorioModelo
    pk_url_kwarg = "rel_modelo_id"
    context_object_name = "modelo"
    template_name = "dashboard/relatorios/detail-modelo.html"

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["itens"] = context["modelo"].itens.all().order_by('concessionaria__nome', 'rodovia__codigo')
        return context


class RelatorioModeloUpdateView(LoginRequiredMixin, UserPassesTestMixin, generic.UpdateView):
    model = RelatorioModelo
    template_name = "dashboard/relatorios/update-modelo.html"
    context_object_name = "modelo"
    pk_url_kwarg = 'rel_modelo_id'
    form_class = RelatorioModeloForm

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('relatorios:detail-modelo', kwargs={'rel_modelo_id': self.object.pk })

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.criado_por = self.request.user
        self.object.atualizado_por = self.request.user
        self.object.save()
        return redirect(self.get_success_url())


class RelatorioModeloCreateView(LoginRequiredMixin, UserPassesTestMixin, generic.CreateView):
    model = RelatorioModelo
    template_name = "dashboard/relatorios/create-modelo.html"
    form_class = RelatorioModeloForm

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('relatorios:detail-modelo', kwargs={'rel_modelo_id': self.object.pk })

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.criado_por = self.request.user
        self.object.atualizado_por = self.request.user
        self.object.save()
        return redirect(self.get_success_url())


class RelatorioIndexListView(LoginRequiredMixin, UserPassesTestMixin, generic.ListView):
    model = RelatorioIndex
    template_name = "dashboard/relatorios/list.html"
    context_object_name = 'relatorios'
    paginate_by = 10

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_queryset(self):
        qs = super().get_queryset()
        filtro_nome = self.request.GET["filtro_nome"] if "filtro_nome" in self.request.GET else None
        filtro_modelos = self.request.GET["filtro_modelos"] if "filtro_modelos" in self.request.GET else None
        filtro_ano = self.request.GET["filtro_ano"] if "filtro_ano" in self.request.GET else None
        filtro_mes = self.request.GET["filtro_mes"] if "filtro_mes" in self.request.GET else None
        filtro_dia = self.request.GET["filtro_dia"] if "filtro_dia" in self.request.GET else None
        filtro_enviado = self.request.GET["filtro_enviado"] if "filtro_enviado" in self.request.GET else None
        if filtro_nome:
            qs = qs.filter(slug__icontains=filtro_nome)
        if filtro_modelos:
            qs = qs.filter(template__pk=filtro_modelos)
        if filtro_ano:
            qs = qs.filter(criado_em__year=filtro_ano)
        if filtro_mes:
            qs = qs.filter(criado_em__month=filtro_mes)
        if filtro_dia:
            qs = qs.filter(criado_em__day=filtro_dia)
        if filtro_enviado:
            filtro_enviado_boolean = bool(int(filtro_enviado))
            qs = qs.filter(enviado_em__isnull=filtro_enviado_boolean)
        return qs


    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["modelos"] = RelatorioModelo.objects.all()
        return context


@login_required
@user_passes_test(is_member)
def detail_relatorio_view(request, rel_id):
    try:
        relatorio_index = RelatorioIndex.objects.get(pk=rel_id)
        itens = relatorio_index.itens.all().order_by('concessionaria__nome', 'rodovia__codigo')
        context = {
            'relatorio': relatorio_index,
            'itens': itens,
        }
        return render(request, 'dashboard/relatorios/detail.html', context)
    except RelatorioIndex.DoesNotExist:
        raise Http404("Relatório inexistente")

    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:list')


class ModeloItemDeleteView(LoginRequiredMixin, UserPassesTestMixin, generic.DeleteView):
    model = RelatorioModeloItens
    pk_url_kwarg = 'item_id'
    template_name = 'dashboard/relatorios/delete-item.html'
    context_object_name = 'item'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_object(self, queryset=None):
        return RelatorioModeloItens.objects.get(pk=self.kwargs.get('item_id'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["modelo"] = RelatorioModelo.objects.get(pk=self.kwargs.get('rel_modelo_id'))
        return context

    def delete(self, request, *args, **kwargs):
        messages.add_message(request, messages.SUCCESS, 'Item excluído com sucesso.')
        return super(ModeloItemDeleteView, self).delete(request, *args, **kwargs)

    def get_success_url(self):
        return reverse('relatorios:detail-modelo', kwargs={'rel_modelo_id': self.kwargs.get('rel_modelo_id')})


@login_required
@user_passes_test(is_member)
def add_item_to_relatorio_modelo(request, rel_modelo_id, rod_id):
    try:
        modelo = RelatorioModelo.objects.get(pk=rel_modelo_id)
        form = RelatorioModeloItemForm(rel_modelo_id, rod_id, request.POST or None)
        if form.is_valid():
            form.save()
            messages.add_message(request, messages.SUCCESS, 'Modelo editado com sucesso.')
            return redirect('relatorios:detail-modelo', rel_modelo_id)
        if request.POST and not form.is_valid():
            messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")
        context = {
            'modelo': modelo,
            'form': form,
        }
        return render(request, 'dashboard/relatorios/add-item-modelo.html', context)
    except RelatorioModelo.DoesNotExist:
        raise Http404("Modelo inexistente")
        
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:detail-modelo', rel_modelo_id)


@login_required
@user_passes_test(is_member)
def add_destinatario_to_relatorio_modelo(request, rel_modelo_id):
    try:
        modelo = RelatorioModelo.objects.get(pk=rel_modelo_id)
        DestFormSet = inlineformset_factory(
            RelatorioModelo, RelatorioDestinatarios, fields=('nome', 'email',), extra=3
        )
        formset = DestFormSet(instance=modelo)
        if request.method == "POST":
            formset = DestFormSet(request.POST, instance=modelo)
            if formset.is_valid():
                formset.save()
                messages.add_message(request, messages.SUCCESS, 'Modelo editado com sucesso.')
                return redirect('relatorios:detail-modelo', modelo.pk)
            else:
                messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")
        context = {
            'modelo': modelo,
            'formset': formset,
        }
        return render(request, 'dashboard/relatorios/add-destinatario.html', context)
    except RelatorioModelo.DoesNotExist:
        raise Http404("Modelo inexistente")
        
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:detail-modelo', rel_modelo_id)


@login_required
@user_passes_test(is_member)
def add_photo_to_relatorio(request, rel_id, item_id):
    try:
        relatorio_index = RelatorioIndex.objects.get(pk=rel_id)
        item = Relatorio.objects.get(pk=item_id)
        RelFotoFormSet = inlineformset_factory(
            Relatorio, RelatorioFoto, fields=('foto', 'legenda',), max_num=3
        )
        formset = RelFotoFormSet(instance=item)
        if request.method == "POST":
            formset = RelFotoFormSet(request.POST, request.FILES, instance=item)
            if formset.is_valid():
                print('is valid')
                formset.save()
                messages.add_message(request, messages.SUCCESS, 'Relatório editado com sucesso.')
                return redirect('relatorios:detail', rel_id)
            else:
                print('form is not valid')
                print(formset.errors)
                print(formset.non_form_errors())
                messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")

        context = {
            'relatorio': relatorio_index,
            'item': item,
            'formset': formset,
        }
        return render(request, 'dashboard/relatorios/add-foto.html', context)
    except RelatorioIndex.DoesNotExist:
        raise Http404("Relatório inexistente")
    except Relatorio.DoesNotExist:
        raise Http404("Relatório inexistente")

        
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:detail', rel_id)


@login_required
@user_passes_test(is_member)
def before_send_relatorio_view(request, rel_id):
    try:
        relatorio = RelatorioIndex.objects.get(pk=rel_id)
        destinatarios = relatorio.template.destinatarios.all()
        context = {
            'relatorio': relatorio,
            'destinatarios': destinatarios
        }
        return render(request, 'dashboard/relatorios/confirm-send.html', context)
        
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('relatorios:detail', rel_id)


@login_required
@user_passes_test(is_member)
def send_relatorio_view(request, rel_id):
    try:
        relatorio = RelatorioIndex.objects.get(pk=rel_id)
        relatorio.enviado_em = timezone.now()
        relatorio.enviado_por = request.user
        relatorio.save()
        itens = relatorio.itens.all().order_by('concessionaria__nome', 'rodovia__codigo')
        context = {
            'relatorio': relatorio,
            'itens': itens,
        }
        html_content = render_to_string('dashboard/relatorios/email.html', context)
        text_content = strip_tags(html_content)
        data_str = relatorio.criado_em.strftime("%d/%m/%Y %H:%M")
        emails = [request.user.email]
        for dest in relatorio.template.destinatarios.all():
            emails.append(dest.email)
        to = list(set(emails))
        email = EmailMultiAlternatives(
            subject=f'{relatorio.template.nome} | {data_str}',
            body=text_content,
            from_email=settings.EMAIL_ADDRESS,
            to=to,
            cc=None
        )
        email.attach_alternative(html_content, "text/html")
        email.send(fail_silently=False)
        messages.add_message(request, messages.SUCCESS, "E-mail enviado com sucesso.")
        return redirect('relatorios:detail', rel_id)
        
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro e o e-mail não foi enviado.")
        return redirect('relatorios:detail', rel_id)

@login_required
@user_passes_test(is_member)
def test_email(request, rel_id):
    try:
        relatorio = RelatorioIndex.objects.get(pk=rel_id)
        itens = relatorio.itens.all().order_by('concessionaria__nome', 'rodovia__codigo')
        context = {
            'relatorio': relatorio,
            'itens': itens,
        }
        return render(request, 'dashboard/relatorios/email.html', context)
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro e não foi possível gerar a pré-visualização.")
        return redirect('relatorios:detail', rel_id)

@login_required
@user_passes_test(is_member)
def generate_pdf(request, rel_id):
    try:
        relatorio = RelatorioIndex.objects.get(pk=rel_id)
        itens = relatorio.itens.all().order_by('concessionaria__nome', 'rodovia__codigo')
        ocorrencias = Ocorrencia.objects.all().filter(dt_hr_termino__isnull=True)
        itens_template = {}
        for item in itens:
            if item.concessionaria not in itens_template.keys():
                itens_template[item.concessionaria] = {'itens': [], 'length': 0}

            itens_template[item.concessionaria]['itens'].append(item)

            if item.fotos.count():
                itens_template[item.concessionaria]['length'] = itens_template[item.concessionaria]['length'] + 2
            else:
                itens_template[item.concessionaria]['length'] = itens_template[item.concessionaria]['length'] + 1
        context = {
            'relatorio': relatorio,
            'itens': itens_template,
            'ocorrencias': ocorrencias
        }


        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=Relatorio-' +\
            relatorio.slug + '.pdf'

        # template_path = 'dashboard/relatorios/pdf.html'
        # template = get_template(template_path)
        # html = template.render(context)
        html = render_to_string('dashboard/relatorios/pdf.html', context)
        # from django.utils.html import linebreaks
        # html = linebreaks(html, True)
        # print(html)
        # create a pdf
        logger.warning("Render to strings ok. Trying to generate PDF file on the fly...")
        pisa_status = pisa.CreatePDF(
        html, dest=response, link_callback=None)
        # if error then show some funy view
        if pisa_status.err:
            return HttpResponse('We had some errors <pre>' + html + '</pre>')

        return response

    except RelatorioIndex.DoesNotExist:
        return Http404("Relatório inexistente")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro e não foi possível gerar o PDF.")
        return redirect('relatorios:detail', rel_id)

@login_required
@user_passes_test(is_member)
def test_pdf(request, rel_id):
    try:
        relatorio = RelatorioIndex.objects.get(pk=rel_id)
        itens = relatorio.itens.all().order_by('concessionaria__nome', 'rodovia__codigo')
        itens_template = {}
        for item in itens:
            if item.concessionaria not in itens_template.keys():
                itens_template[item.concessionaria] = {'itens': [], 'length': 0}
            item_id = item.pk
            itens_template[item.concessionaria]['itens'].append(item)
            if item.fotos.count():
                itens_template[item.concessionaria]['length'] = itens_template[item.concessionaria]['length'] + 2
            else:
                itens_template[item.concessionaria]['length'] = itens_template[item.concessionaria]['length'] + 1
        context = {
            'relatorio': relatorio,
            'itens': itens_template,
        }
        return render(request, 'dashboard/relatorios/pdf.html', context)

    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro e não foi possível gerar a pré-visualização.")
        return redirect('relatorios:detail', rel_id)