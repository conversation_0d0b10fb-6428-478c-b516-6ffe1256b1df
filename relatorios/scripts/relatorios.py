#!/usr/bin/env python


# This file has been automatically generated.
# Instead of changing it, create a file called import_helper.py
# and put there a class called ImportHelper(object) in it.
#
# This class will be specially casted so that instead of extending object,
# it will actually extend the class BasicImportHelper()
#
# That means you just have to overload the methods you want to
# change, leaving the other ones intact.
#
# Something that you might want to do is use transactions, for example.
#
# Also, don't forget to add the necessary Django imports.
#
# This file was generated with the following command:
# manage.py dumpscript relatorios
#
# to restore it, run
# manage.py runscript module_name.this_script_name
#
# example: if manage.py is at ./manage.py
# and the script is at ./some_folder/some_script.py
# you must make sure ./some_folder/__init__.py exists
# and run  ./manage.py runscript some_folder.some_script
import os, sys
from django.db import transaction

class BasicImportHelper:

    def pre_import(self):
        pass

    @transaction.atomic
    def run_import(self, import_data):
        import_data()

    def post_import(self):
        pass

    def locate_similar(self, current_object, search_data):
        # You will probably want to call this method from save_or_locate()
        # Example:
        #   new_obj = self.locate_similar(the_obj, {"national_id": the_obj.national_id } )

        the_obj = current_object.__class__.objects.get(**search_data)
        return the_obj

    def locate_object(self, original_class, original_pk_name, the_class, pk_name, pk_value, obj_content):
        # You may change this function to do specific lookup for specific objects
        #
        # original_class class of the django orm's object that needs to be located
        # original_pk_name the primary key of original_class
        # the_class      parent class of original_class which contains obj_content
        # pk_name        the primary key of original_class
        # pk_value       value of the primary_key
        # obj_content    content of the object which was not exported.
        #
        # You should use obj_content to locate the object on the target db
        #
        # An example where original_class and the_class are different is
        # when original_class is Farmer and the_class is Person. The table
        # may refer to a Farmer but you will actually need to locate Person
        # in order to instantiate that Farmer
        #
        # Example:
        #   if the_class == SurveyResultFormat or the_class == SurveyType or the_class == SurveyState:
        #       pk_name="name"
        #       pk_value=obj_content[pk_name]
        #   if the_class == StaffGroup:
        #       pk_value=8

        search_data = { pk_name: pk_value }
        the_obj = the_class.objects.get(**search_data)
        #print(the_obj)
        return the_obj


    def save_or_locate(self, the_obj):
        # Change this if you want to locate the object in the database
        try:
            the_obj.save()
        except:
            print("---------------")
            print("Error saving the following object:")
            print(the_obj.__class__)
            print(" ")
            print(the_obj.__dict__)
            print(" ")
            print(the_obj)
            print(" ")
            print("---------------")

            raise
        return the_obj


importer = None
try:
    import import_helper
    # We need this so ImportHelper can extend BasicImportHelper, although import_helper.py
    # has no knowlodge of this class
    importer = type("DynamicImportHelper", (import_helper.ImportHelper, BasicImportHelper ) , {} )()
except ImportError as e:
    # From Python 3.3 we can check e.name - string match is for backward compatibility.
    if 'import_helper' in str(e):
        importer = BasicImportHelper()
    else:
        raise

import datetime
from decimal import Decimal
from django.contrib.contenttypes.models import ContentType

try:
    import dateutil.parser
    from dateutil.tz import tzoffset
except ImportError:
    print("Please install python-dateutil")
    sys.exit(os.EX_USAGE)

def run():
    importer.pre_import()
    importer.run_import(import_data)
    importer.post_import()

def import_data():
    # Initial Imports
    from rodovias.models import Rodovia
    from concessionarias.models import Concessionaria
    from accounts.models import Account

    # Processing model: relatorios.models.RelatorioModelo

    from relatorios.models import RelatorioModelo

    relatorios_relatoriomodelo_1 = RelatorioModelo()
    relatorios_relatoriomodelo_1.nome = 'Boletim Hora em Hora'
    relatorios_relatoriomodelo_1.periodicidade = 1
    relatorios_relatoriomodelo_1.criado_em = dateutil.parser.parse("2021-05-20T08:46:17.011023")
    relatorios_relatoriomodelo_1.criado_por =  None
    relatorios_relatoriomodelo_1.atualizado_em = dateutil.parser.parse("2021-05-20T08:46:17.011023")
    relatorios_relatoriomodelo_1.atualizado_por =  None
    relatorios_relatoriomodelo_1 = importer.save_or_locate(relatorios_relatoriomodelo_1)

    from relatorios.models import RelatorioModeloItens

    relatorios_relatoriomodeloitens_1 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_1.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_1.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 82, {'id': 82, 'nome': 'Viaoeste', 'slug': 'viaoeste', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária de Rodovias do Oeste de SÒo Paulo - VIAOESTE S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/viaoeste_wbrveq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rua SÒo JoÒo', 'numero': '30', 'bairro': None, 'cidade': 'Barueri', 'cep': '18147-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 12, 'etapa': None, 'contrato': 'CR/003/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '008/CIC/97', 'grupo': 'CCR', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.932195-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:43:49.493381-03:00")} ) 
    relatorios_relatoriomodeloitens_1.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 337, {'id': 337, 'codigo': 'SP 270', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-270', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.110051-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_1.nome = 'Raposo Tavares'
    relatorios_relatoriomodeloitens_1.sentido = 'Oeste'
    relatorios_relatoriomodeloitens_1.destino = 'Interior'
    relatorios_relatoriomodeloitens_1.km_inicial = Decimal('34.000')
    relatorios_relatoriomodeloitens_1.km_final = Decimal('115.500')
    relatorios_relatoriomodeloitens_1 = importer.save_or_locate(relatorios_relatoriomodeloitens_1)

    relatorios_relatoriomodeloitens_2 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_2.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_2.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 82, {'id': 82, 'nome': 'Viaoeste', 'slug': 'viaoeste', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária de Rodovias do Oeste de SÒo Paulo - VIAOESTE S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/viaoeste_wbrveq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rua SÒo JoÒo', 'numero': '30', 'bairro': None, 'cidade': 'Barueri', 'cep': '18147-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 12, 'etapa': None, 'contrato': 'CR/003/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '008/CIC/97', 'grupo': 'CCR', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.932195-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:43:49.493381-03:00")} ) 
    relatorios_relatoriomodeloitens_2.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 337, {'id': 337, 'codigo': 'SP 270', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-270', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.110051-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_2.nome = 'Raposo Tavares'
    relatorios_relatoriomodeloitens_2.sentido = 'Leste'
    relatorios_relatoriomodeloitens_2.destino = 'Capital'
    relatorios_relatoriomodeloitens_2.km_inicial = Decimal('34.000')
    relatorios_relatoriomodeloitens_2.km_final = Decimal('115.500')
    relatorios_relatoriomodeloitens_2 = importer.save_or_locate(relatorios_relatoriomodeloitens_2)

    relatorios_relatoriomodeloitens_3 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_3.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_3.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 82, {'id': 82, 'nome': 'Viaoeste', 'slug': 'viaoeste', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária de Rodovias do Oeste de SÒo Paulo - VIAOESTE S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/viaoeste_wbrveq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rua SÒo JoÒo', 'numero': '30', 'bairro': None, 'cidade': 'Barueri', 'cep': '18147-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 12, 'etapa': None, 'contrato': 'CR/003/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '008/CIC/97', 'grupo': 'CCR', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.932195-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:43:49.493381-03:00")} ) 
    relatorios_relatoriomodeloitens_3.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 345, {'id': 345, 'codigo': 'SP 280', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-280', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.129154-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_3.nome = 'Castello Branco'
    relatorios_relatoriomodeloitens_3.sentido = 'Oeste'
    relatorios_relatoriomodeloitens_3.destino = 'Interior'
    relatorios_relatoriomodeloitens_3.km_inicial = Decimal('13.700')
    relatorios_relatoriomodeloitens_3.km_final = Decimal('79.380')
    relatorios_relatoriomodeloitens_3 = importer.save_or_locate(relatorios_relatoriomodeloitens_3)

    relatorios_relatoriomodeloitens_4 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_4.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_4.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 82, {'id': 82, 'nome': 'Viaoeste', 'slug': 'viaoeste', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária de Rodovias do Oeste de SÒo Paulo - VIAOESTE S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/viaoeste_wbrveq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rua SÒo JoÒo', 'numero': '30', 'bairro': None, 'cidade': 'Barueri', 'cep': '18147-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 12, 'etapa': None, 'contrato': 'CR/003/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '008/CIC/97', 'grupo': 'CCR', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.932195-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:43:49.493381-03:00")} ) 
    relatorios_relatoriomodeloitens_4.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 345, {'id': 345, 'codigo': 'SP 280', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-280', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.129154-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_4.nome = 'Castello Branco'
    relatorios_relatoriomodeloitens_4.sentido = 'Leste'
    relatorios_relatoriomodeloitens_4.destino = 'Capital'
    relatorios_relatoriomodeloitens_4.km_inicial = Decimal('13.700')
    relatorios_relatoriomodeloitens_4.km_final = Decimal('79.380')
    relatorios_relatoriomodeloitens_4 = importer.save_or_locate(relatorios_relatoriomodeloitens_4)

    relatorios_relatoriomodeloitens_5 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_5.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_5.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 78, {'id': 78, 'nome': 'Tamoios', 'slug': 'tamoios', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionaria Rodovia dos Tamoios S.A.', 'logo': 'cciapp/media/media/images/concessionarias/logos/tamoios_gqtuqt', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Avenida Cassiano Ricardo', 'numero': '601', 'bairro': None, 'cidade': 'SÒo Paulo', 'cep': '12246-870', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 27, 'etapa': None, 'contrato': '008/2014', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '001/2014', 'grupo': 'Queiroz GalvÒo', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.939232-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:44:06.943104-03:00")} ) 
    relatorios_relatoriomodeloitens_5.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 273, {'id': 273, 'codigo': 'SP 099', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-099', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.923071-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_5.nome = 'Rodovia dos Tamoios'
    relatorios_relatoriomodeloitens_5.sentido = 'Sul'
    relatorios_relatoriomodeloitens_5.destino = 'Litoral'
    relatorios_relatoriomodeloitens_5.km_inicial = Decimal('11.500')
    relatorios_relatoriomodeloitens_5.km_final = Decimal('83.400')
    relatorios_relatoriomodeloitens_5 = importer.save_or_locate(relatorios_relatoriomodeloitens_5)

    relatorios_relatoriomodeloitens_6 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_6.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_6.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 78, {'id': 78, 'nome': 'Tamoios', 'slug': 'tamoios', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionaria Rodovia dos Tamoios S.A.', 'logo': 'cciapp/media/media/images/concessionarias/logos/tamoios_gqtuqt', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Avenida Cassiano Ricardo', 'numero': '601', 'bairro': None, 'cidade': 'SÒo Paulo', 'cep': '12246-870', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 27, 'etapa': None, 'contrato': '008/2014', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '001/2014', 'grupo': 'Queiroz GalvÒo', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.939232-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:44:06.943104-03:00")} ) 
    relatorios_relatoriomodeloitens_6.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 273, {'id': 273, 'codigo': 'SP 099', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-099', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.923071-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_6.nome = 'Rodovia dos Tamoios'
    relatorios_relatoriomodeloitens_6.sentido = 'Norte'
    relatorios_relatoriomodeloitens_6.destino = 'São José'
    relatorios_relatoriomodeloitens_6.km_inicial = Decimal('11.500')
    relatorios_relatoriomodeloitens_6.km_final = Decimal('83.400')
    relatorios_relatoriomodeloitens_6 = importer.save_or_locate(relatorios_relatoriomodeloitens_6)

    relatorios_relatoriomodeloitens_7 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_7.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_7.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 76, {'id': 76, 'nome': 'SPMAR', 'slug': 'spmar', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária SPMAR S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/spmar_qwlbzs', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodoanel Mário Covas', 'numero': 'Km 41', 'bairro': None, 'cidade': 'Itapecerica da Serra', 'cep': '06869-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 25, 'etapa': None, 'contrato': '001/ARTESP/2011', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '001/2010', 'grupo': None, 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.928677-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:38:57.644378-03:00")} ) 
    relatorios_relatoriomodeloitens_7.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 237, {'id': 237, 'codigo': 'SP 021', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-021', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.828575-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_7.nome = 'Rodoanel Mário Covas'
    relatorios_relatoriomodeloitens_7.sentido = 'Interno'
    relatorios_relatoriomodeloitens_7.destino = 'Capital'
    relatorios_relatoriomodeloitens_7.km_inicial = Decimal('30.710')
    relatorios_relatoriomodeloitens_7.km_final = Decimal('129.210')
    relatorios_relatoriomodeloitens_7 = importer.save_or_locate(relatorios_relatoriomodeloitens_7)

    relatorios_relatoriomodeloitens_8 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_8.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_8.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 76, {'id': 76, 'nome': 'SPMAR', 'slug': 'spmar', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária SPMAR S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/spmar_qwlbzs', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodoanel Mário Covas', 'numero': 'Km 41', 'bairro': None, 'cidade': 'Itapecerica da Serra', 'cep': '06869-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 25, 'etapa': None, 'contrato': '001/ARTESP/2011', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '001/2010', 'grupo': None, 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.928677-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:38:57.644378-03:00")} ) 
    relatorios_relatoriomodeloitens_8.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 237, {'id': 237, 'codigo': 'SP 021', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-021', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.828575-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_8.nome = 'Rodoanel Mário Covas'
    relatorios_relatoriomodeloitens_8.sentido = 'Externo'
    relatorios_relatoriomodeloitens_8.destino = 'Litoral'
    relatorios_relatoriomodeloitens_8.km_inicial = Decimal('30.710')
    relatorios_relatoriomodeloitens_8.km_final = Decimal('129.210')
    relatorios_relatoriomodeloitens_8 = importer.save_or_locate(relatorios_relatoriomodeloitens_8)

    relatorios_relatoriomodeloitens_9 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_9.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_9.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_9.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 266, {'id': 266, 'codigo': 'SP 083', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-083', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.902463-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_9.nome = 'José Roberto Magalhães Teixeira'
    relatorios_relatoriomodeloitens_9.sentido = 'Sul'
    relatorios_relatoriomodeloitens_9.destino = 'Capital'
    relatorios_relatoriomodeloitens_9.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_9.km_final = Decimal('18.000')
    relatorios_relatoriomodeloitens_9 = importer.save_or_locate(relatorios_relatoriomodeloitens_9)

    relatorios_relatoriomodeloitens_10 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_10.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_10.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_10.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 266, {'id': 266, 'codigo': 'SP 083', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-083', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.902463-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_10.nome = 'José Roberto Magalhães Teixeira'
    relatorios_relatoriomodeloitens_10.sentido = 'Norte'
    relatorios_relatoriomodeloitens_10.destino = 'Interior'
    relatorios_relatoriomodeloitens_10.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_10.km_final = Decimal('18.000')
    relatorios_relatoriomodeloitens_10 = importer.save_or_locate(relatorios_relatoriomodeloitens_10)

    relatorios_relatoriomodeloitens_11 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_11.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_11.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_11.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 392, {'id': 392, 'codigo': 'SP 360', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-360', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.236293-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_11.nome = 'Eng. Constâncio Cintra'
    relatorios_relatoriomodeloitens_11.sentido = 'Sul'
    relatorios_relatoriomodeloitens_11.destino = 'Capital'
    relatorios_relatoriomodeloitens_11.km_inicial = Decimal('61.900')
    relatorios_relatoriomodeloitens_11.km_final = Decimal('81.220')
    relatorios_relatoriomodeloitens_11 = importer.save_or_locate(relatorios_relatoriomodeloitens_11)

    relatorios_relatoriomodeloitens_12 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_12.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_12.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_12.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 392, {'id': 392, 'codigo': 'SP 360', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-360', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.236293-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_12.nome = 'Eng. Constâncio Cintra'
    relatorios_relatoriomodeloitens_12.sentido = 'Norte'
    relatorios_relatoriomodeloitens_12.destino = 'Interior'
    relatorios_relatoriomodeloitens_12.km_inicial = Decimal('61.900')
    relatorios_relatoriomodeloitens_12.km_final = Decimal('81.220')
    relatorios_relatoriomodeloitens_12 = importer.save_or_locate(relatorios_relatoriomodeloitens_12)

    relatorios_relatoriomodeloitens_13 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_13.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_13.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_13.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 374, {'id': 374, 'codigo': 'SP 332', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-332', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.196508-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_13.nome = 'Zeferino Vaz'
    relatorios_relatoriomodeloitens_13.sentido = 'Sul'
    relatorios_relatoriomodeloitens_13.destino = 'Capital'
    relatorios_relatoriomodeloitens_13.km_inicial = Decimal('110.280')
    relatorios_relatoriomodeloitens_13.km_final = Decimal('187.310')
    relatorios_relatoriomodeloitens_13 = importer.save_or_locate(relatorios_relatoriomodeloitens_13)

    relatorios_relatoriomodeloitens_14 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_14.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_14.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_14.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 374, {'id': 374, 'codigo': 'SP 332', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-332', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.196508-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_14.nome = 'Zeferino Vaz'
    relatorios_relatoriomodeloitens_14.sentido = 'Norte'
    relatorios_relatoriomodeloitens_14.destino = 'Interior'
    relatorios_relatoriomodeloitens_14.km_inicial = Decimal('110.280')
    relatorios_relatoriomodeloitens_14.km_final = Decimal('187.310')
    relatorios_relatoriomodeloitens_14 = importer.save_or_locate(relatorios_relatoriomodeloitens_14)

    relatorios_relatoriomodeloitens_15 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_15.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_15.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_15.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 258, {'id': 258, 'codigo': 'SP 065', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-065', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.881353-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_15.nome = 'Dom Pedro I'
    relatorios_relatoriomodeloitens_15.sentido = 'Sul'
    relatorios_relatoriomodeloitens_15.destino = 'Capital'
    relatorios_relatoriomodeloitens_15.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_15.km_final = Decimal('145.500')
    relatorios_relatoriomodeloitens_15 = importer.save_or_locate(relatorios_relatoriomodeloitens_15)

    relatorios_relatoriomodeloitens_16 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_16.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_16.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_16.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 258, {'id': 258, 'codigo': 'SP 065', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-065', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.881353-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_16.nome = 'Dom Pedro I'
    relatorios_relatoriomodeloitens_16.sentido = 'Norte'
    relatorios_relatoriomodeloitens_16.destino = 'Interior'
    relatorios_relatoriomodeloitens_16.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_16.km_final = Decimal('145.500')
    relatorios_relatoriomodeloitens_16 = importer.save_or_locate(relatorios_relatoriomodeloitens_16)

    relatorios_relatoriomodeloitens_17 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_17.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_17.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_17.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 3588, {'id': 3588, 'codigo': 'SP 063', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-063', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:20.371926-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_17.nome = 'Romildo Prado'
    relatorios_relatoriomodeloitens_17.sentido = 'Oeste'
    relatorios_relatoriomodeloitens_17.destino = 'Capital'
    relatorios_relatoriomodeloitens_17.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_17.km_final = Decimal('15.700')
    relatorios_relatoriomodeloitens_17 = importer.save_or_locate(relatorios_relatoriomodeloitens_17)

    relatorios_relatoriomodeloitens_18 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_18.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_18.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 75, {'id': 75, 'nome': 'Rota das Bandeiras', 'slug': 'rota-das-bandeiras', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Rota das Bandeiras S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/rota_das_bandeiras_unr6rq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Dom Pedro l (SP-065) Km 110+400 - Pista Sul', 'numero': 'km 110+400', 'bairro': None, 'cidade': 'Itatiba', 'cep': '13252-800', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 7, 'etapa': None, 'contrato': '003/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2009', 'grupo': 'Odebrecht', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.950291-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:50.694126-03:00")} ) 
    relatorios_relatoriomodeloitens_18.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 3588, {'id': 3588, 'codigo': 'SP 063', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-063', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:20.371926-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_18.nome = 'Romildo Prado'
    relatorios_relatoriomodeloitens_18.sentido = 'Leste'
    relatorios_relatoriomodeloitens_18.destino = 'Interior'
    relatorios_relatoriomodeloitens_18.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_18.km_final = Decimal('15.700')
    relatorios_relatoriomodeloitens_18 = importer.save_or_locate(relatorios_relatoriomodeloitens_18)

    relatorios_relatoriomodeloitens_19 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_19.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_19.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 63, {'id': 63, 'nome': 'CCR RodoAnel', 'slug': 'ccr-rodoanel', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária do Rodoanel Oeste S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ccr_rodoanel_bx2pd4', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Avenida Marcos Penteado Ulhoa Rodrigues', 'numero': '690', 'bairro': None, 'cidade': 'Barueri', 'cep': '06460-040', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 24, 'etapa': None, 'contrato': '001/ARTESP/2008', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '001/2008', 'grupo': 'CCR', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.923651-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:37.660725-03:00")} ) 
    relatorios_relatoriomodeloitens_19.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 237, {'id': 237, 'codigo': 'SP 021', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-021', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.828575-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_19.nome = 'Rodoanel Mário Covas'
    relatorios_relatoriomodeloitens_19.sentido = 'Interno'
    relatorios_relatoriomodeloitens_19.destino = 'Capital'
    relatorios_relatoriomodeloitens_19.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_19.km_final = Decimal('30.710')
    relatorios_relatoriomodeloitens_19 = importer.save_or_locate(relatorios_relatoriomodeloitens_19)

    relatorios_relatoriomodeloitens_20 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_20.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_20.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 63, {'id': 63, 'nome': 'CCR RodoAnel', 'slug': 'ccr-rodoanel', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária do Rodoanel Oeste S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ccr_rodoanel_bx2pd4', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Avenida Marcos Penteado Ulhoa Rodrigues', 'numero': '690', 'bairro': None, 'cidade': 'Barueri', 'cep': '06460-040', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 24, 'etapa': None, 'contrato': '001/ARTESP/2008', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '001/2008', 'grupo': 'CCR', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.923651-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:37.660725-03:00")} ) 
    relatorios_relatoriomodeloitens_20.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 237, {'id': 237, 'codigo': 'SP 021', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-021', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.828575-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_20.nome = 'Rodoanel Mário Covas'
    relatorios_relatoriomodeloitens_20.sentido = 'Externo'
    relatorios_relatoriomodeloitens_20.destino = 'Interior'
    relatorios_relatoriomodeloitens_20.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_20.km_final = Decimal('30.710')
    relatorios_relatoriomodeloitens_20 = importer.save_or_locate(relatorios_relatoriomodeloitens_20)

    relatorios_relatoriomodeloitens_21 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_21.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_21.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_21.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 3519, {'id': 3519, 'codigo': 'SPI 040/150', 'nome_principal': None, 'nome_secundario': None, 'slug': 'spi-040-150', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:20.088760-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_21.nome = 'Interligação Planalto'
    relatorios_relatoriomodeloitens_21.sentido = 'Oeste'
    relatorios_relatoriomodeloitens_21.destino = 'Capital'
    relatorios_relatoriomodeloitens_21.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_21.km_final = Decimal('8.000')
    relatorios_relatoriomodeloitens_21 = importer.save_or_locate(relatorios_relatoriomodeloitens_21)

    relatorios_relatoriomodeloitens_22 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_22.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_22.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_22.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 3519, {'id': 3519, 'codigo': 'SPI 040/150', 'nome_principal': None, 'nome_secundario': None, 'slug': 'spi-040-150', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:20.088760-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_22.nome = 'Interligação Planalto'
    relatorios_relatoriomodeloitens_22.sentido = 'Leste'
    relatorios_relatoriomodeloitens_22.destino = 'Via Anchieta'
    relatorios_relatoriomodeloitens_22.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_22.km_final = Decimal('8.000')
    relatorios_relatoriomodeloitens_22 = importer.save_or_locate(relatorios_relatoriomodeloitens_22)

    relatorios_relatoriomodeloitens_23 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_23.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_23.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_23.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 250, {'id': 250, 'codigo': 'SP 055', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-055', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.860241-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_23.nome = 'Via Padre Manoel da Nóbrega'
    relatorios_relatoriomodeloitens_23.sentido = 'Oeste'
    relatorios_relatoriomodeloitens_23.destino = 'Praia Grande'
    relatorios_relatoriomodeloitens_23.km_inicial = Decimal('270.600')
    relatorios_relatoriomodeloitens_23.km_final = Decimal('292.200')
    relatorios_relatoriomodeloitens_23 = importer.save_or_locate(relatorios_relatoriomodeloitens_23)

    relatorios_relatoriomodeloitens_24 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_24.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_24.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_24.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 250, {'id': 250, 'codigo': 'SP 055', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-055', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.860241-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_24.nome = 'Via Padre Manoel da Nóbrega'
    relatorios_relatoriomodeloitens_24.sentido = 'Leste'
    relatorios_relatoriomodeloitens_24.destino = 'Capital'
    relatorios_relatoriomodeloitens_24.km_inicial = Decimal('270.600')
    relatorios_relatoriomodeloitens_24.km_final = Decimal('292.200')
    relatorios_relatoriomodeloitens_24 = importer.save_or_locate(relatorios_relatoriomodeloitens_24)

    relatorios_relatoriomodeloitens_25 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_25.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_25.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_25.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 250, {'id': 250, 'codigo': 'SP 055', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-055', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.860241-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_25.nome = 'Cônego Domênico Rangoni'
    relatorios_relatoriomodeloitens_25.sentido = 'Oeste'
    relatorios_relatoriomodeloitens_25.destino = 'Capital'
    relatorios_relatoriomodeloitens_25.km_inicial = Decimal('248.500')
    relatorios_relatoriomodeloitens_25.km_final = Decimal('270.600')
    relatorios_relatoriomodeloitens_25 = importer.save_or_locate(relatorios_relatoriomodeloitens_25)

    relatorios_relatoriomodeloitens_26 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_26.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_26.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_26.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 250, {'id': 250, 'codigo': 'SP 055', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-055', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.860241-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_26.nome = 'Cônego Domênico Rangoni'
    relatorios_relatoriomodeloitens_26.sentido = 'Leste'
    relatorios_relatoriomodeloitens_26.destino = 'Guarujá'
    relatorios_relatoriomodeloitens_26.km_inicial = Decimal('248.500')
    relatorios_relatoriomodeloitens_26.km_final = Decimal('270.600')
    relatorios_relatoriomodeloitens_26 = importer.save_or_locate(relatorios_relatoriomodeloitens_26)

    relatorios_relatoriomodeloitens_27 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_27.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_27.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_27.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 298, {'id': 298, 'codigo': 'SP 160', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-160', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.994949-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_27.nome = 'Imigrantes'
    relatorios_relatoriomodeloitens_27.sentido = 'Sul'
    relatorios_relatoriomodeloitens_27.destino = 'Litoral'
    relatorios_relatoriomodeloitens_27.km_inicial = Decimal('11.460')
    relatorios_relatoriomodeloitens_27.km_final = Decimal('70.000')
    relatorios_relatoriomodeloitens_27 = importer.save_or_locate(relatorios_relatoriomodeloitens_27)

    relatorios_relatoriomodeloitens_28 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_28.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_28.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_28.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 298, {'id': 298, 'codigo': 'SP 160', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-160', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.994949-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_28.nome = 'Imigrantes'
    relatorios_relatoriomodeloitens_28.sentido = 'Norte'
    relatorios_relatoriomodeloitens_28.destino = 'Capital'
    relatorios_relatoriomodeloitens_28.km_inicial = Decimal('11.460')
    relatorios_relatoriomodeloitens_28.km_final = Decimal('70.000')
    relatorios_relatoriomodeloitens_28 = importer.save_or_locate(relatorios_relatoriomodeloitens_28)

    relatorios_relatoriomodeloitens_29 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_29.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_29.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_29.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 294, {'id': 294, 'codigo': 'SP 150', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-150', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.984392-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_29.nome = 'Anchieta'
    relatorios_relatoriomodeloitens_29.sentido = 'Sul'
    relatorios_relatoriomodeloitens_29.destino = 'Litoral'
    relatorios_relatoriomodeloitens_29.km_inicial = Decimal('9.700')
    relatorios_relatoriomodeloitens_29.km_final = Decimal('65.600')
    relatorios_relatoriomodeloitens_29 = importer.save_or_locate(relatorios_relatoriomodeloitens_29)

    relatorios_relatoriomodeloitens_30 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_30.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_30.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 68, {'id': 68, 'nome': 'Ecovias', 'slug': 'ecovias', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária Ecovias dos Imigrantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecovias_binwhq', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia dos Imigrantes', 'numero': 'km 28,5', 'bairro': None, 'cidade': 'SÒo Bernardo do Campo', 'cep': '09845-000', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 22, 'etapa': None, 'contrato': '007/CR/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '15/CIC/97', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.955317-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:40:15.255293-03:00")} ) 
    relatorios_relatoriomodeloitens_30.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 294, {'id': 294, 'codigo': 'SP 150', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-150', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.984392-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_30.nome = 'Anchieta'
    relatorios_relatoriomodeloitens_30.sentido = 'Norte'
    relatorios_relatoriomodeloitens_30.destino = 'Capital'
    relatorios_relatoriomodeloitens_30.km_inicial = Decimal('9.700')
    relatorios_relatoriomodeloitens_30.km_final = Decimal('65.600')
    relatorios_relatoriomodeloitens_30 = importer.save_or_locate(relatorios_relatoriomodeloitens_30)

    relatorios_relatoriomodeloitens_31 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_31.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_31.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 67, {'id': 67, 'nome': 'Ecopistas', 'slug': 'ecopistas', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária das Rodovias Ayrton Senna e Carvalho Pinto S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecopistas_tth5e3', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Ayrton Senna da Silva', 'numero': 'km 32', 'bairro': None, 'cidade': 'Itaquaquecetuba', 'cep': '08578-010', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 23, 'etapa': None, 'contrato': '006/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2008', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.915608-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:39:56.883206-03:00")} ) 
    relatorios_relatoriomodeloitens_31.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 236, {'id': 236, 'codigo': 'SP 019', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-019', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.826063-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_31.nome = 'Hélio Smidt'
    relatorios_relatoriomodeloitens_31.sentido = 'Sul'
    relatorios_relatoriomodeloitens_31.destino = 'Capital'
    relatorios_relatoriomodeloitens_31.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_31.km_final = Decimal('2.400')
    relatorios_relatoriomodeloitens_31 = importer.save_or_locate(relatorios_relatoriomodeloitens_31)

    relatorios_relatoriomodeloitens_32 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_32.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_32.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 67, {'id': 67, 'nome': 'Ecopistas', 'slug': 'ecopistas', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária das Rodovias Ayrton Senna e Carvalho Pinto S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecopistas_tth5e3', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Ayrton Senna da Silva', 'numero': 'km 32', 'bairro': None, 'cidade': 'Itaquaquecetuba', 'cep': '08578-010', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 23, 'etapa': None, 'contrato': '006/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2008', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.915608-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:39:56.883206-03:00")} ) 
    relatorios_relatoriomodeloitens_32.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 236, {'id': 236, 'codigo': 'SP 019', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-019', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.826063-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_32.nome = 'Hélio Smidt'
    relatorios_relatoriomodeloitens_32.sentido = 'Norte'
    relatorios_relatoriomodeloitens_32.destino = 'Interior'
    relatorios_relatoriomodeloitens_32.km_inicial = Decimal('0.000')
    relatorios_relatoriomodeloitens_32.km_final = Decimal('2.400')
    relatorios_relatoriomodeloitens_32 = importer.save_or_locate(relatorios_relatoriomodeloitens_32)

    relatorios_relatoriomodeloitens_33 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_33.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_33.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 67, {'id': 67, 'nome': 'Ecopistas', 'slug': 'ecopistas', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária das Rodovias Ayrton Senna e Carvalho Pinto S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecopistas_tth5e3', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Ayrton Senna da Silva', 'numero': 'km 32', 'bairro': None, 'cidade': 'Itaquaquecetuba', 'cep': '08578-010', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 23, 'etapa': None, 'contrato': '006/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2008', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.915608-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:39:56.883206-03:00")} ) 
    relatorios_relatoriomodeloitens_33.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 261, {'id': 261, 'codigo': 'SP 070', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-070', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.888389-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_33.nome = 'Ayrton Senna - Carvalho Pinto'
    relatorios_relatoriomodeloitens_33.sentido = 'Oeste'
    relatorios_relatoriomodeloitens_33.destino = 'Capital'
    relatorios_relatoriomodeloitens_33.km_inicial = Decimal('11.190')
    relatorios_relatoriomodeloitens_33.km_final = Decimal('134.580')
    relatorios_relatoriomodeloitens_33 = importer.save_or_locate(relatorios_relatoriomodeloitens_33)

    relatorios_relatoriomodeloitens_34 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_34.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_34.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 67, {'id': 67, 'nome': 'Ecopistas', 'slug': 'ecopistas', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária das Rodovias Ayrton Senna e Carvalho Pinto S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ecopistas_tth5e3', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Rodovia Ayrton Senna da Silva', 'numero': 'km 32', 'bairro': None, 'cidade': 'Itaquaquecetuba', 'cep': '08578-010', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 23, 'etapa': None, 'contrato': '006/ARTESP/2009', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '003/2008', 'grupo': 'Ecorodovias', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.915608-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:39:56.883206-03:00")} ) 
    relatorios_relatoriomodeloitens_34.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 261, {'id': 261, 'codigo': 'SP 070', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-070', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:08.888389-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_34.nome = 'Ayrton Senna - Carvalho Pinto'
    relatorios_relatoriomodeloitens_34.sentido = 'Leste'
    relatorios_relatoriomodeloitens_34.destino = 'Interior'
    relatorios_relatoriomodeloitens_34.km_inicial = Decimal('11.190')
    relatorios_relatoriomodeloitens_34.km_final = Decimal('134.580')
    relatorios_relatoriomodeloitens_34 = importer.save_or_locate(relatorios_relatoriomodeloitens_34)

    relatorios_relatoriomodeloitens_35 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_35.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_35.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 58, {'id': 58, 'nome': 'Autoban', 'slug': 'autoban', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária do Sistema Anhanguera-Bandeirantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ccr_autoban_uqtvnt', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Avenida Maria do Carmo GuimarÒes Pellegrini', 'numero': '200', 'bairro': None, 'cidade': 'Jundiaí', 'cep': '13209-500', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 1, 'etapa': None, 'contrato': 'CR/005/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '007/CIC/97', 'grupo': 'CCR', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.910582-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:42:24.755198-03:00")} ) 
    relatorios_relatoriomodeloitens_35.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 372, {'id': 372, 'codigo': 'SP 330', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-330', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.190479-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_35.nome = 'Anhanguera'
    relatorios_relatoriomodeloitens_35.sentido = 'Sul'
    relatorios_relatoriomodeloitens_35.destino = 'Capital'
    relatorios_relatoriomodeloitens_35.km_inicial = Decimal('11.360')
    relatorios_relatoriomodeloitens_35.km_final = Decimal('158.500')
    relatorios_relatoriomodeloitens_35 = importer.save_or_locate(relatorios_relatoriomodeloitens_35)

    relatorios_relatoriomodeloitens_36 = RelatorioModeloItens()
    relatorios_relatoriomodeloitens_36.modelo = relatorios_relatoriomodelo_1
    relatorios_relatoriomodeloitens_36.concessionaria =  importer.locate_object(Concessionaria, "id", Concessionaria, "id", 58, {'id': 58, 'nome': 'Autoban', 'slug': 'autoban', 'tipo': 'CONCESSIONÁRIA', 'publica': False, 'fiscalizacao_artesp': True, 'nome_fantasia': None, 'razao_social': 'Concessionária do Sistema Anhanguera-Bandeirantes S/A', 'logo': 'cciapp/media/media/images/concessionarias/logos/ccr_autoban_uqtvnt', 'telefone_adm': None, 'telefone_cco': None, 'logradouro': 'Avenida Maria do Carmo GuimarÒes Pellegrini', 'numero': '200', 'bairro': None, 'cidade': 'Jundiaí', 'cep': '13209-500', 'uf': 'SP', 'ativa': True, 'desc': '', 'lote': 1, 'etapa': None, 'contrato': 'CR/005/1998', 'contrato_dt_ass': None, 'inicio': None, 'termino': None, 'edital': '007/CIC/97', 'grupo': 'CCR', 'link': None, 'senha': None, 'web': None, 'criado_em': dateutil.parser.parse("2021-05-20T08:32:07.910582-03:00"), 'atualizado_em': dateutil.parser.parse("2021-05-20T12:42:24.755198-03:00")} ) 
    relatorios_relatoriomodeloitens_36.rodovia =  importer.locate_object(Rodovia, "id", Rodovia, "id", 372, {'id': 372, 'codigo': 'SP 330', 'nome_principal': None, 'nome_secundario': None, 'slug': 'sp-330', 'criado_em': dateutil.parser.parse("2021-05-20T08:32:09.190479-03:00"), 'criado_por_id': None, 'atualizado_em': None, 'atualizado_por_id': None} ) 
    relatorios_relatoriomodeloitens_36.nome = 'Anhanguera'
    relatorios_relatoriomodeloitens_36.sentido = 'Norte'
    relatorios_relatoriomodeloitens_36.destino = 'Interior'
    relatorios_relatoriomodeloitens_36.km_inicial = Decimal('11.360')
    relatorios_relatoriomodeloitens_36.km_final = Decimal('158.500')
    relatorios_relatoriomodeloitens_36 = importer.save_or_locate(relatorios_relatoriomodeloitens_36)

