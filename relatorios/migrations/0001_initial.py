# Generated by Django 3.2 on 2021-08-29 13:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('concessionarias', '0001_initial'),
        ('rodovias', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Relatorio',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(blank=True, max_length=50, null=True, verbose_name='Nome')),
                ('sentido', models.CharField(choices=[('Norte', 'Norte'), ('Sul', 'Sul'), ('Leste', 'Leste'), ('Oeste', 'Oeste'), ('Interno', 'Interno'), ('Externo', 'Externo'), ('Norte-Sul', 'Norte-Sul'), ('Leste-Oeste', 'Leste-Oeste'), ('Noroeste-Sudeste', 'Noroeste-Sudeste'), ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'), ('Sudeste', 'Sudeste'), ('Sudoeste', 'Sudoeste'), ('Nordeste', 'Nordeste'), ('Noroeste', 'Noroeste'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=50, verbose_name='Sentido')),
                ('destino', models.CharField(blank=True, max_length=50, null=True, verbose_name='Destino Principal')),
                ('km_inicial', models.DecimalField(decimal_places=3, max_digits=8, verbose_name='KM Inicial')),
                ('km_final', models.DecimalField(decimal_places=3, max_digits=8, verbose_name='KM Final')),
                ('obs', models.CharField(blank=True, max_length=255, null=True, verbose_name='Observações')),
                ('status', models.TextField(verbose_name='Status')),
                ('cond_climaticas', models.CharField(choices=[('CHUVA', 'CHUVA'), ('CHUVA COM VENTANIA', 'CHUVA COM VENTANIA'), ('CHUVA TORRENCIAL', 'CHUVA TORRENCIAL'), ('ENCOBERTO', 'ENCOBERTO'), ('GAROA', 'GAROA'), ('NUBLADO', 'NUBLADO'), ('SOL', 'SOL'), ('TEMPO BOM', 'TEMPO BOM'), ('TRECHOS COM CHUVA', 'TRECHOS COM CHUVA'), ('VENTO FORTE', 'VENTO FORTE'), ('DESCONHECIDO', 'DESCONHECIDO')], default='TEMPO BOM', max_length=50, verbose_name='Cond. Climáticas')),
                ('cor', models.CharField(choices=[('#D1FAE5', 'Verde'), ('#FEF3C7', 'Amarela'), ('#DC2626', 'Vermelha')], default='Verde', max_length=20, verbose_name='Cor')),
                ('concessionaria', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='concessionarias.concessionaria', verbose_name='Concessionária')),
            ],
            options={
                'verbose_name': 'Item',
                'verbose_name_plural': 'Itens',
                'ordering': ('km_final', 'km_inicial', 'sentido', 'rodovia__codigo', 'concessionaria__nome'),
            },
        ),
        migrations.CreateModel(
            name='RelatorioModelo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=50, verbose_name='Nome')),
                ('periodicidade', models.SmallIntegerField(choices=[(1, 'HORÁRIA'), (2, 'POR TURNO'), (3, 'DIÁRIA'), (4, 'SEMANAL'), (5, 'MENSAL')], default=1, verbose_name='Periodicidade')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
                ('atualizado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='relatorio_modelo_atualizado_por', to=settings.AUTH_USER_MODEL, verbose_name='Atualizado por')),
                ('criado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='relatorio_modelo_criado_por', to=settings.AUTH_USER_MODEL, verbose_name='Criado por')),
            ],
            options={
                'verbose_name': 'Modelo',
                'verbose_name_plural': 'Modelos',
                'ordering': ('nome',),
            },
        ),
        migrations.CreateModel(
            name='RelatorioIndex',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('slug', models.SlugField(unique=True, verbose_name='Slug')),
                ('enviado_em', models.DateTimeField(blank=True, null=True, verbose_name='Enviado em')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
                ('atualizado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='relatorio_atualizado_por', to=settings.AUTH_USER_MODEL, verbose_name='Atualizado por')),
                ('criado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='relatorio_criado_por', to=settings.AUTH_USER_MODEL, verbose_name='Criado por')),
                ('enviado_por', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='enviado_por', to=settings.AUTH_USER_MODEL, verbose_name='Enviado por')),
                ('template', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='relatorios.relatoriomodelo', verbose_name='Template')),
            ],
            options={
                'verbose_name': 'Relatório',
                'verbose_name_plural': 'Relatórios',
                'ordering': ('-pk',),
            },
        ),
        migrations.CreateModel(
            name='RelatorioFoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('foto', models.ImageField(upload_to='relatorios/fotos', verbose_name='Foto')),
                ('legenda', models.CharField(blank=True, max_length=100, null=True, verbose_name='Legenda')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
                ('relatorio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fotos', to='relatorios.relatorio', verbose_name='Relatório')),
            ],
            options={
                'verbose_name': 'Foto',
                'verbose_name_plural': 'Fotos',
            },
        ),
        migrations.AddField(
            model_name='relatorio',
            name='relatorio_index',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='itens', to='relatorios.relatorioindex', verbose_name='Código'),
        ),
        migrations.AddField(
            model_name='relatorio',
            name='rodovia',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rodovias.rodovia', verbose_name='Rodovia'),
        ),
        migrations.CreateModel(
            name='RelatorioModeloItens',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=50, verbose_name='Nome do trecho')),
                ('sentido', models.CharField(choices=[('Norte', 'Norte'), ('Sul', 'Sul'), ('Leste', 'Leste'), ('Oeste', 'Oeste'), ('Interno', 'Interno'), ('Externo', 'Externo'), ('Norte-Sul', 'Norte-Sul'), ('Leste-Oeste', 'Leste-Oeste'), ('Noroeste-Sudeste', 'Noroeste-Sudeste'), ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'), ('Sudeste', 'Sudeste'), ('Sudoeste', 'Sudoeste'), ('Nordeste', 'Nordeste'), ('Noroeste', 'Noroeste'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='Desconhecido', max_length=50, verbose_name='Sentido')),
                ('destino', models.CharField(blank=True, max_length=50, null=True, verbose_name='Destino Principal')),
                ('km_inicial', models.DecimalField(decimal_places=3, max_digits=8, verbose_name='KM Inicial')),
                ('km_final', models.DecimalField(decimal_places=3, max_digits=8, verbose_name='KM Final')),
                ('concessionaria', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='concessionarias.concessionaria', verbose_name='Concessionária')),
                ('modelo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='itens', to='relatorios.relatoriomodelo', verbose_name='Modelo')),
                ('rodovia', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rodovias.rodovia', verbose_name='Rodovia')),
            ],
            options={
                'verbose_name': 'Item Modelo',
                'verbose_name_plural': 'Itens Modelo',
                'ordering': ('-pk',),
                'unique_together': {('modelo', 'concessionaria', 'rodovia', 'sentido', 'km_inicial', 'km_final')},
            },
        ),
        migrations.CreateModel(
            name='RelatorioDestinatarios',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, verbose_name='E-mail')),
                ('nome', models.CharField(max_length=50, verbose_name='Nome')),
                ('modelo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='destinatarios', to='relatorios.relatoriomodelo', verbose_name='Modelo')),
            ],
            options={
                'verbose_name': 'Destinatário',
                'verbose_name_plural': 'Destinatários',
                'unique_together': {('modelo', 'email')},
            },
        ),
    ]
