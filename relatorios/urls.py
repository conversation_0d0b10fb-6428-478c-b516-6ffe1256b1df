
from django.conf import settings
from django.conf.urls.static import static
from django.urls import path
from django.views.generic import TemplateView

from .views import create_relatorio_view, RelatorioModeloSelectView, RelatorioIndexListView, edit_relatorio_view,\
    before_send_relatorio_view, send_relatorio_view, test_email, detail_relatorio_view, add_photo_to_relatorio,\
        generate_pdf, test_pdf, add_to_relatorio_view, SelectRodoviaListView, RelatorioModeloCreateView,\
            RelatorioModeloListView, RelatorioModeloDetailView, RelatorioModeloUpdateView, add_item_to_relatorio_modelo,\
                SelectRodoviaListModeloView, ModeloItemDeleteView, add_destinatario_to_relatorio_modelo,\
                    add_ocs_to_relatorio_view

app_name = 'relatorios'

urlpatterns = [
    path('', RelatorioIndexListView.as_view(), name='list'),
    path('<int:rel_id>/', detail_relatorio_view, name='detail'),
    path('<int:rel_id>/add-photo/<int:item_id>', add_photo_to_relatorio, name='add-foto'),
    path('<int:rel_id>/<int:rod_id>/add-to/', add_to_relatorio_view, name='add-to'),
    path('<int:rel_id>/add-ocs-to/', add_ocs_to_relatorio_view, name='add-ocs-to'),
    path('<int:rel_id>/create/', create_relatorio_view, name='create'),
    path('<int:rel_id>/edit/', edit_relatorio_view, name='edit'),
    path('<int:rel_id>/confirm/', before_send_relatorio_view, name='before-send'),
    path('<str:rel_id>/select',  SelectRodoviaListView.as_view(), name='select-rodovia'),
    path('<int:rel_id>/sent/', send_relatorio_view, name='send'),
    path('<int:rel_id>/email/', test_email, name='email'),
    path('<int:rel_id>/pdf/', test_pdf, name='pdf'),
    path('<int:rel_id>/generate-pdf/', generate_pdf, name='pdf'),
    path('create/', RelatorioModeloCreateView.as_view(), name='create-modelo'),
    path('modelos/', RelatorioModeloListView.as_view(), name='list-modelo'),
    path('modelos/<int:rel_modelo_id>/item/<int:item_id>/delete', ModeloItemDeleteView.as_view(), name='delete-item-modelo'),
    path('modelos/<int:rel_modelo_id>/<int:rod_id>/add-item', add_item_to_relatorio_modelo, name='add-item-modelo'),
    path('modelos/<int:rel_modelo_id>/add-destinatario', add_destinatario_to_relatorio_modelo, name='add-destinatario'),
    path('modelos/<int:rel_modelo_id>/edit', RelatorioModeloUpdateView.as_view(), name='edit-modelo'),
    path('modelos/<int:rel_modelo_id>/select', SelectRodoviaListModeloView.as_view(), name='select-rodovia-modelo'),
    path('modelos/<int:rel_modelo_id>/', RelatorioModeloDetailView.as_view(), name='detail-modelo'),
    path('select/', RelatorioModeloSelectView.as_view(), name='select'),
]
