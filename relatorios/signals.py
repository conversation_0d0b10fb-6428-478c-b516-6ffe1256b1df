from django.db.models.signals import pre_save, post_save
from django.utils import timezone
from django.dispatch import receiver
from .models import Relatorio
from django.utils.text import slugify


# @receiver(post_save, sender=Relatorio)
# def mark_author(sender, instance, created, **kwargs):
#     if created:
#         instance.criado_em = timezone.now()
#         instance.criado_por = instance.user
#     else:
#         instance.atualizado_em = timezone.now()
#         instance.atualizado_por = instance.user
#     print(instance)
#     instance.save()


# def atualizado_em(sender, instance, *args, **kwargs):
#     now = timezone.now()
#     exists = Rodovia.objects.filter(pk=instance.pk)
#     if exists:
#         instance.atualizado_em = now


# post_save.connect(gen_slug_acidente, sender=Acidente)