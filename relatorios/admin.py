from django.forms.models import BaseInlineFormSet
from django.contrib import admin
from django.utils import timezone
from .models import Relatorio, RelatorioModelo, RelatorioFoto, RelatorioIndex, RelatorioDestinatarios,\
    RelatorioModeloItens

from rodovias.models import Rodovia

class RelatorioBaseAdmin(admin.ModelAdmin):

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False

    def has_view_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False

    def save_model(self, request, obj, form, change):
        if not obj.pk:
            obj.criado_em = timezone.now()
            obj.criado_por = request.user
        else:
            obj.atualizado_em = timezone.now()
            obj.atualizado_por = request.user
        print(obj)
        obj.save()


class RelatorioItemFormSet(BaseInlineFormSet):
    model = Relatorio

    def __init__(self, *args, **kwargs):
        super(RelatorioItemFormSet, self).__init__(*args, **kwargs)


class RelatorioItemAdminInline(admin.TabularInline):
    model = Relatorio
    formset = RelatorioItemFormSet


class RelatorioFotoFormSet(BaseInlineFormSet):
    model = RelatorioFoto

    def __init__(self, *args, **kwargs):
        super(RelatorioFotoFormSet, self).__init__(*args, **kwargs)


class RelatorioFotoAdminInline(admin.TabularInline):
    model = RelatorioFoto
    formset = RelatorioFotoFormSet


class RelatorioIndexAdmin(RelatorioBaseAdmin):
    inlines = [RelatorioItemAdminInline]
    readonly_fields = [
        'criado_em', 'criado_por', 'atualizado_em', 'atualizado_por', 'enviado_por',
        'enviado_em'
    ]


# class RelatorioModeloItemFormSet(BaseInlineFormSet):
#     model = RelatorioModeloItens

#     def __init__(self, *args, **kwargs):
#         super(RelatorioModeloItemFormSet, self).__init__(*args, **kwargs)


# class RelatorioModeloItemAdminInline(admin.TabularInline):
#     model = RelatorioModeloItens
#     formset = RelatorioModeloItemFormSet


class RelatorioModeloItensAdmin(RelatorioBaseAdmin):
    pass


class RelatorioModeloAdmin(RelatorioBaseAdmin):
    readonly_fields = [ 'criado_em', 'criado_por','atualizado_em',  'atualizado_por', ]
    # inlines = [RelatorioModeloItemAdminInline]


class RelatorioAdmin(RelatorioBaseAdmin):
    pass
    # readonly_fields = [ 'criado_em', 'criado_por','atualizado_em',  'atualizado_por', ]
    # readonly_fields = [
    #     'template', 'relatorio_index', 'rodovia'
    # ]

class RelatorioFotoAdmin(RelatorioBaseAdmin):
    pass
    # readonly_fields = [ 'criado_em', 'criado_por','atualizado_em',  'atualizado_por', ]
    # readonly_fields = [
    #     'relatorio'
    # ]

class RelatorioDestinatariosAdmin(RelatorioBaseAdmin):
    pass

admin.site.register(RelatorioFoto, RelatorioFotoAdmin)
admin.site.register(Relatorio, RelatorioAdmin)
admin.site.register(RelatorioModelo, RelatorioModeloAdmin)
admin.site.register(RelatorioIndex, RelatorioIndexAdmin)
admin.site.register(RelatorioDestinatarios, RelatorioDestinatariosAdmin)
admin.site.register(RelatorioModeloItens, RelatorioModeloItensAdmin)
