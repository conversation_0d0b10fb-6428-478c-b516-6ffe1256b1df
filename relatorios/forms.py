#models.py
from django import forms
from django.db.models import fields
from django.utils import timezone
from django.utils.text import slugify

from .models import Relatorio, RelatorioFoto, RelatorioModelo, RelatorioModeloItens


from rodovias.models import <PERSON><PERSON><PERSON>, Trecho
from concessionarias.models import Concessionaria

class RelatorioModeloForm(forms.ModelForm):
    class Meta:
        model = RelatorioModelo
        fields = ('nome', 'periodicidade',)


class RelatorioForm(forms.ModelForm):
    class Meta:
        model = Relatorio
        fields = (
            'concessionaria', 'rodovia', 'nome', 'sentido', 'destino', 'km_inicial', 'km_final', 'status',
            'cond_climaticas', 'cor', 'obs',
        )

    def __init__(self, rel_modelo_itens, *args, **kwargs):
        super(RelatorioForm, self).__init__(*args, **kwargs)
        concessionarias = []
        rodovias = []
        nomes = []
        sentidos = []
        destinos = []
        km_inicial = []
        km_final = []
        for item in rel_modelo_itens:
            concessionarias.append(item.concessionaria.pk)
            rodovias.append(item.rodovia.pk)
            nomes.append((item.nome, item.nome),)
            sentidos.append((item.sentido, item.sentido),)
            destinos.append((item.destino, item.destino),)
            km_inicial.append((item.km_inicial, item.km_inicial),)
            km_final.append((item.km_final, item.km_final),)
        qs_rodovias = Rodovia.objects.filter(pk__in=rodovias)
        qs_concessionarias = Concessionaria.objects.filter(pk__in=concessionarias)
        self.fields['concessionaria'] = forms.ModelChoiceField(
            label='Concessionária', queryset=qs_concessionarias, to_field_name="nome",
            widget=forms.TextInput(attrs={'readonly': True})
        )
        self.fields['rodovia'] = forms.ModelChoiceField(
            label='Rodovia', queryset=qs_rodovias, to_field_name="codigo",
            widget=forms.TextInput(attrs={'readonly': True,})
        )
        self.fields['nome'] = forms.ChoiceField(
            choices=nomes, required=True,
            widget=forms.TextInput(attrs={'readonly': True,})
        )
        self.fields['sentido'] = forms.ChoiceField(
            choices=sentidos, required=True,
            widget=forms.TextInput(attrs={'readonly': True,})
        )
        self.fields['destino'] = forms.ChoiceField(
            choices=destinos, required=False,
            widget=forms.TextInput(attrs={'readonly': True,})
        )
        self.fields['km_inicial'] = forms.ChoiceField(
            choices=km_inicial, required=True,
            widget=forms.TextInput(attrs={'readonly': True,})
        )
        self.fields['km_final'] = forms.ChoiceField(
            choices=km_final, required=True,
            widget=forms.TextInput(attrs={'readonly': True,})
        )
        self.fields['status'] = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}))


class RelatorioModeloItemForm(forms.ModelForm):
    class Meta:
        model = RelatorioModeloItens
        fields = (
            'concessionaria', 'rodovia', 'nome', 'sentido', 'destino', 'km_inicial', 'km_final', 'modelo'
        )
        widgets = {
            'modelo': forms.HiddenInput()
        }

    def __init__(self, rel_modelo_id, rod_id, *args, **kwargs):
        super(RelatorioModeloItemForm, self).__init__(*args, **kwargs)
        rodovia = Rodovia.objects.filter(pk=rod_id)
        nome_rodovia = rodovia.first().nome_principal
        trechos = rodovia.first().trechos.all().prefetch_related('concessionaria').values_list('id')
        concessionarias = Concessionaria.objects.filter(trechos__pk__in=trechos).distinct()
        self.fields['modelo'].initial = rel_modelo_id
        self.fields['concessionaria'] = forms.ModelChoiceField(
            label='Concessionária', queryset=concessionarias
        )
        self.fields['rodovia'] = forms.ModelChoiceField(
            label='Rodovia', queryset=rodovia
        )
        self.fields['nome'].initial = nome_rodovia if nome_rodovia else ''
        self.fields['sentido'].choices = [
            ('Norte', 'Norte'),
            ('Sul', 'Sul'),
            ('Leste', 'Leste'),
            ('Oeste', 'Oeste'),
            ('Interno', 'Interno'),
            ('Externo', 'Externo'),
            ('Norte-Sul', 'Norte-Sul'),
            ('Leste-Oeste', 'Leste-Oeste'),
            ('Noroeste-Sudeste', 'Noroeste-Sudeste'),
            ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'),
            ('Sudeste', 'Sudeste'),
            ('Sudoeste', 'Sudoeste'),
            ('Nordeste', 'Nordeste'),
            ('Noroeste', 'Noroeste'),
            ('Outro', 'Outro'),
            ('Desconhecido', 'Desconhecido'),
        ]

    def clean(self):
        cleaned_data = super().clean()
        rodovia = cleaned_data.get('rodovia')
        concessionaria = cleaned_data.get('concessionaria')
        km_inicial = cleaned_data.get('km_inicial')
        km_final = cleaned_data.get('km_final')
        if km_final < km_inicial:
            km_inicial, km_final = km_final, km_inicial
        if not rodovia:
            raise forms.ValidationError(f"Rodovia é obrigatório.")
        trechos = rodovia.trechos\
            .filter(km_final__gte=km_inicial).filter(km_inicial__lte=km_final)\
                .filter(concessionaria=concessionaria)
        if not trechos:
            erro_trecho_min_km = rodovia.trechos.filter(concessionaria=concessionaria).\
                order_by('km_inicial').first().km_inicial
            erro_trecho_max_km = rodovia.trechos.filter(concessionaria=concessionaria).\
                order_by('km_inicial').last().km_final
            raise forms.ValidationError(f"Não há trechos cadastrados para {rodovia.codigo}, na {concessionaria.nome}"\
                f" e no intervalo de kms fonecido. Trecho da {concessionaria.nome} inicia no km {erro_trecho_min_km}"\
                    f" e termina no km {erro_trecho_max_km}")
        trecho_primeiro_km = trechos.order_by('km_inicial').first().km_inicial
        trecho_ultimo_km = trechos.order_by('km_final').last().km_final
        concessionaria_primeiro_km = Trecho.objects.filter(concessionaria=concessionaria).filter(rodovia=rodovia)\
            .order_by('km_inicial').first().km_inicial
        concessionaria_ultimo_km = Trecho.objects.filter(concessionaria=concessionaria).filter(rodovia=rodovia)\
            .order_by('km_inicial').last().km_final
        if km_inicial < trecho_primeiro_km or km_inicial > trecho_ultimo_km:
            raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
                rodovia {rodovia.codigo} para a concessionária {concessionaria.nome} \
                    (km {concessionaria_primeiro_km} ao km {concessionaria_ultimo_km})")
        if km_final < trecho_primeiro_km or km_final > trecho_ultimo_km:
            raise forms.ValidationError(f"Verifique o km final, pois não está dentro no intervalo de kms da\
                rodovia {rodovia.codigo} para a concessionária {concessionaria.nome} \
                    (km {concessionaria_primeiro_km} ao km {concessionaria_ultimo_km})")
        return cleaned_data



class RelatorioUnitarioForm(forms.ModelForm):
    class Meta:
        model = Relatorio
        fields = (
            'concessionaria', 'rodovia', 'nome', 'sentido', 'destino', 'km_inicial', 'km_final', 'status',
            'cond_climaticas', 'cor', 'obs','relatorio_index'
        )
        widgets = {
            'relatorio_index': forms.HiddenInput()
        }

    def __init__(self, rel_id, rod_id, *args, **kwargs):
        super(RelatorioUnitarioForm, self).__init__(*args, **kwargs)
        rodovia = Rodovia.objects.filter(pk=rod_id)
        nome_rodovia = rodovia.first().nome_principal
        trechos = rodovia.first().trechos.all().prefetch_related('concessionaria').values_list('id')
        concessionarias = Concessionaria.objects.filter(trechos__pk__in=trechos).distinct()
        self.fields['relatorio_index'].initial = rel_id
        self.fields['concessionaria'] = forms.ModelChoiceField(
            label='Concessionária', queryset=concessionarias
        )
        self.fields['rodovia'] = forms.ModelChoiceField(
            label='Rodovia', queryset=rodovia
        )
        self.fields['nome'].initial = nome_rodovia if nome_rodovia else ''
        self.fields['destino'].initial = nome_rodovia if nome_rodovia else ''
        self.fields['sentido'].choices = [
            ('Norte', 'Norte'),
            ('Sul', 'Sul'),
            ('Leste', 'Leste'),
            ('Oeste', 'Oeste'),
            ('Interno', 'Interno'),
            ('Externo', 'Externo'),
            ('Norte-Sul', 'Norte-Sul'),
            ('Leste-Oeste', 'Leste-Oeste'),
            ('Noroeste-Sudeste', 'Noroeste-Sudeste'),
            ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'),
            ('Sudeste', 'Sudeste'),
            ('Sudoeste', 'Sudoeste'),
            ('Nordeste', 'Nordeste'),
            ('Noroeste', 'Noroeste'),
            ('Outro', 'Outro'),
            ('Desconhecido', 'Desconhecido'),
        ]

    def clean(self):
        cleaned_data = super().clean()
        rodovia = cleaned_data.get('rodovia')
        concessionaria = cleaned_data.get('concessionaria')
        km_inicial = cleaned_data.get('km_inicial')
        km_final = cleaned_data.get('km_final')
        if km_final < km_inicial:
            km_inicial, km_final = km_final, km_inicial
        if not rodovia:
            raise forms.ValidationError(f"Rodovia é obrigatório.")
        trechos = rodovia.trechos\
            .filter(km_final__gte=km_inicial).filter(km_inicial__lte=km_final)\
                .filter(concessionaria=concessionaria)
        if not trechos:
            erro_trecho_min_km = rodovia.trechos.filter(concessionaria=concessionaria).\
                order_by('km_inicial').first().km_inicial
            erro_trecho_max_km = rodovia.trechos.filter(concessionaria=concessionaria).\
                order_by('km_inicial').last().km_final
            raise forms.ValidationError(f"Não há trechos cadastrados para {rodovia.codigo}, na {concessionaria.nome}"\
                f" e no intervalo de kms fonecido. Trecho da {concessionaria.nome} inicia no km {erro_trecho_min_km}"\
                    f" e termina no km {erro_trecho_max_km}")
        trecho_primeiro_km = trechos.order_by('km_inicial').first().km_inicial
        trecho_ultimo_km = trechos.order_by('km_final').last().km_final
        concessionaria_primeiro_km = Trecho.objects.filter(concessionaria=concessionaria).filter(rodovia=rodovia)\
            .order_by('km_inicial').first().km_inicial
        concessionaria_ultimo_km = Trecho.objects.filter(concessionaria=concessionaria).filter(rodovia=rodovia)\
            .order_by('km_inicial').last().km_final
        if km_inicial < trecho_primeiro_km or km_inicial > trecho_ultimo_km:
            raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
                rodovia {rodovia.codigo} para a concessionária {concessionaria.nome} \
                    (km {concessionaria_primeiro_km} ao km {concessionaria_ultimo_km})")
        if km_final < trecho_primeiro_km or km_final > trecho_ultimo_km:
            raise forms.ValidationError(f"Verifique o km final, pois não está dentro no intervalo de kms da\
                rodovia {rodovia.codigo} para a concessionária {concessionaria.nome} \
                    (km {concessionaria_primeiro_km} ao km {concessionaria_ultimo_km})")
        return cleaned_data


class RelatorioEditForm(forms.ModelForm):
    class Meta:
        model = Relatorio
        fields = ('status', 'cond_climaticas', 'cor', 'obs',)


    # def __init__(self, concessionaria, *args, **kwargs):
    #     super(RelatorioEditForm, self).__init__(*args, **kwargs)
    #     self.fields['concessionaria'] = forms.ModelForm(concessionaria, widget=forms.HiddenInput())


    # def __init__(self, rel_modelo_itens, *args, **kwargs):
    #     super(RelatorioEditForm, self).__init__(*args, **kwargs)
    #     concessionarias = []
    #     rodovias = []
    #     sentidos = []
    #     km_inicial = []
    #     km_final = []
    #     for item in rel_modelo_itens:
    #         concessionarias.append(item.concessionaria.pk)
    #         rodovias.append(item.rodovia.pk)
    #         sentidos.append((item.sentido, item.sentido),)
    #         km_inicial.append((item.km_inicial, item.km_inicial),)
    #         km_final.append((item.km_final, item.km_final),)
    #     qs_rodovias = Rodovia.objects.filter(pk__in=rodovias)
    #     qs_concessionarias = Concessionaria.objects.filter(pk__in=concessionarias)
    #     self.fields['concessionaria'] = forms.ModelChoiceField(
    #         label='Concessionária', queryset=qs_concessionarias,
    #         widget=forms.TextInput(attrs={'readonly': True}),
    #         required=True
    #     )
    #     self.fields['rodovia'] = forms.ModelChoiceField(
    #         label='Rodovia', queryset=qs_rodovias,
    #         widget=forms.TextInput(attrs={'readonly': True}),
    #         required=True
    #     )
    #     self.fields['sentido'] = forms.ChoiceField(
    #         choices=sentidos, required=True,
    #         widget=forms.TextInput(attrs={'readonly': True})
    #     )
    #     self.fields['km_inicial'] = forms.ChoiceField(
    #         choices=km_inicial, required=True,
    #         widget=forms.TextInput(attrs={'readonly': True})
    #     )
    #     self.fields['km_final'] = forms.ChoiceField(
    #         choices=km_final, required=True,
    #         widget=forms.TextInput(attrs={'readonly': True})
    #     )


class RelatorioFotoForm(forms.ModelForm):
    class Meta:
        model = RelatorioFoto
        fields = ('foto', 'legenda',)