from django.db import models
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db.models import Q

from django.db import models

class RelatorioDestinatarios(models.Model):
    modelo = models.ForeignKey("relatorios.RelatorioModelo", verbose_name=_("Modelo"), on_delete=models.CASCADE, related_name="destinatarios")
    email = models.EmailField(_("E-mail"), max_length=254)
    nome = models.CharField(_("Nome"), max_length=50)

    class Meta:
        verbose_name = 'Destinatário'
        verbose_name_plural = 'Destinatários'
        unique_together = ('modelo', 'email')

    def __str__(self):
        return f'{self.nome} <{self.email}>'


class RelatorioModeloItens(models.Model):
    SENTIDO_CHOICES = [
        ('Norte', 'Norte'),
        ('Sul', 'Sul'),
        ('Leste', 'Leste'),
        ('Oeste', 'Oeste'),
        ('Interno', 'Interno'),
        ('Externo', 'Externo'),
        ('Norte-Sul', 'Norte-Sul'),
        ('Leste-Oeste', 'Leste-Oeste'),
        ('Noroeste-Sudeste', 'Noroeste-Sudeste'),
        ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'),
        ('Sudeste', 'Sudeste'),
        ('Sudoeste', 'Sudoeste'),
        ('Nordeste', 'Nordeste'),
        ('Noroeste', 'Noroeste'),
        ('Outro', 'Outro'),
        ('Desconhecido', 'Desconhecido'),
    ]
    modelo = models.ForeignKey("relatorios.RelatorioModelo", verbose_name=_("Modelo"), on_delete=models.CASCADE, related_name="itens")
    concessionaria = models.ForeignKey("concessionarias.Concessionaria", verbose_name=_("Concessionária"), on_delete=models.CASCADE)
    rodovia = models.ForeignKey("rodovias.Rodovia", verbose_name=_("Rodovia"), on_delete=models.CASCADE)
    nome = models.CharField(_("Nome do trecho"), max_length=50)
    sentido = models.CharField(_("Sentido"), max_length=50, choices=SENTIDO_CHOICES, default='Desconhecido')
    destino = models.CharField(_("Destino Principal"), max_length=50, null=True, blank=True)
    km_inicial = models.DecimalField(_("KM Inicial"), max_digits=8, decimal_places=3)
    km_final = models.DecimalField(_("KM Final"), max_digits=8, decimal_places=3)

    class Meta:
        ordering = ('-pk',)
        verbose_name = 'Item Modelo'
        verbose_name_plural  = 'Itens Modelo'
        unique_together = ('modelo', 'concessionaria', 'rodovia', 'sentido', 'km_inicial', 'km_final')

    def __str__(self):
        return f'{self.concessionaria.nome} | {self.rodovia.codigo} | {self.nome} | {self.sentido} | KMi{self.km_inicial} | KMf:{self.km_final}'

    def __repr__(self):
        return f'<RelatorioModeloItens {self.concessionaria.nome} | {self.rodovia.codigo} | {self.sentido} | KMi{self.km_inicial} | KMf:{self.km_final}>'


class RelatorioModelo(models.Model):
    PERIODICIDADE = [
        (1, 'HORÁRIA'),
        (2, 'POR TURNO'),
        (3, 'DIÁRIA'),
        (4, 'SEMANAL'),
        (5, 'MENSAL'),
    ]
    nome = models.CharField(_("Nome"), max_length=50)
    # destinatarios = models.ManyToManyField(RelatorioDestinatarios, verbose_name=_("Destinatários"), blank=True)
    # itens = models.ManyToManyField(RelatorioModeloItens, verbose_name=_("Itens"), related_name='itens')
    periodicidade = models.SmallIntegerField(_("Periodicidade"), choices=PERIODICIDADE, default=1)
    criado_em = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='relatorio_modelo_criado_por'
    )
    atualizado_em = models.DateTimeField(auto_now=True, null=True, blank=True, verbose_name='Atualizado em')
    atualizado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Atualizado por', related_name='relatorio_modelo_atualizado_por'
    )

    class Meta:
        ordering = ('nome',)
        verbose_name = 'Modelo'
        verbose_name_plural  = 'Modelos'

    def __str__(self):
        return f'{self.nome}'

    def lista_trechos(self):
        trechos = []
        for item in self.itens.all():
            trecho = {}
            trecho['concessionaria'] = item.concessionaria
            trecho['rodovia'] = item.rodovia
            trecho['nome'] = item.nome
            trecho['sentido'] = item.sentido
            trecho['km_inicial'] = item.km_inicial
            trecho['km_final'] = item.km_final
            trechos.append(trecho)
        return trechos

    def lista_destinatarios(self):
        destinatarios = [destinatario.email for destinatario in self.destinatarios.all()]
        return (', ').join(destinatarios)


class Relatorio(models.Model):
    CLIMA_CHOICES = [
        ('CHUVA','CHUVA'),
        ('CHUVA COM VENTANIA','CHUVA COM VENTANIA'),
        ('CHUVA TORRENCIAL','CHUVA TORRENCIAL'),
        ('ENCOBERTO','ENCOBERTO'),
        ('GAROA','GAROA'),
        ('NUBLADO','NUBLADO'),
        ('SOL','SOL'),
        ('TEMPO BOM','TEMPO BOM'),
        ('TRECHOS COM CHUVA','TRECHOS COM CHUVA'),
        ('VENTO FORTE','VENTO FORTE'),
        ('DESCONHECIDO','DESCONHECIDO'),
    ]
    CORES_CHOICES = [
        ('#D1FAE5', 'Verde'),
        ('#FEF3C7', 'Amarela'),
        ('#DC2626', 'Vermelha'),
    ]
    SENTIDO_CHOICES = [
        ('Norte', 'Norte'),
        ('Sul', 'Sul'),
        ('Leste', 'Leste'),
        ('Oeste', 'Oeste'),
        ('Interno', 'Interno'),
        ('Externo', 'Externo'),
        ('Norte-Sul', 'Norte-Sul'),
        ('Leste-Oeste', 'Leste-Oeste'),
        ('Noroeste-Sudeste', 'Noroeste-Sudeste'),
        ('Nordeste-Sudoeste', 'Nordeste-Sudoeste'),
        ('Sudeste', 'Sudeste'),
        ('Sudoeste', 'Sudoeste'),
        ('Nordeste', 'Nordeste'),
        ('Noroeste', 'Noroeste'),
        ('Outro', 'Outro'),
        ('Desconhecido', 'Desconhecido'),
    ]
    relatorio_index = models.ForeignKey("relatorios.RelatorioIndex", verbose_name=_("Código"), on_delete=models.CASCADE, related_name='itens')
    concessionaria = models.ForeignKey("concessionarias.Concessionaria", verbose_name=_("Concessionária"), on_delete=models.CASCADE)
    rodovia = models.ForeignKey("rodovias.Rodovia", verbose_name=_("Rodovia"), on_delete=models.CASCADE)
    nome = models.CharField(_("Nome"), max_length=50, null=True, blank=True)
    sentido = models.CharField(_("Sentido"), max_length=50, choices=SENTIDO_CHOICES, default='Desconhecido')
    destino = models.CharField(_("Destino Principal"), max_length=50, null=True, blank=True)
    km_inicial = models.DecimalField(_("KM Inicial"), max_digits=8, decimal_places=3)
    km_final = models.DecimalField(_("KM Final"), max_digits=8, decimal_places=3)
    obs = models.CharField(_("Observações"), max_length=255, null=True, blank=True)
    status = models.TextField(_("Status"))
    cond_climaticas = models.CharField(_("Cond. Climáticas"), choices=CLIMA_CHOICES, default='TEMPO BOM', max_length=50)
    cor = models.CharField(_("Cor"), max_length=20, choices=CORES_CHOICES, default='Verde', null=False, blank=False)

    class Meta:
        verbose_name = 'Item'
        verbose_name_plural = 'Itens'
        ordering = ('km_final', 'km_inicial', 'sentido', 'rodovia__codigo', 'concessionaria__nome')

    def __str__(self):
        return f'{self.relatorio_index.pk} | {self.relatorio_index.slug} <{self.pk}>'


class RelatorioIndex(models.Model):
    slug = models.SlugField(_("Slug"), unique=True, max_length=50)
    template = models.ForeignKey(RelatorioModelo, verbose_name=_("Template"), on_delete=models.SET_NULL, null=True)
    enviado_em = models.DateTimeField(_("Enviado em"), auto_now=False, auto_now_add=False, blank=True, null=True)
    enviado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, blank=True, null=True, on_delete=models.SET_NULL,
        verbose_name='Enviado por', related_name='enviado_por'
    )
    criado_em = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='relatorio_criado_por'
    )
    atualizado_em = models.DateTimeField(auto_now=True, null=True, blank=True, verbose_name='Atualizado em')
    atualizado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Atualizado por', related_name='relatorio_atualizado_por'
    )

    class Meta:
        ordering = ('-pk',)
        verbose_name = 'Relatório'
        verbose_name_plural = 'Relatórios'

    def __str__(self):
        return f'{self.slug}'


class RelatorioFoto(models.Model):
    relatorio = models.ForeignKey(Relatorio, verbose_name=_("Relatório"), on_delete=models.CASCADE, related_name='fotos')
    foto = models.ImageField(_("Foto"), upload_to='relatorios/fotos', height_field=None, width_field=None, max_length=100)
    legenda = models.CharField(_("Legenda"), max_length=100, null=True, blank=True)
    criado_em = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    atualizado_em = models.DateTimeField(auto_now=True, null=True, blank=True, verbose_name='Atualizado em')

    class Meta:
        verbose_name = 'Foto'
        verbose_name_plural  = 'Fotos'

    def __str__(self):
        return f'Rel. id#{self.relatorio.pk} <{self.pk}>'
