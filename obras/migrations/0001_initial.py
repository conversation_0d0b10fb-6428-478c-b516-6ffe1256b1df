# Generated by Django 3.2 on 2023-08-02 22:49

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import obras.models
import obras.validators


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('municipios', '0001_initial'),
        ('rodovias', '0008_operacao_sistema'),
        ('concessionarias', '0003_concessionaria_link_mapa'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Obra',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bloqueio', models.CharField(blank=True, default='Não', max_length=255, null=True, verbose_name='Bloqueio')),
                ('tipo_obra', models.Char<PERSON>ield(max_length=255, verbose_name='Tipo de obra')),
                ('complemento', models.CharField(blank=True, max_length=255, null=True, verbose_name='Complemento')),
                ('slug', models.SlugField(blank=True, null=True, verbose_name='Código')),
                ('cod_conc', models.CharField(blank=True, max_length=50, null=True, verbose_name='Código Concessionária')),
                ('km_inicial', models.DecimalField(decimal_places=3, max_digits=8, verbose_name='KM inicial')),
                ('km_final', models.DecimalField(blank=True, decimal_places=3, max_digits=8, verbose_name='KM final')),
                ('nro_faixas_local', models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('7', '7'), ('8', '8'), ('9', '9'), ('10', '10'), ('11', '11'), ('12', '12'), ('13', '13'), ('14', '14'), ('15', '15')], max_length=255, null=True, verbose_name='Nro fxs local')),
                ('tem_ac', models.BooleanField(default=True, help_text='Sim, há acostamento no local', verbose_name='Tem AC.')),
                ('dt_hr_conhecimento', models.DateTimeField(verbose_name='Data conhecimento')),
                ('dt_hr_inicio', models.DateTimeField(blank=True, null=True, verbose_name='Data início')),
                ('dt_hr_termino_previsto', models.DateTimeField(blank=True, null=True, verbose_name='Data término previsto')),
                ('dt_hr_termino', models.DateTimeField(blank=True, null=True, verbose_name='Data término')),
                ('publicada', models.BooleanField(default=True, verbose_name='Publicar')),
                ('aprovada', models.BooleanField(default=False, verbose_name='Aprovada')),
                ('origem_comunicacao_cci', models.CharField(blank=True, choices=[('Concessionária', 'Concessionária'), ('Imprensa', 'Imprensa'), ('ARTESP', 'ARTESP'), ('Outro', 'Outro'), ('Desconhecido', 'Desconhecido')], default='CONCESSIONÁRIA', max_length=255, null=True, verbose_name='Origem da inf.')),
                ('pista', models.CharField(blank=True, choices=[('DUPLA', 'DUPLA'), ('SIMPLES', 'SIMPLES'), ('PLANEJADA', 'PLANEJADA'), ('DISPOSITIVO', 'DISPOSITIVO'), ('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO')], default='SEM INFORMAÇÃO', max_length=50, null=True, verbose_name='Pista')),
                ('sentido', models.CharField(choices=[('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'), ('NORTE', 'NORTE'), ('SUL', 'SUL'), ('LESTE', 'LESTE'), ('OESTE', 'OESTE'), ('NORTE/SUL', 'NORTE/SUL'), ('LESTE/OESTE', 'LESTE/OESTE'), ('EXTERNO', 'EXTERNO'), ('INTERNO', 'INTERNO'), ('NÃO SE APLICA', 'NÃO SE APLICA')], default='SEM INFORMAÇÃO', max_length=20, verbose_name='Sentido')),
                ('lat_inicial', models.DecimalField(blank=True, decimal_places=7, max_digits=12, null=True, verbose_name='Latitude inicial')),
                ('lng_inicial', models.DecimalField(blank=True, decimal_places=7, max_digits=12, null=True, verbose_name='Longitude inicial')),
                ('lat_final', models.DecimalField(blank=True, decimal_places=7, max_digits=12, null=True, verbose_name='Latitude final')),
                ('lng_final', models.DecimalField(blank=True, decimal_places=7, max_digits=12, null=True, verbose_name='Longitude final')),
                ('sinalizacao', models.CharField(blank=True, max_length=50, null=True, verbose_name='Sinalização')),
                ('comunicacao_usuario', models.CharField(blank=True, max_length=50, null=True, verbose_name='Comunicação com usuário')),
                ('recursos_internos', models.CharField(blank=True, max_length=255, null=True, verbose_name='Recursos internos')),
                ('recursos_externos', models.CharField(blank=True, max_length=255, null=True, verbose_name='Recursos externos')),
                ('acoes_operacionais', models.CharField(blank=True, max_length=255, null=True, verbose_name='Ações operacionais')),
                ('observacoes_cci', models.TextField(blank=True, null=True, verbose_name='Observações CCI')),
                ('atualizacao_min', models.IntegerField(default=15, help_text='Tempo em dias. Valor máximo: 15 dias', validators=[django.core.validators.MaxValueValidator(15)], verbose_name='Atualização - Periodicidade (dias)')),
                ('nro_mits', models.PositiveIntegerField(blank=True, null=True, verbose_name='Nro MITS')),
                ('arquivo', models.FileField(blank=True, null=True, upload_to=obras.models.get_file_path, validators=[obras.validators.validate_file_extension, obras.validators.validate_file_size], verbose_name='Arquivo complementar PDF (máx 1 arq. < 10mb)')),
                ('arquivo_desc', models.CharField(blank=True, max_length=50, null=True, verbose_name='Descrição do arquivo')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
                ('atualizado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_atualizador', to=settings.AUTH_USER_MODEL, verbose_name='Atualizado por')),
                ('concessionarias', models.ManyToManyField(related_name='obra_concessionarias_set', to='concessionarias.Concessionaria', verbose_name='Concessionarias')),
                ('criado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_criador', to=settings.AUTH_USER_MODEL, verbose_name='Criado por')),
                ('municipios', models.ManyToManyField(related_name='obra_municipios_set', to='municipios.Municipio', verbose_name='Municipios')),
                ('rodovia', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_rodovia_set', to='rodovias.rodovia', verbose_name='Rodovia')),
            ],
            options={
                'verbose_name': 'Intervenção Viária',
                'verbose_name_plural': 'Intervencões Viárias',
            },
        ),
        migrations.CreateModel(
            name='Interdicao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sentido', models.CharField(choices=[('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'), ('NORTE', 'NORTE'), ('SUL', 'SUL'), ('LESTE', 'LESTE'), ('OESTE', 'OESTE'), ('NORTE/SUL', 'NORTE/SUL'), ('LESTE/OESTE', 'LESTE/OESTE'), ('EXTERNO', 'EXTERNO'), ('INTERNO', 'INTERNO'), ('NÃO SE APLICA', 'NÃO SE APLICA')], max_length=20, verbose_name='Sentido')),
                ('faixa', models.CharField(choices=[('Acostamento', 'Acostamento'), ('Alça parcial', 'Alça parcial'), ('Alça total', 'Alça total'), ('Faixa 1', 'Faixa 1'), ('Faixa 2', 'Faixa 2'), ('Faixa 3', 'Faixa 3'), ('Faixa 4', 'Faixa 4'), ('Faixa 5', 'Faixa 5'), ('Faixa 6', 'Faixa 6'), ('Faixa adicional', 'Faixa adicional'), ('Faixa zebrada', 'Faixa zebrada'), ('Faixa de aceleração', 'Faixa de aceleração'), ('Faixa de desaceleração', 'Faixa de desaceleração'), ('Praça de pedágio', 'Praça de pedágio'), ('Todas as faixas', 'Todas as faixas'), ('Todas as faixas (trecho sem AC)', 'Todas as faixas (trecho sem AC)'), ('Todas as faixas e acostamento', 'Todas as faixas e acostamento')], max_length=40, verbose_name='Faixa')),
                ('dt_hora_inicio', models.DateTimeField(blank=True, null=True, verbose_name='Data Hora Início')),
                ('dt_hora_termino', models.DateTimeField(blank=True, null=True, verbose_name='Data Hora Término')),
                ('obs', models.CharField(blank=True, max_length=255, null=True, verbose_name='Obs.')),
                ('obra', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='obra_interdicao_set', to='obras.obra', verbose_name='Interdição/Bloqueio')),
                ('rodovia', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_rodovia', to='rodovias.rodovia', verbose_name='Rodovia')),
            ],
            options={
                'verbose_name': 'Interdição',
                'verbose_name_plural': 'Interdições',
                'ordering': ['-dt_hora_termino', 'dt_hora_inicio', 'faixa'],
            },
        ),
        migrations.CreateModel(
            name='Foto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('foto', models.ImageField(upload_to=obras.models.get_slug, validators=[obras.models.validate_image], verbose_name='Foto')),
                ('foto_hr', models.ImageField(upload_to=obras.models.get_slug, validators=[obras.models.validate_image], verbose_name='Foto original')),
                ('publica', models.BooleanField(default=True, verbose_name='Pública')),
                ('legenda', models.CharField(blank=True, max_length=100, null=True, verbose_name='Legenda')),
                ('adicionada_em', models.DateTimeField(auto_now_add=True, verbose_name='Adicionada em')),
                ('adicionada_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_foto_adic_por', to=settings.AUTH_USER_MODEL, verbose_name='Adicionada por')),
                ('obra', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='obra_fotos_set', to='obras.obra', verbose_name='Foto')),
            ],
            options={
                'verbose_name': 'Foto',
                'verbose_name_plural': 'Fotos',
                'ordering': ['-pk'],
            },
        ),
        migrations.CreateModel(
            name='Atualizacao',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('atualizacao', models.TextField(verbose_name='Atualização')),
                ('publica', models.BooleanField(default=True, verbose_name='Pública')),
                ('atualizacao_criado_em', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Criado em')),
                ('obra_atualizada_em', models.DateTimeField(null=True, verbose_name='atualizado em')),
                ('atualizacao_apagada_em', models.DateTimeField(blank=True, null=True, verbose_name='Apagada em')),
                ('atualizacao_apagada_por', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_atualizacao_apagador', to=settings.AUTH_USER_MODEL, verbose_name='Apagada por')),
                ('atualizacao_criado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_atualizacao_criador', to=settings.AUTH_USER_MODEL, verbose_name='Criado por')),
                ('obra', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='obra_atualizacao_set', to='obras.obra', verbose_name='Atualização')),
                ('obra_atualizada_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_atualizacao_atualizador', to=settings.AUTH_USER_MODEL, verbose_name='Obra atualizada por')),
            ],
            options={
                'verbose_name': 'Atualização',
                'verbose_name_plural': 'Atualizações',
                'ordering': ('atualizacao_criado_em', 'obra_atualizada_em'),
            },
        ),
    ]
