# Generated by Django 3.2 on 2023-08-17 08:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('obras', '0007_alter_categoria_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='categoria',
            name='atualizado_em',
            field=models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em'),
        ),
        migrations.AddField(
            model_name='categoria',
            name='atualizado_por',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_categoria_atualizador', to=settings.AUTH_USER_MODEL, verbose_name='Atualizado por'),
        ),
        migrations.AddField(
            model_name='categoria',
            name='criado_em',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='Criado em'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='categoria',
            name='criado_por',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='obra_categoria_criador', to=settings.AUTH_USER_MODEL, verbose_name='Criado por'),
        ),
        migrations.AlterField(
            model_name='categoria',
            name='nome',
            field=models.CharField(max_length=255, unique=True, verbose_name='Categoria'),
        ),
    ]
