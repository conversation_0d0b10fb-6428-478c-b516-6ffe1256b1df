from django.forms.models import BaseInlineFormSet
from django.contrib import admin
from django.utils import timezone
from rangefilter.filters import DateRangeFilter
import admin_thumbnails
from django_admin_listfilter_dropdown.filters import (
    DropdownFilter, ChoiceDropdownFilter, RelatedDropdownFilter
)

from .models import Categoria, Foto, Obra, Atualizacao, Interdicao
from .forms import ObraAdminForm

from concessionarias.models import Concessionaria

class ObraAdmin(admin.ModelAdmin):
    list_per_page = 20
    readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('rodovia',)
    search_fields = ('slug',)
    list_select_related = ('criado_por',)
    list_filter = (
        ('dt_hr_inicio', DateRangeFilter),
        ('criado_por', RelatedDropdownFilter),
        ('concessionarias', RelatedDropdownFilter),
        ('rodovia', RelatedDropdownFilter),
    )
    list_display = ('__str__', 'criado_por',)

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def save_model(self, request, obj, form, change):
        print('calling save_model from admin')
        if not obj.pk:
            obj.criado_por = request.user
            obj.criado_em = timezone.now()
        print('obj.atualizado_em', obj.atualizado_em)
        obj.atualizado_por = request.user
        obj.atualizado_em = timezone.now()
        print('obj.atualizado_em', obj.atualizado_em)
        obj.save()

@admin_thumbnails.thumbnail('foto')
class FotoAdminInline(admin.TabularInline):
    model = Foto
    extra = 3


class AtualizacaoAdmin(admin.ModelAdmin):
    model = Atualizacao
    list_per_page = 10
    list_select_related = False
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('obra', 'atualizacao_criado_por', 'obra_atualizada_por', 'atualizacao_apagada_por')
    # list_display = ('obra',)
    search_fields = ('Obra__slug',)
    # list_select_related = ('obra',)

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False


class InterdicaoAdmin(admin.ModelAdmin):
    model = Interdicao
    list_per_page = 10
    list_select_related = False
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('obra', 'rodovia',)
    # list_display = ('obra',)
    search_fields = ('Obra__slug',)
    # list_select_related = ('obra',)

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False


class FotoAdmin(admin.ModelAdmin):
    model = Foto
    list_per_page = 10
    list_select_related = False
    # readonly_fields = ('slug', 'criado_por', 'atualizado_por', 'criado_em', 'atualizado_em')
    raw_id_fields = ('obra', 'adicionada_por')
    # list_display = ('obra',)
    search_fields = ('pk', 'obra__slug',)
    # list_select_related = ('obra',)

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False


class ObraAtualizacaoAdminInline(admin.TabularInline):
    model = Atualizacao
    extra = 0


class ObraInterdicaoAdminInline(admin.TabularInline):
    model = Interdicao
    extra = 0

class ObraAdmin(ObraAdmin):
    form = ObraAdminForm
    inlines = [
        ObraAtualizacaoAdminInline,
        FotoAdminInline,
        ObraInterdicaoAdminInline
    ]

    def get_form(self, request, obj=None, **kwargs):
        form = super(ObraAdmin, self).get_form(request, obj, **kwargs)
        form.base_fields['rodovia'].required = True
        form.base_fields['origem_comunicacao_cci'].initial = "CONCESSIONÁRIA"
        return form
    

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

class CategoriaAdmin(admin.ModelAdmin):
    model = Categoria
    list_per_page = 10

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_adm = request.user.groups.filter(name='adms').exists()
        if is_superuser or is_adm:
            return True
        return False

admin.site.register(Categoria, CategoriaAdmin)
admin.site.register(Obra, ObraAdmin)
admin.site.register(Foto, FotoAdmin)
admin.site.register(Interdicao, InterdicaoAdmin)
admin.site.register(Atualizacao, AtualizacaoAdmin)
