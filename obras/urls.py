
from django.conf import settings
from django.conf.urls.static import static
from django.urls import path
from django.views.generic import TemplateView

from .views import list_view, ObraCreateView, \
    add_photo_to_obra, detail_view, edit_view, SelectRodoviaListView, add_atualizacao_to_obra,\
                download_csv, download_excel, add_interdicao_to_obra, CategoriaCreateView, \
                CategoriaListView, CategoriaUpdateView, duplicate_view, show_photo_hr

app_name = 'obras'

urlpatterns = [
    path('', list_view, name='list'),
    path('download-csv', download_csv, name='download-csv'),
    path('download-excel', download_excel, name='download-excel'),
    path('select', SelectRodoviaListView.as_view(), name='select'),
    path('categorias/<pk>/edit', CategoriaUpdateView.as_view(), name='edit-categoria'),
    path('categorias/new', CategoriaCreateView.as_view(), name='create-categoria'),
    path('categorias', CategoriaListView.as_view(), name='list-categoria'),
    path('<int:rod_id>/new', ObraCreateView.as_view(), name='create'),
    path('<str:obra_slug>/<int:rod_id>/edit', edit_view, name='edit'),
    path('<str:oc_slug>/foto/<int:pk>', show_photo_hr, name='ver-foto'),
    path('<str:obra_slug>/select',  SelectRodoviaListView.as_view(), name='select-edit'),
    path('<str:obra_slug>/add-atualizacao', add_atualizacao_to_obra, name='add-atualizacao'),
    path('<str:obra_slug>/add-interdicao', add_interdicao_to_obra, name='add-interdicao'),
    path('<str:obra_slug>/add-photo', add_photo_to_obra, name='add-photo'),
    path('<str:obra_slug>/duplicar', duplicate_view, name='duplicar'),
    path('<str:obra_slug>', detail_view, name='detail'),
]
