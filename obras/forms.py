#models.py
from datetime import datetime, timedelta
from decimal import Decimal
from django import forms
from .models import Categoria, Obra, Interdicao
from rodovias.models import Rodovia, Trecho
from concessionarias.models import Concessionaria
from django.db.models import <PERSON>, Min, Sum
from django.utils import timezone
from django.forms import (
    BaseInlineFormSet,
    ModelForm
)


from dal import autocomplete

class RodoviaForm(forms.ModelForm):
    rodovia = forms.TextInput()


class ObraAdminForm(forms.ModelForm):

    class Meta:
        model = Obra
        fields = '__all__'

    def clean(self):
        cleaned_data = super().clean()
        rodovia = cleaned_data.get('rodovia')
        km_inicial = cleaned_data.get('km_inicial')
        km_final = cleaned_data.get('km_final')
        if km_final < km_inicial:
            km_inicial, km_final = km_final, km_inicial
        if not rodovia:
            raise forms.ValidationError(f"Rodovia é obrigatório.")
        trechos = rodovia.trechos.filter(km_final__gte=km_inicial).filter(km_inicial__lte=km_final)
        if not trechos:
            raise forms.ValidationError(f"Não há trechos cadastrados para esta rodovia e estes kms.")
        rodovia_primeiro_km = rodovia.trechos.aggregate(Min('km_inicial'))['km_inicial__min']
        # print('rodovia_primeiro_km')
        # print(rodovia_primeiro_km)
        rodovia_ultimo_km = rodovia.trechos.aggregate(Max('km_final'))['km_final__max']
        # print('rodovia_ultimo_km')
        # print(rodovia_ultimo_km)
        if km_inicial < rodovia_primeiro_km or km_inicial > rodovia_ultimo_km:
            raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
                rodovia {rodovia.codigo} (km {rodovia_primeiro_km} ao km {rodovia_ultimo_km})")
        if km_final < rodovia_primeiro_km or km_final > rodovia_ultimo_km:
            raise forms.ValidationError(f"Verifique o km inicial, pois não está dentro no intervalo de kms da\
                rodovia {rodovia.codigo} (km {rodovia_primeiro_km} ao km {rodovia_ultimo_km})")
        return cleaned_data


class ObraForm(forms.ModelForm):
    class Meta:
        model = Obra
        fields = [
            'categoria',
            'tipo_obra', 'complemento', 'rodovia', 'km_inicial', 'km_final', 
            'nro_faixas_local', 'tem_ac', 'dt_hr_conhecimento', 'dt_hr_inicio', 'dt_hr_termino',
            'dt_hr_termino_previsto', 'paralisada', 'publicada','origem_comunicacao_cci', 'pista','sentido', 
            'lat_inicial', 'lng_inicial', 'lat_final', 'lng_final',
            'sinalizacao', 'comunicacao_usuario', 
            'recursos_internos', 'recursos_externos',
            'acoes_operacionais', 'observacoes_cci', 'atualizacao_min', 'cod_conc', 'nro_mits',
            'arquivo', 'arquivo_desc',
        ]
        widgets = {
            'dt_hr_conhecimento': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'dt_hr_inicio': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'dt_hr_termino': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'dt_hr_termino_previsto': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'rodovia': forms.HiddenInput(),
        }
        help_texts={
            'bloq_faixas': 'Sim, houve bloqueio de faixa(s)',
            'tem_ac': 'Sim, há acostamento no local',
        },

    def __init__(self, rod_id, *args, **kwargs):
        super(ObraForm, self).__init__(*args, **kwargs)
        rodovia = Rodovia.objects.get(pk=rod_id)
        self.fields['categoria'].queryset = Categoria.objects.all()
        self.fields['rodovia'].initial = rodovia
        self.fields['origem_comunicacao_cci'].initial = 'Concessionária'
        self.fields['dt_hr_conhecimento'].initial = timezone.now()

    def clean(self):
        _now = timezone.now()
        cleaned_data = super().clean()


        # validacao dos campos de data
        dt_hr_conhecimento = cleaned_data.get('dt_hr_conhecimento')
        if not dt_hr_conhecimento:
            dt_hr_conhecimento = timezone.now()
        dt_hora_inicio = cleaned_data.get('dt_hr_inicio')
        if not dt_hora_inicio:
            dt_hora_inicio = dt_hr_conhecimento
        dt_hr_termino = cleaned_data.get('dt_hr_termino')
        dt_hr_termino_previsto = cleaned_data.get('dt_hr_termino_previsto')

        if dt_hr_termino_previsto and (dt_hr_termino_previsto < dt_hora_inicio):
            raise forms.ValidationError(f"Verifique a data de prevista de término, pois\
                detectou-se data prevista de término < data hora de início da obra.")
        if dt_hr_termino and (dt_hr_termino < dt_hora_inicio):
            raise forms.ValidationError(f"Verifique a data de encerramento, pois\
                detectou-se data hora de término < data hora de início da obra.")
        if dt_hr_termino and (dt_hr_termino > timezone.now()):
            raise forms.ValidationError(f"Verifique a data de encerramento, pois\
                detectou-se data hora de término > data hora atual.")

        rodovia = cleaned_data.get('rodovia')

        if not rodovia:
            raise forms.ValidationError(f"Rodovia é obrigatório.")

        km_inicial = cleaned_data.get('km_inicial')
        km_final = cleaned_data.get('km_final', None)
        sentido = cleaned_data.get('sentido')
        paralisada = cleaned_data.get('paralisada')
        # pista = cleaned_data.get('pista')
        # define km_final == km_inicial
        ordenacao = None
        
        # filtra sentido
        sentido = sentido if sentido != 'SEM INFORMAÇÃO' else None
        trechos = rodovia.trechos.all()
        if km_final is None:
            km_final = km_inicial
        
        if km_inicial < 0 or km_final < 0:
            raise forms.ValidationError(f"Verifique os marcos quilométricos inseridos.")

        if sentido:
            temp_trechos = rodovia.trechos.filter(sentido=sentido)
            if temp_trechos:
                trechos = temp_trechos
        
        ordenacao = trechos.values_list('sentido_ordem', flat=True).first()

        ordenacao_usuario = 'DECRESCENTE' if km_inicial > km_final else ordenacao

        if ordenacao != ordenacao_usuario:
            raise forms.ValidationError(f"Não foram encontrados trechos para esta rodovia, este(s) km(s) e sentido.\
                Verifique também se os marcos quilométricos, para o sentido escolhido, são crescentes ou decrescentes")

        if ordenacao == 'DECRESCENTE':
            trechos = trechos\
                .filter(km_inicial__gte=km_inicial).filter(km_final__lte=km_final)
        
        else:
            trechos = trechos\
                .filter(km_final__gte=km_inicial).filter(km_inicial__lte=km_final)

        if len(trechos) < 1:
            raise forms.ValidationError(f"Não foram encontrados trechos para esta rodovia, este(s) km(s) e sentido.\
                Verifique também se os marcos quilométricos, para o sentido escolhido, são crescentes ou decrescentes")


        # validacao de bloqueios
        if dt_hr_termino and self.instance.pk and self.instance.bloqueio != "Não":
            raise forms.ValidationError(f"Não é possível encerrar uma obra se ainda\
                existirem faixas de rolamento ou acostamento interditados.")
    
        # validacao de obras paralisadas
        if dt_hr_termino and paralisada:
            raise forms.ValidationError(f"Não é possível encerrar uma obra que está paralisada.")
        
        return cleaned_data

class CustomAddAtualizacaoInlineFormSet(BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super(CustomAddAtualizacaoInlineFormSet,self ).__init__(*args,**kwargs)
    
    def get_queryset(self):
        queryset = super(CustomAddAtualizacaoInlineFormSet, self).get_queryset()
        queryset = queryset.filter(atualizacao_apagada_em__isnull=True)
        # print(len(queryset))
        return queryset


class CustomAddInterdInlineFormSet(forms.ModelForm):
    class Meta:
        model = Interdicao
        fields = ['rodovia', 'sentido', 'km_inicial', 'km_final', 'faixa', 'dt_hora_inicio', 
                  'dt_hora_termino', 'desvios', 'obs',]
        widgets={
            'dt_hora_inicio': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'dt_hora_termino': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            'rodovia': autocomplete.ModelSelect2(url='rodovias:rodovia-autocomplete')
        }

    def __init__(self, *args, **kwargs):
        super(CustomAddInterdInlineFormSet,self ).__init__(*args,**kwargs)
        self.fields['rodovia'].required = False
    
    def get_queryset(self):
        queryset = super(CustomAddInterdInlineFormSet, self).get_queryset()
        # print(len(queryset))
        return queryset
    
    def clean(self):
        data = self.cleaned_data
        dt_hora_termino = self.cleaned_data.get('dt_hora_termino')
        dt_hora_inicio = self.cleaned_data.get('dt_hora_inicio')
        if dt_hora_termino and dt_hora_inicio and dt_hora_termino < dt_hora_inicio:
            raise forms.ValidationError(f"Datahora início deve ser menor que datahora término")

        return data


class CustomAddFotoInlineFormSet(BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super(CustomAddFotoInlineFormSet,self ).__init__(*args,**kwargs)
    
    def get_queryset(self):
        queryset = super(CustomAddFotoInlineFormSet, self).get_queryset()
        # print(len(queryset))
        return queryset


class FotoValidacaoForm(forms.Form):
    foto_valida = forms.BooleanField(
        label='Eu valido esta foto, pois não há corpos e/ou qualquer outra informação sensível.',
        help_text='Sim, eu valido esta foto',
        required=False
    )

    def clean_foto_valida(self):
        foto_valida = self.cleaned_data.get('foto_valida', '')
        return foto_valida

    def clean(self):
        data = self.cleaned_data

        return data


class CategoriaForm(ModelForm):
    class Meta:
        model = Categoria
        fields = [
            'nome',
        ]
    
    def clean_nome(self):
        nome = self.cleaned_data.get('nome')
        cat = Categoria.objects.filter(nome__iexact=nome).first()

        # editing
        if self.instance.pk and \
            cat and \
            cat.pk != self.instance.pk and \
            nome.lower() == cat.nome.lower():
            raise forms.ValidationError(f"Categoria com nome '{nome}' já existe.")

        # new categoria
        if not self.instance.pk and cat and nome.lower() == cat.nome.lower():
            raise forms.ValidationError(f"Categoria com nome '{nome}' já existe.") 
        return nome