import base64
from decimal import Decimal
from typing import Tuple
import uuid
import pathlib
import datetime
import sys
from enum import Enum
from itertools import chain
from django.db import models
from django.db.models import F, Q, Sum
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.validators import MaxValueValidator, MinValueValidator
from model_utils.managers import InheritanceManager
from django.core.exceptions import EmptyResultSet, ValidationError


from PIL import Image
from io import BytesIO
from django.core.files.uploadedfile import InMemoryUploadedFile

from .validators import validate_file_extension, validate_file_size
from rodovias.models import Rodovia, Trecho


def get_file_path(instance, filename):
    filename = f'{uuid.uuid4()}.pdf'
    filepath = f'obras/arquivos/{filename}'
    return filepath


def validate_image(fieldfile_obj):
    filesize = fieldfile_obj.file.size
    megabyte_limit = 3.0
    if filesize > megabyte_limit*1024*1024:
        raise ValidationError("O tamanho máximo da foto é %sMB" % str(megabyte_limit))

class Bloqueio(models.TextChoices):
    APENAS_ACOSTAMENTO = 'Acostamento', 'Acostamento'
    ALCA_PARCIAL = 'Alça parcial', 'Alça parcial'
    ALCA_TOTAL = 'Alça total', 'Alça total'
    FAIXA_1 = 'Faixa 1', 'Faixa 1'
    FAIXA_2 = 'Faixa 2', 'Faixa 2'
    FAIXA_3 = 'Faixa 3', 'Faixa 3'
    FAIXA_4 = 'Faixa 4', 'Faixa 4'
    FAIXA_5 = 'Faixa 5', 'Faixa 5'
    FAIXA_6 = 'Faixa 6', 'Faixa 6'
    FAIXA_ZEBRADA = 'Faixa zebrada', 'Faixa zebrada'
    FAIXA_DE_ACELERACAO = 'Faixa de aceleração', 'Faixa de aceleração'
    FAIXA_DE_DESACELERACAO = 'Faixa de desaceleração', 'Faixa de desaceleração'
    PRACA_DE_PEDAGIO = 'Praça de pedágio', 'Praça de pedágio'
    TODAS_AS_FAIXAS_EXCETO_AC = 'Todas as faixas exceto AC', 'Todas as faixas exceto AC'
    TODAS_AS_FAIXAS_TRECHO_SEM_AC = 'Todas as faixas (trecho sem AC)', 'Todas as faixas (trecho sem AC)'
    TODAS_AS_FAIXAS_E_ACOSTAMENTO = 'Todas as faixas e acostamento', 'Todas as faixas e acostamento'


class BloqueioResumido(Enum):
    TOTAL = "Total"
    TOTAL_ALCA = "Total (Alça)"
    PARCIAL_ALCA = "Parcial (Alça)"
    PRACA_PEDAGIO = "Praca de pedágio"
    APENAS_AC_LIVRE = "Apenas AC livre"
    PARCIAL = "Parcial"
    ACOSTAMENTO = "Acostamento"
    NAO = "Não"


class Obra(models.Model):

    ORIGEMCCI_CHOICES = [
        ('Concessionária', 'Concessionária'),
        ('Imprensa', 'Imprensa'),
        ('ARTESP', 'ARTESP'),
        ('Outro', 'Outro'),
        ('Desconhecido', 'Desconhecido'),
    ]
    PISTA_CHOICES = [
        ('DUPLA', 'DUPLA'),
        ('SIMPLES', 'SIMPLES'),
        ('PLANEJADA', 'PLANEJADA'),
        ('DISPOSITIVO', 'DISPOSITIVO'),
        ('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'),
    ]
    SENTIDO_CHOICES = [
        ('SEM INFORMAÇÃO', 'SEM INFORMAÇÃO'),
        ('NORTE', 'NORTE'),
        ('SUL', 'SUL'),
        ('LESTE', 'LESTE'),
        ('OESTE', 'OESTE'),
        ('NORTE/SUL', 'NORTE/SUL'),
        ('LESTE/OESTE', 'LESTE/OESTE'),
        ('EXTERNO', 'EXTERNO'),
        ('INTERNO', 'INTERNO'),
        ('NÃO SE APLICA', 'NÃO SE APLICA'),
    ]
    FAIXAS_LOCAL = [
        ('1', '1'),
        ('2', '2'),
        ('3', '3'),
        ('4', '4'),
        ('5', '5'),
        ('6', '6'),
        ('7', '7'),
        ('8', '8'),
        ('9', '9'),
        ('10', '10'),
        ('11', '11'),
        ('12', '12'),
        ('13', '13'),
        ('14', '14'),
        ('15', '15'),
    ]

    CLIMA_CHOICES = [
        ('Chuva', 'Chuva'),
        ('Garoa', 'Garoa'),
        ('Neblina', 'Neblina'),
        ('Nublado', 'Nublado'),
        ('Trechos com chuva', 'Trechos com chuva'),
        ('Trechos com garoa', 'Trechos com garoa'),
        ('Trechos com neblina', 'Trechos com neblina'),
        ('Trechos com garoa e neblina', 'Trechos com garoa e neblina'),
        ('Trechos com chuva e neblina', 'Trechos com chuva e neblina'),
        ('Trechos com chuva e nublado', 'Trechos com chuva e nublado'),
        ('Chuva com ventania', 'Chuva com ventania'),
        ('Chuva torrencial', 'Chuva torrencial'),
        ('Sol', 'Sol'),
        ('Tempo bom', 'Tempo bom'),
        ('Vento forte', 'Vento forte'),
        ('Desconhecidas', 'Desconhecidas'),
    ]
    categoria = models.ForeignKey(
        "Categoria",
        verbose_name='Categoria', related_name='categoria', null=True, blank=False, on_delete=models.SET_NULL
    )
    bloqueio = models.CharField(
        _("Bloqueio"), max_length=255, blank=True, null=True, default="Não"
    )
    tipo_obra = models.CharField(
        _("Tipo de obra"), max_length=255,)
    complemento = models.CharField(
        _("Complemento"), max_length=255, null=True, blank=True)
    slug = models.SlugField(_("Código"), blank=True,
                            null=True, max_length=50, db_index=True,)
    cod_conc = models.CharField(
        _("Código Concessionária"), max_length=50, null=True, blank=True)
    rodovia = models.ForeignKey(
        Rodovia,
        verbose_name='Rodovia', related_name='obra_rodovia_set', null=True, blank=False, on_delete=models.SET_NULL
    )
    concessionarias = models.ManyToManyField(
        "concessionarias.Concessionaria", verbose_name=_("Concessionarias"), related_name='obra_concessionarias_set')
    municipios = models.ManyToManyField("municipios.Municipio", verbose_name=_(
        "Municipios"), related_name="obra_municipios_set")
    km_inicial = models.DecimalField(
        _("KM inicial"), max_digits=8, decimal_places=3)
    km_final = models.DecimalField(
        _("KM final"), max_digits=8, decimal_places=3, blank=True)
    nro_faixas_local = models.CharField(
        _("Nro fxs local"), choices=FAIXAS_LOCAL, max_length=255, blank=True, null=True
    )
    tem_ac = models.BooleanField(_("Tem AC."), default=True, help_text='Sim, há acostamento no local')
    dt_hr_conhecimento = models.DateTimeField(
        _("Data conhecimento"), auto_now=False, auto_now_add=False)
    dt_hr_inicio = models.DateTimeField(
        _("Data início"), auto_now=False, auto_now_add=False, blank=True, null=True)
    dt_hr_termino_previsto = models.DateTimeField(
        _("Data prevista de término"), auto_now=False, auto_now_add=False, blank=True, null=True)
    dt_hr_termino = models.DateTimeField(
        _("Data término"), auto_now=False, auto_now_add=False, blank=True, null=True)
    publicada = models.BooleanField(_("Publicar"), default=True)
    paralisada = models.BooleanField(_("Paralisada"), default=False)
    aprovada = models.BooleanField(_("Aprovada"), default=False)
    origem_comunicacao_cci = models.CharField(
        _("Origem da inf."), choices=ORIGEMCCI_CHOICES, max_length=255,
        default="CONCESSIONÁRIA", blank=True, null=True
    )
    pista = models.CharField(
        max_length=50,
        choices=PISTA_CHOICES,
        null=True,
        blank=True,
        verbose_name='Pista',
        default="SEM INFORMAÇÃO"
    )
    sentido = models.CharField(
        max_length=20,
        choices=SENTIDO_CHOICES,
        verbose_name='Sentido',
        default="SEM INFORMAÇÃO"
    )
    lat_inicial = models.DecimalField(
        _("Latitude inicial"), max_digits=12, decimal_places=7, blank=True, null=True)
    lng_inicial = models.DecimalField(
        _("Longitude inicial"), max_digits=12, decimal_places=7, blank=True, null=True)
    lat_final = models.DecimalField(
        _("Latitude final"), max_digits=12, decimal_places=7, blank=True, null=True)
    lng_final = models.DecimalField(
        _("Longitude final"), max_digits=12, decimal_places=7, blank=True, null=True)
    sinalizacao = models.CharField(
        _("Sinalização"), max_length=50, blank=True, null=True)
    comunicacao_usuario = models.CharField(
        _("Comunicação com usuário"), max_length=50, blank=True, null=True)
    recursos_internos = models.CharField(
        _("Recursos internos"), max_length=255, blank=True, null=True)
    recursos_externos = models.CharField(
        _("Recursos externos"), max_length=255, blank=True, null=True)
    acoes_operacionais = models.CharField(
        _("Ações operacionais"), max_length=255, blank=True, null=True)
    observacoes_cci = models.TextField(
        _("Observações CCI"), blank=True, null=True)
    atualizacao_min = models.IntegerField(
        _("Atualização - Periodicidade (dias)"), default=15, validators=[MaxValueValidator(15)], help_text='Tempo em dias. Valor máximo: 15 dias')
    nro_mits = models.PositiveIntegerField(
        _("Nro MITS"), blank=True, null=True)
    arquivo = models.FileField(
        _("Arquivo complementar PDF (máx 1 arq. < 10mb)"), upload_to=get_file_path, validators=[validate_file_extension, validate_file_size],
        null=True, blank=True
    )
    arquivo_desc = models.CharField(
        _("Descrição do arquivo"), max_length=50, blank=True, null=True)
    criado_em = models.DateTimeField(
        auto_now_add=True, verbose_name='Criado em')
    criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='obra_criador'
    )
    atualizado_em = models.DateTimeField(
        auto_now=True, null=True, blank=True, verbose_name='Atualizado em')
    atualizado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Atualizado por', related_name='obra_atualizador'
    )

    objects = InheritanceManager()

    class Meta:
        abstract: True
        ordering: ['rodovia__codigo, -pk']
        verbose_name = 'Intervenção Viária'
        verbose_name_plural = 'Intervencões Viárias'

    def __str__(self):
        local = f'{self.rodovia.codigo} | KMi: {self.km_inicial}'
        if self.km_final:
            local += f' | KMf: {self.km_final}'
        if self.sentido:
            local += f' | {self.sentido}'
        tipo_obra = self.tipo_obra
        if tipo_obra and len(tipo_obra) > 12:
            tipo_obra = self.tipo_obra[:12] + '...'
        return f'{local} [ {self.slug.upper()} ] {tipo_obra}'

    def get_trechos(self, tipo=None):
        if self.km_final is None:
            self.km_final = self.km_inicial

        ordenacao = None
        sentido = self.sentido if self.sentido != 'SEM INFORMAÇÃO' else None

        result = self.rodovia.trechos.all()\
                    .filter(sentido=sentido)\
                    .filter(concessionaria__fiscalizacao_artesp=True)

        if self.km_inicial > self.km_final:
            # self.km_inicial, self.km_final = self.km_final, self.km_inicial
            ordenacao = 'DECRESCENTE'
        
        elif self.km_inicial < self.km_final:
            ordenacao = 'CRESCENTE'

        else:
            ordenacao = result.values_list('sentido_ordem', flat=True).first()
        
        
        if ordenacao == 'DECRESCENTE':
            result = result\
                    .filter(km_inicial__gte=self.km_inicial).filter(km_final__lte=self.km_final)
        else:
            result = result\
                    .filter(km_final__gte=self.km_inicial).filter(km_inicial__lte=self.km_final)

        if result.count() < 1:
            raise EmptyResultSet('Não foram encontrados trechos com as características informadas.')       
        else:
            if tipo == 'concessionaria':
                return result.prefetch_related('concessionaria')
            if tipo == 'municipio':
                return result.prefetch_related('municipio')
            return result
    
    def get_trechos_template(self, tipo=None):
        if self.km_final is None:
            self.km_final = self.km_inicial

        ordenacao = None
        sentido = self.sentido if self.sentido != 'SEM INFORMAÇÃO' else None

        result = self.rodovia.trechos.all().filter(sentido=sentido)

        if self.km_inicial > self.km_final:
            # self.km_inicial, self.km_final = self.km_final, self.km_inicial
            ordenacao = 'DECRESCENTE'
        
        elif self.km_inicial < self.km_final:
            ordenacao = 'CRESCENTE'

        else:
            ordenacao = result.values_list('sentido_ordem', flat=True).first()
        
        
        if ordenacao == 'DECRESCENTE':
            result = result\
                    .filter(km_inicial__gte=self.km_inicial).filter(km_final__lte=self.km_final)
        else:
            result = result\
                    .filter(km_final__gte=self.km_inicial).filter(km_inicial__lte=self.km_final)
        # print('result')
        # print(result)

        if result.count() < 1:
            return Trecho.objects.none()        
        else:
            if tipo == 'concessionaria':
                return result.prefetch_related('concessionaria')
            if tipo == 'municipio':
                return result.prefetch_related('municipio')
            return result

    def get_municipios(self):
        trechos = self.get_trechos('municipio')
        municipios = [trecho.municipio for trecho in trechos]
        return list(set(municipios))

    def get_municipios_ids(self):
        trechos = self.get_trechos('municipio')
        municipios = trechos.values_list('municipio__pk', flat=True)
        return municipios

    def get_municipios_names(self):
        trechos = self.get_trechos('municipio')
        mun_names = trechos.values_list('municipio__nome', flat=True)
        return ", ".join(mun_names)
    
    def get_municipios_names_template(self, mun_list):
        mun_names = list(set(mun_list))
        return ", ".join(sorted(mun_names))

    def get_concessionarias(self):
        trechos = self.get_trechos('concessionaria')
        filtered_concessionarias = {
            trecho.concessionaria
            for trecho in trechos
            if trecho.concessionaria.fiscalizacao_artesp
            and trecho.dt_fim is None
        }
        return list(filtered_concessionarias)

    def get_concessionarias_names(self):
        trechos = self.get_trechos('concessionaria')
        concessionarias = trechos.values_list(
            'concessionaria__nome', flat=True)
        return list(set(concessionarias))

    def get_concessionarias_ids(self):
        trechos = self.get_trechos('concessionaria')
        concessionarias = trechos.values_list('concessionaria__pk', flat=True)
        return list(set(concessionarias))

    def get_coords(self):
        return f'início: {self.lat_inicial},{self.lng_inicial} | término: {self.lat_final},{self.lng_final}'

    def get_icon(self):
        return 'images/obras/icones/obra.png'
    

    def get_sigla(self):
        return f'OBR'

    def get_congestionamento(self):
        if self.congestionamento.all().count():
            total = 0
            for cong in self.congestionamento.all():
                total += cong.get_total()
            return "{:.3f}".format(total)
        else:
            return None
    
    def get_tem_congestionamento_ou_lento_maior_ou_igual_que_n_quilometros(self, congestionamentos, n_quilometros) -> bool:
        total_geral = 0
        if congestionamentos:
            for cong in congestionamentos:
                if cong.trafego in ['LENTO', 'CONGESTIONADO']:
                    total = cong.get_total()
                    if total:
                        total_geral += total
        return total_geral >= n_quilometros
    
    def is_closed_cond_trafego(self):
        all_closed = True
        for congest in self.congestionamento.all():
            if not congest.dt_hora_inicio or not congest.dt_hora_termino:
                all_closed = False
        return all_closed

    def get_status_str(self):
        if not self.dt_hr_termino:
            return 'Em andamento'
        else:
            return 'Finalizada'
    
    def get_esta_atrasada(self) -> bool:
        _now = datetime.datetime.now()
        if not self.dt_hr_termino and self.dt_hr_termino_previsto and self.dt_hr_termino_previsto < _now:
            return True
        return False


    def desatualizada(self):
        """
        Uma obra ativa e sem atualização por mais tempo que o definido 
        em  `atualizacao_min` será considerada desatualizada
        """
        total_seconds = (timezone.now() - self.atualizado_em).total_seconds()
        total_hours = (total_seconds / 60) / 60
        days = 24
        if (total_hours > (14 * days)):
            return True
        else:
            return False

    def get_ultima_atualizacao(self):
        return self.atualizado_em

    def get_url_ultima_foto(self):
        if self.tem_foto():
            url = self.fotos.all().last().get_absolute_image_url
            return url
        return ''
    
    def tem_foto(self):
       return bool(self.fotos.all().count())

    def has_conc(self, conc_id):
        concessionarias_ids = self.get_concessionarias_ids()
        if conc_id in concessionarias_ids:
            return True
        return False

    def get_bloqueio(self, interdicoes):
        bloqueio_parcial = False
        bloqueio_total = False
        bloqueio_fxs_rolamento = False
        bloqueio_ac = False
        bloqueio_alca_total = False
        bloqueio_alca_parcial = False
        bloqueio_praca_pedagio = False
        todas_as_faixas_exceto_ac = Bloqueio.TODAS_AS_FAIXAS_EXCETO_AC # "Todas as faixas"
        todas_as_faixas_e_acostamento = Bloqueio.TODAS_AS_FAIXAS_E_ACOSTAMENTO
        todas_as_faixas_trecho_sem_ac = Bloqueio.TODAS_AS_FAIXAS_TRECHO_SEM_AC
        alca_total = Bloqueio.ALCA_TOTAL
        alca_parcial = Bloqueio.ALCA_PARCIAL
        praca_pedagio = Bloqueio.PRACA_DE_PEDAGIO
        apenas_ac = Bloqueio.APENAS_ACOSTAMENTO
        for interd in interdicoes.filter(dt_hora_termino__isnull=True):
            if interd.faixa == todas_as_faixas_e_acostamento or interd.faixa == todas_as_faixas_trecho_sem_ac:
                bloqueio_total = True
            elif interd.faixa == todas_as_faixas_exceto_ac:
                bloqueio_fxs_rolamento = True
            elif interd.faixa == alca_total:
                bloqueio_alca_total = True
            elif interd.faixa == alca_parcial:
                bloqueio_alca_parcial = True
            elif interd.faixa == praca_pedagio:
                bloqueio_praca_pedagio = True
            elif interd.faixa == praca_pedagio:
                bloqueio_praca_pedagio = True
            else:
                if interd.faixa == apenas_ac:
                    bloqueio_ac = True
                else:
                    bloqueio_parcial = True
        if bloqueio_total:
            return BloqueioResumido.TOTAL.value
        if bloqueio_alca_total:
            return BloqueioResumido.TOTAL_ALCA.value
        if bloqueio_alca_parcial:
            return BloqueioResumido.PARCIAL_ALCA.value
        if bloqueio_praca_pedagio:
            return BloqueioResumido.PRACA_PEDAGIO.value
        if bloqueio_fxs_rolamento:
            return BloqueioResumido.APENAS_AC_LIVRE.value
        if not bloqueio_total and bloqueio_parcial:
            return BloqueioResumido.PARCIAL.value
        if not bloqueio_total and not bloqueio_parcial and bloqueio_ac:
            return BloqueioResumido.ACOSTAMENTO.value
        return BloqueioResumido.NAO.value

    def get_tem_interdicao_parcial_ativa_ou_maior_que_n_minutos(self, interdicoes, minutos) -> bool:
        if interdicoes:
            for interd in interdicoes.filter(
                Q(faixa=Bloqueio.ALCA_PARCIAL) | 
                Q(faixa=Bloqueio.TODAS_AS_FAIXAS_EXCETO_AC) |
                Q(faixa=Bloqueio.FAIXA_1) |
                Q(faixa=Bloqueio.FAIXA_2) |
                Q(faixa=Bloqueio.FAIXA_3) |
                Q(faixa=Bloqueio.FAIXA_4) |
                Q(faixa=Bloqueio.FAIXA_5) |
                Q(faixa=Bloqueio.FAIXA_6) |
                Q(faixa=Bloqueio.PRACA_DE_PEDAGIO)
            ):
                if interd.bloqueio_ativo_ou_maior_que_n_minutos(minutos):
                    # print(f'interdicao parcial maior que {minutos}')
                    return True
        # print(f'NAO TEM interdicao parcial maior que {minutos}')
        return False

    def get_tem_interdicao_total_ativa_ou_maior_que_n_minutos(self, interdicoes, minutos) -> bool:
        if interdicoes:
            for interd in interdicoes.filter(
                Q(faixa=Bloqueio.TODAS_AS_FAIXAS_TRECHO_SEM_AC) | 
                Q(faixa=Bloqueio.TODAS_AS_FAIXAS_E_ACOSTAMENTO) |
                Q(faixa=Bloqueio.ALCA_TOTAL)
            ):
                if interd.bloqueio_ativo_ou_maior_que_n_minutos(minutos):
                    # print(f'interdicao total maior que {minutos}')
                    return True
        # print(f'NAO TEM interdicao total maior que {minutos}')
        return False
 
    def get_tem_interdicao_praca_pedagio(self, interdicoes, minutos) -> bool:
        if interdicoes:
            for interd in interdicoes.filter(faixa=Bloqueio.PRACA_DE_PEDAGIO):
                if interd.bloqueio_ativo_ou_maior_que_n_minutos(minutos):
                    # print(f'interdicao PP maior que {minutos}')
                    return True
        # print(f'NAO TEM interdicao PP maior que {minutos}')
        return False
   
    def get_bloqueios_txt(self):
        txt_container = []
        contador = 1
        for interd in self.interdicao.all():
            rodovia = interd.rodovia
            sentido = interd.sentido
            faixa = interd.faixa
            hr_bloqueio = f'[Bloqueado em {interd.dt_hora_inicio}]' if interd.dt_hora_inicio is not None else ''
            hr_liberacao = f'[Liberado em {interd.dt_hora_termino}]' if interd.dt_hora_termino is not None else ''
            obs = interd.obs if interd.obs is not None else ''
            if rodovia:
                str_to_append = f'{contador}) {rodovia}/{sentido}/{faixa} {hr_bloqueio} {hr_liberacao} {obs}'
            else:
                str_to_append = f'{contador}) {sentido}/{faixa} {hr_bloqueio} {hr_liberacao} {obs}'
            txt_container.append(str_to_append)
            contador += 1
        return ' || '.join(txt_container)

    def get_bloqueios_txt_zap(self):
        txt_container = []
        total_itens = self.interdicao.all().count()
        if total_itens:
            for interd in self.interdicao.all():
                # pista = interd.pista
                rodovia = interd.rodovia
                sentido = interd.sentido.lower()
                faixa = interd.get_nome_faixa_resumido()
                obs = interd.obs
                if obs and type(obs) == str:
                    obs = obs.lower()
                    if len(obs) > 15:
                        obs = obs[:12] + '...'
                bloqueio = f'[{interd.dt_hora_inicio.strftime("%d/%m %H:%M")}]&nbsp;' if interd.dt_hora_inicio else ''
                liberado = f'{interd.dt_hora_termino.strftime("%d/%m %H:%M")}' if interd.dt_hora_termino else ''
                if rodovia:
                    str_to_append = f'&nbsp;&nbsp;▫️&nbsp;{bloqueio}{rodovia}/{sentido}/{faixa}'
                else:
                    str_to_append = f'&nbsp;&nbsp;▫️&nbsp;{bloqueio}{sentido}/{faixa}'
                if obs:
                    str_to_append += f'/{obs}'
                if liberado:
                    str_to_append += f' | *T*: {liberado}'
                # str_to_append += ''
                total_itens -= 1
                if total_itens > 0:
                    str_to_append += '<br />'
                txt_container.append(str_to_append)
            return ''.join(txt_container)
        return None
    

    def save(self, *args, **kwargs):
        ''' On save, update timestamps '''
        if not self.id:
            self.criado_em = timezone.now()
        self.atualizado_em = timezone.now()
        return super(Obra, self).save(*args, **kwargs)


def get_slug(instance, filename):
    _suffix = pathlib.Path(filename).suffix
    return f"obra/fotos/{instance.obra.slug}/{uuid.uuid4()}{_suffix}"


class Categoria(models.Model):
    nome = models.CharField(_("Categoria"), max_length=255, unique=True)
    criado_em = models.DateTimeField(
        auto_now_add=True, verbose_name='Criado em')
    criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='obra_categoria_criador'
    )
    atualizado_em = models.DateTimeField(
        auto_now=True, null=True, blank=True, verbose_name='Atualizado em')
    atualizado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Atualizado por', related_name='obra_categoria_atualizador'
    )


    class Meta:
        ordering = ['nome']
        verbose_name = 'Categoria'
        verbose_name_plural = 'Categorias'
    
    def __str__(self):
        return f'{self.nome}'

    

class Foto(models.Model):
    obra = models.ForeignKey(Obra, verbose_name=_(
        "Foto"), related_name='obra_fotos_set', on_delete=models.CASCADE)

    foto = models.ImageField(
        _("Foto"), upload_to=get_slug, height_field=None, width_field=None, max_length=100,
        validators=[validate_image], null=True, blank=True    
    )
    foto_blob = models.TextField('foto_blob', blank=True, null=True)
                            
    foto_hr = models.ImageField(
        _("Foto original"), upload_to=get_slug,
        height_field=None, width_field=None, max_length=100,
        validators=[validate_image], null=True, blank=True
    )
    foto_hr_blob = models.TextField('foto_hr_blob', blank=True, null=True)

    publica = models.BooleanField(_("Pública"), default=True)
    legenda = models.CharField(
        _("Legenda"), max_length=100, null=True, blank=True)
    adicionada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Adicionada por', related_name='obra_foto_adic_por'
    )
    adicionada_em = models.DateTimeField(
        auto_now_add=True, verbose_name='Adicionada em')

    class Meta:
        ordering = ['-pk']
        verbose_name = 'Foto'
        verbose_name_plural = 'Fotos'

    def __str__(self):
        if self.legenda:
            return f'{self.pk} {self.legenda} <{self.obra.slug}>'
        return f'{self.pk} <{self.obra.slug}>'

    @property
    def get_absolute_image_url(self):
        return "{0}".format(self.foto.url)

    @property
    def get_absolute_image_url_hr(self):
        return "{0}".format(self.foto_hr.url)
    
    def set_foto_blob(self, data):
        self.foto_blob = base64.encodebytes(data).decode()

    def get_foto_blob(self):
        return base64.decodebytes(self.foto_blob)

    def set_foto_hr_blob(self, data):
        self.foto_hr_blob = base64.encodebytes(data).decode()

    def get_foto_hr_blob(self):
        return base64.decodebytes(self.foto_hr_blob)
    
    def save(self, *args, **kwargs):
        if self.foto_hr:
            im = Image.open(self.foto_hr)
            im_hr = Image.open(self.foto_hr)

            if im.mode in ("RGBA", "P"):
                im = im.convert('RGB')

            if im_hr.mode in ("RGBA", "P"):
                im_hr = im_hr.convert('RGB')

            output = BytesIO()
            output_hr = BytesIO()

            fixed_height = 250

            # Resize/modify the image
            height_percent = (fixed_height / float(im.size[1]))
            width_size = int((float(im.size[0]) * float(height_percent)))
            im = im.resize((width_size, fixed_height), Image.NEAREST)

            # after modifications, save it to the output
            im.save(output, format='JPEG', quality=65)
            im_hr.save(output_hr, format='JPEG', quality=80)

            output.seek(0)
            output_hr.seek(0)

            if output:
                self.set_foto_blob(output.read())
                # self.foto_blob = bytes(output.read())
                self.foto = None
            if output_hr:
                self.set_foto_hr_blob(output_hr.read())
                # self.foto_blob_hr = bytes(output_hr.read())
                self.foto_hr = None

        super(Foto, self).save()

class Atualizacao(models.Model):
    atualizacao = models.TextField(_("Atualização"))
    publica = models.BooleanField(_("Pública"), default=True)
    obra = models.ForeignKey(Obra, verbose_name=_(
        "Atualização"), on_delete=models.CASCADE,  related_name='obra_atualizacao_set')
    atualizacao_criado_em = models.DateTimeField(
        auto_now_add=True, verbose_name='Criado em', null=True)
    atualizacao_criado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Criado por', related_name='obra_atualizacao_criador'
    )
    obra_atualizada_em = models.DateTimeField(verbose_name='atualizado em', null=True)
    obra_atualizada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, on_delete=models.SET_NULL,
        verbose_name='Obra atualizada por', related_name='obra_atualizacao_atualizador'
    )
    # at_atualizada_posteriormente = models.BooleanField(_("Atualizada posteriormente"), default=False)
    atualizacao_apagada_em = models.DateTimeField(verbose_name='Apagada em', null=True, blank=True,)
    atualizacao_apagada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name='Apagada por', related_name='obra_atualizacao_apagador'
    )

    def __str__(self):
        return f'{self.obra_atualizada_em}: {self.atualizacao} <{self.obra.slug}>'

    class Meta:
        ordering = ('atualizacao_criado_em', 'obra_atualizada_em',)
        verbose_name = 'Atualização'
        verbose_name_plural = 'Atualizações'

class Interdicao(models.Model):
    FAIXAS_CHOICES = [
        ('Acostamento', 'Acostamento'),
        ('Alça parcial', 'Alça parcial'),
        ('Alça total', 'Alça total'),
        ('Faixa 1', 'Faixa 1'),
        ('Faixa 2', 'Faixa 2'),
        ('Faixa 3', 'Faixa 3'),
        ('Faixa 4', 'Faixa 4'),
        ('Faixa 5', 'Faixa 5'),
        ('Faixa 6', 'Faixa 6'),
        ('Faixa adicional', 'Faixa adicional'),
        ('Faixa zebrada', 'Faixa zebrada'),
        ('Faixa de aceleração', 'Faixa de aceleração'),
        ('Faixa de desaceleração', 'Faixa de desaceleração'),
        ('Praça de pedágio', 'Praça de pedágio'),
        ('Todas as faixas', 'Todas as faixas'),
        ('Todas as faixas (trecho sem AC)', 'Todas as faixas (trecho sem AC)'),
        ('Todas as faixas e acostamento', 'Todas as faixas e acostamento')
    ]
    obra = models.ForeignKey(
        Obra, verbose_name=_("Interdição/Bloqueio"), related_name='obra_interdicao_set', on_delete=models.CASCADE
    )
    rodovia = models.ForeignKey(
        Rodovia,
        verbose_name='Rodovia', related_name='obra_rodovia', null=True, blank=False, on_delete=models.SET_NULL
    )
    km_inicial = models.DecimalField(
        _("KM inicial"), max_digits=8, decimal_places=3, blank=True, null=True)
    km_final = models.DecimalField(
        _("KM final"), max_digits=8, decimal_places=3, blank=True, null=True)
    sentido = models.CharField(
        max_length=20,
        choices=Obra.SENTIDO_CHOICES,
        verbose_name='Sentido',
    )
    faixa = models.CharField(
        max_length=40,
        choices=FAIXAS_CHOICES,
        verbose_name='Faixa',
    )
    dt_hora_inicio = models.DateTimeField(
        _("Data Hora Início"), null=True, blank=True, auto_now=False, auto_now_add=False)
    dt_hora_termino = models.DateTimeField(
        _("Data Hora Término"), null=True, blank=True, auto_now=False, auto_now_add=False)
    desvios = models.CharField(_("Desvios"), max_length=255, null=True, blank=True)
    obs = models.CharField(_("Obs."), max_length=255, null=True, blank=True)

    def bloqueio_ativo_ou_maior_que_n_minutos(self, minutos) -> bool:
        if not self.dt_hora_inicio and not self.dt_hora_termino:
            return False
        if self.dt_hora_inicio and not self.dt_hora_termino:
            return True
        if self.dt_hora_inicio and self.dt_hora_termino:
            return (self.dt_hora_termino - self.dt_hora_inicio).total_seconds() >= (minutos*60)
        return False

    class Meta:
        ordering = ['-dt_hora_termino', 'dt_hora_inicio', 'faixa']
        verbose_name = 'Interdição'
        verbose_name_plural = 'Interdições'

    def __str__(self):
        return f'{self.pk} <{self.obra.slug}>'
    
    def get_nome_faixa_resumido(self):
        nomes = {
            'Acostamento': 'ac',
            'Alça parcial': 'alça parc',
            'Alça total': 'alça tot',
            'Faixa 1': 'f1',
            'Faixa 2': 'f2',
            'Faixa 3': 'f3',
            'Faixa 4': 'f4',
            'Faixa 5': 'f5',
            'Faixa 6': 'f6',
            'Faixa adicional': 'fx.adicional',
            'Faixa zebrada': 'fx.zebrada',
            'Faixa de aceleração': 'fx.aceleração',
            'Faixa de desaceleração': 'fx.desaceleração',
            'Praça de pedágio': 'pp',
            'Todas as faixas': 'total -ac',
            'Todas as faixas (trecho sem AC)': 'total',
            'Todas as faixas e acostamento': 'total',
        }
        return nomes.get(self.faixa, self.faixa)
    
    def get_nome_sentido_resumido(self):
        nomes = {
            'SEM INFORMAÇÃO': 'Ñ.D.',
            'NORTE': 'N',
            'SUL': 'S',
            'LESTE': 'L',
            'OESTE': 'O',
            'NORTE/SUL': 'N/S',
            'LESTE/OESTE': 'L/O',
            'EXTERNO': 'EXT',
            'INTERNO': 'INT',
            'NÃO SE APLICA': 'N/A',
        }
        return nomes.get(self.sentido, self.sentido)