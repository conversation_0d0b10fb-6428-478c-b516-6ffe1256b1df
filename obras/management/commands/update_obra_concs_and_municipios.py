from django.core.management.base import BaseCommand
from django.utils.timezone import make_aware
from datetime import datetime

from obras.models import Obra

class Command(BaseCommand):
    help = "Atualiza os campos concessionarias e municipios das obras criadas após 2025-03-29"

    def handle(self, *args, **options):
        cutoff = make_aware(datetime.fromisoformat("2025-03-29T23:59:59"))
        obras = Obra.objects.filter(criado_em__gt=cutoff)  # ✅ uso correto

        total = obras.count()
        if total == 0:
            self.stdout.write(self.style.WARNING("Nenhuma obra encontrada para atualizar."))
            return

        updated = 0
        for obra in obras:
            obra.concessionarias.clear()
            obra.municipios.clear()

            concessionarias = obra.get_concessionarias()
            municipios = obra.get_municipios()

            obra.concessionarias.add(*concessionarias)
            obra.municipios.add(*municipios)

            updated += 1

        self.stdout.write(self.style.SUCCESS(f"{updated} obras atualizadas com sucesso."))
