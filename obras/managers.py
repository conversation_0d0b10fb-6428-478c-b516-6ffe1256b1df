from django.db import models
from django.db.models.functions import Coalesce

from model_utils.managers import QueryManagerMixin
from model_utils.managers import QueryManager

class CustomManager(QueryManagerMixin):
    def get_queryset(self):
        qs = super().get_queryset().filter(self._q)
        if self._order_by is not None:
            return qs.order_by(*self._order_by)
        return qs

    def has_conc(self):
        qs = super(CustomManager, self).get_queryset()
        return qs