from django.db.models.signals import pre_save, post_save
from django.utils import timezone
from django.dispatch import receiver
from .models import Obra
from django.utils.text import slugify


@receiver(pre_save)
def check_km_final(sender, instance, *args, **kwargs):
    if not issubclass(sender, Obra):
        return
    if instance.km_final is None:
        instance.km_final = instance.km_inicial


@receiver(post_save)
def gen_slug_obras(sender, instance, created, **kwargs):
    if not issubclass(sender, Obra):
        return
    if created:
        #classe = instance.get_sigla().lower()
        # slug=instance.criado_em.strftime("%Y-%m-%d")
        instance.slug = f'ob{instance.pk}'
        instance.save()

@receiver(post_save)
def populate_concs_and_municipios(sender, instance, created, **kwargs):
    if not issubclass(sender, Obra):
        return
    instance.concessionarias.clear()
    instance.municipios.clear()
    concessionarias = instance.get_concessionarias()
    municipios = instance.get_municipios()
    instance.concessionarias.add(*concessionarias)
    instance.municipios.add(*municipios)


@receiver(pre_save)
def set_bloqueio(sender, instance, *args, **kwargs):
    if not issubclass(sender, Obra):
        return
    if instance.pk:
        instance.bloqueio = instance.get_bloqueio(instance.obra_interdicao_set.all())
