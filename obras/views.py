from io import StringIO, BytesIO
import logging
import csv
import datetime
import zipfile
import tempfile
from itertools import chain
from django.db.models import Q
from django.http import HttpResponse, JsonResponse
from django.db.models import Case, When, Sum
from django.db.models.query import Prefetch
from django.core.exceptions import PermissionDenied
from django.template.loader import get_template
from django.http import HttpResponse
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.urls import reverse
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic import ListView, CreateView, UpdateView
from django.forms import inlineformset_factory
from django.http import Http404, HttpResponse
from django.contrib import messages
from django.shortcuts import render, redirect, get_object_or_404
from django import forms
from django.core.paginator import Paginator
from django.db.models import Count
from xhtml2pdf import pisa
from .models import Categoria, Interdicao, Obra, Foto, Atualizacao
from .forms import CategoriaForm, ObraForm, CustomAddAtualizacaoInlineFormSet, FotoValidacaoForm, \
        CustomAddFotoInlineFormSet, CustomAddInterdInlineFormSet
from concessionarias.models import ConcessionariaEquipe
from rodovias.models import Rodovia

import pandas as pd

logger = logging.getLogger("django_file")

def is_member(user):
    if user.groups.filter(name='cci').exists():
        return True
    else:
        raise PermissionDenied()

@login_required
@user_passes_test(is_member)
def list_view(request):
    try:
        _now = datetime.datetime.now()
        # 30 dias atras ou nao encerrada
        _timedelta = datetime.timedelta(days=365)
        past_timedelta = _now - _timedelta
        obras_qs = Obra.objects.filter(
            Q(dt_hr_termino__gt=past_timedelta) | Q(dt_hr_termino__isnull=True)
        )
        obras_qs_resumo = obras_qs
        obras_qs = obras_qs.prefetch_related(
            'categoria', 'rodovia','concessionarias','municipios', 'criado_por', 'atualizado_por')\
                .order_by('-dt_hr_termino', '-atualizado_em')

        tipos = [x[0] for x in obras_qs.values_list('tipo_obra') if x[0] is not None]
        tipos = list(set(tipos))
        tipos.sort()

        context = {}
        context['nro_obras_ativas'] = obras_qs.filter(dt_hr_termino__isnull=True).count()

        context['rodovias'] = list(set([obra.rodovia for obra in obras_qs]))
        context['rodovias'] = sorted(context['rodovias'], key=lambda x: x.codigo, reverse=False)

        context['categorias'] = list(set([obra.categoria for obra in obras_qs if obra.categoria]))
        context['categorias'] = sorted(context['categorias'], key=lambda x: x.nome, reverse=False)

        concessionarias_lst = [obra.concessionarias.all() for obra in obras_qs]
        concessionarias = list(set(list(chain.from_iterable(concessionarias_lst))))
        context['concessionarias'] = sorted(concessionarias, key=lambda x: x.lote, reverse=False)

        municipios_lst = [obra.municipios.all() for obra in obras_qs]
        municipios = list(set(list(chain.from_iterable(municipios_lst))))
        context['municipios'] = sorted(municipios, key=lambda x: x.nome, reverse=False)

        filtro_nome = request.GET["filtro_nome"] if "filtro_nome" in request.GET else None
        filtro_concessionaria = request.GET["filtro_concessionaria"] if "filtro_concessionaria" in request.GET else None
        filtro_rodovia = request.GET["filtro_rodovia"] if "filtro_rodovia" in request.GET else None
        filtro_municipio = request.GET["filtro_municipio"] if "filtro_municipio" in request.GET else None
        filtro_categoria = request.GET["filtro_categoria"] if "filtro_categoria" in request.GET else None
        filtro_tipo = request.GET["filtro_tipo"] if "filtro_tipo" in request.GET else None
        filtro_finalizada = request.GET["filtro_finalizada"] if "filtro_finalizada" in request.GET else None
        filtro_publicada = request.GET["filtro_publicada"] if "filtro_publicada" in request.GET else None
        if filtro_nome:
            try:
                numero_oc = int(filtro_nome)
                obras_qs = Obra.objects.filter(nro_mits=numero_oc).order_by('-pk')
            except Exception as err:
                obras_qs = Obra.objects.filter(slug__iexact=filtro_nome).order_by('-pk')
        if filtro_concessionaria:
            filtro_concessionaria = int(filtro_concessionaria)
            obras_qs = obras_qs.filter(concessionarias__pk=filtro_concessionaria)
        if filtro_rodovia:
            filtro_rodovia = int(filtro_rodovia)
            obras_qs = obras_qs.filter(rodovia=filtro_rodovia)
        if filtro_municipio:
            filtro_municipio = int(filtro_municipio)
            obras_qs = obras_qs.filter(municipios__pk=filtro_municipio)
        if filtro_categoria:
            filtro_categoria = int(filtro_categoria)
            obras_qs = obras_qs.filter(categoria__pk=filtro_categoria)
        if filtro_tipo:
            obras_qs = obras_qs.filter(tipo_obra__icontains=filtro_tipo)
        if filtro_finalizada:
            try:
                filtro_finalizada = int(filtro_finalizada)
                if filtro_finalizada == 0:
                    # FINALIZADA
                    obras_qs = obras_qs.filter(dt_hr_termino__isnull=False)
                if filtro_finalizada == 1:
                    # ATIVA
                    obras_qs = obras_qs.filter(dt_hr_termino__isnull=True)
                if filtro_finalizada == 2:
                    # PARALISADA
                    obras_qs = obras_qs.filter(paralisada=True)
                if filtro_finalizada == 3:
                    # ATRASADA
                    obras_qs = obras_qs.filter(
                        Q(dt_hr_termino_previsto__lt=_now) & Q(dt_hr_termino__isnull=True))
                if filtro_finalizada == 4:
                    # SEM PREVISÃO
                    obras_qs = obras_qs.filter(
                        Q(dt_hr_termino_previsto__isnull=True) & Q(dt_hr_termino__isnull=True))
                if filtro_finalizada == 5:
                    # À VENCER
                    DIAS_A_VENCER = 3
                    dt_target = _now + datetime.timedelta(days=DIAS_A_VENCER)
                    obras_qs = obras_qs.filter(
                        Q(dt_hr_termino_previsto__isnull=False) 
                        & Q(dt_hr_termino_previsto__lt=dt_target)
                        & Q(dt_hr_termino_previsto__gt=_now)
                        & Q(dt_hr_termino__isnull=True)
                    )
            except:
                pass
        if filtro_publicada:
            filtro_publicada = int(filtro_publicada)
            obras_qs = obras_qs.filter(publicada=filtro_publicada)

        context['tipos'] = tipos
        context['filtro_nome'] = filtro_nome
        context['filtro_concessionaria'] = filtro_concessionaria
        context['filtro_rodovia'] = filtro_rodovia
        context['filtro_categoria'] = filtro_categoria
        context['filtro_municipio'] = filtro_municipio
        context['filtro_tipo'] = filtro_tipo
        context['filtro_finalizada'] = filtro_finalizada
        context['filtro_publicada'] = filtro_publicada

        paginator = Paginator(obras_qs, 10)
        page_number = request.GET.get('page')
        for obra in obras_qs:
            obra.mun_tmpl = obra\
                .get_municipios_names_template(
                    obra.municipios.values_list('nome', flat=True))
        context['is_paginated'] = True
        context['paginator'] = paginator
        context['obras'] = paginator.get_page(page_number)
        context['busca_tipos_obras_url'] = request.build_absolute_uri(reverse('busca-tipos-obras'))

        #resumo
        context['resumo'] = obras_qs\
            .filter(Q(dt_hr_termino__isnull=True) & Q(paralisada=False))\
            .values('tipo_obra')\
            .order_by().annotate(qtd=Count('pk'))
        
        resumo_zap = obras_qs_resumo\
            .filter(Q(dt_hr_termino__isnull=True) & Q(paralisada=False))\
            .values('categoria__nome')\
            .order_by().annotate(qtd=Count('pk'))

        context['resumo_zap'] = resumo_zap


        return render(request, 'dashboard/obras/list.html', context)

    except Exception as err:
        logger.error(err)
        raise err

class ObraCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = Obra
    template_name = 'dashboard/obras/create.html'
    form_class = ObraForm

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('obras:detail', kwargs={'obra_slug': self.object.slug })

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.tipo_obra: str = self.object.tipo_obra.upper().strip()
        self.object.criado_por = self.request.user
        self.object.atualizado_por = self.request.user
        self.object.atualizado_em = datetime.datetime.now()
        self.object.save()
        return redirect(self.get_success_url())

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        rodovia = Rodovia.objects.get(pk=self.kwargs['rod_id'])
        context['rodovia'] = rodovia
        context['tabela'] = rodovia.get_concessionarias_km_inicial_final()
        return context

    def get_form_kwargs(self):
        """
        Returns the keyword arguments for instantiating the form.
        """
        form_kwargs  = super(ObraCreateView, self).get_form_kwargs()
        form_kwargs.update({ 'rod_id': self.kwargs['rod_id'] })
        return form_kwargs

class SelectRodoviaListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Rodovia
    paginate_by = 30
    template_name = 'dashboard/obras/select.html'
    context_object_name = 'rodovias'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_queryset(self):
        qs = super(SelectRodoviaListView, self).get_queryset()
        search = self.request.GET.get('search', None)
        if search:
            qs = Rodovia.objects.filter(codigo__icontains=search)
        return qs

    def get_context_data(self, **kwargs):
        context = super(SelectRodoviaListView, self).get_context_data(**kwargs)
        context['obra_slug'] = None
        obra_slug = self.kwargs.get('obra_slug', None)
        if obra_slug:
            context['obra_slug'] = obra_slug
        return context


@login_required
@user_passes_test(is_member)
def edit_view(request, obra_slug, rod_id):
    try:
        obra = Obra.objects.get(slug=obra_slug)
        rodovia_antiga = obra.rodovia.pk
        rodovia = Rodovia.objects.get(pk=rod_id)
        obra.rodovia = rodovia
        form = ObraForm(rod_id, data=None, instance=obra)
        if request.method == 'POST':
            form = ObraForm(rod_id, data=request.POST, files=request.FILES, instance=obra)
            if not form.has_changed() and rod_id == rodovia_antiga:
                messages.add_message(request, messages.ERROR, "Obra não foi atualizada, pois não foram detectadas alterações.")
                return redirect('obras:list')
            if form.is_valid():
                obra.concessionarias.clear()
                obra.municipios.clear()
                obra = form.save(commit=False)
                obra.tipo_obra: str = obra.tipo_obra.upper().strip()
                obra.atualizado_por = request.user
                obra.atualizado_em = datetime.datetime.now()
                obra.save()
                messages.add_message(request, messages.SUCCESS, 'Obra editada com sucesso.')
                return redirect('obras:detail', obra.slug)
            else:
                messages.add_message(request, messages.ERROR, "Ocorreu um erro.")

        context = {
            'rodovia': rodovia,
            'obra': obra,
            'form': form,
            'tabela': rodovia.get_concessionarias_km_inicial_final()
        }
        return render(request, 'dashboard/obras/edit.html', context)
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('obras:list')


@login_required
@user_passes_test(is_member)
def detail_view(request, obra_slug):
    try:
        obra = Obra.objects.prefetch_related(
            'concessionarias', 'municipios', 
            Prefetch('obra_fotos_set',
                queryset=Foto.objects.select_related(
                    'adicionada_por',
                )
            ),
            Prefetch('obra_atualizacao_set',
                queryset=Atualizacao.objects.select_related(
                    'atualizacao_criado_por',
                    'obra_atualizada_por',
                    'atualizacao_apagada_por',
                )
            ),
            Prefetch('obra_interdicao_set',
                queryset=Interdicao.objects.select_related(
                    'rodovia',
                )
            ),
        ).select_related('categoria', 'rodovia', 'criado_por', 'atualizado_por').get(slug=obra_slug)
        anterior = Obra.objects.filter(Q(pk__gt=obra.pk) & Q(publicada=True)).order_by('pk').first()
        proxima = Obra.objects.filter(Q(pk__lt=obra.pk) & Q(publicada=True)).order_by('-pk').first()
        
        context = {}
        context['obra'] = obra
        context['anterior'] = anterior
        context['proxima'] = proxima

        return render(request, 'dashboard/obras/detail.html', context)
    except Obra.DoesNotExist:
        raise Http404("Obra inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('obras:list')


@login_required
@user_passes_test(is_member)
def duplicate_view(request, obra_slug):
    try:
        obra = Obra.objects.prefetch_related('concessionarias', 'municipios',
        'obra_fotos_set', 'obra_interdicao_set', )\
            .select_related('rodovia').get(slug=obra_slug)
        
        context = {}

        context['obra'] = obra

        if request.method == "POST":
            duplicar = True if 'Duplicar' in request.POST.get('salvar', []) else False
            if duplicar:
                _now = datetime.datetime.now()
                nova_obra = obra
                nova_obra.pk = None
                nova_obra.slug = None
                nova_obra.nro_mits = None
                nova_obra.cod_conc = None
                nova_obra.dt_hr_inicio = _now
                nova_obra.dt_hr_conhecimento = _now
                nova_obra.dt_hr_termino_previsto = None
                nova_obra.dt_hr_termino = None
                nova_obra.publicada = False
                nova_obra.criado_por = request.user
                nova_obra.criado_em = _now
                nova_obra.atualizado_por = request.user
                nova_obra.atualizado_em = _now
                nova_obra.save()

                return redirect('obras:edit', obra_slug=nova_obra.slug, rod_id=obra.rodovia.pk)

        return render(request, 'dashboard/obras/duplicate-obra.html', context)
    except Obra.DoesNotExist:
        raise Http404("Obra inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('obras:list')


@login_required
@user_passes_test(is_member)
def add_interdicao_to_obra(request, obra_slug):
    try:
        obra = Obra.objects.get(slug=obra_slug)
        if obra.dt_hr_termino:
            messages.add_message(request, messages.ERROR, "Não é possível alterar esta informação para uma obra encerrada.")
            return redirect('obras:detail', obra_slug)
        OcInterdFormSet = inlineformset_factory(
            Obra, Interdicao, form=CustomAddInterdInlineFormSet,
            extra=3, can_delete=True
        )
        formset = OcInterdFormSet(instance=obra)
        if request.method == "POST":
            formset = OcInterdFormSet(request.POST, instance=obra)
            if not formset.has_changed():
                messages.add_message(request, messages.ERROR, "Obra não foi atualizada, pois não foram detectadas alterações.")
                return redirect('obras:detail', obra_slug)
            if formset.is_valid():
                interdicoes = formset.save(commit=False)
                for interdicao in interdicoes:
                    if not interdicao.rodovia:
                        interdicao.rodovia = obra.rodovia
                    interdicao.save()
                for obj in formset.deleted_objects:
                    obj.delete()
                obra.atualizado_em = datetime.datetime.now()
                obra.atualizado_por = request.user
                obra.save()
                messages.add_message(request, messages.SUCCESS, 'Obra editada com sucesso.')
                return redirect('obras:detail', obra_slug)
            else:
                messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")

        context = {
            'obra': obra,
            'formset': formset,
        }
        return render(request, 'dashboard/obras/add-interdicao.html', context)
    except Obra.DoesNotExist:
        raise Http404("Obra inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('obras:detail', obra_slug)


@login_required
@user_passes_test(is_member)
def add_photo_to_obra(request, obra_slug):
    # try:
        obra = Obra.objects.get(slug=obra_slug)
        fotos = obra.obra_fotos_set.all()
        qtd_fotos_total = fotos.count()
        if qtd_fotos_total > 6:
            messages.add_message(request, messages.ERROR, "O limite de fotos já foi atingido.")
            return redirect('obras:detail', obra_slug)
        if obra.dt_hr_termino:
            messages.add_message(request, messages.ERROR, "Não é possível alterar esta informação para uma obra encerrada.")
            return redirect('obras:detail', obra_slug)
        OcFotoFormSet = inlineformset_factory(
            Obra, Foto, fields=('foto_hr', 'legenda', 'publica'), extra=(6-qtd_fotos_total),
            max_num=6, can_delete=True,
            formset=CustomAddFotoInlineFormSet
        )
        formset = OcFotoFormSet(instance=obra, queryset=Foto.objects.filter(foto_blob__isnull=False))
        if request.method == "POST":
            formset = OcFotoFormSet(request.POST, request.FILES, instance=obra)
            if not formset.has_changed():
                messages.add_message(request, messages.ERROR, "Obra não foi atualizada, pois não foram detectadas alterações.")
                return redirect('obras:detail', obra_slug)
            if formset.is_valid():
                instances = formset.save(commit=False)
                for instance in instances:
                    instance.adicionada_por = request.user
                    instance.save()
                for obj in formset.deleted_objects:
                    obj.delete()
                obra.atualizado_em = datetime.datetime.now()
                obra.atualizado_por = request.user
                obra.save()
                messages.add_message(request, messages.SUCCESS, 'Obra editada com sucesso.')
                return redirect('obras:detail', obra_slug)
            else:
                messages.add_message(request, messages.ERROR, "Verifique os erros abaixo.")

        context = {
            'obra': obra,
            'formset': formset,
        }
        return render(request, 'dashboard/obras/add-foto.html', context)
    # except Obra.DoesNotExist:
    #     raise Http404("Obra inexistente")
    # except Exception as err:
    #     logger.error(err)
    #     messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
    #     return redirect('obras:detail', obra_slug)

@login_required
@user_passes_test(is_member)
def add_atualizacao_to_obra(request, obra_slug):
    try:
        obra = Obra.objects.get(slug=obra_slug)
        if obra.dt_hr_termino:
            messages.add_message(request, messages.ERROR, "Não é possível alterar esta informação para uma obra encerrada.")
            return redirect('obras:detail', obra_slug)
        ObraAtualizacaoFormSet = inlineformset_factory(
            Obra, Atualizacao,
            fields=(
                'atualizacao', 'publica'
            ),
            extra=1,
            can_delete=True,
            formset=CustomAddAtualizacaoInlineFormSet
        )
        formset = ObraAtualizacaoFormSet(request.POST or None, instance=obra)
        if request.method == 'POST':
            if not formset.has_changed():
                messages.add_message(request, messages.ERROR, "Obra não foi atualizada, pois não foram detectadas alterações.")
                return redirect('obras:detail', obra_slug)
            if formset.is_valid():
                atualizacoes = formset.save(commit=False)
                for atualizacao in atualizacoes:
                    _now = datetime.datetime.now()
                    if atualizacao.pk: # atualizacao foi editada pelo usuario
                        atualizacao.obra_atualizada_em = _now
                        atualizacao.obra_atualizada_por = request.user
                        # atualizacao.at_atualizada_posteriormente = True
                    else: # nova atualizacao inserida
                        atualizacao.atualizacao_criado_em = _now
                        atualizacao.atualizacao_criado_por = request.user
                        atualizacao.obra_atualizada_em = _now
                        atualizacao.obra_atualizada_por = request.user
                    # atualizacao.obra_atualizada_por = request.user
                    # # atualiza datetime apenas se for atualização nova
                    # if not atualizacao.obra_atualizada_em:
                    #     atualizacao.obra_atualizada_em = datetime.datetime.now()
                    atualizacao.save()

                # atualizacoes a apagar
                for obj in formset.deleted_objects:
                    obj.atualizacao_apagada_em = datetime.datetime.now()
                    obj.atualizacao_apagada_por = request.user
                    obj.save()

                obra.atualizado_por = request.user
                obra.atualizada_em = datetime.datetime.now()
                obra.save()
                messages.add_message(request, messages.SUCCESS, 'Obra atualizada com sucesso.')
                return redirect('obras:detail', obra_slug)
            else:
                messages.add_message(request, messages.ERROR, "Verifique os campos em vermelho.")

        context = {
            'obra': obra,
            'formset': formset,
        }
        return render(request, 'dashboard/obras/add-atualizacao.html', context)
    except Obra.DoesNotExist:
        raise Http404("Obra inexistente")
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('obras:detail', obra_slug)

@login_required
@user_passes_test(is_member)
def download_csv(request):
    csv_container = []

    #obras
    obras_csv = get_csv_obras()
    csv_container.append(('obras.csv', obras_csv.getvalue(),))

    #atualizacoes
    atualizacoes_csv = get_csv_atualizacoes()
    csv_container.append(('atualizacoes.csv', atualizacoes_csv.getvalue(),))

    #fotos
    fotos_csv = get_csv_fotos()
    csv_container.append(('fotos.csv', fotos_csv.getvalue(),))

    #interdicoes
    interdicoes_csv = get_csv_interdicao()
    csv_container.append(('interdicoes.csv', interdicoes_csv.getvalue(),))


    #zip file
    with tempfile.SpooledTemporaryFile() as tmp:
        with zipfile.ZipFile(tmp, 'w', zipfile.ZIP_DEFLATED) as archive:
            for csv in csv_container:
                archive.writestr(csv[0], csv[1])

        # Reset file pointer
        tmp.seek(0)

        # Write file data to response
        response = HttpResponse(tmp, content_type='application/force-download')
        response['Content-Disposition'] = 'attachment; filename="%s"' % 'portalcci_obras.zip'
        return response


class CategoriaCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = Categoria
    form_class = CategoriaForm
    template_name = 'dashboard/obras/create-categoria.html'
    success_url = ""

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('obras:list-categoria')

    def form_valid(self, form):
        self.object = form.save(commit=False)
        # self.object.nome =  self.object.nome.strip().upper()
        self.object.criado_por = self.request.user
        self.object.criado_em = datetime.datetime.now()
        self.object.save()
        return redirect(self.get_success_url())


class CategoriaUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Categoria
    form_class = CategoriaForm
    template_name = 'dashboard/obras/edit-categoria.html'
    success_url = ""

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('obras:list-categoria')

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.atualizado_por = self.request.user
        self.object.atualizado_em = datetime.datetime.now()
        self.object.save()
        return redirect(self.get_success_url())


class CategoriaListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Categoria
    paginate_by = 20
    template_name = 'dashboard/obras/list-categoria.html'
    context_object_name = 'categorias'

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_context_data(self, **kwargs):
        context = super(CategoriaListView, self).get_context_data(**kwargs)
        return context

    def get_queryset(self):
        queryset = Categoria.objects.all()
        return queryset


@login_required
@user_passes_test(is_member)
def download_excel(request):
    try:
        excel_file = get_excel_obras()
        if not excel_file:
            raise ValueError('não foram encontrados registros de obras')
        response = HttpResponse(
            excel_file, 
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="%s"' % 'intervencoes_viarias.xlsx'
        return response
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        return redirect('obras:list')


def get_csv_atualizacoes():
    atualizacoes_qs = Atualizacao.objects.all()
    atualizacoes_values = [
        'obra__pk', 'atualizacao', 'publica', 'atualizacao_criado_por__first_name', 'atualizacao_criado_em',
        'obra_atualizada_por__first_name', 'obra_atualizada_em', 'atualizacao_apagada_por__first_name', 'atualizacao_apagada_em',
    ]
    atualizacoes_header = [
        'obid', 'atualizacao', 'publica','atualizacao_criado_por__first_name', 'atualizacao_criado_em',
        'obra_atualizada_por__first_name', 'obra_atualizada_em', 'atualizacao_apagada_por__first_name', 'atualizacao_apagada_em',]
    atualizacoes_csv: StringIO = get_csv_buffer_from_qs(atualizacoes_qs, atualizacoes_values, atualizacoes_header)
    return atualizacoes_csv

def get_csv_fotos():
    fotos_qs = Foto.objects.all()
    fotos_values = [
        'obra__pk', 'foto', 'publica', 'legenda', 'adicionada_por__first_name', 'adicionada_em'
    ]
    fotos_header = ['obid', 'link', 'publica', 'legenda', 'adicionada_por', 'adicionada_em']
    fotos_csv: StringIO = get_csv_buffer_from_qs(fotos_qs, fotos_values, fotos_header)
    return fotos_csv

def get_csv_interdicao():
    interdicao_qs = Interdicao.objects.all()
    interdicao_values = [
        'obra__pk', 'sentido', 'faixa', 'dt_hora_inicio', 'dt_hora_termino', 'obs',
    ]
    interdicao_header = ['obid', 'sentido', 'faixa', 'dt_hora_inicio', 'dt_hora_termino', 'obs',]
    interdicao_csv: StringIO = get_csv_buffer_from_qs(interdicao_qs, interdicao_values, interdicao_header)
    return interdicao_csv

def get_csv_obras():
    obras_qs = Obra.objects.prefetch_related(
            'rodovia','concessionarias','municipios',)\
                .order_by('-dt_hr_termino', '-atualizado_em')

    values = [
                'slug',
                'tipo_obra',
                'cod_conc',
                'rodovia__codigo',
                'concessionarias__nome',
                'municipios__nome',
                'km_inicial',
                'km_final',
                'dt_hr_conhecimento',
                'dt_hr_inicio',
                'dt_hr_termino_previsto',
                'dt_hr_termino',
                'publicada',
                'aprovada',
                'origem_comunicacao_cci',
                'pista',
                'sentido',
                'lat_inicial',
                'lng_inicial',
                'lat_final',
                'lng_final',
                'sinalizacao',
                'comunicacao_usuario',
                'recursos_internos',
                'recursos_externos',
                'acoes_operacionais',
                'observacoes_cci',
                'atualizacao_min',
                'nro_mits',
                'arquivo',
                'arquivo_desc',
                'criado_em',
                'criado_por__first_name',
                'atualizado_em',
                'atualizado_por__first_name'
    ]
    header = [
            'slug',
                'tipo_obra',
                'cod_conc',
                'rodovia',
                'concessionarias',
                'municipios',
                'km_inicial',
                'km_final',
                'dt_hr_conhecimento',
                'dt_hr_inicio',
                'dt_hr_termino_previsto',
                'dt_hr_termino',
                'publicada',
                'aprovada',
                'origem_comunicacao_cci',
                'pista',
                'sentido',
                'lat_inicial',
                'lng_inicial',
                'lat_final',
                'lng_final',
                'sinalizacao',
                'comunicacao_usuario',
                'recursos_internos',
                'recursos_externos',
                'acoes_operacionais',
                'observacoes_cci',
                'atualizacao_min',
                'nro_mits',
                'arquivo',
                'arquivo_desc',
                'criado_em',
                'criado_por',
                'atualizado_em',
                'atualizado_por'
        ]
    obras_csv: StringIO = get_csv_buffer_from_qs(obras_qs, values, header)
    return obras_csv

def get_csv_obras():
    obras_qs = Obra.objects.prefetch_related(
            'rodovia','concessionarias','municipios',)\
                .order_by('-dt_hr_termino', '-atualizado_em')

    values = [
                'slug',
                'tipo_obra',
                'cod_conc',
                'rodovia__codigo',
                'concessionarias__nome',
                'municipios__nome',
                'km_inicial',
                'km_final',
                'dt_hr_conhecimento',
                'dt_hr_inicio',
                'dt_hr_termino_previsto',
                'dt_hr_termino',
                'publicada',
                'aprovada',
                'origem_comunicacao_cci',
                'pista',
                'sentido',
                'lat_inicial',
                'lng_inicial',
                'lat_final',
                'lng_final',
                'sinalizacao',
                'comunicacao_usuario',
                'recursos_internos',
                'recursos_externos',
                'acoes_operacionais',
                'observacoes_cci',
                'atualizacao_min',
                'nro_mits',
                'arquivo',
                'arquivo_desc',
                'criado_em',
                'criado_por__first_name',
                'atualizado_em',
                'atualizado_por__first_name'
    ]
    header = [
            'slug',
                'tipo_obra',
                'cod_conc',
                'rodovia',
                'concessionarias',
                'municipios',
                'km_inicial',
                'km_final',
                'dt_hr_conhecimento',
                'dt_hr_inicio',
                'dt_hr_termino_previsto',
                'dt_hr_termino',
                'publicada',
                'aprovada',
                'origem_comunicacao_cci',
                'pista',
                'sentido',
                'lat_inicial',
                'lng_inicial',
                'lat_final',
                'lng_final',
                'sinalizacao',
                'comunicacao_usuario',
                'recursos_internos',
                'recursos_externos',
                'acoes_operacionais',
                'observacoes_cci',
                'atualizacao_min',
                'nro_mits',
                'arquivo',
                'arquivo_desc',
                'criado_em',
                'criado_por',
                'atualizado_em',
                'atualizado_por'
        ]
    obras_csv: StringIO = get_csv_buffer_from_qs(obras_qs, values, header)
    return obras_csv

def get_excel_obras() -> StringIO:
    obras_qs = Obra.objects.prefetch_related(
            'rodovia','concessionarias','municipios')\
                .filter(publicada=True)\
                .filter(dt_hr_termino__isnull=True)\
                .order_by('-atualizado_em')
    if not obras_qs:
        return None
    atualizacoes_qs = Atualizacao.objects\
        .filter(Q(publica=True) & Q(obra__publicada=True) & Q(obra__dt_hr_termino__isnull=True))
    interdicoes_qs = Interdicao.objects\
        .filter(Q(obra__publicada=True) & Q(obra__dt_hr_termino__isnull=True))
    values = [
                'pk',
                'slug',
                'tipo_obra',
                'complemento',
                'concessionarias__nome',
                'municipios__nome',
                'rodovia__codigo',
                'km_inicial',
                'km_final',
                'dt_hr_inicio',
                'dt_hr_termino_previsto',
                'paralisada',
                'origem_comunicacao_cci',
                'pista',
                'sentido',
                'sinalizacao',
                'recursos_internos',
                'acoes_operacionais',
                'observacoes_cci',
    ]
    
    df_obras = pd.DataFrame.from_records(
        obras_qs.values(*values)
    )

    if atualizacoes_qs:
        df_atualizacoes = pd.DataFrame.from_records(atualizacoes_qs.values())
        df_atualizacoes['obra_atualizada_em'] = pd.to_datetime(df_atualizacoes['obra_atualizada_em'], errors='ignore')
        df_atualizacoes['obra_atualizada_em'] = df_atualizacoes['obra_atualizada_em'].dt.strftime('%m/%d/%Y')
        df_atualizacoes['atualizacao_txt'] = '[' + df_atualizacoes['obra_atualizada_em'] + '] ' + df_atualizacoes['atualizacao']
        df_atualizacoes = df_atualizacoes[
            ['atualizacao_txt', 'obra_id']
        ]
        df_atualizacoes = df_atualizacoes.groupby('obra_id')['atualizacao_txt'].apply(lambda row: ' | '.join(row.values.astype(str))).reset_index()
    else:
        df_atualizacoes = pd.DataFrame({'obra_id': [], 'atualizacao_txt': []})

    
    if interdicoes_qs:
        df_interdicoes = pd.DataFrame.from_records(interdicoes_qs.values())
        df_desvios = df_interdicoes[['obra_id', 'desvios']].dropna()
        df_desvios = df_desvios.groupby('obra_id')['desvios'].apply(lambda row: ' | '.join(row.values.astype(str))).reset_index()
    else:
        df_desvios = pd.DataFrame({'obra_id': [], 'desvios': []})

    df_concessionarias = df_obras[['slug', 'concessionarias__nome']]\
        .drop_duplicates()
    df_concessionarias = df_concessionarias.groupby('slug')['concessionarias__nome']\
        .apply(lambda row: ', '.join(row.values.astype(str))).reset_index()

    df_municipios = df_obras[['slug', 'municipios__nome']].drop_duplicates()
    df_municipios = df_municipios.groupby('slug')['municipios__nome']\
        .apply(lambda row: ', '.join(row.values.astype(str))).reset_index()
    
    df_obras = df_obras[
        [
            'pk', 'slug', 'tipo_obra', 'complemento', 'rodovia__codigo', 'km_inicial', 'km_final',
            'dt_hr_inicio', 'dt_hr_termino_previsto', 'paralisada',
            'origem_comunicacao_cci', 'pista', 'sentido', 'sinalizacao',
            'recursos_internos', 'acoes_operacionais', 'observacoes_cci'
        ]
    ]
    df_obras.drop_duplicates(inplace=True)
    df_obras.head()
    
    df_obras = df_obras.merge(df_concessionarias, on='slug', how='left')
    df_obras = df_obras.merge(df_municipios, on='slug', how='left')
    df_obras = df_obras.merge(df_atualizacoes, how='left', left_on='pk', right_on='obra_id')
    df_obras = df_obras.merge(df_desvios, how='left', left_on='pk', right_on='obra_id')

    df_obras['dt_hr_inicio'] = pd.to_datetime(df_obras['dt_hr_inicio'], errors='ignore')
    df_obras['dt_hr_termino_previsto'] = pd.to_datetime(df_obras['dt_hr_termino_previsto'], errors='ignore')

    df_obras['dt_hr_inicio'] = df_obras['dt_hr_inicio'].dt.strftime('%d/%m/%Y')
    df_obras['dt_hr_termino_previsto'] = df_obras['dt_hr_termino_previsto'].dt.strftime('%d/%m/%Y')

    df_obras['paralisada'] = df_obras['paralisada'].apply(lambda v: 'Não' if v == False else 'Sim')

    df_obras = df_obras[
        [
            'slug', 'tipo_obra', 'complemento', 'concessionarias__nome', 
            'municipios__nome', 'rodovia__codigo', 'km_inicial',
            'km_final', 'dt_hr_inicio', 'dt_hr_termino_previsto', 'paralisada',
            'origem_comunicacao_cci', 'pista', 'sentido', 'sinalizacao',
            'recursos_internos', 'acoes_operacionais', 'desvios',
            'atualizacao_txt', 'observacoes_cci', 
            
        ]
    ]
    header = [
            'CÓDIGO', 'TIPO', 'COMPLEMENTO', 'CONCESSIONÁRIA(S)', 
            'MUNICÍPIO(S)', 'RODOVIA', 'M.Q. INICIAL',
            'M.Q. FINAL', 'INÍCIO', 'TÉRMINO (PREVISÃO)', 'PARALISADA?',
            'ORIGEM INF.', 'PISTA', 'SENTIDO', 'SINALIZAÇÃO',
            'RECURSOS', 'AÇÕES OP.', 'DESVIOS', 'ATUALIZAÇÕES', 'OBSERVAÇÕES',
        ]
    df_obras = df_obras.map(lambda value: value.upper() if isinstance(value, str) else value)
    obras_excel: BytesIO = get_excel_buffer_from_df(df_obras, header)
    return obras_excel

def get_csv_buffer_from_qs(qs, values, header):
    df = pd.DataFrame.from_records(
        qs.values(*values)
    )
    df_buffer = StringIO()
    df.to_csv(
        path_or_buf=df_buffer,
        index=False,
        quoting=csv.QUOTE_NONNUMERIC,
        sep="|",
        header=header
    )
    df_buffer.seek(0)
    return df_buffer


def get_excel_buffer_from_df(df, header):
    df_buffer = BytesIO()
    writer = pd.ExcelWriter(df_buffer, engine='xlsxwriter')
    df.to_excel(
        writer, 
        sheet_name='INTERVENÇÕES VIÁRIAS', 
        index=False, 
        header=header
    )
    writer.close()
    df_buffer.seek(0)
    return df_buffer


@login_required
def show_photo_hr(request, oc_slug, pk):
    try:
        foto = get_object_or_404(Foto, pk=pk)
        obra_da_foto = foto.obra
        obra = get_object_or_404(Obra, slug=oc_slug)
        if obra_da_foto.pk != obra.pk:
            raise Http404()

        context = {
            'obra': obra,
            'foto': foto
        }
        return render(request, 'dashboard/obras/foto.html', context)

    except Obra.DoesNotExist:
        raise Http404("Obra inexistente")

    except Foto.DoesNotExist:
        raise Http404("Foto inexistente")
    
    except Exception as err:
        logger.error(err)
        messages.add_message(request, messages.ERROR, "Ocorreu um erro.")
        raise err