#!/bin/bash

# Set variables
DB_NAME="portalcci"
DB_USER="doretto"
OUTPUT_FILE="/home/<USER>/cciapp/cciapp/logs/table_sizes.txt"

# Run the query and save the output
psql -U $DB_USER -d $DB_NAME -c "\
SELECT 
    table_name AS \"Table\", 
    pg_size_pretty(pg_table_size(table_name::regclass)) AS \"Table Size\", 
    pg_size_pretty(pg_indexes_size(table_name::regclass)) AS \"Indexes Size\", 
    pg_size_pretty(pg_total_relation_size(table_name::regclass)) AS \"Total Size\" 
FROM 
    information_schema.tables 
WHERE 
    table_schema = 'public' 
ORDER BY 
    pg_total_relation_size(table_name::regclass) DESC;" > $OUTPUT_FILE

