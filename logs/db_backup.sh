#!/bin/bash

DB_NAME="portalcci"
DB_USER="doretto"
DB_HOST="localhost"
DB_PORT="5432"
BACKUP_DIR="/home/<USER>/cciapp/cciapp/backups"
DATE=$(date +"%Y%m%d%H%M")
BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_backup_$DATE.sql"

mkdir -p $BACKUP_DIR

PGPASSWORD="doritos2023" pg_dump -U $DB_USER -h $DB_HOST -p $DB_PORT -F c -b -v -f $BACKUP_FILE $DB_NAME

# Optional: Remove backups older than 1 days
find $BACKUP_DIR -type f -name "*.sql" -mtime +1 -exec rm {} \;

