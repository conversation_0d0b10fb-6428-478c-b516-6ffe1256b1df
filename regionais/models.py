from django.db import models
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class Regional(models.Model):
    nome = models.CharField(max_length=100, unique=True, verbose_name='Nome')
    tel1 = models.Char<PERSON><PERSON>(_("Telefone 1"), max_length=20, null=True, blank=True)
    tel2 = models.Char<PERSON><PERSON>(_("Telefone 2"), max_length=20, null=True, blank=True)
    logradouro = models.CharField(max_length=255, null=True, blank=True)
    numero = models.CharField(max_length=10, null=True, blank=True)
    bairro = models.CharField(max_length=100, null=True, blank=True)
    cidade = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    cep = models.Char<PERSON>ield(max_length=10, null=True, blank=True)
    uf = models.CharField(max_length=2, null=True, blank=True)
    email = models.EmailField(_("E-mail"), max_length=254, null=True, blank=True)
    diretor = models.Char<PERSON><PERSON>(_("Diretor"), max_length=50, null=True, blank=True)
    email_diretor = models.EmailField(_("E-mail diretor"), max_length=254, null=True, blank=True)

    class Meta:
        ordering = ('nome',)
        verbose_name = 'Regional DER'
        verbose_name_plural  = 'Regionais DER'

    def __str__(self):
        return self.nome

