# Generated by Django 3.2 on 2021-08-29 13:13

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Regional',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.Char<PERSON>ield(max_length=100, unique=True, verbose_name='Nome')),
                ('tel1', models.Char<PERSON>ield(blank=True, max_length=20, null=True, verbose_name='Telefone 1')),
                ('tel2', models.Char<PERSON>ield(blank=True, max_length=20, null=True, verbose_name='Telefone 2')),
                ('logradouro', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('numero', models.Char<PERSON>ield(blank=True, max_length=10, null=True)),
                ('bairro', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('cidade', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('cep', models.Char<PERSON>ield(blank=True, max_length=10, null=True)),
                ('uf', models.CharField(blank=True, max_length=2, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='E-mail')),
                ('diretor', models.CharField(blank=True, max_length=50, null=True, verbose_name='Diretor')),
                ('email_diretor', models.EmailField(blank=True, max_length=254, null=True, verbose_name='E-mail diretor')),
            ],
            options={
                'verbose_name': 'Regional DER',
                'verbose_name_plural': 'Regionais DER',
                'ordering': ('nome',),
            },
        ),
    ]
