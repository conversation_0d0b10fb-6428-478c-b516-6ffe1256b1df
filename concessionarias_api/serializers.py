from rest_framework import serializers
from concessionarias.models import Concessionaria

class ConcessionariaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Concessionaria
        fields = [
            "nome",
            "slug",
            "tipo",
            "fiscalizacao_artesp",
            "nome_fantasia",
            "razao_social",
            "telefone_adm",
            "telefone_cco",
            "logradouro",
            "numero",
            "bairro",
            "cidade",
            "cep",
            "uf",
            "ativa",
            "desc",
            "lote",
            "etapa",
            "contrato",
            "contrato_dt_ass",
            "inicio",
            "termino",
            "edital",
            "grupo",
            "atualizado_em",
        ]