from rest_framework.permissions import BasePermission, SAFE_METHODS
from rest_framework import viewsets
from rest_framework import pagination
from concessionarias.models import Concessionaria
from rest_framework.permissions import IsAdminUser, DjangoModelPermissions
from rest_framework.response import Response
from rest_framework.decorators import action

from .serializers import ConcessionariaSerializer
from rodovias_api.serializers import RodoviaSerializer

class ReadOnly(BasePermission):
    def has_permission(self, request, view):
        return request.method in SAFE_METHODS


class LargeResultsSetPagination(pagination.LimitOffsetPagination):
    default_limit = 10_000
    limit_query_param = 'limit'
    offset_query_param = 'offset'
    max_limit = 10_000

# class Concessionaria(viewsets.ModelViewSet):
#   permission_classes = [DjangoModelPermissions]
#   serializer_class = ConcessionariaSerializer
#   queryset = Concessionaria.objects.all()


class ConcessionariaViewSet(viewsets.ModelViewSet):
  permission_classes = [DjangoModelPermissions]
  queryset = Concessionaria.objects.all()
  serializer_class = ConcessionariaSerializer

  @action(detail=True, methods=['get'])
  def rodovias(self, request, pk=None):
    concessionaria = self.get_object()
    serializer = RodoviaSerializer(concessionaria.rodovias.all(), many=True)
    return Response(serializer.data)


class ConcessionariaPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    pagination_class = LargeResultsSetPagination
    serializer_class = ConcessionariaSerializer

    def get_queryset(self):
        qs = Concessionaria.objects.all()
        return qs