import logging
import datetime
from django.core.exceptions import PermissionDenied
from django.urls import reverse
from django.shortcuts import redirect
from django.views.generic import ListView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin


from .models import Painel
from .forms import PainelForm

logger = logging.getLogger("django_file")


def is_member(user):
    if user.groups.filter(name='cci').exists():
        return True
    else:
        raise PermissionDenied()


class PainelListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Painel
    ordering = ['-publicado', '-dt_hr_atualizacao', ]
    paginate_by = 10
    template_name = 'dashboard/paineis/list.html'
    context_object_name = 'paineis'

    def get_context_data(self, **kwargs):
        context = super(PainelListView, self).get_context_data(**kwargs)
        context['filtro_nome'] = self.request.GET.get('filtro_nome', None)
        context['filtro_publicado'] = self.request.GET.get('filtro_publicado', None)
        context['filtro_auth'] = self.request.GET.get('filtro_auth', None)
        return context
    

    def get_queryset(self):
        queryset = Painel.objects.all()
        nome = self.request.GET.get('filtro_nome', None)
        publicado = self.request.GET.get('filtro_publicado', None)
        auth = self.request.GET.get('filtro_auth', None)

        if nome:
            queryset = queryset.filter(nome__icontains=nome)

        queryset = self.check_publicado(queryset, publicado)

        queryset = self.check_auth(queryset, auth)

        return queryset.order_by('-publicado', '-dt_hr_atualizacao',)

    def check_auth(self, queryset, auth):
        try:
            auth = int(auth)
        except:
            return queryset
        if auth == 1:
            return queryset.filter(apenas_usr_autenticado=True)
        if auth == 2:
            return queryset.filter(apenas_usr_autenticado=False)
        return queryset

    def check_publicado(self, queryset, publicado):
        try:
            publicado = int(publicado)
        except:
            return queryset
        if publicado == 1:
            return queryset.filter(publicado=True)
        if publicado == 2:
            return queryset.filter(publicado=False)
        return queryset

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()


class PainelCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    model = Painel
    form_class = PainelForm
    template_name = 'dashboard/paineis/create.html'
    success_url = ""

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('paineis:list')

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.atualizado_por = self.request.user
        self.object.save()
        return redirect(self.get_success_url())


class PainelUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    model = Painel
    form_class = PainelForm
    template_name = 'dashboard/paineis/update.html'
    success_url = ""

    def test_func(self):
        if self.request.user.groups.filter(name='cci').exists():
            return True
        else:
            raise PermissionDenied()

    def get_success_url(self):
        return reverse('paineis:list')

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.atualizado_por = self.request.user
        self.object.atualizado_em = datetime.datetime.now()
        self.object.save()
        form.save_m2m()
        return redirect(self.get_success_url())
