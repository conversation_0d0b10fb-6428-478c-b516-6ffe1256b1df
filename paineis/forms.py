from django import forms
from django.forms import ModelForm
from django.core.exceptions import ValidationError
from .models import Painel
from django.contrib.auth.models import Group


class PainelForm(ModelForm):
    grupos_acesso = forms.ModelMultipleChoiceField(
        queryset=Group.objects.all().order_by('name'),
        widget=forms.CheckboxSelectMultiple,
        required=False,
    )
    class Meta:
        model = Painel
        fields = [
            'nome', 'tipo', 'icone', 'link', 'exibir_obs', 'obs',
            'exibir_dt_hr_atualizacao', 'dt_hr_atualizacao', 'publicado',
            'url_download', 'apenas_usr_autenticado', 'frequencia_atualizacao',
            'link_alternativo_url', 'link_alternativo_desc', 'link_alternativo_titulo',
            'link_alternativo_usr_autenticado', 'grupos_acesso'
        ]
        widgets = {
            'dt_hr_atualizacao': forms.DateTimeInput(
                format='%Y-%m-%dT%H:%M',
                attrs={'type': 'datetime-local'}
            ),
            # 'grupos_acesso': forms.ModelMultipleChoiceField(
            #     queryset=Group.objects.all().order_by('name'),
            #     widget=forms.CheckboxSelectMultiple
            # )
        }