# Generated by Django 3.2 on 2021-11-15 10:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Painel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nome', models.CharField(max_length=50, verbose_name='Nome do painel')),
                ('icone', models.ImageField(upload_to='media/images/paineis/icones', verbose_name='Ícone')),
                ('link', models.URLField(blank=True, null=True, verbose_name='Link')),
                ('dt_hr_atualizacao', models.DateTimeField(verbose_name='Atualizado em')),
                ('publicado', models.BooleanField(default=True, verbose_name='Publicado')),
                ('criado_em', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('atualizado_em', models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')),
                ('atualizado_por', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='painel_atualizado_por', to=settings.AUTH_USER_MODEL, verbose_name='Atualizado por')),
            ],
            options={
                'verbose_name': 'Painel',
                'verbose_name_plural': 'Painéis',
            },
        ),
    ]
