# Generated by Django 3.2 on 2023-05-07 12:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paineis', '0008_alter_painel_frequencia_atualizacao'),
    ]

    operations = [
        migrations.AddField(
            model_name='painel',
            name='apenas_cci',
            field=models.BooleanField(default=False, help_text='Sim, este painel será visível apenas para o CCI', verbose_name='Visível apenas para o CCI'),
        ),
        migrations.AlterField(
            model_name='painel',
            name='frequencia_atualizacao',
            field=models.CharField(blank=True, choices=[('A CADA 1h', 'A CADA 1h'), ('A CADA 2h', 'A CADA 2h'), ('A CADA 3h', 'A CADA 3h'), ('A CADA 6h', 'A CADA 6h'), ('A CADA 8h', 'A CADA 8h'), ('A CADA 12h', 'A CADA 12h'), ('DIÁRIA', 'DIÁRIA'), ('SEMANAL', 'SEMANAL'), ('MENSAL', 'MENSAL'), ('BIMESTRAL', 'BIMESTRAL'), ('TRIMESTRAL', 'TRIMESTRAL'), ('SEMESTRAL', 'SEMESTRAL'), ('ANUAL', 'ANUAL')], default='SEMANAL', max_length=255, null=True, verbose_name='Frequência de atualização'),
        ),
    ]
