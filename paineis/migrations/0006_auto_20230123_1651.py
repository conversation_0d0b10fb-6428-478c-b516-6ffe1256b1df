# Generated by Django 3.2 on 2023-01-23 16:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paineis', '0005_auto_20221222_0743'),
    ]

    operations = [
        migrations.AlterField(
            model_name='painel',
            name='exibir_dt_hr_atualizacao',
            field=models.BooleanField(default=True, help_text='Sim, exibir data e hora de atualização', verbose_name='Exibir data hora de atualização'),
        ),
        migrations.AlterField(
            model_name='painel',
            name='frequencia_atualizacao',
            field=models.CharField(blank=True, choices=[('HORÁRIA', 'HORÁRIA'), ('DIÁRIA', 'DIÁRIA'), ('SEMANAL', 'SEMANAL'), ('MENSAL', 'MENSAL'), ('BIMESTRAL', 'BIMESTRAL'), ('TRIMESTRAL', 'TRIMESTRAL'), ('SEMESTRAL', 'SEMESTRAL'), ('ANUAL', 'ANUAL')], default='SEMANAL', max_length=255, null=True, verbose_name='Frequência de atualização'),
        ),
    ]
