# Generated by Django 3.2 on 2022-12-22 07:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paineis', '0004_painel_apenas_usr_autenticado'),
    ]

    operations = [
        migrations.AddField(
            model_name='painel',
            name='frequencia_atualizacao',
            field=models.CharField(blank=True, choices=[('HORÁRIA', 'HORÁRIA'), ('DIÁRIA', 'DIÁRIA'), ('SEMANAL', 'SEMANAL'), ('MENSAL', 'MENSAL'), ('BIMESTRAL', 'BIMESTRAL'), ('TRIMESTRAL', 'TRIMESTRAL'), ('SEMESTRAL', 'SEMESTRAL'), ('ANUAL', 'ANUAL')], default='SEMANAL', max_length=255, null=True, verbose_name='Atualização'),
        ),
        migrations.AlterField(
            model_name='painel',
            name='apenas_usr_autenticado',
            field=models.BooleanField(default=False, help_text='Sim, este painel será visível apenas para usuários autenticados', verbose_name='Visível apenas para usuários autenticados'),
        ),
        migrations.AlterField(
            model_name='painel',
            name='exibir_obs',
            field=models.BooleanField(default=False, help_text='Sim, exibir observações', verbose_name='Exibir obs.'),
        ),
        migrations.AlterField(
            model_name='painel',
            name='publicado',
            field=models.BooleanField(default=True, help_text='Sim, publicar este painel', verbose_name='Publicado'),
        ),
    ]
