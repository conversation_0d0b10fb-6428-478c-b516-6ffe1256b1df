# Generated by Django 3.2 on 2023-05-14 07:43

from django.db import migrations, models
from django.utils.text import slugify
from django.db.models import F

def forwards_func(apps, schema_editor):
    Painel = apps.get_model("paineis", "Painel")
    for painel in Painel.objects.all():
        painel.slug = painel.nome
        painel.slug = slugify(painel.slug)
        painel.save()
    # Painel.objects.update(slug=slugify(F("nome")))

def reverse_func(apps, schema_editor):
    pass

class Migration(migrations.Migration):

    dependencies = [
        ('paineis', '0010_painel_click_count'),
    ]

    operations = [
        migrations.AddField(
            model_name='painel',
            name='slug',
            field=models.SlugField(max_length=100, verbose_name='slug', null=True),
            preserve_default=False,
        ),
        migrations.RunPython(forwards_func, reverse_func),
        migrations.AlterField(
            model_name='painel',
            name='slug',
            field=models.SlugField(max_length=100, verbose_name='slug'),
        ),
    ]
