# Generated by Django 4.2.5 on 2024-04-01 13:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paineis', '0015_painel_link_alternativo_desc_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='painel',
            name='link_alternativo_titulo',
            field=models.CharField(blank=True, default='Painel(2)', help_text='Ex.: Painel (2) ou Dados (2)', max_length=50, null=True, verbose_name='Título alternativo'),
        ),
        migrations.AddField(
            model_name='painel',
            name='link_alternativo_usr_autenticado',
            field=models.BooleanField(default=True, help_text='Sim, esta opção será visível apenas para usuários autenticados', verbose_name='Alternativo visível apenas para usuários autenticados'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='painel',
            name='link_alternativo_desc',
            field=models.CharField(blank=True, help_text='Ex.: painel/diretório alternativo apenas p/ download de dados', max_length=50, null=True, verbose_name='Descrição'),
        ),
        migrations.AlterField(
            model_name='painel',
            name='link_alternativo_url',
            field=models.URLField(blank=True, null=True, verbose_name='Link p/ outra opção painel ou dados'),
        ),
    ]
