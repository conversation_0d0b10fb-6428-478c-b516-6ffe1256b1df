
from django.contrib import admin
from django.utils import timezone

from accounts.models import Account
from .models import Painel

class UserAdminInline(admin.StackedInline):
    model = Account

class PainelAdmin(admin.ModelAdmin):
    list_display = ['nome', 'publicado', 'criado_em', 'atualizado_em']
    list_filter = ['publicado', 'criado_em', 'atualizado_em']
    search_fields = ['nome', 'obs']
    filter_horizontal = ['grupos_acesso', 'usuarios_acesso']
    model = Painel

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return request.user.groups.filter(name='adms').exists()

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return request.user.groups.filter(name='adms').exists()

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        if is_superuser:
            return True
        else:
            return False
            # return request.user.groups.filter(name='cci').exists()

    def save_model(self, request, obj, form, change):
        obj.atualizado_em = timezone.now()
        obj.save()


admin.site.register(Painel, PainelAdmin)