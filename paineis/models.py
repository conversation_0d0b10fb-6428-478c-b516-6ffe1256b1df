from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.template.defaultfilters import slugify
from django.contrib.auth.models import Group

class DadosChoice(models.TextChoices):
    PAINEL = u'PAINEL', 'Painel'
    DIRETORIO = u'DIRETORIO', '<PERSON>ret<PERSON>rio'
    ARQUIVO = u'ARQUIVO', 'Arquivo'

class Painel(models.Model):
    FREQ_ATUALIZACAO = [
        # ('HORÁRIA', 'HORÁRIA',),
        ('A CADA 1h', 'A CADA 1h',),
        ('A CADA 2h', 'A CADA 2h',),
        ('A CADA 3h', 'A CADA 3h',),
        ('A CADA 6h', 'A CADA 6h',),
        ('A CADA 8h', 'A CADA 8h',),
        ('A CADA 12h', 'A CADA 12h',),
        ('DIÁRIA', 'DIÁRIA',),
        ('SEMANAL', 'SEMANAL',),
        ('MENSAL', 'MENSAL',),
        ('BIMESTRAL', 'BIMESTRAL',),
        ('TRIMESTRAL', 'TRIMESTRAL',),
        ('SEMESTRAL', 'SEMESTRAL',),
        ('ANUAL', 'ANUAL',),
    ]
    nome = models.CharField(_("Nome do painel"), max_length=50)
    icone = models.ImageField(
        _("Ícone"), upload_to="images/paineis/icones", null=False, blank=False, max_length=100
    )
    link = models.URLField(_("Link"), max_length=200, null=True, blank=True)
    url_download = models.URLField(_("Link download dos dados"), max_length=200, null=True, blank=True)
    exibir_obs = models.BooleanField(_("Exibir obs."), help_text="Sim, exibir observações", default=False)
    apenas_usr_autenticado = models.BooleanField(_("Visível apenas para usuários autenticados"), help_text="Sim, este painel será visível apenas para usuários autenticados", default=False)
    apenas_cci = models.BooleanField(_("Visível apenas para o CCI"), help_text="Sim, este painel será visível apenas para o CCI", default=False)
    obs = models.CharField(_("Observação"), max_length=255, null=True, blank=True)
    exibir_dt_hr_atualizacao = models.BooleanField(_("Exibir data hora de atualização"), 
        help_text="Sim, exibir data e hora de atualização", default=True)
    dt_hr_atualizacao = models.DateTimeField(_("Atualizado em"), auto_now=False, auto_now_add=False)
    publicado = models.BooleanField(_("Publicado"), help_text="Sim, publicar este painel", default=True)
    frequencia_atualizacao = models.CharField(
        _("Frequência de atualização"), choices=FREQ_ATUALIZACAO, max_length=255,
        default="SEMANAL", blank=True, null=True
    )
    slug = models.SlugField(_("slug"), max_length=100, unique=True)
    click_count = models.IntegerField(default=0)
    tipo = models.CharField(
        _("Tipo"), choices=DadosChoice.choices, max_length=255,
        default=DadosChoice.PAINEL,
    )

    link_alternativo_url = models.URLField(_("Link p/ outra opção painel ou dados"), max_length=200, null=True, blank=True)
    link_alternativo_desc = models.CharField(
        _("Descrição"), 
        max_length=50, 
        help_text='Ex.: painel/diretório alternativo apenas p/ download de dados',
        blank=True,
        null=True,
    )
    link_alternativo_titulo = models.CharField(
        _("Título alternativo"), 
        max_length=50, 
        help_text='Ex.: Painel (2) ou Dados (2)',
        null=True,
        blank=True,
        default='Painel(2)'
    )
    link_alternativo_usr_autenticado = models.BooleanField(
        _("Alternativo visível apenas para usuários autenticados"), 
        help_text="Sim, esta opção será visível apenas para usuários autenticados", 
        default=True)
    
    grupos_acesso = models.ManyToManyField(Group, verbose_name='Grupos', related_name='paineis')
    usuarios_acesso = models.ManyToManyField(settings.AUTH_USER_MODEL, verbose_name='Usuários', related_name='painel_usuarios', blank=True)

    criado_em = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    atualizado_em = models.DateTimeField(auto_now=True, null=True, verbose_name='Atualizado em')
    atualizado_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name='Atualizado por', related_name='painel_atualizado_por'
    )

    class Meta:
        ordering: ['-id']
        verbose_name = 'Painel'
        verbose_name_plural  = 'Painéis'

    def __repr__(self):
        return f'Painel {self.nome} <{self.pk}>'

    def __str__(self):
        return f'{self.nome}'
    
    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('painel-view-and-count', args=[str(self.slug)])
    
    def save(self, *args, **kwargs):  # new
        if not self.slug:
            self.slug = slugify(self.nome)
        return super().save(*args, **kwargs)

    def get_bg_color(self):
        if self.tipo == DadosChoice.ARQUIVO:
            return 'bg-green-900'
        if self.tipo == DadosChoice.DIRETORIO:
            return 'bg-pink-900'
        return 'bg-indigo-900'


    def get_text_color(self):
        if self.tipo == DadosChoice.ARQUIVO:
            return 'text-green-100'
        if self.tipo == DadosChoice.DIRETORIO:
            return 'text-pink-100'
        return 'text-indigo-100'
    
    def get_tipo(self):
        if self.tipo == DadosChoice.ARQUIVO:
            return 'ARQUIVO'
        if self.tipo == DadosChoice.DIRETORIO:
            return 'DIRETÓRIO'
        return 'PAINEL'
    