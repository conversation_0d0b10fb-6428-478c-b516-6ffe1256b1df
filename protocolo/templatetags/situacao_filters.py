# protocolo/templatetags/situacao_filters.py
from django import template

register = template.Library()

# Define the mapping dictionary
SITUACAO_OBRA_MAPPING = {
    "1": "Cancelada",
    "2": "Pendente",
    "3": "Não Iniciada",
    "4": "Iniciada",
    "5": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "6": "Não Concluída",
    "7": "A ser iniciada",
}

@register.filter(name='map_situacao')
def map_situacao(value):
    """
    Map the numeric situacao value to its corresponding string.
    
    Args:
        value: The numeric or string value representing the situação da obra.
        
    Returns:
        The mapped string or an empty string if the value is not found.
    """
    return SITUACAO_OBRA_MAPPING.get(str(value), "")
