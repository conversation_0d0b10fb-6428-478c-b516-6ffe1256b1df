import datetime
import hashlib
import json
import os
import uuid
from dotenv import load_dotenv
from django.db import models
from django.core.validators import EmailValidator
from concessionarias.models import Concessionaria
from rodovias.models import Rodovia

load_dotenv()

class Keyword(models.Model):
    name = models.CharField(max_length=50, unique=True)
    
    def __str__(self):
        return self.name


class AtivoManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(ativo=True)



class Protocolo(models.Model):
    class ObraModelChoices(models.TextChoices):
        PONTUAL = 'PONTUAL', 'PONTUAL',
        LINEAR = 'LINEAR', 'LINEAR',

    uid = models.UUIDField(default=uuid.uuid4, db_index=True, editable=False)
    protocolo = models.CharField(
        max_length=50,
        unique=True,
        db_index=True,
        editable=False,
        blank=True,
        null=True,
        verbose_name="Protocolo",
    )
    concessionaria = models.ForeignKey(
        Concessionaria,
        on_delete=models.SET_NULL,
        null=True,
        blank=False,
        verbose_name="Concessionária"
    )
    interessado = models.ForeignKey(
        "Interessado",
        on_delete=models.SET_NULL,
        null=True,
        blank=False,
        verbose_name="Interessado"
    )
    manif_proc_em_andamento = models.BooleanField(
        "MANIFESTAÇÃO DE PROCESSO EM ANDAMNETO",
        default=False,
        help_text=""
    )
    processo_sei_ref = models.CharField(
        max_length=25,
        null=True,
        blank=True,
        verbose_name="Nro. Processo SEI referencial"
    )
    processo_sei = models.CharField(
        max_length=25,
        null=True,
        blank=True,
        verbose_name="Nro. Processo SEI"
    )
    tipo_documento = models.ForeignKey(
        "TipoDocumento",
        on_delete=models.SET_NULL,
        null=True,
        blank=False,
        verbose_name="Tipo de documento"
    )
    encaminhado_por = models.ForeignKey(
        "EncaminhadoPor",
        on_delete=models.SET_NULL,
        null=True,
        blank=False,
        verbose_name="Encaminhado por"
    )
    destinatario = models.ForeignKey(
        "Destinatario",
        on_delete=models.SET_NULL,
        null=True,
        blank=False,
        verbose_name="Destinatário"
    )
    
    assunto = models.ForeignKey(
        "Assunto",
        on_delete=models.SET_NULL,
        null=True,
        blank=False,
        verbose_name="Assunto"
    )
    
    tipo_obra = models.ForeignKey(
        "TipoObra",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Tipo Implantação"
    )
    
    obra_modo_repr = models.CharField(
        "Modo de Implantação",
        choices=ObraModelChoices.choices,
        null=True,
        blank=True
    )
    
    item_codigo = models.ForeignKey(
        "DbObrasInformix",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Código do Item",
        help_text="Código do item na lista de obras/implantações"
    )
    
    descricao = models.TextField("Descrição", max_length=256, null=True, blank=True)
    
    cod_doc = models.CharField(
        "Código do Documento", max_length=80, null=True, blank=True)
    
    modifications_counter = models.PositiveSmallIntegerField(
        "Nro. Atualizações", default=0)
    
    rodovia = models.ForeignKey(
        Rodovia,
        verbose_name='Rodovia', related_name='protocolos', null=True, blank=True, on_delete=models.SET_NULL
    )
    km_inicial = models.DecimalField(
        "KM inicial", max_digits=8, decimal_places=3, null=True, blank=True)
    km_final = models.DecimalField(
        "KM final", max_digits=8, decimal_places=3, null=True, blank=True)

    valor = models.DecimalField(
        max_digits=25,
        decimal_places=2,
        verbose_name="Valor estimado(R$)",
        null=True,
        blank=True
    )
    data_base = models.DateField(verbose_name="Data-base", null=True, blank=True)
    nova_data_pleito_inicio = models.DateField(verbose_name="Nova data pleiteada para início", null=True, blank=True)
    nova_data_pleito_termino = models.DateField(verbose_name="Nova data pleiteada para término", null=True, blank=True)
    latitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        null=True,
        blank=True,
        verbose_name="Latitude",
        help_text="Ex: -23.550520"
    )
    
    longitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        null=True,
        blank=True,
        verbose_name="Longitude",
        help_text="Ex: -46.633308"
    )
    user_name = models.CharField(max_length=150, verbose_name="Nome")
    user_email = models.EmailField(
        validators=[EmailValidator()],
        verbose_name="E-mail"
    )
    ip_address = models.GenericIPAddressField(
        verbose_name="End. IP",
        null=True,
        blank=True
    )
    hash = models.CharField(max_length=64, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At")

    def __str__(self):
        return self.protocolo


# class ItemCodigo(models.Model):
#     # GERENCIAS = [
#     #     ('OAE', 'OAE'),
#     #     ('PAVIMENTO', 'PAVIMENTO'),
#     #     ('GOE', 'GOE'),
#     #     ('GSS', 'GSS'),
#     # ]
#     # gerencia = models.CharField(
#     #     "Gerência", choices=GERENCIAS, max_length=255, blank=True, null=True
#     # )
#     codigo = models.CharField(max_length=100, unique=True, verbose_name="Código")
#     description = models.CharField(max_length=255, verbose_name="Descrição")
#     ativo = models.BooleanField("Ativo", default=True, help_text='Obra ativa')
#     created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")
#     updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At")
    
#     objects = AtivoManager()  # Default manager - will only return active records
#     all_objects = models.Manager()  # Secondary manager - will return all records

#     def __str__(self):
#         return f'{self.codigo} - {self.description}'.upper()

#     class Meta:
#         ordering: ['codigo']


class TipoDocumento(models.Model):
    description = models.CharField(max_length=100, unique=True, verbose_name="Tipo de Documento")
    ativo = models.BooleanField("Ativo", default=True, help_text='Opção ativa')
    
    objects = AtivoManager()  # Default manager - will only return active records
    all_objects = models.Manager()  # Secondary manager - will return all records

    def __str__(self):
        return self.description

    class Meta:
        ordering: ['description']


class Interessado(models.Model):
    description = models.CharField(max_length=100, unique=True, verbose_name="Interessado")
    ativo = models.BooleanField("Ativo", default=True, help_text='Opção ativa')
    
    objects = AtivoManager()  # Default manager - will only return active records
    all_objects = models.Manager()  # Secondary manager - will return all records

    def __str__(self):
        return self.description
    
    class Meta:
        ordering: ['description']


class EncaminhadoPor(models.Model):
    description = models.CharField(max_length=100, unique=True, verbose_name="Encaminhado por")
    ativo = models.BooleanField("Ativo", default=True, help_text='Opção ativa')
    
    objects = AtivoManager()  # Default manager - will only return active records
    all_objects = models.Manager()  # Secondary manager - will return all records

    def __str__(self):
        return self.description
    
    class Meta:
        ordering: ['description']


class Destinatário(models.Model):
    description = models.CharField(max_length=100, unique=True, verbose_name="Destinatário")
    ativo = models.BooleanField("Ativo", default=True, help_text='Opção ativa')
    
    objects = AtivoManager()  # Default manager - will only return active records
    all_objects = models.Manager()  # Secondary manager - will return all records

    def __str__(self):
        return self.description
    
    class Meta:
        ordering: ['description']
    


class Assunto(models.Model):
    description = models.CharField(max_length=100, unique=True, verbose_name="Assunto")
    ativo = models.BooleanField("Ativo", default=True, help_text='Opção ativa')
    
    objects = AtivoManager()  # Default manager - will only return active records
    all_objects = models.Manager()  # Secondary manager - will return all records

    def __str__(self):
        return self.description
    
    class Meta:
        ordering: ['description']


class TipoObra(models.Model):
    description = models.CharField(max_length=100, unique=True, verbose_name="Tipo de obra")
    ativo = models.BooleanField("Ativo", default=True, help_text='Opção ativa')
    
    objects = AtivoManager()  # Default manager - will only return active records
    all_objects = models.Manager()  # Secondary manager - will return all records

    def __str__(self):
        return self.description
    
    class Meta:
        ordering: ['description']


class Destinatario(models.Model):
    description = models.CharField(max_length=100, unique=True, verbose_name="Destinatário")
    ativo = models.BooleanField("Ativo", default=True, help_text='Opção ativa')
    
    objects = AtivoManager()  # Default manager - will only return active records
    all_objects = models.Manager()  # Secondary manager - will return all records

    def __str__(self):
        return self.description

    class Meta:
        ordering: ['description']


class DbObrasInformix(models.Model):
    codigo_obra = models.IntegerField(null=False, blank=False,)
    codigo_lote = models.IntegerField(null=True, blank=True)
    concessionaria = models.CharField(max_length=50, null=True, blank=True)
    cdrodovia = models.CharField(max_length=20, null=True, blank=True)
    nmrodovia = models.CharField(max_length=50, null=True, blank=True)
    codtrecho = models.IntegerField(null=True, blank=True)
    kminicial = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True)
    kmfinal = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True)
    descricao_obra = models.CharField(max_length=255, null=True, blank=True)
    codigo_tipo_servico = models.IntegerField(null=True, blank=True)
    descricao_tipo_servico = models.CharField(max_length=75, null=True, blank=True)
    situacao_obra = models.IntegerField(null=False, blank=False)
    progresso_obra = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    dtreceb = models.DateTimeField(null=True, blank=True)
    nrcstreceb = models.CharField(max_length=10, null=True, blank=True)
    nrprocessoreceb = models.CharField(max_length=25, null=True, blank=True)
    nrprotocoloreceb = models.CharField(max_length=25, null=True, blank=True)
    nraceiteprov = models.CharField(max_length=25, null=True, blank=True)
    dtaceiteprov = models.DateTimeField(null=True, blank=True)
    nraceitedef = models.CharField(max_length=25, null=True, blank=True)
    dtaceitedef = models.DateTimeField(null=True, blank=True)
    cdcgc = models.CharField(max_length=14, null=True, blank=True)
    dsobsemptecno = models.CharField(max_length=255, null=True, blank=True)
    cdgrd = models.IntegerField(null=False, blank=False)
    cddocprojeto = models.IntegerField(null=False, blank=False)
    codop = models.IntegerField(null=True, blank=True)
    dtaalter = models.DateTimeField(null=True, blank=True)
    situacao_projeto = models.IntegerField(null=True, blank=True)
    codigo_item_servico = models.CharField(max_length=15, null=True, blank=True)
    dtentregaproj = models.DateTimeField(null=True, blank=True)
    dtaprovaproj = models.DateTimeField(null=True, blank=True)
    dsobs = models.CharField(max_length=255, null=True, blank=True)
    codigotota = models.IntegerField(null=True, blank=True)
    flprojeto = models.SmallIntegerField(null=True, blank=True)
    nrultadequa = models.IntegerField(null=True, blank=True)
    dtprocessorec = models.DateTimeField(null=True, blank=True)
    dttermoaceiterec = models.DateTimeField(null=True, blank=True)
    dtasbuiltprev = models.DateTimeField(null=True, blank=True)
    dtasbuiltent = models.DateTimeField(null=True, blank=True)
    dtvistiniobra = models.DateTimeField(null=True, blank=True)
    dtvistfimobra = models.DateTimeField(null=True, blank=True)
    dtiniobra = models.DateTimeField(null=True, blank=True)
    dtfimobra = models.DateTimeField(null=True, blank=True)
    fllicambiental = models.SmallIntegerField(null=True, blank=True)
    fldesapropria = models.SmallIntegerField(null=True, blank=True)
    vlextreal = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True)
    flpendrima = models.SmallIntegerField(null=True, blank=True)
    flpenddesaprop = models.SmallIntegerField(null=True, blank=True)
    flpendreloca = models.SmallIntegerField(null=True, blank=True)
    flpendalterdata = models.SmallIntegerField(null=True, blank=True)
    flpendjudicial = models.SmallIntegerField(null=True, blank=True)
    flfiltro = models.SmallIntegerField(null=True, blank=True)
    flincluidoprog = models.SmallIntegerField(null=True, blank=True)
    flemanalise = models.IntegerField(null=True, blank=True)
    nroae = models.IntegerField(null=True, blank=True)
    floae = models.IntegerField(null=True, blank=True)
    dspseudonimoobra = models.CharField(max_length=100, null=True, blank=True)
    cdrodoviaant = models.CharField(max_length=20, null=True, blank=True)
    lixo = models.CharField(max_length=15, null=True, blank=True)
    cdrodder = models.CharField(max_length=20, null=True, blank=True)
    vlpercexec = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    data_programada_inicio = models.DateTimeField(null=True, blank=True)
    data_programada_fim = models.DateTimeField(null=True, blank=True)
    data_analise_inicio = models.DateTimeField(null=True, blank=True)
    data_analise_fim = models.DateTimeField(null=True, blank=True)
    data_execucao_inicio = models.DateTimeField(null=True, blank=True)
    data_execucao_fim = models.DateTimeField(null=True, blank=True)
    data_prevista_inicio = models.DateField(null=True, blank=True)
    data_prevista_termino = models.DateField(null=True, blank=True)
    observacao = models.TextField(null=True, blank=True)
    descricao = models.TextField(null=True, blank=True)
    prioritaria = models.BooleanField(null=True, blank=True)
    valor_total_obra = models.DecimalField(max_digits=19, decimal_places=4, null=True, blank=True)
    valor_total_obra_formatado = models.CharField(max_length=128, null=True, blank=True)

    class Meta:
        verbose_name = "DbObraInformix"
        verbose_name_plural = "DbObrasInformix"
        ordering = ("codigo_tipo_servico",)

    def __str__(self):
        result = []
        if hasattr(self, 'concessionaria'):
            result.append(f"{self.concessionaria}")

        if hasattr(self, 'codigo_item_servico'):
            result.append(f"({self.codigo_item_servico})")
            
        if hasattr(self, 'cdrodder'):
            result.append(f"[{self.cdrodder}]")
            
        if hasattr(self, 'descricao_tipo_servico'):
            result.append(f"{self.descricao_tipo_servico}")
            
        if hasattr(self, 'descricao_obra'):
            result.append(f" - {self.descricao_obra}")
        
        return " ".join(result)


class ObraInformix(models.Model):
    """Django model representing an Obra (construction work)"""
    CodigoObra = models.IntegerField(primary_key=True)
    CodigoLote = models.CharField(max_length=3)
    Concessionaria = models.CharField(max_length=50)
    CodigoItemServico = models.CharField(max_length=15, null=True, blank=True)
    DescricaoObra = models.CharField(max_length=255, null=True, blank=True)
    kminicial = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True)
    kmfinal = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True)
    DataProgramadaInicio = models.DateTimeField(null=True, blank=True)
    DataProgramadaFim = models.DateTimeField(null=True, blank=True)
    DataAnaliseInicio = models.DateTimeField(null=True, blank=True)
    DataAnaliseFim = models.DateTimeField(null=True, blank=True)
    DataExecucaoInicio = models.DateTimeField(null=True, blank=True)
    DataExecucaoFim = models.DateTimeField(null=True, blank=True)
    SituacaoObra = models.IntegerField()
    FlagProjeto = models.SmallIntegerField(null=True, blank=True)
    cdrodder = models.CharField(max_length=20, null=True, blank=True)
    ProgressoObra = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    ValorTotalObra = models.DecimalField(max_digits=19, decimal_places=4, null=True, blank=True)  # For money type
    ValorTotalObraFormatado = models.CharField(max_length=128, null=True, blank=True)
    Inaugurada = models.BooleanField(null=True, blank=True)
    Prioritaria = models.BooleanField(null=True, blank=True)
    DataInclusaoObraPrioritaria = models.DateTimeField(null=True, blank=True)
    Apelido = models.CharField(max_length=128, null=True, blank=True)
    Descricao = models.TextField(null=True, blank=True)
    Observacao = models.TextField(null=True, blank=True)
    TrechosEntregues = models.CharField(max_length=500, null=True, blank=True)
    DataPrevistaInicio = models.DateField(null=True, blank=True)
    DataPrevistaTermino = models.DateField(null=True, blank=True)
    DataEventoInicial = models.DateField(null=True, blank=True)
    DataInauguracao = models.DateField(null=True, blank=True)
    LicencaNaoSeAplica = models.BooleanField(null=True, blank=True)
    MotivoLicencaNaoSeAplica = models.CharField(max_length=250, null=True, blank=True)
    DecretoNaoSeAplica = models.BooleanField(null=True, blank=True)

    def __str__(self):
        return f"Obra {self.CodigoObra} - {self.DescricaoObra or 'Sem descrição'}"

    class Meta:
        verbose_name = 'ObraInformix'
        verbose_name_plural = 'ObrasInformix'