# forms.py
from decimal import Decimal, InvalidOperation
import re
import datetime
from django import forms
from django.core.exceptions import ValidationError
from django.db.models import Q
from concessionarias.models import Concessionaria
from django.core.validators import MaxLengthValidator
from .models import Interessado, Protocolo, TipoObra
from concessionarias.models import ModoTransporte
from .fields import TipoObraModelChoiceField

from captcha.fields import CaptchaField, CaptchaTextInput
from dal import autocomplete


class CustomCaptchaTextInput(CaptchaTextInput):
    template_name = 'accounts/custom-captcha.html'


class MonthYearField(forms.DateField):
    """A custom field for handling month-year inputs with DateField models."""
    
    widget = forms.DateInput(attrs={'type': 'month'})
    
    def __init__(self, *args, **kwargs):
        kwargs['input_formats'] = ['%Y-%m']
        super().__init__(*args, **kwargs)
    
    def to_python(self, value):
        """
        Override to_python to convert the string value to a date object with day=1
        """
        if not value:
            return None
            
        if isinstance(value, datetime.date):
            return value
            
        try:
            year, month = value.split('-')
            return datetime.date(int(year), int(month), 1)
        except (ValueError, TypeError):
            raise forms.ValidationError('Please enter a valid month and year.')


class DecimalBRField(forms.DecimalField):
    """
    A custom DecimalField that accepts Brazilian formatted numbers,
    e.g. "1.234,56" becomes Decimal("1234.56").
    """
    def to_python(self, value):
        if value in self.empty_values:
            return None
        if isinstance(value, (Decimal, float, int)):
            return value

        normalized = value.replace('.', '').replace(',', '.')
        try:
            return Decimal(normalized)
        except InvalidOperation:
            raise ValidationError("Informe um número válido.", code="invalid")


class ProtocoloForm(forms.ModelForm):
    user_email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={'placeholder': 'Seu e-mail'}),
        help_text="Entre com um e-mail válido"
    )

    email_confirmation = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'placeholder': 'Confirme seu e-mail',
            'onpaste': 'return false',
            'autocomplete': 'off'
        }),
        help_text="Confirme seu e-mail exatamente"
    )
    user_name = forms.CharField(
        required=True,
        max_length=100,
        widget=forms.TextInput(attrs={'placeholder': 'Nome completo'}),
        help_text="Entre com seu nome completo"
    )
    modo_transporte = forms.ModelChoiceField(
        queryset=ModoTransporte.objects.order_by('description'),  # Set queryset here
        widget=forms.Select(),  # Keep the Select widget
        required=True,  # If it's a required field
        empty_label=None #Removes the default '----' option
    )
    
    interessado = forms.ModelChoiceField(
        queryset=Interessado.objects.order_by('description'),  # Set queryset here
        widget=forms.Select(),  # Keep the Select widget
        required=True,  # If it's a required field
        empty_label=None #Removes the default '----' option
    )
    
    manif_proc_em_andamento = forms.BooleanField(
        required=False,
        help_text="Marque esta opção caso já exista um processo em andamento sobre o assunto."
    )
    
    processo_sei_ref = forms.CharField(
        required=False,
        max_length=25,
        widget=forms.TextInput(attrs={
            'placeholder': 'Nro. do processo SEI de referência',
            'pattern': r'\d{1,3}\.\d{8}\/\d{4}-\d{2}'
        }),
        help_text="Entre com o número do processo SEI (se houver)"
    )
    
    descricao = forms.CharField(
        validators=[MaxLengthValidator(256)],
        widget=forms.Textarea(attrs={
            'maxlength': '200',
            'rows': '4',
            'columns': '10',
        })
    )
    
    concessionaria = forms.ModelChoiceField(
        queryset=Concessionaria.objects.none(),  # Initially empty
        widget=forms.Select(attrs={"id": "id_concessionaria"}),
        required=True,
        empty_label=None
    )

    class Meta:
        model = Protocolo
        fields = [
            'user_name',
            'user_email',
            'email_confirmation',
            'interessado',
            'modo_transporte',
            'concessionaria',
            'destinatario', 
            'assunto',
            'descricao',
            'cod_doc',
            'manif_proc_em_andamento',
            'processo_sei_ref',
        ]
        # widgets = {
        #     'interessado': forms.Select(attrs={'class': 'form-control'}),
        #     'destinatario': forms.Select(attrs={'class': 'form-control'}),
        #     'assunto': forms.Select(attrs={'class': 'form-control'}),
        # }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if "modo_transporte" in self.data:
            try:
                modo_transporte_id = int(self.data.get("modo_transporte"))
                self.fields["concessionaria"].queryset = Concessionaria.objects\
                .filter(ativa=True)\
                .filter(
                    modo_transporte_id=modo_transporte_id
                ).order_by("nome")
            except (ValueError, TypeError):
                pass  # Ignore errors and keep queryset empty
    
    def clean_processo_sei_ref(self):
        """Validate that if provided, the value matches the required format."""
        value = self.cleaned_data.get('processo_sei_ref')
        if value:
            pattern = r'^\d{1,3}\.\d{8}\/\d{4}-\d{2}$'
            if not re.match(pattern, value):
                raise ValidationError(
                    'Formato inválido para o processo SEI. Use o formato: 134.00000605/2025-04'
                )
        return value

    def clean(self):
        """Ensure that the two email fields match."""
        cleaned_data = super().clean()
        email = cleaned_data.get("user_email")
        email_conf = cleaned_data.get("email_confirmation")
        if email and email_conf and email != email_conf:
            self.add_error("email_confirmation", "Os e-mails não correspondem.")
        return cleaned_data
    

class ProtocoloInclusaoForm(forms.ModelForm):
    tipo_obra = TipoObraModelChoiceField(
        queryset=TipoObra.objects.all(),
        required=True,
        widget=autocomplete.ModelSelect2(
            url='protocolo:tipo-obra-autocomplete',
            attrs={
                'data-minimum-input-length': 0,
                'data-tags': "true",
                'data-placeholder': "Digite para buscar ou criar",
                'data-ajax--delay': 250,
            }
        )
    )
    obra_modo_repr = forms.ChoiceField(
        choices=Protocolo.ObraModelChoices.choices,
        required=True,
        label='Modo Representação'
    )
    latitude = forms.DecimalField(
        required=False,
        max_digits=9,
        decimal_places=6,
        widget=forms.HiddenInput()
    )
    longitude = forms.DecimalField(
        required=False,
        max_digits=9,
        decimal_places=6,
        widget=forms.HiddenInput()
    )
    valor = DecimalBRField(
        required=False,
        max_digits=23,
        decimal_places=2,
        widget=forms.TextInput(attrs={
            'placeholder': 'Valor',
            'class': 'valor-mask w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none',
            'autocomplete': 'off'
        })
    )
    lote = forms.CharField(show_hidden_initial=True)
    # captcha = CaptchaField(
    #     widget=CustomCaptchaTextInput,
    #     error_messages={
    #         'invalid': 'O código de verificação "captcha" está incorreto. Por favor, tente novamente.'
    #     }
    # )
    data_base = MonthYearField()
    class Meta:
        model = Protocolo
        fields = [
            'tipo_obra',
            'obra_modo_repr',
            'data_base',
            'valor',
            'rodovia',
            'km_inicial',
            'km_final',
            'latitude',
            'longitude',
        ]
        widgets = {
            'rodovia': autocomplete.ModelSelect2(
                url='protocolo:rodovia-autocomplete',
                forward=['lote'],
                attrs={
                    'class':'w-full px-4 py-8 border rounded-md',
                    'data-minimum-input-length': 0,
                    'data-language': 'pt-br',
                }
            ),
            'km_inicial': forms.NumberInput(attrs={'class': 'w-full border px-1 py-1 rounded-md'}),
            'km_final': forms.NumberInput(attrs={'class': 'w-full border px-1 py-1 rounded-md'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        conc_lote = self.initial.get('concessionaria_lote')
        self.fields['lote'].initial = conc_lote

class ProtocoloAlteracaoPrazoForm(forms.ModelForm):
    valor = DecimalBRField(
        required=False,
        max_digits=23,
        decimal_places=2,
        widget=forms.TextInput(attrs={
            'placeholder': 'Valor',
            'class': 'valor-mask w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none',
            'autocomplete': 'off'
        })
    )
    # captcha = CaptchaField(
    #     widget=CustomCaptchaTextInput,
    #     error_messages={
    #         'invalid': 'O código de verificação "captcha" está incorreto. Por favor, tente novamente.'
    #     }
    # )
    lote = forms.CharField(show_hidden_initial=True)
    nova_data_pleito_inicio = MonthYearField()
    nova_data_pleito_termino = MonthYearField()
    class Meta:
        model = Protocolo
        fields = [
            'item_codigo',
            'nova_data_pleito_inicio',
            'nova_data_pleito_termino',
            'valor',
        ]
        widgets = {
            'item_codigo': autocomplete.ModelSelect2(
                url='protocolo:item-obra-autocomplete',
                forward=['lote'],
                attrs={
                'class':'w-full px-4 py-8 border rounded-md',
                'data-minimum-input-length': 0,
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        conc_lote = self.initial.get('concessionaria_lote')
        self.fields['lote'].initial = conc_lote
        self.fields['item_codigo'].required = True

class ProtocoloItemForm(forms.ModelForm):
    valor = DecimalBRField(
        required=False,
        max_digits=23,
        decimal_places=2,
        widget=forms.TextInput(attrs={
            'placeholder': 'Valor',
            'class': 'valor-mask w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none',
            'autocomplete': 'off'
        })
    )
    # captcha = CaptchaField(
    #     widget=CustomCaptchaTextInput,
    #     error_messages={
    #         'invalid': 'O código de verificação "captcha" está incorreto. Por favor, tente novamente.'
    #     }
    # )
    lote = forms.CharField(show_hidden_initial=True)
    class Meta:
        model = Protocolo
        fields = [
            'item_codigo',
            'valor',
        ]
        widgets = {
            'item_codigo': autocomplete.ModelSelect2(
                url='protocolo:item-obra-autocomplete',
                forward=['lote'],
                attrs={
                'class':'w-full px-4 py-8 border rounded-md',
                'data-minimum-input-length': 0,
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        conc_lote = self.initial.get('concessionaria_lote')
        self.fields['lote'].initial = conc_lote
        self.fields['item_codigo'].required = True


ASSUNTO_FORM_MAPPING = {
    "PLEITO DE INCLUSÃO / NOVO INVESTIMENTO": ProtocoloInclusaoForm,
    "PLEITO DE POSTERGAÇÃO DA DATA DE INÍCIO E/OU TÉRMINO": ProtocoloAlteracaoPrazoForm,
    "PLEITO DE ANTECIPAÇÃO DA DATA DE INÍCIO E/OU TÉRMINO": ProtocoloAlteracaoPrazoForm,
    "PLEITO DE CANCELAMENTO (EXCLUSÃO DE ITEM)": ProtocoloItemForm,
    "PLEITO DE REDUÇÃO (DIMINUIÇÃO DO VALOR DE ITEM)": ProtocoloItemForm,
    "PLEITO DE ACRÉSCIMO (AUMENTO DO VALOR DE ITEM)": ProtocoloItemForm,
}

TEMPLATE_FORM_MAPPING = {
    "PLEITO DE INCLUSÃO / NOVO INVESTIMENTO": "protocolo_form_step2_inclusao.html",
    "PLEITO DE POSTERGAÇÃO DA DATA DE INÍCIO E/OU TÉRMINO": "protocolo_form_step2_alteracao.html",
    "PLEITO DE ANTECIPAÇÃO DA DATA DE INÍCIO E/OU TÉRMINO": "protocolo_form_step2_alteracao.html",
    "PLEITO DE CANCELAMENTO (EXCLUSÃO DE ITEM)":"protocolo_form_step2_item.html",
    "PLEITO DE REDUÇÃO (DIMINUIÇÃO DO VALOR DE ITEM)":"protocolo_form_step2_item.html",
    "PLEITO DE ACRÉSCIMO (AUMENTO DO VALOR DE ITEM)":"protocolo_form_step2_item.html",
}