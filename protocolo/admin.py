from django.contrib import admin
from django.db.models import Count
from django.utils.html import format_html
from .models import (
    DbObrasInformix,
    Keyword,
    Protocolo,
    TipoDocumento,
    Interessado,
    EncaminhadoPor,
    Destinatario,
    Assunto,
    TipoObra,
)

@admin.register(Protocolo)
class ProtocoloAdmin(admin.ModelAdmin):
    # list_display = (
    #     'protocolo',
    #     'concessionaria',
    #     'rodovia',
    #     'interessado',
    #     'assunto',
    #     'created_at',
    #     'user_name',
    #     'user_email',
    # )
    # list_filter = (
    #     'concessionaria',
    #     'tipo_documento',
    #     'assunto',
    #     'created_at',
    # )
    # search_fields = (
    #     'protocolo',
    #     'processo_sei',
    #     'processo_sei_ref',
    #     'descricao',
    #     'user_name',
    #     'user_email',
    # )
    # readonly_fields = (
    #     'protocolo',
    #     'uid',
    #     'hash',
    #     'created_at',
    #     'updated_at',
    #     'modifications_counter',
    #     'ip_address',
    # )
    # fieldsets = (
    #     ('Informações Básicas', {
    #         'fields': (
    #             'protocolo',
    #             'uid',
    #             'concessionaria',
    #             'rodovia',
    #             'modo_transporte',
    #             'interessado',
    #         )
    #     }),
    #     ('Localização', {
    #         'fields': (
    #             'latitude',
    #             'longitude',
    #         )
    #     }),
    #     ('Documentação', {
    #         'fields': (
    #             'processo_sei_ref',
    #             'processo_sei',
    #             'tipo_documento',
    #             'cod_doc',
    #             'encaminhado_por',
    #             'destinatario',
    #         )
    #     }),
    #     ('Detalhes', {
    #         'fields': (
    #             'assunto',
    #             'tipo_obra',
    #             'descricao',
    #             'valor',
    #             'data_base',
    #         )
    #     }),
    #     ('Usuário', {
    #         'fields': (
    #             'user_name',
    #             'user_email',
    #             'ip_address',
    #         )
    #     }),
    #     ('Metadados', {
    #         'fields': (
    #             'hash',
    #             'created_at',
    #             'updated_at',
    #             'modifications_counter',
    #         )
    #     }),
    # )
    date_hierarchy = 'created_at'

@admin.register(Keyword)
class KeywordAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

class BaseDescriptionAdmin(admin.ModelAdmin):
    list_display = ('description',)
    search_fields = ('description',)
    ordering = ('description',)

@admin.register(TipoDocumento)
class TipoDocumentoAdmin(BaseDescriptionAdmin):
    pass

@admin.register(Interessado)
class InteressadoAdmin(BaseDescriptionAdmin):
    pass

@admin.register(EncaminhadoPor)
class EncaminhadoPorAdmin(BaseDescriptionAdmin):
    pass

@admin.register(Destinatario)
class DestinatarioAdmin(BaseDescriptionAdmin):
    pass

@admin.register(Assunto)
class AssuntoAdmin(BaseDescriptionAdmin):
    pass

@admin.register(TipoObra)
class TipoObraAdmin(BaseDescriptionAdmin):
    pass

@admin.register(DbObrasInformix)
class DbObrasAdmin(admin.ModelAdmin):
    pass