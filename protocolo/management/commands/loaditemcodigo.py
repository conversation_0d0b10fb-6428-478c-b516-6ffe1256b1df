from django.core.management.base import BaseCommand
from ...models import ItemCodigo
import random

class Command(BaseCommand):
    help = 'Ingest thousands of fake ItemCodigo records'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=1000, help='Number of records to generate (default: 1000)')

    def handle(self, *args, **options):
        count = options['count']

        # Lista de descrições de obras civis em português
        descricoes = [
            "Obra de pavimentação asfáltica em via urbana",
            "Construção de muro de contenção em concreto armado",
            "Reforma estrutural de edifício comercial",
            "Execução de fundações profundas tipo estaca",
            "Instalação de sistema de drenagem pluvial",
            "Construção de ponte em concreto protendido",
            "Reforma de fachada com revestimento cerâmico",
            "Execução de terraplanagem para loteamento",
            "Construção de reservatório elevado em concreto",
            "Revitalização de praça pública com paisagismo",
            "Execução de rede de esgoto sanitário",
            "Construção de escola municipal padrão FNDE",
            "Reforma de unidade básica de saúde",
            "Execução de calçamento em bloco intertravado",
            "Construção de ginásio poliesportivo coberto"
        ]

        for _ in range(count):
            # Gera código no formato X.XXX.XX.XX
            codigo_parts = [
                str(random.randint(1, 9)),
                f"{random.randint(0, 999):03d}",
                f"{random.randint(0, 99):02d}",
                f"{random.randint(0, 99):02d}"
            ]
            codigo = ".".join(codigo_parts)
            
            description = random.choice(descricoes)
            ativo = random.choice([True, False])

            ItemCodigo.objects.create(
                codigo=codigo,
                description=description,
                ativo=ativo,
            )

        self.stdout.write(self.style.SUCCESS(f'Successfully ingested {count} fake records.'))