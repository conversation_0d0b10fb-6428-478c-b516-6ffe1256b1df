from django.core import management
from django.core.management.commands import loaddata
from django.contrib.auth.models import Group, Permission

from django.core.management.base import BaseCommand, CommandError

class Command(BaseCommand):
    help = 'Carrega dados iniciais do app PROTOCOLO'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Loading data...'))

        # DADOS INICIAIS
        management.call_command('loaddata', 'interessado', verbosity=1)
        management.call_command('loaddata', 'modo-transporte', verbosity=1)
        management.call_command('loaddata', 'tipo-doc', verbosity=1)
        management.call_command('loaddata', 'encaminhado-por', verbosity=1)
        management.call_command('loaddata', 'destinatario', verbosity=1)
        management.call_command('loaddata', 'assunto', verbosity=1)
        management.call_command('loaddata', 'tipo-obra', verbosity=1)
        # END
        self.stdout.write(self.style.SUCCESS('Data loaded'))

