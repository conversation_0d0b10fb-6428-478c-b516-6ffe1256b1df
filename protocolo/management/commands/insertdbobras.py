import time
import os
import pyodbc  # if you need to handle pyodbc errors
from django.core.management.base import BaseCommand, CommandError
from protocolo.models import DbObrasInformix
from protocolo.util.recuperar_dados_informix import map_sp_results_to_dbobras, DatabaseConnection


class Command(BaseCommand):
    help = "Import DbObrasInformix data from the stored procedure"

    def handle(self, *args, **options):
        self.stdout.write("Starting import of DbObrasInformix data from stored procedure...")
        server = os.getenv('BUSCA_OBRA_DB_SERVER', '')
        database = os.getenv('BUSCA_OBRA_DB_NAME', '')
        username = os.getenv('BUSCA_OBRA_DB_USER', '')
        password = os.getenv('BUSCA_OBRA_DB_PASSWORD', '')
        driver = os.getenv('BUSCA_OBRA_DB_DRIVER', 'FreeTDS')
        port = os.getenv('BUSCA_OBRA_DB_PORT', '1433')
        tds_version = os.getenv('DB_TDS_VERSION', '7.4')

        service = DatabaseConnection(
            server=server,
            database=database,
            username=username,
            password=password,
            driver=driver,
            port=port,
            tds_version=tds_version,
        )

        try:
            # Execute the stored procedure
            sp_results = service.execute_stored_procedure_get_db_obras()
            if not sp_results:
                self.stdout.write(self.style.WARNING("No data returned from stored procedure."))
                return

            # Map the results to a list of unsaved DbObrasInformix instances
            self.stdout.write(self.style.NOTICE("Mapping the resources..."))
            instances = map_sp_results_to_dbobras(sp_results)

            if not instances:
                self.stdout.write(self.style.WARNING("Mapping resulted in no instances to save."))
                return

            # Save all instances using bulk_create for efficiency
            self.stdout.write(self.style.NOTICE("bulk create..."))
            DbObrasInformix.objects.bulk_create(instances, ignore_conflicts=True)
            self.stdout.write(self.style.SUCCESS(f"Successfully imported {len(instances)} records."))
        except Exception as e:
            raise CommandError(f"An error occurred during import: {e}")
