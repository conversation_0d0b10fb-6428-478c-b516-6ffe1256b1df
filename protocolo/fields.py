from django import forms
from .models import TipoObra
from django.utils.html import escape

def sanitize_text_input(text):
    """
    Sanitize general text input by:
    1. Stripping whitespace
    2. Escaping HTML characters
    3. Removing any null bytes
    """
    if not isinstance(text, str):
        return ''
    
    # Strip whitespace and remove null bytes
    cleaned = text.strip().replace('\x00', '').upper()
    
    # Escape HTML characters
    return escape(cleaned)

class TipoObraModelChoiceField(forms.ModelChoiceField):
    def valid_value(self, value):
        """
        If the value is an instance of `TipoObra`, it's valid.
        If it's a string, check if it exists; if not, create it.
        """
        if isinstance(value, TipoObra):
            return True

        if isinstance(value, str):
            value = value.strip().upper()
            value = sanitize_text_input(value)
            tipo_obra, created = TipoObra.objects.get_or_create(description=value)
            self.queryset = TipoObra.objects.all()  # Ensure queryset includes the new object
            return True

        return False

    def clean(self, value):
        """
        Overrides clean() to ensure `TipoObra` instances are returned.
        If the value is a string, create or retrieve it.
        """
        if not value:
            raise forms.ValidationError("Escolha ou crie um tipo de obra válido.")

        if isinstance(value, TipoObra):
            return value

        # Ensure the value is created if it doesn't exist
        
        value = value.strip().upper()
        value = sanitize_text_input(value)
        tipo_obra, created = TipoObra.objects.get_or_create(description=value)
        return tipo_obra
