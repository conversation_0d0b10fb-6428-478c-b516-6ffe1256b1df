# permissions.py
from rest_framework.permissions import BasePermission
from django.contrib.auth.models import AnonymousUser

class CCIGroupOrAPIKeyPermission(BasePermission):
    """
    Permission allowing:
     - Authenticated users who belong to group 'cci', OR
     - Any user (even AnonymousUser) whose request.auth == 'API_KEY'
    """
    message = "Você não tem permissão para realizar esta operação. É necessário o grupo CCI ou um API Key válida."

    def has_permission(self, request, view):
        # 1) If user is authenticated and in 'cci' group, allow
        if (
            request.user
            and request.user.is_authenticated
            and request.user.groups.filter(name='cci').exists()
        ):
            return True

        # 2) Else, check if they have a valid API Key from our custom auth
        #    i.e. request.auth == "API_KEY"
        if request.auth == "API_KEY":
            return True

        # Otherwise, deny
        return False
