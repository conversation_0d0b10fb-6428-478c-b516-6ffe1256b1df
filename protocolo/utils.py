# protocolo/utils.py
import hashlib
from dotenv import load_dotenv
import json
import os
import uuid
from datetime import datetime

load_dotenv()


def generate_protocolo(pk: int, uid: str, dt: datetime):
    prefix = "ART"
    nro = str(pk).zfill(8)
    uuid_comp = str(uid)[-8:]
    return f"{prefix}-{uuid_comp}-{nro}/{dt.year}".upper()

    
def generate_hash(protocolo):
    secret_key = os.getenv("PROTOCOL_HASH_SECRET_KEY", "custom-key-secret")
    data = (
        f"{protocolo}|{secret_key}"
    )
    return hashlib.sha256(data.encode('utf-8')).hexdigest()

def generate_json_data(obj):
    data = {
            'user_name': obj.user_name,
            'user_email': obj.user_email,
            'concessionaria': obj.concessionaria.nome if obj.concessionaria else None,
            'modo_transporte': obj.modo_transporte.description if obj.modo_transporte else None,
            'tipo_documento': obj.tipo_documento.description if obj.tipo_documento else None,
            'interessado': obj.interessado.description if obj.interessado else None,
            'processo_sei': obj.processo_sei.description if obj.processo_sei else None,
            'valor': str(obj.valor),
            'data_base': str(obj.data_base),
            'ip_address': obj.ip_address,
            'hash': obj.hash,
        }
    return json.dumps(data, sort_keys=True, indent=4)