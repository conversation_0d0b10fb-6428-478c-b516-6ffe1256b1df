from datetime import datetime
from decimal import Decimal
import io
import json
from io import BytesIO
import logging
import os
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, Http404, HttpResponseServerError, FileResponse, JsonResponse
from django.urls import reverse
from django.views import View
from concessionarias.models import Concessionaria
from rodovias.models import Rodovia
from xhtml2pdf import pisa
from django.template.loader import get_template, render_to_string
from .forms import ASSUNTO_FORM_MAPPING, TEMPLATE_FORM_MAPPING, ProtocoloForm
from .models import Assunto, DbObrasInformix, Destinatario, EncaminhadoPor, \
    Interessado, Protocolo, TipoObra
from concessionarias.models import ModoTransporte
from django.utils.html import escape
from django.db.models import Q
from django.core.paginator import Paginator
import pandas as pd

logger = logging.getLogger("django_file")


def sanitize_text_input(text):
    """
    Sanitize general text input by:
    1. Stripping whitespace
    2. Escaping HTML characters
    3. Removing any null bytes
    """
    if not isinstance(text, str):
        return ''
    
    # Strip whitespace and remove null bytes
    cleaned = text.strip().replace('\x00', '').upper()
    
    # Escape HTML characters
    return escape(cleaned)


def protocolo_step2_view(request):
    step1_data = request.session.get("step1_data")
    if not step1_data:
        return redirect("protocolo:protocolo-create")

    nome = step1_data.get('user_name', None)
    email = step1_data.get('user_email', None)
    interessado_id = step1_data.get('interessado', None)
    assunto_id = step1_data.get("assunto")
    destinatario_id = step1_data.get('destinatario', None)
    modo_transporte_id = step1_data.get('modo_transporte', None)
    concessionaria_id = step1_data.get('concessionaria', None)
    descricao = step1_data.get('descricao', None)
    cod_doc = step1_data.get('cod_doc', None)
    processo_sei_ref = step1_data.get('processo_sei_ref', None)

    try:
        interessado = Interessado.objects.get(pk=interessado_id)
        assunto = Assunto.objects.get(pk=assunto_id)
        destinatario = Destinatario.objects.get(pk=destinatario_id)
        modo_transporte = ModoTransporte.objects.get(pk=modo_transporte_id)
        concessionaria = Concessionaria.objects.get(pk=concessionaria_id)
    except Exception as err:
        raise HttpResponseServerError("Erro interno no servidor")

    if assunto.description in ASSUNTO_FORM_MAPPING:
        FormClass = ASSUNTO_FORM_MAPPING[assunto.description]
        template = TEMPLATE_FORM_MAPPING[assunto.description]
    else:
        raise Http404()
    
    initial_form_data = {
        'concessionaria_lote': concessionaria.lote
    }

    if request.method == "POST":
        form = FormClass(request.POST, initial=initial_form_data)
        if form.is_valid():
            protocolo = form.save(commit=False)

            protocolo.user_name = nome
            protocolo.user_email = email
            protocolo.interessado_id = interessado_id
            protocolo.assunto_id = assunto_id
            protocolo.destinatario_id = destinatario_id
            protocolo.concessionaria_id = concessionaria_id
            protocolo.modo_transporte = modo_transporte_id
            protocolo.ip_address = get_client_ip(request)
            protocolo.descricao = descricao
            protocolo.cod_doc = cod_doc
            protocolo.processo_sei_ref = processo_sei_ref


            protocolo.save()
            request.session.pop("step1_data", None)
            return redirect("protocolo:success", protocolo_uid=str(protocolo.uid))

    else:
        form = FormClass(initial=initial_form_data)

    context = {
        'form': form,
        'nome': nome,
        'email': email,
        'interessado': interessado,
        'assunto': assunto,
        'destinatario': destinatario,
        'modo_transporte': modo_transporte,
        'concessionaria': concessionaria,
    }

    return render(request, f"protocolo/{template}", context)


def protocolo_create_view(request):
    step1_data = request.session.get("step1_data", {})

    if request.method == "POST":
        form = ProtocoloForm(request.POST)
        if form.is_valid():
            request.session["step1_data"] = request.POST.dict()
            return redirect(reverse("protocolo:protocolo-create-step2"))
    else:
        form = ProtocoloForm(initial=step1_data)

    return render(request, "protocolo/protocolo_form_step1.html", {"form": form})


def list_protocolos(request):
    """
    List of 'Protocolo' objects with filtering/pagination.
    Supports 'export_format=csv|excel' and includes modal PDF download.
    """
    try:
        # Base QuerySet
        qs = Protocolo.objects.all().order_by('-id')

        # Lists for Select Fields
        concessionarias_list = Concessionaria.objects.filter(
            fiscalizacao_artesp=True, ativa=True
        ).exclude(nome='ARTESP').order_by('lote')

        modos_transporte_list = ModoTransporte.objects.all()
        interessados_list = Interessado.objects.all()
        # tipos_documento_list = TipoDocumento.objects.all()
        # encaminhado_por_list = EncaminhadoPor.objects.all() 
        destinatarios_list = Destinatario.objects.all()
        assuntos_list = Assunto.objects.all()
        tipos_obra_list = TipoObra.objects.all()

        # Retrieve Filters
        filters = {
            "protocolo": request.GET.get('protocolo', '').strip(),
            "concessionaria": request.GET.get('concessionaria', '').strip(),
            "modo_transporte": request.GET.get('modo_transporte', '').strip(),
            "interessado": request.GET.get('interessado', '').strip(),
            "processo_sei_ref": request.GET.get('processo_sei_ref', '').strip(),
            "processo_sei": request.GET.get('processo_sei', '').strip(),
            # "tipo_documento": request.GET.get('tipo_documento', '').strip(),
            # "encaminhado_por": request.GET.get('encaminhado_por', '').strip(),
            "destinatario": request.GET.get('destinatario', '').strip(),
            "assunto": request.GET.get('assunto', '').strip(),
            "tipo_obra": request.GET.get('tipo_obra', '').strip(),
            "descricao": request.GET.get('descricao', '').strip(),
            "cod_doc": request.GET.get('cod_doc', '').strip(),
            "rodovia": request.GET.get('rodovia', '').strip(),
            "user_email": request.GET.get('user_email', '').strip(),
            "created_at": request.GET.get('created_at', '').strip(),
        }

        # Apply Filters
        if filters["protocolo"]:
            qs = qs.filter(protocolo__icontains=filters["protocolo"])
        if filters["concessionaria"]:
            qs = qs.filter(concessionaria__pk=filters["concessionaria"])
        if filters["modo_transporte"]:
            qs = qs.filter(modo_transporte__pk=filters["modo_transporte"])
        if filters["interessado"]:
            qs = qs.filter(interessado__pk=filters["interessado"])
        if filters["processo_sei_ref"]:
            qs = qs.filter(processo_sei_ref__icontains=filters["processo_sei_ref"])
        if filters["processo_sei"]:
            qs = qs.filter(processo_sei__icontains=filters["processo_sei"])
        # if filters["tipo_documento"]:
        #     qs = qs.filter(tipo_documento__pk=filters["tipo_documento"])
        # if filters["encaminhado_por"]:
        #     qs = qs.filter(encaminhado_por__pk=filters["encaminhado_por"])
        if filters["destinatario"]:
            qs = qs.filter(destinatario__pk=filters["destinatario"])
        if filters["assunto"]:
            qs = qs.filter(assunto__pk=filters["assunto"])
        if filters["tipo_obra"]:
            qs = qs.filter(tipo_obra__pk=filters["tipo_obra"])
        if filters["descricao"]:
            qs = qs.filter(descricao__icontains=filters["descricao"])
        if filters["cod_doc"]:
            qs = qs.filter(cod_doc__icontains=filters["cod_doc"])
        if filters["rodovia"]:
            qs = qs.filter(rodovia__icontains=filters["rodovia"])
        if filters["user_email"]:
            qs = qs.filter(user_email__icontains=filters["user_email"])
        if filters["created_at"]:
            try:
                date_val = datetime.strptime(filters["created_at"], '%Y-%m-%d').date()
                qs = qs.filter(created_at__date=date_val)
            except ValueError:
                pass

        # Handle Export (CSV/Excel)
        export_format = request.GET.get('export_format')
        if export_format in ['csv', 'excel']:
            all_protocolos = qs
            if export_format == 'csv':
                return export_protocolos_csv(all_protocolos)
            elif export_format == 'excel':
                return export_protocolos_excel(all_protocolos)

        # Pagination for HTML
        paginator = Paginator(qs, 10)
        page_number = request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)

        # Context Data
        context = {
            'protocolos': page_obj.object_list,
            'page_obj': page_obj,
            'filters': filters,
            'concessionarias_list': concessionarias_list,
            'modos_transporte_list': modos_transporte_list,
            'interessados_list': interessados_list,
            # 'tipos_documento_list': tipos_documento_list,
            # 'encaminhado_por_list': encaminhado_por_list,
            'destinatarios_list': destinatarios_list,
            'assuntos_list': assuntos_list,
            'tipos_obra_list': tipos_obra_list,
        }
        return render(request, 'protocolo/list_protocolos.html', context)

    except Exception as e:
        logger.exception("An unexpected error occurred in 'list_protocolos'")
        raise



def success(request, protocolo_uid):
    protocolo = get_object_or_404(Protocolo, uid=protocolo_uid)
    template_path = 'protocolo/success.html'
    context = {
        'protocolo': protocolo,
    }
    
    return render(request, template_path, context)


def protocolo_pdf_view(request, protocolo_uid):
    protocolo = get_object_or_404(Protocolo, uid=protocolo_uid)
    template_path = 'protocolo/pdf_template.html'
    context = {
        'protocolo': protocolo,
    }

    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{protocolo.protocolo}.pdf"'

    # Generate the PDF
    pdf = render_to_pdf(template_path, context)
    if pdf:
        response.write(pdf.getvalue())
        return response
    return HttpResponse('Error generating PDF', status=500)


def protocolo_pdf_view_html(request, protocolo_uid):
    protocolo = get_object_or_404(Protocolo, uid=protocolo_uid)
    template_path = 'protocolo/pdf_template.html'
    context = {
        'protocolo': protocolo,
    }

    return render(request, template_path, context)


def protocolo_arquivo_view(request, protocolo_uid):
    protocolo = get_object_or_404(Protocolo, uid=protocolo_uid)
    data = generate_arquivo(protocolo)
    response = HttpResponse(data, content_type='application/json')
    response['Content-Disposition'] = f'attachment; filename="{protocolo.protocolo}.json"'
    return response


def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def render_to_pdf(template_src, context_dict):
    template = get_template(template_src)
    html = template.render(context_dict)
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html.encode("utf-8")), result)
    if not pdf.err:
        return result
    return None

def not_implemented_view(request):
    return render(request, 'protocolo/protocolo_not_implemented.html')

def protocolo_view(request):
    protocolo_value = request.GET.get('protocolo')
    if not protocolo_value:
        return render(request, 'protocolo/protocolo_not_found.html', {
            'error': 'Parâmetro de protocolo não fornecido'
        }, status=400)
    
    # Convert to uppercase to match the stored format
    protocolo_value = protocolo_value.strip().upper()
    
    try:
        # Case insensitive query
        protocolo = get_object_or_404(Protocolo, Q(protocolo__iexact=protocolo_value))
        template_path = 'protocolo/protocolo_view.html'
        context = {
            'protocolo': protocolo,
        }
        return render(request, template_path, context)
    except Http404:
        return render(request, 'protocolo/protocolo_not_found.html', {
            'protocolo_value': protocolo_value
        }, status=404)


def generate_arquivo(protocolo):
    arquivo_data = get_protocolo_data_dict(protocolo)

    return json.dumps(arquivo_data, indent=4).encode('utf-8')

def get_protocolo_data_dict(protocolo):
    return {
        "nome": protocolo.user_name,
        "email": protocolo.user_email,
        "protocolo": protocolo.protocolo,
        "assunto": protocolo.assunto.description if protocolo.assunto else None,
        "destinatario": protocolo.destinatario.description if protocolo.destinatario else None,
        "modo_transporte": protocolo.concessionaria.modo_transporte.description if protocolo.concessionaria.modo_transporte else None,
        "concessionaria": protocolo.concessionaria.nome if protocolo.concessionaria else None,
        "cod_doc": protocolo.cod_doc if protocolo.cod_doc else None,
        "descricao": protocolo.descricao if protocolo.descricao else None,
        "processo_sei_ref": protocolo.processo_sei_ref if protocolo.processo_sei_ref else None,
        "tipo_implantacao": protocolo.tipo_obra.description if protocolo.tipo_obra else None,
        "item_codigo": str(protocolo.item_codigo) if str(protocolo.item_codigo) else None,
        "rodovia": str(protocolo.rodovia) if str(protocolo.rodovia) else None,
        "km_inicial": str(protocolo.km_inicial) if str(protocolo.km_inicial) else None,
        "km_final": str(protocolo.km_final) if str(protocolo.km_final) else None,
        "latitude": str(protocolo.latitude) if str(protocolo.latitude) else None,
        "longitude": str(protocolo.longitude) if str(protocolo.longitude) else None,
        "valor_estimado_desequilibrio": float(protocolo.valor) if protocolo.valor else None,
        "data_base": str(protocolo.data_base) if protocolo.data_base else None,
        "nova_data_pleito_inicio": str(protocolo.nova_data_pleito_inicio) if protocolo.nova_data_pleito_inicio else None,
        "nova_data_pleito_termino": str(protocolo.nova_data_pleito_termino) if protocolo.nova_data_pleito_termino else None,
        "ip_address": protocolo.ip_address,
        "hash": protocolo.hash,
        "processo_sei": protocolo.processo_sei if protocolo.processo_sei else None,
        "criado_em": protocolo.created_at.strftime("%Y-%m-%dT%H:%M:%S"),
        "atualizado_em": protocolo.updated_at.strftime("%Y-%m-%dT%H:%M:%S"),
    }

def download_pdf_modal(request, protocolo_uid):
    protocolo = get_object_or_404(Protocolo, uid=protocolo_uid)
    template_path = 'protocolo/pdf_template.html'
    context = {
        'protocolo': protocolo,
    }
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{protocolo.protocolo}.pdf"'


    try:
        pdf = render_to_pdf(template_path, context)
        if pdf:
            response.write(pdf.getvalue())
            return response
        return HttpResponse('Error generating PDF', status=500)
    except FileNotFoundError:
        raise Http404("PDF file not found.")

from dal import autocomplete

class RodoviaAutocompleteView(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        # qs = Rodovia.objects.all()
        lote = self.forwarded.get('lote')
        qs = Rodovia.objects.filter(trechos__concessionaria__lote=lote).distinct()

        if self.q:
            qs = qs.filter(codigo__icontains=self.q)

        return qs

class ConcessionariaAutoCompleteView(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        qs = Concessionaria.objects.all()

        if self.q:
            qs = qs.filter(
                Q(CodigoItemServico__icontains=self.q) |
                Q(CodigoRodovia__icontains=self.q) |
                Q(DescricaoTipoServico__icontains=self.q) |
                Q(DescricaoObra__icontains=self.q)
            )

        return qs

def export_protocolos_csv(protocolos_qs):
    """
    Use pandas to create a CSV from the queryset, exporting all specified fields.
    """
    data = []
    for protocolo in protocolos_qs:
        data.append(get_protocolo_data_dict(protocolo))

    # Build a DataFrame
    df = pd.DataFrame(data)

    # Convert DataFrame to CSV text (semicolon or comma, your choice)
    csv_string = df.to_csv(index=False, sep='|', encoding='utf-8')

    # Create a BytesIO stream from CSV text
    csv_bytes = io.BytesIO(csv_string.encode('utf-8'))

    # Return FileResponse with as_attachment
    return FileResponse(
        csv_bytes,
        as_attachment=True,
        filename='protocolos.csv',
        content_type='text/csv'
    )

def export_protocolos_excel(protocolos_qs):
    """
    Use pandas to create an Excel file from the queryset, exporting all specified fields.
    """
    data = []
    for protocolo in protocolos_qs:
        data.append(get_protocolo_data_dict(protocolo))

    # Build a DataFrame
    df = pd.DataFrame(data)

    # Use BytesIO for the Excel file
    buffer = io.BytesIO()

    with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Protocolos')

    buffer.seek(0)

    # Return FileResponse
    return FileResponse(
        buffer,
        as_attachment=True,
        filename='protocolos.xlsx',
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

class TipoObraAutocomplete(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        qs = TipoObra.objects.all()

        if self.q:
            qs = qs.filter(description__icontains=self.q)

        return qs

    def get_result_label(self, item):
        return item.description


class DbObrasAutocomplete(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        qs = DbObrasInformix.objects.all()
        lote = self.forwarded.get('lote') 
        query = self.q
        if lote:
            qs = qs.filter(codigo_lote=lote)

        if query:
            qs = qs.filter(
                Q(codigo_item_servico__icontains=query) |
                Q(cdrodovia__icontains=query) |
                Q(descricao_tipo_servico__icontains=query) |
                Q(descricao_obra__icontains=query)
            )

        return qs

def get_concessionarias(request):
    modo_transporte_id = request.GET.get("modo_transporte")
    if not modo_transporte_id:
        return JsonResponse({"error": "Modo Transporte não fornecido"}, status=400)

    concessionarias = Concessionaria.objects.filter(
        modo_transporte_id=modo_transporte_id
    ).filter(ativa=True).order_by("nome")

    data = [{"id": c.id, "nome": c.nome} for c in concessionarias]

    return JsonResponse(data, safe=False)


def get_obra_details(request):
    item_id = request.GET.get('item_id')
    lote = request.GET.get('lote')
    lote = int(lote)

    try:
        obra_db_obras = DbObrasInformix.objects.get(pk=item_id, codigo_lote=lote)
        obra_codigo = obra_db_obras.codigo_obra
        obra = recuperar_obra_por_codigo(obra_codigo)
        if not obra:
            raise Http404()
        context = {
            'obra': obra
        }
        
        return render(request, 'protocolo/partials/obra_details.html', context)
    except DbObrasInformix.DoesNotExist:
        raise Http404("Obra não encontrada")


def recuperar_obra_por_codigo(codigo):
    from .util.recuperar_dados_informix import DatabaseConnection
    from .models import ObraInformix
    """Django view function to retrieve Obra by codigo"""
    try:
        # First, try to get the Obra directly from the database

        try:
            obra = ObraInformix.objects.get(CodigoObra=codigo)
            # If we found it, return it directly without calling the stored procedure
        except ObraInformix.DoesNotExist:
            # If it doesn't exist, proceed with the stored procedure
            server = os.getenv('BUSCA_OBRA_DB_SERVER', '')
            database = os.getenv('BUSCA_OBRA_DB_NAME', '')
            username = os.getenv('BUSCA_OBRA_DB_USER', '')
            password = os.getenv('BUSCA_OBRA_DB_PASSWORD', '')
            driver = os.getenv('BUSCA_OBRA_DB_DRIVER', 'FreeTDS')
            port = os.getenv('BUSCA_OBRA_DB_PORT', '1433')
            tds_version = os.getenv('DB_TDS_VERSION', '7.4')
            db_conn = DatabaseConnection(
                server=server,
                database=database,
                username=username,
                password=password,
                driver=driver,
                port=port,
                tds_version=tds_version
            )
            
            # Execute stored procedure with the required codigo parameter
            obra_data = db_conn.execute_stored_procedure_with_codigo('SPRecuperarObraPorCodigo', codigo)
            
            if not obra_data:
                raise Http404(f"Obra com código {codigo} não encontrada")
            
            # Create a model instance without saving to the database
            obra = ObraInformix()
            
            # Map the stored procedure results to the model fields
            for field_name, value in obra_data.items():
                if hasattr(obra, field_name):
                    # Get the field type and convert the value appropriately
                    field = ObraInformix._meta.get_field(field_name)
                    
                    # Skip None values
                    if value is None:
                        setattr(obra, field_name, None)
                        continue
                    
                    # Handle specific field types
                    if field.get_internal_type() == 'DecimalField':
                        setattr(obra, field_name, Decimal(str(value)))
                    elif field.get_internal_type() in ['DateTimeField', 'DateField'] and isinstance(value, str):
                        try:
                            setattr(obra, field_name, datetime.strptime(value, '%Y-%m-%d %H:%M:%S'))
                        except ValueError:
                            try:
                                setattr(obra, field_name, datetime.strptime(value, '%Y-%m-%d'))
                            except ValueError:
                                setattr(obra, field_name, None)
                    elif field.get_internal_type() == 'BooleanField':
                        setattr(obra, field_name, bool(value))
                    else:
                        setattr(obra, field_name, value)
            
            # Optionally, you can save this object to the database to avoid 
            # calling the stored procedure in future requests
            obra.save()
        
        # Return the obra object
        return obra
        
    except Http404:
        raise
    except Exception as e:
        logger.error(e)
        return None
