
from django.db.models.signals import post_save
from django.dispatch import receiver

from .utils import generate_protocolo, generate_hash, generate_json_data

from .models import Protocolo


@receiver(post_save)
def gen_slug_ocorrencia(sender, instance, created, **kwargs):
    changes = False
    if not issubclass(sender, Protocolo):
        return
    if not instance.protocolo:
        changes = True
        instance.protocolo = generate_protocolo(instance.pk, instance.uid, instance.created_at)
    if not instance.hash and instance.protocolo:
        changes = True
        instance.hash = generate_hash(instance.protocolo)
    if changes:
        instance.save()