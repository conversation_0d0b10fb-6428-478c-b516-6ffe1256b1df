# serializers.py
from rest_framework import serializers
from .models import Protocolo
import re

class ProtocoloProcessoSeiSerializer(serializers.ModelSerializer):
    concessionaria = serializers.CharField(source='concessionaria.nome', read_only=True)
    rodovia = serializers.CharField(source='rodovia.codigo', read_only=True)
    modo_transporte = serializers.CharField(source='modo_transporte.description', read_only=True)
    interessado = serializers.CharField(source='interessado.description', read_only=True)
    tipo_documento = serializers.CharField(source='tipo_documento.description', read_only=True)
    tipo_obra = serializers.CharField(source='tipo_documento.description', read_only=True)
    encaminhado_por = serializers.CharField(source='encaminhado_por.description', read_only=True)
    destinatario = serializers.CharField(source='destinatario.description', read_only=True)
    assunto = serializers.Char<PERSON>ield(source='assunto.description', read_only=True)
    modo_transporte = serializers.Char<PERSON>ield(source='modo_transporte.description', read_only=True)
    
    class Meta:
        model = Protocolo
        # Return all model fields in the response
        # fields = '__all__'
        exclude = ('id',)
        # Allow only processo_sei to be updated. The rest are read-only.
        read_only_fields = [
            'uid',
            'protocolo',
            'modifications_counter',
            'latitude',
            'longitude',
            'concessionaria',
            'modo_transporte',
            'interessado',
            'tipo_documento',
            'encaminhado_por',
            'destinatario',
            'assunto',
            'processo_sei_ref',
            'tipo_obra',
            'descricao',
            'cod_doc',
            'rodovia',
            'valor',
            'data_base',
            'user_name',
            'user_email',
            'ip_address',
            'hash',
            'created_at',
            'updated_at',
        ]

    def validate_processo_sei(self, value):
        """
        Validate the provided processo_sei.
        Expected format (example): 134.00000605/2025-04
        Adjust the regex below if your format requirements change.
        """
        pattern = r'^\d{1,3}\.\d{8}/\d{4}-\d{2}$'
        if not re.match(pattern, value):
            raise serializers.ValidationError(
                "Código do 'processo_sei' inválido. Formato experado: XXX.XXXXXXXX/XXXX-XX"
            )
        return value

    def update(self, instance, validated_data):
        """
        Update the processo_sei field and increment modifications_counter.
        """
        instance.processo_sei = validated_data.get('processo_sei', instance.processo_sei)
        instance.modifications_counter = instance.modifications_counter + 1
        instance.save()
        return instance
