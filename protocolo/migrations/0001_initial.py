# Generated by Django 4.2.5 on 2025-03-13 15:06

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('concessionarias', '0005_modotransporte_concessionaria_modo_transporte'),
        ('rodovias', '0010_trecho_dt_inicio'),
    ]

    operations = [
        migrations.CreateModel(
            name='Assunto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=100, unique=True, verbose_name='Assunto')),
                ('ativo', models.BooleanField(default=True, help_text='Opção ativa', verbose_name='Ativo')),
            ],
        ),
        migrations.CreateModel(
            name='DbObrasInformix',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('codigo_obra', models.IntegerField()),
                ('codigo_lote', models.IntegerField(blank=True, null=True)),
                ('concessionaria', models.CharField(blank=True, max_length=50, null=True)),
                ('cdrodovia', models.CharField(blank=True, max_length=20, null=True)),
                ('nmrodovia', models.CharField(blank=True, max_length=50, null=True)),
                ('codtrecho', models.IntegerField(blank=True, null=True)),
                ('kminicial', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('kmfinal', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('descricao_obra', models.CharField(blank=True, max_length=255, null=True)),
                ('codigo_tipo_servico', models.IntegerField(blank=True, null=True)),
                ('descricao_tipo_servico', models.CharField(blank=True, max_length=75, null=True)),
                ('situacao_obra', models.IntegerField()),
                ('progresso_obra', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
                ('dtreceb', models.DateTimeField(blank=True, null=True)),
                ('nrcstreceb', models.CharField(blank=True, max_length=10, null=True)),
                ('nrprocessoreceb', models.CharField(blank=True, max_length=25, null=True)),
                ('nrprotocoloreceb', models.CharField(blank=True, max_length=25, null=True)),
                ('nraceiteprov', models.CharField(blank=True, max_length=25, null=True)),
                ('dtaceiteprov', models.DateTimeField(blank=True, null=True)),
                ('nraceitedef', models.CharField(blank=True, max_length=25, null=True)),
                ('dtaceitedef', models.DateTimeField(blank=True, null=True)),
                ('cdcgc', models.CharField(blank=True, max_length=14, null=True)),
                ('dsobsemptecno', models.CharField(blank=True, max_length=255, null=True)),
                ('cdgrd', models.IntegerField()),
                ('cddocprojeto', models.IntegerField()),
                ('codop', models.IntegerField(blank=True, null=True)),
                ('dtaalter', models.DateTimeField(blank=True, null=True)),
                ('situacao_projeto', models.IntegerField(blank=True, null=True)),
                ('codigo_item_servico', models.CharField(blank=True, max_length=15, null=True)),
                ('dtentregaproj', models.DateTimeField(blank=True, null=True)),
                ('dtaprovaproj', models.DateTimeField(blank=True, null=True)),
                ('dsobs', models.CharField(blank=True, max_length=255, null=True)),
                ('codigotota', models.IntegerField(blank=True, null=True)),
                ('flprojeto', models.SmallIntegerField(blank=True, null=True)),
                ('nrultadequa', models.IntegerField(blank=True, null=True)),
                ('dtprocessorec', models.DateTimeField(blank=True, null=True)),
                ('dttermoaceiterec', models.DateTimeField(blank=True, null=True)),
                ('dtasbuiltprev', models.DateTimeField(blank=True, null=True)),
                ('dtasbuiltent', models.DateTimeField(blank=True, null=True)),
                ('dtvistiniobra', models.DateTimeField(blank=True, null=True)),
                ('dtvistfimobra', models.DateTimeField(blank=True, null=True)),
                ('dtiniobra', models.DateTimeField(blank=True, null=True)),
                ('dtfimobra', models.DateTimeField(blank=True, null=True)),
                ('fllicambiental', models.SmallIntegerField(blank=True, null=True)),
                ('fldesapropria', models.SmallIntegerField(blank=True, null=True)),
                ('vlextreal', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('flpendrima', models.SmallIntegerField(blank=True, null=True)),
                ('flpenddesaprop', models.SmallIntegerField(blank=True, null=True)),
                ('flpendreloca', models.SmallIntegerField(blank=True, null=True)),
                ('flpendalterdata', models.SmallIntegerField(blank=True, null=True)),
                ('flpendjudicial', models.SmallIntegerField(blank=True, null=True)),
                ('flfiltro', models.SmallIntegerField(blank=True, null=True)),
                ('flincluidoprog', models.SmallIntegerField(blank=True, null=True)),
                ('flemanalise', models.IntegerField(blank=True, null=True)),
                ('nroae', models.IntegerField(blank=True, null=True)),
                ('floae', models.IntegerField(blank=True, null=True)),
                ('dspseudonimoobra', models.CharField(blank=True, max_length=100, null=True)),
                ('cdrodoviaant', models.CharField(blank=True, max_length=20, null=True)),
                ('lixo', models.CharField(blank=True, max_length=15, null=True)),
                ('cdrodder', models.CharField(blank=True, max_length=20, null=True)),
                ('vlpercexec', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
                ('data_programada_inicio', models.DateTimeField(blank=True, null=True)),
                ('data_programada_fim', models.DateTimeField(blank=True, null=True)),
                ('data_analise_inicio', models.DateTimeField(blank=True, null=True)),
                ('data_analise_fim', models.DateTimeField(blank=True, null=True)),
                ('data_execucao_inicio', models.DateTimeField(blank=True, null=True)),
                ('data_execucao_fim', models.DateTimeField(blank=True, null=True)),
                ('data_prevista_inicio', models.DateField(blank=True, null=True)),
                ('data_prevista_termino', models.DateField(blank=True, null=True)),
                ('observacao', models.TextField(blank=True, null=True)),
                ('descricao', models.TextField(blank=True, null=True)),
                ('prioritaria', models.BooleanField(blank=True, null=True)),
                ('valor_total_obra', models.DecimalField(blank=True, decimal_places=4, max_digits=19, null=True)),
                ('valor_total_obra_formatado', models.CharField(blank=True, max_length=128, null=True)),
            ],
            options={
                'verbose_name': 'DbObraInformix',
                'verbose_name_plural': 'DbObrasInformix',
                'ordering': ('codigo_tipo_servico',),
            },
        ),
        migrations.CreateModel(
            name='Destinatario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=100, unique=True, verbose_name='Destinatário')),
                ('ativo', models.BooleanField(default=True, help_text='Opção ativa', verbose_name='Ativo')),
            ],
        ),
        migrations.CreateModel(
            name='Destinatário',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=100, unique=True, verbose_name='Destinatário')),
                ('ativo', models.BooleanField(default=True, help_text='Opção ativa', verbose_name='Ativo')),
            ],
        ),
        migrations.CreateModel(
            name='EncaminhadoPor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=100, unique=True, verbose_name='Encaminhado por')),
                ('ativo', models.BooleanField(default=True, help_text='Opção ativa', verbose_name='Ativo')),
            ],
        ),
        migrations.CreateModel(
            name='Interessado',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=100, unique=True, verbose_name='Interessado')),
                ('ativo', models.BooleanField(default=True, help_text='Opção ativa', verbose_name='Ativo')),
            ],
        ),
        migrations.CreateModel(
            name='Keyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='ObraInformix',
            fields=[
                ('CodigoObra', models.IntegerField(primary_key=True, serialize=False)),
                ('CodigoLote', models.CharField(max_length=3)),
                ('Concessionaria', models.CharField(max_length=50)),
                ('CodigoItemServico', models.CharField(blank=True, max_length=15, null=True)),
                ('DescricaoObra', models.CharField(blank=True, max_length=255, null=True)),
                ('kminicial', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('kmfinal', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('DataProgramadaInicio', models.DateTimeField(blank=True, null=True)),
                ('DataProgramadaFim', models.DateTimeField(blank=True, null=True)),
                ('DataAnaliseInicio', models.DateTimeField(blank=True, null=True)),
                ('DataAnaliseFim', models.DateTimeField(blank=True, null=True)),
                ('DataExecucaoInicio', models.DateTimeField(blank=True, null=True)),
                ('DataExecucaoFim', models.DateTimeField(blank=True, null=True)),
                ('SituacaoObra', models.IntegerField()),
                ('FlagProjeto', models.SmallIntegerField(blank=True, null=True)),
                ('cdrodder', models.CharField(blank=True, max_length=20, null=True)),
                ('ProgressoObra', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
                ('ValorTotalObra', models.DecimalField(blank=True, decimal_places=4, max_digits=19, null=True)),
                ('ValorTotalObraFormatado', models.CharField(blank=True, max_length=128, null=True)),
                ('Inaugurada', models.BooleanField(blank=True, null=True)),
                ('Prioritaria', models.BooleanField(blank=True, null=True)),
                ('DataInclusaoObraPrioritaria', models.DateTimeField(blank=True, null=True)),
                ('Apelido', models.CharField(blank=True, max_length=128, null=True)),
                ('Descricao', models.TextField(blank=True, null=True)),
                ('Observacao', models.TextField(blank=True, null=True)),
                ('TrechosEntregues', models.CharField(blank=True, max_length=500, null=True)),
                ('DataPrevistaInicio', models.DateField(blank=True, null=True)),
                ('DataPrevistaTermino', models.DateField(blank=True, null=True)),
                ('DataEventoInicial', models.DateField(blank=True, null=True)),
                ('DataInauguracao', models.DateField(blank=True, null=True)),
                ('LicencaNaoSeAplica', models.BooleanField(blank=True, null=True)),
                ('MotivoLicencaNaoSeAplica', models.CharField(blank=True, max_length=250, null=True)),
                ('DecretoNaoSeAplica', models.BooleanField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'ObraInformix',
                'verbose_name_plural': 'ObrasInformix',
            },
        ),
        migrations.CreateModel(
            name='TipoDocumento',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=100, unique=True, verbose_name='Tipo de Documento')),
                ('ativo', models.BooleanField(default=True, help_text='Opção ativa', verbose_name='Ativo')),
            ],
        ),
        migrations.CreateModel(
            name='TipoObra',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=100, unique=True, verbose_name='Tipo de obra')),
                ('ativo', models.BooleanField(default=True, help_text='Opção ativa', verbose_name='Ativo')),
            ],
        ),
        migrations.CreateModel(
            name='Protocolo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uid', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ('protocolo', models.CharField(blank=True, db_index=True, editable=False, max_length=50, null=True, unique=True, verbose_name='Protocolo')),
                ('manif_proc_em_andamento', models.BooleanField(default=False, verbose_name='MANIFESTAÇÃO DE PROCESSO EM ANDAMNETO')),
                ('processo_sei_ref', models.CharField(blank=True, max_length=25, null=True, verbose_name='Nro. Processo SEI referencial')),
                ('processo_sei', models.CharField(blank=True, max_length=25, null=True, verbose_name='Nro. Processo SEI')),
                ('obra_modo_repr', models.CharField(blank=True, choices=[('PONTUAL', 'PONTUAL'), ('LINEAR', 'LINEAR')], null=True, verbose_name='Modo de Implantação')),
                ('descricao', models.TextField(blank=True, max_length=256, null=True, verbose_name='Descrição')),
                ('cod_doc', models.CharField(blank=True, max_length=80, null=True, verbose_name='Código do Documento')),
                ('modifications_counter', models.PositiveSmallIntegerField(default=0, verbose_name='Nro. Atualizações')),
                ('km_inicial', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='KM inicial')),
                ('km_final', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True, verbose_name='KM final')),
                ('valor', models.DecimalField(blank=True, decimal_places=2, max_digits=25, null=True, verbose_name='Valor estimado(R$)')),
                ('data_base', models.DateField(blank=True, null=True, verbose_name='Data-base')),
                ('nova_data_pleito_inicio', models.DateField(blank=True, null=True, verbose_name='Nova data pleiteada para início')),
                ('nova_data_pleito_termino', models.DateField(blank=True, null=True, verbose_name='Nova data pleiteada para término')),
                ('latitude', models.DecimalField(blank=True, decimal_places=6, help_text='Ex: -23.550520', max_digits=9, null=True, verbose_name='Latitude')),
                ('longitude', models.DecimalField(blank=True, decimal_places=6, help_text='Ex: -46.633308', max_digits=9, null=True, verbose_name='Longitude')),
                ('user_name', models.CharField(max_length=150, verbose_name='Nome')),
                ('user_email', models.EmailField(max_length=254, validators=[django.core.validators.EmailValidator()], verbose_name='E-mail')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='End. IP')),
                ('hash', models.CharField(blank=True, max_length=64, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('assunto', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='protocolo.assunto', verbose_name='Assunto')),
                ('concessionaria', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='concessionarias.concessionaria', verbose_name='Concessionária')),
                ('destinatario', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='protocolo.destinatario', verbose_name='Destinatário')),
                ('encaminhado_por', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='protocolo.encaminhadopor', verbose_name='Encaminhado por')),
                ('interessado', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='protocolo.interessado', verbose_name='Interessado')),
                ('item_codigo', models.ForeignKey(blank=True, help_text='Código do item na lista de obras/implantações', null=True, on_delete=django.db.models.deletion.SET_NULL, to='protocolo.dbobrasinformix', verbose_name='Código do Item')),
                ('rodovia', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='protocolos', to='rodovias.rodovia', verbose_name='Rodovia')),
                ('tipo_documento', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='protocolo.tipodocumento', verbose_name='Tipo de documento')),
                ('tipo_obra', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='protocolo.tipoobra', verbose_name='Tipo Implantação')),
            ],
        ),
    ]
