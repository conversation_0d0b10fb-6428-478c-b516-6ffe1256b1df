from protocolo.models import DbObrasInformix
import pyodbc
from contextlib import contextmanager
from typing import Optional, Dict, Any
import logging
from functools import wraps
import time
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def format_currency_in_millions(value: float | None) -> str:
    """
    Format currency value in millions, billions or thousands with detailed representation.
    
    Args:
        value: The value to format, can be None
        
    Returns:
        Formatted string with both abbreviated and full number representation
    """
    TEXTO_NAO_PREENCHIDO = "-"
    
    if value is None or value == 0:
        return TEXTO_NAO_PREENCHIDO
        
    # Multiply by 1000 as in the C# version
    value *= 1000
    
    # Initialize return value
    valor_retorno = ""
    
    # Format based on value range
    if value > 1_000_000_000:  # billions
        valor_retorno = f"R$ {round(value / 1_000_000_000, 2)} bilhões"
    elif value > 1_000_000:  # millions
        valor_retorno = f"R$ {round(value / 1_000_000, 2)} milhões"
    elif value > 1_000:  # thousands
        valor_retorno = f"R$ {round(value / 1_000, 2)} mil"
    else:
        valor_retorno = f"R$ {round(value, 2)}"
    
    # Format the full number with thousands separator and 2 decimal places
    valor_com_duas_casas = f"{value:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
    
    return f"{valor_retorno} ({valor_com_duas_casas})"

class DatabaseConnection:
    """Class to handle database connections and operations with connection pooling"""
    
    def __init__(self, server: str, database: str, username: str, password: str, 
                 driver: str = 'FreeTDS', port: str = '1433', tds_version: str = '7.4'):
        # Database connection parameters
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.driver = driver
        self.port = port
        self.tds_version = tds_version
        
        # Connection pooling and timeout settings
        self._pool = []
        self.max_pool_size = 5
        self.connection_timeout = 30  # seconds
        self.query_timeout = 60  # seconds
        
        # Setup logging
        self._setup_logging()
        
        self.connection_string = (
            f"DRIVER={{{self.driver}}};"
            f"SERVER={self.server};"
            f"PORT={self.port};"
            f"DATABASE={self.database};"
            f"UID={self.username};"
            f"PWD={self.password};"
            f"TDS_Version={self.tds_version};"
            f"Connection Timeout={self.connection_timeout};"
            "Mars_Connection=yes;"  # Enable multiple active result sets
            "Pooling=yes;"
        )

    def _setup_logging(self) -> None:
        """Configure logging for database operations"""
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    @contextmanager
    def get_connection(self) -> pyodbc.Connection:
        """Context manager for database connections with pooling"""
        connection = None
        try:
            # Try to get a connection from the pool
            if self._pool:
                connection = self._pool.pop()
                if not self._test_connection(connection):
                    connection = None
            
            # If no valid connection from pool, create new one
            if connection is None:
                connection = pyodbc.connect(
                    self.connection_string,
                    timeout=self.connection_timeout
                )
                # Set query timeout at connection level
                connection.timeout = self.query_timeout
            
            yield connection
            
            # Return the connection to the pool if it's still valid
            if self._test_connection(connection) and len(self._pool) < self.max_pool_size:
                self._pool.append(connection)
            else:
                connection.close()
                
        except pyodbc.Error as e:
            self.logger.error(f"Database connection error: {str(e)}")
            if connection:
                connection.close()
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error: {str(e)}")
            if connection:
                connection.close()
            raise

    def _test_connection(self, connection: pyodbc.Connection) -> bool:
        """Test if a connection is still valid"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return True
        except:
            return False

    def execute_stored_procedure_with_codigo(
        self, 
        procedure_name: str, 
        codigo: int,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Optional[Dict[str, Any]]:
        """
        Execute a stored procedure with the required @codigo parameter
        
        Args:
            procedure_name: Name of the stored procedure
            codigo: The codigo parameter value
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
        
        Returns:
            Dictionary with the result row or None if no results
        """
        attempt = 0
        last_error = None
        
        while attempt < max_retries:
            try:
                with self.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute(
                            f"EXEC {procedure_name} @codigo = ?", 
                            [codigo]
                        )
                        
                        if cursor.description is None:
                            return None
                            
                        columns = [column[0] for column in cursor.description]
                        row = cursor.fetchone()
                        
                        if row is None:
                            return None
                            
                        result = {}
                        for i, value in enumerate(row):
                            column_name = columns[i]
                            
                            # Handle specific data types
                            if isinstance(value, (bytes, bytearray)):
                                value = (value if isinstance(value, bytes) else bytes(value))
                                value = value.decode('utf-8', errors='ignore')
                            
                            # Store the original value
                            result[column_name] = value
                            
                            # Add formatted version for ValorTotalObra
                            if column_name == 'ValorTotalObra':
                                result['ValorTotalObraFormatado'] = format_currency_in_millions(value)
                        
                        return result
                        
            except pyodbc.Error as e:
                last_error = e
                self.logger.warning(
                    f"Attempt {attempt + 1}/{max_retries} failed: {str(e)}"
                )
                if self._is_retryable_error(e):
                    attempt += 1
                    if attempt < max_retries:
                        time.sleep(retry_delay)
                        continue
                break
                
            except Exception as e:
                self.logger.error(f"Unexpected error: {str(e)}")
                raise
                
        error_msg = f"Failed to execute stored procedure after {max_retries} attempts"
        if last_error:
            error_msg += f": {str(last_error)}"
        raise Exception(error_msg)
    
    def execute_stored_procedure_get_db_obras(
        self, 
        procedure_name: str = 'SPListarObrasPorFiltros', 
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Optional[Dict[str, Any]]:
        """
        Execute a stored procedure with the required @codigo parameter
        
        Args:
            procedure_name: Name of the stored procedure
            codigo: The codigo parameter value
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
        
        Returns:
            Dictionary with the result row or None if no results
        """
        attempt = 0
        last_error = None
        
        while attempt < max_retries:
            try:
                with self.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute(f"EXEC {procedure_name}")
                        
                        if cursor.description is None:
                            return None
                            
                        columns = [column[0] for column in cursor.description]
                        rows = cursor.fetchall()
                        
                        if rows is None:
                            return None

                        results = []
                        for n_row,row in enumerate(rows):
                            result = {}
                            result['id'] = n_row
                            for i, value in enumerate(row):
                                if i == 6:
                                    # ignora a segunda col com mesmo nome "CodigoObra"
                                    continue
                                column_name = columns[i]
                                
                                if column_name == 'CodigoObra':
                                    if value is None:
                                        continue
                                    else:
                                        value = int(value)
                                
                                # Handle specific data types
                                if isinstance(value, (bytes, bytearray)):
                                    value = (value if isinstance(value, bytes) else bytes(value))
                                    value = value.decode('utf-8', errors='ignore')
                                
                                if isinstance(value, str):
                                    value = value.strip()

                                # Store the original value
                                result[column_name] = value
                                
                                # Add formatted version for ValorTotalObra
                                if column_name == 'ValorTotalObra':
                                    result['ValorTotalObraFormatado'] = format_currency_in_millions(value)
                                
                                 # Add formatted version for ValorTotalObra
                                if column_name == 'CodigoLote':
                                    try:
                                        result['CodigoLote'] = int(value)
                                    except:
                                        result['CodigoLote'] = None

                            results.append(result)
                        return results
                        
            except pyodbc.Error as e:
                last_error = e
                self.logger.warning(
                    f"Attempt {attempt + 1}/{max_retries} failed: {str(e)}"
                )
                if self._is_retryable_error(e):
                    attempt += 1
                    if attempt < max_retries:
                        time.sleep(retry_delay)
                        continue
                break
                
            except Exception as e:
                self.logger.error(f"Unexpected error: {str(e)}")
                raise
                
        error_msg = f"Failed to execute stored procedure after {max_retries} attempts"
        if last_error:
            error_msg += f": {str(last_error)}"
        raise Exception(error_msg)

    def _is_retryable_error(self, error: pyodbc.Error) -> bool:
        """Determine if an error is retryable"""
        retryable_errors = [
            'Connection reset by peer',
            'Lost connection',
            'Connection timed out',
            'Deadlock victim',
            'Lock request time out'
        ]
        error_msg = str(error).lower()
        return any(err.lower() in error_msg for err in retryable_errors)

from typing import List, Dict, Any
def map_sp_results_to_dbobras(sp_results: List[Dict[str, Any]]) -> List[DbObrasInformix]:
    """
    Map the stored procedure results (a list of dictionaries) to a list of DbObrasInformix model instances.
    
    Args:
        sp_results: List of dictionaries returned from execute_stored_procedure_get_db_obras
        
    Returns:
        List of unsaved DbObrasInformix model instances.
    """
    # Mapping from stored procedure column names to DbObrasInformix model field names.
    column_to_field = {
        "CodigoObra": "codigo_obra",
        "CodigoLote": "codigo_lote",
        "Concessionaria": "concessionaria",
        "cdrodovia": "cdrodovia",
        "NMRODOVIA": "nmrodovia",
        # The second "CodigoObra" is skipped in your SP result (index 6) so we ignore it.
        "codtrecho": "codtrecho",
        "kminicial": "kminicial",
        "kmfinal": "kmfinal",
        "DescricaoObra": "descricao_obra",
        "CodigoTipoServico": "codigo_tipo_servico",
        "DescricaoTipoServico": "descricao_tipo_servico",
        "SituacaoObra": "situacao_obra",
        "ProgressoObra": "progresso_obra",
        "dtreceb": "dtreceb",
        "nrcstreceb": "nrcstreceb",
        "nrprocessoreceb": "nrprocessoreceb",
        "nrprotocoloreceb": "nrprotocoloreceb",
        "nraceiteprov": "nraceiteprov",
        "dtaceiteprov": "dtaceiteprov",
        "nraceitedef": "nraceitedef",
        "dtaceitedef": "dtaceitedef",
        "cdcgc": "cdcgc",
        "dsobsemptecno": "dsobsemptecno",
        "cdgrd": "cdgrd",
        "cddocprojeto": "cddocprojeto",
        "codop": "codop",
        "dtaalter": "dtaalter",
        "SituacaoProjeto": "situacao_projeto",
        "CodigoItemServico": "codigo_item_servico",
        "dtentregaproj": "dtentregaproj",
        "dtaprovaproj": "dtaprovaproj",
        "dsobs": "dsobs",
        "codigotota": "codigotota",
        "flprojeto": "flprojeto",
        "nrultadequa": "nrultadequa",
        "dtprocessorec": "dtprocessorec",
        "dttermoaceiterec": "dttermoaceiterec",
        "dtasbuiltprev": "dtasbuiltprev",
        "dtasbuiltent": "dtasbuiltent",
        "dtvistiniobra": "dtvistiniobra",
        "dtvistfimobra": "dtvistfimobra",
        "dtiniobra": "dtiniobra",
        "dtfimobra": "dtfimobra",
        "fllicambiental": "fllicambiental",
        "fldesapropria": "fldesapropria",
        "vlextreal": "vlextreal",
        "flpendrima": "flpendrima",
        "flpenddesaprop": "flpenddesaprop",
        "flpendreloca": "flpendreloca",
        "flpendalterdata": "flpendalterdata",
        "flpendjudicial": "flpendjudicial",
        "flfiltro": "flfiltro",
        "flincluidoprog": "flincluidoprog",
        "flemanalise": "flemanalise",
        "nroae": "nroae",
        "floae": "floae",
        "dspseudonimoobra": "dspseudonimoobra",
        "cdrodoviaant": "cdrodoviaant",
        "lixo": "lixo",
        "CDRODDER": "cdrodder",
        "vlpercexec": "vlpercexec",
        "DataProgramadaInicio": "data_programada_inicio",
        "DataProgramadaFim": "data_programada_fim",
        "DataAnaliseInicio": "data_analise_inicio",
        "DataAnaliseFim": "data_analise_fim",
        "DataExecucaoInicio": "data_execucao_inicio",
        "DataExecucaoFim": "data_execucao_fim",
        "DataPrevistaInicio": "data_prevista_inicio",
        "DataPrevistaTermino": "data_prevista_termino",
        "Observacao": "observacao",
        "Descricao": "descricao",
        "Prioritaria": "prioritaria",
        "ValorTotalObra": "valor_total_obra",
    }
    
    db_obras_instances = []
    for row in sp_results:
        # Build a dictionary with the correct field names and values for DbObrasInformix
        model_data = {}
        for col_name, value in row.items():
            if col_name in column_to_field:
                field_name = column_to_field[col_name]
                model_data[field_name] = value

        # Create a new instance of DbObrasInformix (unsaved)
        instance = DbObrasInformix(**model_data)
        db_obras_instances.append(instance)
    
    return db_obras_instances
