import os
import pandas as pd
from celery import shared_task
from celery.utils.log import get_task_logger
from protocolo.models import DbObrasInformix
from protocolo.util.recuperar_dados_informix import map_sp_results_to_dbobras, DatabaseConnection

# Configure logger for this task
logger = get_task_logger(__name__)

@shared_task(
    name='protocolo.tasks.import_db_obras_data',
    bind=True,
    max_retries=3,
    default_retry_delay=300,  # 5 minutes
    acks_late=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
)
def import_db_obras_data(self):
    logger.info("Starting import of DbObrasInformix data from stored procedure...")
    server = os.getenv('BUSCA_OBRA_DB_SERVER', '')
    database = os.getenv('BUSCA_OBRA_DB_NAME', '')
    username = os.getenv('BUSCA_OBRA_DB_USER', '')
    password = os.getenv('BUSCA_OBRA_DB_PASSWORD', '')
    driver = os.getenv('BUSCA_OBRA_DB_DRIVER', 'FreeTDS')
    port = os.getenv('BUSCA_OBRA_DB_PORT', '1433')
    tds_version = os.getenv('DB_TDS_VERSION', '7.4')

    service = DatabaseConnection(
        server=server,
        database=database,
        username=username,
        password=password,
        driver=driver,
        port=port,
        tds_version=tds_version,
    )  # This class must implement the method execute_stored_procedure_get_db_obras

    try:
        # Execute the stored procedure
        sp_results = service.execute_stored_procedure_get_db_obras()
        if not sp_results:
            logger.warning("No data returned from stored procedure.")
            return

        # Map the results to a list of unsaved DbObrasInformix instances
        instances = map_sp_results_to_dbobras(sp_results)

        if not instances:
            logger.warning("Mapping resulted in no instances to save.")
            return

        DbObrasInformix.objects.bulk_create(instances, ignore_conflicts=True)
        logger.info(f"Successfully imported {len(instances)} records.")
    except Exception as e:
        logger.error(f"An error occurred during import: {e}")
        return f"An error occurred during import: {e}"