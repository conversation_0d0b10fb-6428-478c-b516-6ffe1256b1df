# to implement

# curl -X PUT "http://localhost:8000/api/v1/protocolo/atualiza-processo-sei/" \
#     #  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM3NTUwMjc2LCJpYXQiOjE3Mzc1NDkzNzYsImp0aSI6IjUxN2U1MjgxNDcwZDQwODc4NDQyYjQ4MzJlYjE4MzA3IiwidXNlcl9pZCI6MX0.LvXPATQSz5l3BTnRK6FCegW_wBHjwmz-W1cHhAjAFRc" \
#      -H "Content-Type: application/json" \
#      -d '{"protocolo": "ART-0287FC6E-00000013/2025", "processo_sei": "134.00000605/2025-04"}'


# curl -X GET "http://localhost:8000/api/v1/protocolo/?codigo=ART-0287FC6E-00000013/2025" \
#       -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM3NTUwMjc2LCJpYXQiOjE3Mzc1NDkzNzYsImp0aSI6IjUxN2U1MjgxNDcwZDQwODc4NDQyYjQ4MzJlYjE4MzA3IiwidXNlcl9pZCI6MX0.LvXPATQSz5l3BTnRK6FCegW_wBHjwmz-W1cHhAjAFRc" \
#      -H "Accept: application/json"



# curl -X GET \
#   "http://localhost:8000/api/v1/protocolo/?codigo=ART-DE3486E1-00000017/2025" \
#         -H "X-API-KEY: my-secret-api-key" \
#       -H "Accept: application/json"


# curl -X PUT \
#         -H "X-API-KEY: my-secret-api-key" \
#         -H "Content-Type: application/json" \
#         -d '{
#             "protocolos": [
#                 {
#                     "protocolo": "ART-2E13B913-00000010/2025",
#                     "processo_sei": "199.00000605/2025-05"
#                 },
#                 {
#                     "protocolo": "ART-E94CE674-00000009/2025",
#                     "processo_sei": "200.00000605/2025-06"
#                 }
#             ]
#         }' \
#         http://localhost:8000/api/v1/protocolo/atualiza-bulk-processos-sei/

# curl -X OPTIONS http://localhost:8000/api/v1/protocolo/atualiza-bulk-processos-sei/ \
#    -H "X-API-KEY: my-secret-api-key" \
#   -H "Origin: https://sei.sp.gov.br" \
#   -H "Access-Control-Request-Method: PUT" \
#   -H "Access-Control-Request-Headers: authorization,content-type" -v