from django.urls import path
from . import views
from .api import ListProtocolosAPIView, UpdateProcessoSeiAPIView, RetrieveProtocoloAPIView

app_name = 'protocolo'

urlpatterns = [
    
    path("", views.protocolo_create_view, name="protocolo-create"),
    path("novo/p2/", views.protocolo_step2_view, name="protocolo-create-step2"),
    path(
        'atualiza-processo-sei/',
        UpdateProcessoSeiAPIView.as_view(),
        name='api-update-processo-sei'
    ),
    path('list/', ListProtocolosAPIView.as_view(), name='api-list-protocolos'),
    path(
        'retrieve/',
        RetrieveProtocoloAPIView.as_view(),
        name='api-retrieve-protocolo'
    ),
    path('consulta/', views.protocolo_view, name='protocolo-view'),
    path('lista/', views.list_protocolos, name='list-protocolos'),
    path('not-implemented/', views.not_implemented_view, name='not-implemented'),
    path(
        'rodovia-autocomplete/', 
        views.RodoviaAutocompleteView.as_view(), 
        name='rodovia-autocomplete'
    ),
    path('item-obra-autocomplete/', views.DbObrasAutocomplete.as_view(), name='item-obra-autocomplete'),
    path('<uuid:protocolo_uid>/download', views.protocolo_arquivo_view, name='arquivo'),
    path('tipo-obra-autocomplete/', views.TipoObraAutocomplete.as_view(), name='tipo-obra-autocomplete'),
    path('get-obra-details/', views.get_obra_details, name='get-obra-details'),
    path("get-concessionarias/", views.get_concessionarias, name="get_concessionarias"),
    path('<uuid:protocolo_uid>/pdf', views.protocolo_pdf_view, name='pdf'),
    path('<uuid:protocolo_uid>/pdf-view', views.protocolo_pdf_view_html, name='pdf-view'),
    path('<uuid:protocolo_uid>/pdf-modal-download', views.download_pdf_modal, name='download-pdf-modal'),
    path('<uuid:protocolo_uid>', views.success, name='success'),
    
]
