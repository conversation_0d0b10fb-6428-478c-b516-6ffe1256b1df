# api.py

import os
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from django.db import transaction
# from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>  # optional

from rest_framework.permissions import BasePermission
from rest_framework_simplejwt.authentication import J<PERSON>TAuthentication
from rest_framework.authentication import SessionAuthentication
from rest_framework.exceptions import PermissionDenied
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

# Import your new auth and permission classes
from .auth import APIKeyAuthentication
from .permissions import CC<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>eyPermission

from .models import Protocolo
from .serializers import ProtocoloProcessoSeiSerializer


class UpdateProcessoSeiAPIView(APIView):
    """
    API endpoint for updating the 'processo_sei' field of a protocol record.
    
    Authentication:
        - JWT Bearer token
        - Session authentication
        - API Key via X-API-KEY header
    
    Authorization:
        Users must either:
        - Be authenticated and belong to the 'cci' group
        - Provide a valid API key in the X-API-KEY header
    
    Request Body:
        {
            "protocolo": "string",     # Required. Protocol identifier (case-insensitive)
            "processo_sei": "string"   # Required. New SEI process number
        }
    
    Responses:
        200 OK: Protocol successfully updated
            {
                "protocolo": "ABC123",
                "processo_sei": "12345.123456/2024-01"
            }
        
        400 Bad Request:
            - Missing 'protocolo' field
            - Multiple protocols found
            - Invalid processo_sei format
            
        404 Not Found:
            - Protocol not found
            
        401 Unauthorized:
            - Invalid or missing authentication
            
        403 Forbidden:
            - User not in 'cci' group
            - Invalid API key
    """
    authentication_classes = [
        APIKeyAuthentication,
        JWTAuthentication,
        SessionAuthentication,
    ]
    permission_classes = [CCIGroupOrAPIKeyPermission]

    def put(self, request, format=None):
        data = request.data
        
        # Verify that the protocolo value is provided
        protocolo_value = data.get("protocolo")
        if not protocolo_value:
            return Response(
                {"error": "Campo 'protocolo' ausente no corpo da solicitação."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Perform a case-insensitive lookup on the protocolo field.
        try:
            instance = Protocolo.objects.get(protocolo__iexact=protocolo_value)
        except Protocolo.DoesNotExist:
            return Response(
                {"error": f"Protocolo não encontrado: {protocolo_value}."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Protocolo.MultipleObjectsReturned:
            return Response(
                {"error": f"Múltiplos protocolos encontrados para o código {protocolo_value}."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update with our serializer
        serializer = ProtocoloProcessoSeiSerializer(instance, data=data, partial=True)
        if serializer.is_valid():
            updated_instance = serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RetrieveProtocoloAPIView(APIView):
    """
    API endpoint para consultar um protocolo.
    GET /?codigo=ART-ABCD1234-00000001/2025
    Aceita JWT+grupo 'cci' ou API Key.
    """
    authentication_classes = [
        APIKeyAuthentication,
        JWTAuthentication,
        SessionAuthentication,
    ]
    permission_classes = [CCIGroupOrAPIKeyPermission]

    def get(self, request, format=None):
        # Get protocolo from query parameters
        protocolo_value = request.query_params.get("codigo")
        if not protocolo_value:
            return Response(
                {"error": "Parâmetro 'codigo' não fornecido na URL."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        protocolo_value = protocolo_value.strip()
        
        try:
            instance = Protocolo.objects.get(protocolo__iexact=protocolo_value)
        except Protocolo.DoesNotExist:
            return Response(
                {"error": f"Protocolo não encontrado: {protocolo_value}."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Protocolo.MultipleObjectsReturned:
            return Response(
                {"error": f"Múltiplos protocolos encontrados para o código {protocolo_value}."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = ProtocoloProcessoSeiSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ListProtocolosAPIView(APIView):
    """
    API endpoint para listar todos os Protocolos, com suporte a:
      - Paginação (page, page_size)
      - Limite de itens (limit)
      - Ordenação (ordering)

    Exemplo de uso:
      GET /api/v1/protocolos/list?page=2&page_size=5&limit=50&ordering=-created_at

    Autenticação:
      - JWT + grupo 'cci', OU
      - API Key (via cabeçalho X-API-KEY)
    """
    authentication_classes = [
        APIKeyAuthentication,
        JWTAuthentication,
        SessionAuthentication,
    ]
    permission_classes = [CCIGroupOrAPIKeyPermission]

    def get(self, request, format=None):
        qs = Protocolo.objects.all()

        # 1) Ordering
        # Default: order by '-id' (descending). The user can pass e.g. 'ordering=created_at'
        ordering = request.query_params.get('ordering', '-id')
        qs = qs.order_by(ordering)

        # 2) Optional 'limit'
        # If provided, cap the total results returned (BEFORE pagination).
        limit_str = request.query_params.get('limit')
        if limit_str is not None:
            try:
                limit_val = int(limit_str)
                if limit_val > 0:
                    qs = qs[:limit_val]
            except ValueError:
                pass  # If invalid, ignore or handle error

        # 3) Pagination
        page = request.query_params.get('page', 1)
        page_size_str = request.query_params.get('page_size', 10)
        try:
            page_size = int(page_size_str)
        except ValueError:
            page_size = 10

        paginator = Paginator(qs, page_size)
        try:
            page_obj = paginator.page(page)
        except PageNotAnInteger:
            page_obj = paginator.page(1)
        except EmptyPage:
            # If the page is out of range, return an empty page or the last page
            page_obj = paginator.page(paginator.num_pages)

        serializer = ProtocoloProcessoSeiSerializer(page_obj.object_list, many=True)

        # 4) Build response with pagination metadata
        response_data = {
            'count': paginator.count,               # total items across all pages
            'page': page_obj.number,                # current page number
            'num_pages': paginator.num_pages,       # total number of pages
            'page_size': page_size,                 # current page size
            'ordering': ordering,                   # the field(s) used for ordering
            'results': serializer.data,             # serialized data for this page
        }

        return Response(response_data, status=status.HTTP_200_OK)


class BulkUpdateProcessoSeiAPIView(APIView):
    """
    API endpoint for bulk updating the 'processo_sei' field of multiple protocol records.
    
    Authentication:
        - JWT Bearer token
        - Session authentication
        - API Key via X-API-KEY header
    
    Authorization:
        Users must either:
        - Be authenticated and belong to the 'cci' group
        - Provide a valid API key in the X-API-KEY header
    
    Request Body:
        {
            "protocolos": [
                {
                    "protocolo": "string",     # Required. Protocol identifier (case-insensitive)
                    "processo_sei": "string"   # Required. New SEI process number
                },
                ...
            ]
        }
    
    Responses:
        200 OK: All protocols successfully updated
            {
                "success": [
                    {
                        "protocolo": "ABC123",
                        "processo_sei": "12345.123456/2024-01"
                    },
                    ...
                ],
                "errors": []
            }
        
        207 Multi-Status: Some updates succeeded, some failed
            {
                "success": [
                    {
                        "protocolo": "ABC123",
                        "processo_sei": "12345.123456/2024-01"
                    }
                ],
                "errors": [
                    {
                        "protocolo": "XYZ789",
                        "error": "Protocolo não encontrado"
                    }
                ]
            }
            
        400 Bad Request:
            - Missing 'protocolos' field
            - Empty protocolos list
            - Invalid data format
            
        401 Unauthorized:
            - Invalid or missing authentication
            
        403 Forbidden:
            - User not in 'cci' group
            - Invalid API key
    """
    authentication_classes = [
        APIKeyAuthentication,
        JWTAuthentication,
        SessionAuthentication,
    ]
    permission_classes = [CCIGroupOrAPIKeyPermission]

    def put(self, request, format=None):
        """
        Bulk update processo_sei fields for multiple protocols.
        
        Args:
            request: HTTP request object containing the protocols data
            format: Response format (unused, kept for compatibility)
            
        Returns:
            Response object with update results and/or error messages
        """
        data = request.data
        protocolos_data = data.get("protocolos", [])

        # Validate request structure
        if not isinstance(protocolos_data, list):
            return Response(
                {"error": "O campo 'protocolos' deve ser uma lista de protocolos."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not protocolos_data:
            return Response(
                {"error": "A lista de protocolos está vazia."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Initialize results containers
        successful_updates = []
        failed_updates = []

        # Process each protocol update within a transaction
        with transaction.atomic():
            for item in protocolos_data:
                protocolo_value = item.get("protocolo")
                
                if not protocolo_value:
                    failed_updates.append({
                        "protocolo": None,
                        "error": "Campo 'protocolo' ausente"
                    })
                    continue

                try:
                    # Find protocol record (case-insensitive)
                    instance = Protocolo.objects.get(protocolo__iexact=protocolo_value)
                    
                    # Validate and save changes
                    serializer = ProtocoloProcessoSeiSerializer(
                        instance, 
                        data=item,
                        partial=True
                    )
                    
                    if serializer.is_valid():
                        serializer.save()
                        successful_updates.append(serializer.data)
                    else:
                        failed_updates.append({
                            "protocolo": protocolo_value,
                            "error": serializer.errors
                        })

                except Protocolo.DoesNotExist:
                    failed_updates.append({
                        "protocolo": protocolo_value,
                        "error": "Protocolo não encontrado"
                    })
                except Protocolo.MultipleObjectsReturned:
                    failed_updates.append({
                        "protocolo": protocolo_value,
                        "error": "Múltiplos protocolos encontrados"
                    })
                except Exception as e:
                    failed_updates.append({
                        "protocolo": protocolo_value,
                        "error": str(e)
                    })

        # Prepare response
        response_data = {
            "success": successful_updates,
            "errors": failed_updates
        }

        # Choose appropriate status code
        if not failed_updates and successful_updates:
            response_status = status.HTTP_200_OK
        elif failed_updates and successful_updates:
            response_status = status.HTTP_207_MULTI_STATUS
        else:
            response_status = status.HTTP_400_BAD_REQUEST

        return Response(response_data, status=response_status)

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

@csrf_exempt
@require_http_methods(["OPTIONS"])
def preflight_view(request):
    response = JsonResponse({"message": "Preflight OK"})
    response["Access-Control-Allow-Origin"] = "https://sei.sp.gov.br"
    response["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response["Access-Control-Allow-Headers"] = "Authorization, Content-Type"
    response["Access-Control-Allow-Credentials"] = "true"
    return response
