# auth.py
import os
from dotenv import load_dotenv
from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from rest_framework.authentication import BaseAuthentication
from rest_framework import exceptions

load_dotenv()

class APIKeyAuthentication(BaseAuthentication):
    """
    Custom authentication class that checks for a header 'X-API-KEY'.
    If the key matches the environment variable API_KEY_PROTOCOLO,
    the request is authenticated as an AnonymousUser with a special marker.
    """
    def authenticate(self, request):
        # Retrieve the API key from a custom header (could also be 'Authorization: Api-Key ...')
        api_key = request.headers.get('X-API-KEY')
        if not api_key:
            return None  # No API key provided => proceed to next auth method

        # Compare to the expected key from your environment variable
        expected_key = os.getenv('API_KEY_PROTOCOLO', '')
        if expected_key and api_key == expected_key:
            # Mark the request as authenticated via API Key
            # We return (AnonymousUser, "API_KEY") so that
            # later permission checks know it was API-key auth
            return (AnonymousUser(), "API_KEY")

        # If the key doesn't match
        raise exceptions.AuthenticationFailed('API key inválida')
