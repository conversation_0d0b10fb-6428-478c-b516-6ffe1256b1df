var wms_layers = [];


        var lyr_OSMStandard_0 = new ol.layer.Tile({
            'title': 'OSM Standard',
            'type':'base',
            'opacity': 1.000000,
            
            
            source: new ol.source.XYZ({
            attributions: ' &middot; <a href="https://www.openstreetmap.org/copyright">© OpenStreetMap contributors, CC-BY-SA</a>',
                url: 'http://tile.openstreetmap.org/{z}/{x}/{y}.png'
            })
        });
var format_ESTADODESP_1 = new ol.format.GeoJSON();
var features_ESTADODESP_1 = format_ESTADODESP_1.readFeatures(json_ESTADODESP_1, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_ESTADODESP_1 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_ESTADODESP_1.addFeatures(features_ESTADODESP_1);
var lyr_ESTADODESP_1 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_ESTADODESP_1, 
                style: style_ESTADODESP_1,
                popuplayertitle: "ESTADO DE SP",
                interactive: true,
                title: '<img src="/static/styles/legend/ESTADODESP_1.png" /> ESTADO DE SP'
            });
var format_MUNCIPIOSSP_2 = new ol.format.GeoJSON();
var features_MUNCIPIOSSP_2 = format_MUNCIPIOSSP_2.readFeatures(json_MUNCIPIOSSP_2, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_MUNCIPIOSSP_2 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_MUNCIPIOSSP_2.addFeatures(features_MUNCIPIOSSP_2);
var lyr_MUNCIPIOSSP_2 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_MUNCIPIOSSP_2, 
                style: style_MUNCIPIOSSP_2,
                popuplayertitle: "MUNÍCIPIOS - SP",
                interactive: true,
                title: '<img src="/static/styles/legend/MUNCIPIOSSP_2.png" /> MUNÍCIPIOS - SP'
            });
var format_DER_3 = new ol.format.GeoJSON();
var features_DER_3 = format_DER_3.readFeatures(json_DER_3, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_DER_3 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_DER_3.addFeatures(features_DER_3);
var lyr_DER_3 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_DER_3, 
                style: style_DER_3,
                popuplayertitle: "DER",
                interactive: true,
                title: '<img src="/static/styles/legend/DER_3.png" /> DER'
            });
var format_L32NOVOLITORAL_4 = new ol.format.GeoJSON();
var features_L32NOVOLITORAL_4 = format_L32NOVOLITORAL_4.readFeatures(json_L32NOVOLITORAL_4, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L32NOVOLITORAL_4 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L32NOVOLITORAL_4.addFeatures(features_L32NOVOLITORAL_4);
var lyr_L32NOVOLITORAL_4 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L32NOVOLITORAL_4, 
                style: style_L32NOVOLITORAL_4,
                popuplayertitle: "L32-NOVOLITORAL",
                interactive: true,
                title: '<img src="/static/styles/legend/L32NOVOLITORAL_4.png" /> L32-NOVOLITORAL'
            });
var format_L31ECONOROESTEFXD_5 = new ol.format.GeoJSON();
var features_L31ECONOROESTEFXD_5 = format_L31ECONOROESTEFXD_5.readFeatures(json_L31ECONOROESTEFXD_5, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L31ECONOROESTEFXD_5 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L31ECONOROESTEFXD_5.addFeatures(features_L31ECONOROESTEFXD_5);
var lyr_L31ECONOROESTEFXD_5 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L31ECONOROESTEFXD_5, 
                style: style_L31ECONOROESTEFXD_5,
                popuplayertitle: "L31-ECONOROESTE-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L31ECONOROESTEFXD_5.png" /> L31-ECONOROESTE-FXD'
            });
var format_L31ECONOROESTE_6 = new ol.format.GeoJSON();
var features_L31ECONOROESTE_6 = format_L31ECONOROESTE_6.readFeatures(json_L31ECONOROESTE_6, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L31ECONOROESTE_6 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L31ECONOROESTE_6.addFeatures(features_L31ECONOROESTE_6);
var lyr_L31ECONOROESTE_6 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L31ECONOROESTE_6, 
                style: style_L31ECONOROESTE_6,
                popuplayertitle: "L31-ECONOROESTE",
                interactive: true,
                title: '<img src="/static/styles/legend/L31ECONOROESTE_6.png" /> L31-ECONOROESTE'
            });
var format_L30EIXOSPFXD_7 = new ol.format.GeoJSON();
var features_L30EIXOSPFXD_7 = format_L30EIXOSPFXD_7.readFeatures(json_L30EIXOSPFXD_7, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L30EIXOSPFXD_7 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L30EIXOSPFXD_7.addFeatures(features_L30EIXOSPFXD_7);
var lyr_L30EIXOSPFXD_7 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L30EIXOSPFXD_7, 
                style: style_L30EIXOSPFXD_7,
                popuplayertitle: "L30-EIXOSP-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L30EIXOSPFXD_7.png" /> L30-EIXOSP-FXD'
            });
var format_L30EIXOSP_8 = new ol.format.GeoJSON();
var features_L30EIXOSP_8 = format_L30EIXOSP_8.readFeatures(json_L30EIXOSP_8, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L30EIXOSP_8 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L30EIXOSP_8.addFeatures(features_L30EIXOSP_8);
var lyr_L30EIXOSP_8 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L30EIXOSP_8, 
                style: style_L30EIXOSP_8,
                popuplayertitle: "L30-EIXOSP",
                interactive: true,
                title: '<img src="/static/styles/legend/L30EIXOSP_8.png" /> L30-EIXOSP'
            });
var format_L29VIAPAULISTAFXD_9 = new ol.format.GeoJSON();
var features_L29VIAPAULISTAFXD_9 = format_L29VIAPAULISTAFXD_9.readFeatures(json_L29VIAPAULISTAFXD_9, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L29VIAPAULISTAFXD_9 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L29VIAPAULISTAFXD_9.addFeatures(features_L29VIAPAULISTAFXD_9);
var lyr_L29VIAPAULISTAFXD_9 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L29VIAPAULISTAFXD_9, 
                style: style_L29VIAPAULISTAFXD_9,
                popuplayertitle: "L29-VIAPAULISTA-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L29VIAPAULISTAFXD_9.png" /> L29-VIAPAULISTA-FXD'
            });
var format_L29VIAPAULISTA_10 = new ol.format.GeoJSON();
var features_L29VIAPAULISTA_10 = format_L29VIAPAULISTA_10.readFeatures(json_L29VIAPAULISTA_10, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L29VIAPAULISTA_10 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L29VIAPAULISTA_10.addFeatures(features_L29VIAPAULISTA_10);
var lyr_L29VIAPAULISTA_10 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L29VIAPAULISTA_10, 
                style: style_L29VIAPAULISTA_10,
                popuplayertitle: "L29-VIAPAULISTA",
                interactive: true,
                title: '<img src="/static/styles/legend/L29VIAPAULISTA_10.png" /> L29-VIAPAULISTA'
            });
var format_L28ENTREVIASFXD_11 = new ol.format.GeoJSON();
var features_L28ENTREVIASFXD_11 = format_L28ENTREVIASFXD_11.readFeatures(json_L28ENTREVIASFXD_11, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L28ENTREVIASFXD_11 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L28ENTREVIASFXD_11.addFeatures(features_L28ENTREVIASFXD_11);
var lyr_L28ENTREVIASFXD_11 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L28ENTREVIASFXD_11, 
                style: style_L28ENTREVIASFXD_11,
                popuplayertitle: "L28-ENTREVIAS-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L28ENTREVIASFXD_11.png" /> L28-ENTREVIAS-FXD'
            });
var format_L28ENTREVIAS_12 = new ol.format.GeoJSON();
var features_L28ENTREVIAS_12 = format_L28ENTREVIAS_12.readFeatures(json_L28ENTREVIAS_12, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L28ENTREVIAS_12 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L28ENTREVIAS_12.addFeatures(features_L28ENTREVIAS_12);
var lyr_L28ENTREVIAS_12 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L28ENTREVIAS_12, 
                style: style_L28ENTREVIAS_12,
                popuplayertitle: "L28-ENTREVIAS",
                interactive: true,
                title: '<img src="/static/styles/legend/L28ENTREVIAS_12.png" /> L28-ENTREVIAS'
            });
var format_L27TAMOIOSFXD_13 = new ol.format.GeoJSON();
var features_L27TAMOIOSFXD_13 = format_L27TAMOIOSFXD_13.readFeatures(json_L27TAMOIOSFXD_13, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L27TAMOIOSFXD_13 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L27TAMOIOSFXD_13.addFeatures(features_L27TAMOIOSFXD_13);
var lyr_L27TAMOIOSFXD_13 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L27TAMOIOSFXD_13, 
                style: style_L27TAMOIOSFXD_13,
                popuplayertitle: "L27-TAMOIOS-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L27TAMOIOSFXD_13.png" /> L27-TAMOIOS-FXD'
            });
var format_L27TAMOIOS_14 = new ol.format.GeoJSON();
var features_L27TAMOIOS_14 = format_L27TAMOIOS_14.readFeatures(json_L27TAMOIOS_14, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L27TAMOIOS_14 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L27TAMOIOS_14.addFeatures(features_L27TAMOIOS_14);
var lyr_L27TAMOIOS_14 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L27TAMOIOS_14, 
                style: style_L27TAMOIOS_14,
                popuplayertitle: "L27-TAMOIOS",
                interactive: true,
                title: '<img src="/static/styles/legend/L27TAMOIOS_14.png" /> L27-TAMOIOS'
            });
var format_L25SPMARFXD_15 = new ol.format.GeoJSON();
var features_L25SPMARFXD_15 = format_L25SPMARFXD_15.readFeatures(json_L25SPMARFXD_15, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L25SPMARFXD_15 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L25SPMARFXD_15.addFeatures(features_L25SPMARFXD_15);
var lyr_L25SPMARFXD_15 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L25SPMARFXD_15, 
                style: style_L25SPMARFXD_15,
                popuplayertitle: "L25-SPMAR-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L25SPMARFXD_15.png" /> L25-SPMAR-FXD'
            });
var format_L25SPMAR_16 = new ol.format.GeoJSON();
var features_L25SPMAR_16 = format_L25SPMAR_16.readFeatures(json_L25SPMAR_16, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L25SPMAR_16 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L25SPMAR_16.addFeatures(features_L25SPMAR_16);
var lyr_L25SPMAR_16 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L25SPMAR_16, 
                style: style_L25SPMAR_16,
                popuplayertitle: "L25-SPMAR",
                interactive: true,
                title: '<img src="/static/styles/legend/L25SPMAR_16.png" /> L25-SPMAR'
            });
var format_L24RODOANELFXD_17 = new ol.format.GeoJSON();
var features_L24RODOANELFXD_17 = format_L24RODOANELFXD_17.readFeatures(json_L24RODOANELFXD_17, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L24RODOANELFXD_17 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L24RODOANELFXD_17.addFeatures(features_L24RODOANELFXD_17);
var lyr_L24RODOANELFXD_17 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L24RODOANELFXD_17, 
                style: style_L24RODOANELFXD_17,
                popuplayertitle: "L24-RODOANEL-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L24RODOANELFXD_17.png" /> L24-RODOANEL-FXD'
            });
var format_L24RODOANEL_18 = new ol.format.GeoJSON();
var features_L24RODOANEL_18 = format_L24RODOANEL_18.readFeatures(json_L24RODOANEL_18, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L24RODOANEL_18 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L24RODOANEL_18.addFeatures(features_L24RODOANEL_18);
var lyr_L24RODOANEL_18 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L24RODOANEL_18, 
                style: style_L24RODOANEL_18,
                popuplayertitle: "L24-RODOANEL",
                interactive: true,
                title: '<img src="/static/styles/legend/L24RODOANEL_18.png" /> L24-RODOANEL'
            });
var format_L23ECOPISTASFXD_19 = new ol.format.GeoJSON();
var features_L23ECOPISTASFXD_19 = format_L23ECOPISTASFXD_19.readFeatures(json_L23ECOPISTASFXD_19, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L23ECOPISTASFXD_19 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L23ECOPISTASFXD_19.addFeatures(features_L23ECOPISTASFXD_19);
var lyr_L23ECOPISTASFXD_19 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L23ECOPISTASFXD_19, 
                style: style_L23ECOPISTASFXD_19,
                popuplayertitle: "L23-ECOPISTAS-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L23ECOPISTASFXD_19.png" /> L23-ECOPISTAS-FXD'
            });
var format_L23ECOPISTAS_20 = new ol.format.GeoJSON();
var features_L23ECOPISTAS_20 = format_L23ECOPISTAS_20.readFeatures(json_L23ECOPISTAS_20, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L23ECOPISTAS_20 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L23ECOPISTAS_20.addFeatures(features_L23ECOPISTAS_20);
var lyr_L23ECOPISTAS_20 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L23ECOPISTAS_20, 
                style: style_L23ECOPISTAS_20,
                popuplayertitle: "L23-ECOPISTAS",
                interactive: true,
                title: '<img src="/static/styles/legend/L23ECOPISTAS_20.png" /> L23-ECOPISTAS'
            });
var format_L22ECOVIASFXD_21 = new ol.format.GeoJSON();
var features_L22ECOVIASFXD_21 = format_L22ECOVIASFXD_21.readFeatures(json_L22ECOVIASFXD_21, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L22ECOVIASFXD_21 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L22ECOVIASFXD_21.addFeatures(features_L22ECOVIASFXD_21);
var lyr_L22ECOVIASFXD_21 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L22ECOVIASFXD_21, 
                style: style_L22ECOVIASFXD_21,
                popuplayertitle: "L22-ECOVIAS-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L22ECOVIASFXD_21.png" /> L22-ECOVIAS-FXD'
            });
var format_L21TIETFXD_22 = new ol.format.GeoJSON();
var features_L21TIETFXD_22 = format_L21TIETFXD_22.readFeatures(json_L21TIETFXD_22, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L21TIETFXD_22 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L21TIETFXD_22.addFeatures(features_L21TIETFXD_22);
var lyr_L21TIETFXD_22 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L21TIETFXD_22, 
                style: style_L21TIETFXD_22,
                popuplayertitle: "L21-TIETÊ-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L21TIETFXD_22.png" /> L21-TIETÊ-FXD'
            });
var format_L21TIET_23 = new ol.format.GeoJSON();
var features_L21TIET_23 = format_L21TIET_23.readFeatures(json_L21TIET_23, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L21TIET_23 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L21TIET_23.addFeatures(features_L21TIET_23);
var lyr_L21TIET_23 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L21TIET_23, 
                style: style_L21TIET_23,
                popuplayertitle: "L21-TIETÊ",
                interactive: true,
                title: '<img src="/static/styles/legend/L21TIET_23.png" /> L21-TIETÊ'
            });
var format_L20SPVIASFXD_24 = new ol.format.GeoJSON();
var features_L20SPVIASFXD_24 = format_L20SPVIASFXD_24.readFeatures(json_L20SPVIASFXD_24, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L20SPVIASFXD_24 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L20SPVIASFXD_24.addFeatures(features_L20SPVIASFXD_24);
var lyr_L20SPVIASFXD_24 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L20SPVIASFXD_24, 
                style: style_L20SPVIASFXD_24,
                popuplayertitle: "L20-SPVIAS-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L20SPVIASFXD_24.png" /> L20-SPVIAS-FXD'
            });
var format_L22ECOVIAS_25 = new ol.format.GeoJSON();
var features_L22ECOVIAS_25 = format_L22ECOVIAS_25.readFeatures(json_L22ECOVIAS_25, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L22ECOVIAS_25 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L22ECOVIAS_25.addFeatures(features_L22ECOVIAS_25);
var lyr_L22ECOVIAS_25 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L22ECOVIAS_25, 
                style: style_L22ECOVIAS_25,
                popuplayertitle: "L22-ECOVIAS",
                interactive: true,
                title: '<img src="/static/styles/legend/L22ECOVIAS_25.png" /> L22-ECOVIAS'
            });
var format_L20SPVIAS_26 = new ol.format.GeoJSON();
var features_L20SPVIAS_26 = format_L20SPVIAS_26.readFeatures(json_L20SPVIAS_26, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L20SPVIAS_26 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L20SPVIAS_26.addFeatures(features_L20SPVIAS_26);
var lyr_L20SPVIAS_26 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L20SPVIAS_26, 
                style: style_L20SPVIAS_26,
                popuplayertitle: "L20-SPVIAS",
                interactive: true,
                title: '<img src="/static/styles/legend/L20SPVIAS_26.png" /> L20-SPVIAS'
            });
var format_L19VIARONDONFXD_27 = new ol.format.GeoJSON();
var features_L19VIARONDONFXD_27 = format_L19VIARONDONFXD_27.readFeatures(json_L19VIARONDONFXD_27, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L19VIARONDONFXD_27 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L19VIARONDONFXD_27.addFeatures(features_L19VIARONDONFXD_27);
var lyr_L19VIARONDONFXD_27 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L19VIARONDONFXD_27, 
                style: style_L19VIARONDONFXD_27,
                popuplayertitle: "L19-VIARONDON-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L19VIARONDONFXD_27.png" /> L19-VIARONDON-FXD'
            });
var format_L19VIARONDON_28 = new ol.format.GeoJSON();
var features_L19VIARONDON_28 = format_L19VIARONDON_28.readFeatures(json_L19VIARONDON_28, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L19VIARONDON_28 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L19VIARONDON_28.addFeatures(features_L19VIARONDON_28);
var lyr_L19VIARONDON_28 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L19VIARONDON_28, 
                style: style_L19VIARONDON_28,
                popuplayertitle: "L19-VIARONDON",
                interactive: true,
                title: '<img src="/static/styles/legend/L19VIARONDON_28.png" /> L19-VIARONDON'
            });
var format_L16CARTFXD_29 = new ol.format.GeoJSON();
var features_L16CARTFXD_29 = format_L16CARTFXD_29.readFeatures(json_L16CARTFXD_29, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L16CARTFXD_29 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L16CARTFXD_29.addFeatures(features_L16CARTFXD_29);
var lyr_L16CARTFXD_29 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L16CARTFXD_29, 
                style: style_L16CARTFXD_29,
                popuplayertitle: "L16-CART-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L16CARTFXD_29.png" /> L16-CART-FXD'
            });
var format_L16CART_30 = new ol.format.GeoJSON();
var features_L16CART_30 = format_L16CART_30.readFeatures(json_L16CART_30, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L16CART_30 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L16CART_30.addFeatures(features_L16CART_30);
var lyr_L16CART_30 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L16CART_30, 
                style: style_L16CART_30,
                popuplayertitle: "L16-CART",
                interactive: true,
                title: '<img src="/static/styles/legend/L16CART_30.png" /> L16-CART'
            });
var format_L13COLINASFXD_31 = new ol.format.GeoJSON();
var features_L13COLINASFXD_31 = format_L13COLINASFXD_31.readFeatures(json_L13COLINASFXD_31, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L13COLINASFXD_31 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L13COLINASFXD_31.addFeatures(features_L13COLINASFXD_31);
var lyr_L13COLINASFXD_31 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L13COLINASFXD_31, 
                style: style_L13COLINASFXD_31,
                popuplayertitle: "L13-COLINAS-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L13COLINASFXD_31.png" /> L13-COLINAS-FXD'
            });
var format_L13COLINAS_32 = new ol.format.GeoJSON();
var features_L13COLINAS_32 = format_L13COLINAS_32.readFeatures(json_L13COLINAS_32, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L13COLINAS_32 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L13COLINAS_32.addFeatures(features_L13COLINAS_32);
var lyr_L13COLINAS_32 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L13COLINAS_32, 
                style: style_L13COLINAS_32,
                popuplayertitle: "L13-COLINAS",
                interactive: true,
                title: '<img src="/static/styles/legend/L13COLINAS_32.png" /> L13-COLINAS'
            });
var format_L12VIAOESTEFXD_33 = new ol.format.GeoJSON();
var features_L12VIAOESTEFXD_33 = format_L12VIAOESTEFXD_33.readFeatures(json_L12VIAOESTEFXD_33, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L12VIAOESTEFXD_33 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L12VIAOESTEFXD_33.addFeatures(features_L12VIAOESTEFXD_33);
var lyr_L12VIAOESTEFXD_33 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L12VIAOESTEFXD_33, 
                style: style_L12VIAOESTEFXD_33,
                popuplayertitle: "L12-VIAOESTE-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L12VIAOESTEFXD_33.png" /> L12-VIAOESTE-FXD'
            });
var format_L12VIAOESTE_34 = new ol.format.GeoJSON();
var features_L12VIAOESTE_34 = format_L12VIAOESTE_34.readFeatures(json_L12VIAOESTE_34, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L12VIAOESTE_34 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L12VIAOESTE_34.addFeatures(features_L12VIAOESTE_34);
var lyr_L12VIAOESTE_34 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L12VIAOESTE_34, 
                style: style_L12VIAOESTE_34,
                popuplayertitle: "L12-VIAOESTE",
                interactive: true,
                title: '<img src="/static/styles/legend/L12VIAOESTE_34.png" /> L12-VIAOESTE'
            });
var format_L11RENOVIASFXD_35 = new ol.format.GeoJSON();
var features_L11RENOVIASFXD_35 = format_L11RENOVIASFXD_35.readFeatures(json_L11RENOVIASFXD_35, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L11RENOVIASFXD_35 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L11RENOVIASFXD_35.addFeatures(features_L11RENOVIASFXD_35);
var lyr_L11RENOVIASFXD_35 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L11RENOVIASFXD_35, 
                style: style_L11RENOVIASFXD_35,
                popuplayertitle: "L11-RENOVIAS-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L11RENOVIASFXD_35.png" /> L11-RENOVIAS-FXD'
            });
var format_L11RENOVIAS_36 = new ol.format.GeoJSON();
var features_L11RENOVIAS_36 = format_L11RENOVIAS_36.readFeatures(json_L11RENOVIAS_36, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L11RENOVIAS_36 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L11RENOVIAS_36.addFeatures(features_L11RENOVIAS_36);
var lyr_L11RENOVIAS_36 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L11RENOVIAS_36, 
                style: style_L11RENOVIAS_36,
                popuplayertitle: "L11-RENOVIAS",
                interactive: true,
                title: '<img src="/static/styles/legend/L11RENOVIAS_36.png" /> L11-RENOVIAS'
            });
var format_L07ROTAFXD_37 = new ol.format.GeoJSON();
var features_L07ROTAFXD_37 = format_L07ROTAFXD_37.readFeatures(json_L07ROTAFXD_37, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L07ROTAFXD_37 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L07ROTAFXD_37.addFeatures(features_L07ROTAFXD_37);
var lyr_L07ROTAFXD_37 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L07ROTAFXD_37, 
                style: style_L07ROTAFXD_37,
                popuplayertitle: "L07-ROTA-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L07ROTAFXD_37.png" /> L07-ROTA-FXD'
            });
var format_L07ROTA_38 = new ol.format.GeoJSON();
var features_L07ROTA_38 = format_L07ROTA_38.readFeatures(json_L07ROTA_38, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L07ROTA_38 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L07ROTA_38.addFeatures(features_L07ROTA_38);
var lyr_L07ROTA_38 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L07ROTA_38, 
                style: style_L07ROTA_38,
                popuplayertitle: "L07-ROTA",
                interactive: true,
                title: '<img src="/static/styles/legend/L07ROTA_38.png" /> L07-ROTA'
            });
var format_L06INTERVIASFXD_39 = new ol.format.GeoJSON();
var features_L06INTERVIASFXD_39 = format_L06INTERVIASFXD_39.readFeatures(json_L06INTERVIASFXD_39, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L06INTERVIASFXD_39 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L06INTERVIASFXD_39.addFeatures(features_L06INTERVIASFXD_39);
var lyr_L06INTERVIASFXD_39 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L06INTERVIASFXD_39, 
                style: style_L06INTERVIASFXD_39,
                popuplayertitle: "L06-INTERVIAS-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L06INTERVIASFXD_39.png" /> L06-INTERVIAS-FXD'
            });
var format_L06INTERVIAS_40 = new ol.format.GeoJSON();
var features_L06INTERVIAS_40 = format_L06INTERVIAS_40.readFeatures(json_L06INTERVIAS_40, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L06INTERVIAS_40 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L06INTERVIAS_40.addFeatures(features_L06INTERVIAS_40);
var lyr_L06INTERVIAS_40 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L06INTERVIAS_40, 
                style: style_L06INTERVIAS_40,
                popuplayertitle: "L06-INTERVIAS",
                interactive: true,
                title: '<img src="/static/styles/legend/L06INTERVIAS_40.png" /> L06-INTERVIAS'
            });
var format_L03TEBEFXD_41 = new ol.format.GeoJSON();
var features_L03TEBEFXD_41 = format_L03TEBEFXD_41.readFeatures(json_L03TEBEFXD_41, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L03TEBEFXD_41 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L03TEBEFXD_41.addFeatures(features_L03TEBEFXD_41);
var lyr_L03TEBEFXD_41 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L03TEBEFXD_41, 
                style: style_L03TEBEFXD_41,
                popuplayertitle: "L03-TEBE-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L03TEBEFXD_41.png" /> L03-TEBE-FXD'
            });
var format_L03TEBE_42 = new ol.format.GeoJSON();
var features_L03TEBE_42 = format_L03TEBE_42.readFeatures(json_L03TEBE_42, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L03TEBE_42 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L03TEBE_42.addFeatures(features_L03TEBE_42);
var lyr_L03TEBE_42 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L03TEBE_42, 
                style: style_L03TEBE_42,
                popuplayertitle: "L03-TEBE",
                interactive: true,
                title: '<img src="/static/styles/legend/L03TEBE_42.png" /> L03-TEBE'
            });
var format_L01AUTOBANFXD_43 = new ol.format.GeoJSON();
var features_L01AUTOBANFXD_43 = format_L01AUTOBANFXD_43.readFeatures(json_L01AUTOBANFXD_43, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L01AUTOBANFXD_43 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L01AUTOBANFXD_43.addFeatures(features_L01AUTOBANFXD_43);
var lyr_L01AUTOBANFXD_43 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L01AUTOBANFXD_43, 
                style: style_L01AUTOBANFXD_43,
                popuplayertitle: "L01-AUTOBAN-FXD",
                interactive: true,
                title: '<img src="/static/styles/legend/L01AUTOBANFXD_43.png" /> L01-AUTOBAN-FXD'
            });
var format_L01AUTOBAN_44 = new ol.format.GeoJSON();
var features_L01AUTOBAN_44 = format_L01AUTOBAN_44.readFeatures(json_L01AUTOBAN_44, 
            {dataProjection: 'EPSG:4326', featureProjection: 'EPSG:3857'});
var jsonSource_L01AUTOBAN_44 = new ol.source.Vector({
    attributions: ' ',
});
jsonSource_L01AUTOBAN_44.addFeatures(features_L01AUTOBAN_44);
var lyr_L01AUTOBAN_44 = new ol.layer.Vector({
                declutter: false,
                source:jsonSource_L01AUTOBAN_44, 
                style: style_L01AUTOBAN_44,
                popuplayertitle: "L01-AUTOBAN",
                interactive: true,
                title: '<img src="/static/styles/legend/L01AUTOBAN_44.png" /> L01-AUTOBAN'
            });

// MERGE LAYERS: Add TEBE features to ECONOROESTE layers
// Get features from L03-TEBE-FXD and add them to L31-ECONOROESTE-FXD
var tebeFxdFeatures = jsonSource_L03TEBEFXD_41.getFeatures();
jsonSource_L31ECONOROESTEFXD_5.addFeatures(tebeFxdFeatures);

// Get features from L03-TEBE and add them to L31-ECONOROESTE
var tebeFeatures = jsonSource_L03TEBE_42.getFeatures();
jsonSource_L31ECONOROESTE_6.addFeatures(tebeFeatures);

// Refresh the target layers to show the new features
lyr_L31ECONOROESTEFXD_5.changed();
lyr_L31ECONOROESTE_6.changed();

// Hide the original L03-TEBE layers
lyr_L03TEBEFXD_41.setVisible(false);
lyr_L03TEBE_42.setVisible(false);

// Make sure the target layers are visible
lyr_L31ECONOROESTEFXD_5.setVisible(false);
lyr_L31ECONOROESTE_6.setVisible(false);

// Update the title of L31 layers to indicate they now include L03-TEBE data
lyr_L31ECONOROESTEFXD_5.set('title', '<img src="/static/styles/legend/L31ECONOROESTEFXD_5.png" /> L31-ECONOROESTE-FXD');
lyr_L31ECONOROESTE_6.set('title', '<img src="/static/styles/legend/L31ECONOROESTE_6.png" /> L31-ECONOROESTE');

lyr_OSMStandard_0.setVisible(true);lyr_ESTADODESP_1.setVisible(true);lyr_MUNCIPIOSSP_2.setVisible(false);lyr_DER_3.setVisible(false);lyr_L32NOVOLITORAL_4.setVisible(false);lyr_L31ECONOROESTEFXD_5.setVisible(false);lyr_L31ECONOROESTE_6.setVisible(false);lyr_L30EIXOSPFXD_7.setVisible(false);lyr_L30EIXOSP_8.setVisible(false);lyr_L29VIAPAULISTAFXD_9.setVisible(false);lyr_L29VIAPAULISTA_10.setVisible(false);lyr_L28ENTREVIASFXD_11.setVisible(false);lyr_L28ENTREVIAS_12.setVisible(false);lyr_L27TAMOIOSFXD_13.setVisible(false);lyr_L27TAMOIOS_14.setVisible(false);lyr_L25SPMARFXD_15.setVisible(false);lyr_L25SPMAR_16.setVisible(false);lyr_L24RODOANELFXD_17.setVisible(false);lyr_L24RODOANEL_18.setVisible(false);lyr_L23ECOPISTASFXD_19.setVisible(false);lyr_L23ECOPISTAS_20.setVisible(false);lyr_L22ECOVIASFXD_21.setVisible(false);lyr_L21TIETFXD_22.setVisible(false);lyr_L21TIET_23.setVisible(false);lyr_L20SPVIASFXD_24.setVisible(false);lyr_L22ECOVIAS_25.setVisible(false);lyr_L20SPVIAS_26.setVisible(false);lyr_L19VIARONDONFXD_27.setVisible(false);lyr_L19VIARONDON_28.setVisible(false);lyr_L16CARTFXD_29.setVisible(false);lyr_L16CART_30.setVisible(false);lyr_L13COLINASFXD_31.setVisible(false);lyr_L13COLINAS_32.setVisible(false);lyr_L12VIAOESTEFXD_33.setVisible(false);lyr_L12VIAOESTE_34.setVisible(false);lyr_L11RENOVIASFXD_35.setVisible(false);lyr_L11RENOVIAS_36.setVisible(false);lyr_L07ROTAFXD_37.setVisible(false);lyr_L07ROTA_38.setVisible(false);lyr_L06INTERVIASFXD_39.setVisible(false);lyr_L06INTERVIAS_40.setVisible(false);lyr_L03TEBEFXD_41.setVisible(false);lyr_L03TEBE_42.setVisible(false);lyr_L01AUTOBANFXD_43.setVisible(false);lyr_L01AUTOBAN_44.setVisible(false);

// Remove L03-TEBE layers from the layers list, so they don't appear in the layer control
var layersList = [
    lyr_OSMStandard_0,
    lyr_ESTADODESP_1,
    lyr_MUNCIPIOSSP_2,
    lyr_DER_3,
    lyr_L32NOVOLITORAL_4,
    lyr_L31ECONOROESTEFXD_5,
    lyr_L31ECONOROESTE_6,
    lyr_L30EIXOSPFXD_7,
    lyr_L30EIXOSP_8,
    lyr_L29VIAPAULISTAFXD_9,
    lyr_L29VIAPAULISTA_10,
    lyr_L28ENTREVIASFXD_11,
    lyr_L28ENTREVIAS_12,
    lyr_L27TAMOIOSFXD_13,
    lyr_L27TAMOIOS_14,
    lyr_L25SPMARFXD_15,
    lyr_L25SPMAR_16,
    lyr_L24RODOANELFXD_17,
    lyr_L24RODOANEL_18,
    lyr_L23ECOPISTASFXD_19,
    lyr_L23ECOPISTAS_20,
    lyr_L22ECOVIASFXD_21,
    lyr_L21TIETFXD_22,
    lyr_L21TIET_23,
    lyr_L20SPVIASFXD_24,
    lyr_L22ECOVIAS_25,
    lyr_L20SPVIAS_26,
    lyr_L19VIARONDONFXD_27,
    lyr_L19VIARONDON_28,
    lyr_L16CARTFXD_29,
    lyr_L16CART_30,
    lyr_L13COLINASFXD_31,
    lyr_L13COLINAS_32,
    lyr_L12VIAOESTEFXD_33,
    lyr_L12VIAOESTE_34,
    lyr_L11RENOVIASFXD_35,
    lyr_L11RENOVIAS_36,
    lyr_L07ROTAFXD_37,
    lyr_L07ROTA_38,
    lyr_L06INTERVIASFXD_39,
    lyr_L06INTERVIAS_40,
    // L03-TEBE layers removed from here
    lyr_L01AUTOBANFXD_43,
    lyr_L01AUTOBAN_44
];
lyr_ESTADODESP_1.set('fieldAliases', {'fid': 'fid', 'CD_UF': 'CD_UF', 'NM_UF': 'NM_UF', 'SIGLA_UF': 'SIGLA_UF', 'NM_REGIAO': 'NM_REGIAO', 'AREA_KM2': 'AREA_KM2', });
lyr_MUNCIPIOSSP_2.set('fieldAliases', {'fid': 'fid', 'CD_MUN': 'CD_MUN', 'NM_MUN': 'NM_MUN', 'SIGLA_UF': 'SIGLA_UF', 'AREA_KM2': 'AREA_KM2', });
lyr_DER_3.set('fieldAliases', {'fid': 'fid', 'RODOVIA0': 'RODOVIA0', 'TIPORODO1': 'TIPORODO1', 'ORIENTAC2': 'ORIENTAC2', 'ORIGEM3': 'ORIGEM3', 'MUNICIPI4': 'MUNICIPI4', 'CODREGIO5': 'CODREGIO5', 'SEDEREGI6': 'SEDEREGI6', 'RESIDENC7': 'RESIDENC7', 'SEDERESI8': 'SEDERESI8', 'KMINICIA9': 'KMINICIA9', 'KMFINAL10': 'KMFINAL10', 'EXTENSAO11': 'EXTENSAO11', 'DESCRICA12': 'DESCRICA12', 'DESCRICA13': 'DESCRICA13', 'JURISDIC14': 'JURISDIC14', 'ADMINIST15': 'ADMINIST15', 'CONSERVA16': 'CONSERVA16', 'TIPOPIST17': 'TIPOPIST17', 'SOBREPOS18': 'SOBREPOS18', 'SUBPOSIC19': 'SUBPOSIC19', 'PERIMETR20': 'PERIMETR20', 'DATAINIC21': 'DATAINIC21', 'DATAFIMC22': 'DATAFIMC22', 'ESTADUAL23': 'ESTADUAL23', 'DENOMINA24': 'DENOMINA24', 'LEGISLAC25': 'LEGISLAC25', 'GID26': 'GID26', });
lyr_L32NOVOLITORAL_4.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L31ECONOROESTEFXD_5.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L31ECONOROESTE_6.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L30EIXOSPFXD_7.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L30EIXOSP_8.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L29VIAPAULISTAFXD_9.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L29VIAPAULISTA_10.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L28ENTREVIASFXD_11.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L28ENTREVIAS_12.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L27TAMOIOSFXD_13.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L27TAMOIOS_14.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L25SPMARFXD_15.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L25SPMAR_16.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L24RODOANELFXD_17.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L24RODOANEL_18.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L23ECOPISTASFXD_19.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L23ECOPISTAS_20.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L22ECOVIASFXD_21.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L21TIETFXD_22.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L21TIET_23.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L20SPVIASFXD_24.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L22ECOVIAS_25.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L20SPVIAS_26.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L19VIARONDONFXD_27.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L19VIARONDON_28.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L16CARTFXD_29.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L16CART_30.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L13COLINASFXD_31.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L13COLINAS_32.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L12VIAOESTEFXD_33.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L12VIAOESTE_34.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L11RENOVIASFXD_35.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L11RENOVIAS_36.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L07ROTAFXD_37.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L07ROTA_38.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L06INTERVIASFXD_39.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L06INTERVIAS_40.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L03TEBEFXD_41.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L03TEBE_42.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_L01AUTOBANFXD_43.set('fieldAliases', {'fid': 'fid', 'name': 'name', 'folders': 'folders', 'description': 'description', 'altitude': 'altitude', 'alt_mode': 'alt_mode', 'time_begin': 'time_begin', 'time_end': 'time_end', 'time_when': 'time_when', });
lyr_L01AUTOBAN_44.set('fieldAliases', {'LAT': 'LAT', 'LON': 'LON', 'RODOVIA': 'RODOVIA', 'KM': 'KM', 'LOTE': 'LOTE', });
lyr_ESTADODESP_1.set('fieldImages', {'fid': 'TextEdit', 'CD_UF': 'TextEdit', 'NM_UF': 'TextEdit', 'SIGLA_UF': 'TextEdit', 'NM_REGIAO': 'TextEdit', 'AREA_KM2': 'TextEdit', });
lyr_MUNCIPIOSSP_2.set('fieldImages', {'fid': 'TextEdit', 'CD_MUN': 'TextEdit', 'NM_MUN': 'TextEdit', 'SIGLA_UF': 'TextEdit', 'AREA_KM2': 'TextEdit', });
lyr_DER_3.set('fieldImages', {'fid': 'TextEdit', 'RODOVIA0': 'TextEdit', 'TIPORODO1': 'TextEdit', 'ORIENTAC2': 'TextEdit', 'ORIGEM3': 'TextEdit', 'MUNICIPI4': 'TextEdit', 'CODREGIO5': 'TextEdit', 'SEDEREGI6': 'TextEdit', 'RESIDENC7': 'TextEdit', 'SEDERESI8': 'TextEdit', 'KMINICIA9': 'TextEdit', 'KMFINAL10': 'TextEdit', 'EXTENSAO11': 'TextEdit', 'DESCRICA12': 'TextEdit', 'DESCRICA13': 'TextEdit', 'JURISDIC14': 'TextEdit', 'ADMINIST15': 'TextEdit', 'CONSERVA16': 'TextEdit', 'TIPOPIST17': 'TextEdit', 'SOBREPOS18': 'TextEdit', 'SUBPOSIC19': 'TextEdit', 'PERIMETR20': 'TextEdit', 'DATAINIC21': 'TextEdit', 'DATAFIMC22': 'TextEdit', 'ESTADUAL23': 'TextEdit', 'DENOMINA24': 'TextEdit', 'LEGISLAC25': 'TextEdit', 'GID26': 'TextEdit', });
lyr_L32NOVOLITORAL_4.set('fieldImages', {'fid': '', 'name': '', 'folders': '', 'description': '', 'altitude': '', 'alt_mode': '', 'time_begin': '', 'time_end': '', 'time_when': '', });
lyr_L31ECONOROESTEFXD_5.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L31ECONOROESTE_6.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L30EIXOSPFXD_7.set('fieldImages', {'fid': '', 'name': '', 'folders': '', 'description': '', 'altitude': '', 'alt_mode': '', 'time_begin': '', 'time_end': '', 'time_when': '', });
lyr_L30EIXOSP_8.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L29VIAPAULISTAFXD_9.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L29VIAPAULISTA_10.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L28ENTREVIASFXD_11.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L28ENTREVIAS_12.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L27TAMOIOSFXD_13.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L27TAMOIOS_14.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L25SPMARFXD_15.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L25SPMAR_16.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L24RODOANELFXD_17.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L24RODOANEL_18.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L23ECOPISTASFXD_19.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L23ECOPISTAS_20.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L22ECOVIASFXD_21.set('fieldImages', {'fid': 'TextEdit', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L21TIETFXD_22.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L21TIET_23.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L20SPVIASFXD_24.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L22ECOVIAS_25.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L20SPVIAS_26.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L19VIARONDONFXD_27.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L19VIARONDON_28.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L16CARTFXD_29.set('fieldImages', {'fid': 'TextEdit', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L16CART_30.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L13COLINASFXD_31.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L13COLINAS_32.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L12VIAOESTEFXD_33.set('fieldImages', {'fid': 'TextEdit', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L12VIAOESTE_34.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L11RENOVIASFXD_35.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L11RENOVIAS_36.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L07ROTAFXD_37.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L07ROTA_38.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L06INTERVIASFXD_39.set('fieldImages', {'fid': 'TextEdit', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L06INTERVIAS_40.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L03TEBEFXD_41.set('fieldImages', {'fid': 'TextEdit', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L03TEBE_42.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_L01AUTOBANFXD_43.set('fieldImages', {'fid': '', 'name': 'TextEdit', 'folders': 'TextEdit', 'description': 'TextEdit', 'altitude': 'TextEdit', 'alt_mode': 'TextEdit', 'time_begin': 'TextEdit', 'time_end': 'TextEdit', 'time_when': 'TextEdit', });
lyr_L01AUTOBAN_44.set('fieldImages', {'LAT': '', 'LON': '', 'RODOVIA': '', 'KM': '', 'LOTE': '', });
lyr_ESTADODESP_1.set('fieldLabels', {'fid': 'no label', 'CD_UF': 'no label', 'NM_UF': 'no label', 'SIGLA_UF': 'no label', 'NM_REGIAO': 'no label', 'AREA_KM2': 'no label', });
lyr_MUNCIPIOSSP_2.set('fieldLabels', {'fid': 'no label', 'CD_MUN': 'no label', 'NM_MUN': 'no label', 'SIGLA_UF': 'no label', 'AREA_KM2': 'no label', });
lyr_DER_3.set('fieldLabels', {'fid': 'no label', 'RODOVIA0': 'no label', 'TIPORODO1': 'no label', 'ORIENTAC2': 'no label', 'ORIGEM3': 'no label', 'MUNICIPI4': 'no label', 'CODREGIO5': 'no label', 'SEDEREGI6': 'no label', 'RESIDENC7': 'no label', 'SEDERESI8': 'no label', 'KMINICIA9': 'no label', 'KMFINAL10': 'no label', 'EXTENSAO11': 'no label', 'DESCRICA12': 'no label', 'DESCRICA13': 'no label', 'JURISDIC14': 'no label', 'ADMINIST15': 'no label', 'CONSERVA16': 'no label', 'TIPOPIST17': 'no label', 'SOBREPOS18': 'no label', 'SUBPOSIC19': 'no label', 'PERIMETR20': 'no label', 'DATAINIC21': 'no label', 'DATAFIMC22': 'no label', 'ESTADUAL23': 'no label', 'DENOMINA24': 'no label', 'LEGISLAC25': 'no label', 'GID26': 'no label', });
lyr_L32NOVOLITORAL_4.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L31ECONOROESTEFXD_5.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L31ECONOROESTE_6.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L30EIXOSPFXD_7.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L30EIXOSP_8.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L29VIAPAULISTAFXD_9.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L29VIAPAULISTA_10.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L28ENTREVIASFXD_11.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L28ENTREVIAS_12.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L27TAMOIOSFXD_13.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L27TAMOIOS_14.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L25SPMARFXD_15.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L25SPMAR_16.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L24RODOANELFXD_17.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L24RODOANEL_18.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L23ECOPISTASFXD_19.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L23ECOPISTAS_20.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L22ECOVIASFXD_21.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L21TIETFXD_22.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L21TIET_23.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L20SPVIASFXD_24.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L22ECOVIAS_25.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L20SPVIAS_26.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L19VIARONDONFXD_27.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L19VIARONDON_28.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L16CARTFXD_29.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L16CART_30.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L13COLINASFXD_31.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L13COLINAS_32.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L12VIAOESTEFXD_33.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L12VIAOESTE_34.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L11RENOVIASFXD_35.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L11RENOVIAS_36.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L07ROTAFXD_37.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L07ROTA_38.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L06INTERVIASFXD_39.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L06INTERVIAS_40.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L03TEBEFXD_41.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L03TEBE_42.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L01AUTOBANFXD_43.set('fieldLabels', {'fid': 'no label', 'name': 'no label', 'folders': 'no label', 'description': 'no label', 'altitude': 'no label', 'alt_mode': 'no label', 'time_begin': 'no label', 'time_end': 'no label', 'time_when': 'no label', });
lyr_L01AUTOBAN_44.set('fieldLabels', {'LAT': 'no label', 'LON': 'no label', 'RODOVIA': 'no label', 'KM': 'no label', 'LOTE': 'no label', });
lyr_L01AUTOBAN_44.on('precompose', function(evt) {
    evt.context.globalCompositeOperation = 'normal';
});