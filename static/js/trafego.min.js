function scrollFunction(){document.body.scrollTop>20||document.documentElement.scrollTop>20?topButton.style.display="block":topButton.style.display="none"}function topFunction(){document.body.scrollTop=0,document.documentElement.scrollTop=0}topButton=document.getElementById("topButton"),window.onscroll=function(){scrollFunction()};const trafegos=document.querySelectorAll("#trafego"),selRodoviaEl=document.getElementById("sel_rodovia"),selConcEl=document.getElementById("sel_conc"),selTrafegoEl=document.getElementById("sel_tipo_trafego"),divLimpaFiltro=document.getElementById("limpaFiltro");function selectTrafego(e){for(let l=0;l<selTrafegoEl.options.length;l++)selTrafegoEl.options[l].value==e&&(selTrafegoEl.options[l].selected="selected",filtraTrafego("trafego"))}function filtraRodoviasSelect(e){if("ALL"!==e)for(let l=1;l<selRodoviaEl.options.length;l++){const[o,t]=selRodoviaEl.options[l].value.split("|");t.split(",").includes(e)?selRodoviaEl.options[l].style.display="list-item":selRodoviaEl.options[l].style.display="none"}else selRodoviaEl.options[0].selected="selected"}function recarregaRodoviasSelect(){resetStyles();for(let e=0;e<selRodoviaEl.options.length;e++)selRodoviaEl.options[e].style.display="list-item";selRodoviaEl.options[0].selected="selected"}function limpaFiltros(){divLimpaFiltro.classList.add("hidden"),recarregaRodoviasSelect(),selConcEl.options[0].selected="selected",selRodoviaEl.options[0].selected="selected",selTrafegoEl.options[0].selected="selected",filtraLista("ALL","ALL","ALL","ALL"),resetStyles()}function resetStyles(){selConcEl.style.color="rgb(0, 0, 0)",selConcEl.style.background="white",selConcEl.style.boxShadow="0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",selRodoviaEl.style.color="rgb(0, 0, 0)",selRodoviaEl.style.background="white",selRodoviaEl.style.boxShadow="0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",selTrafegoEl.style.color="rgb(0, 0, 0)",selTrafegoEl.style.background="white",selTrafegoEl.style.boxShadow="0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"}function filtraTrafego(e){divLimpaFiltro.classList.remove("hidden"),"concessionaria"===e&&(recarregaRodoviasSelect(),selTrafegoEl.options[0].selected="selected",filtraRodoviasSelect(selConcEl.value));const[l,o]=selRodoviaEl.value.split("|")[0].split(" - ");"ALL"!=selTrafegoEl.value&&(selTrafegoEl.style.background="rgba(243, 244, 246, 1)",selTrafegoEl.style.boxShadow="none"),"ALL - ALL|ALL"!=selRodoviaEl.value&&(selRodoviaEl.style.background="rgba(243, 244, 246, 1)",selRodoviaEl.style.boxShadow="none"),"ALL"!=selConcEl.value&&(selConcEl.style.background="rgba(243, 244, 246, 1)",selConcEl.style.boxShadow="none"),filtraLista(selConcEl.value,l,o,selTrafegoEl.value)}function filtraLista(e,l,o,t){let s=0;for(let a=0;a<trafegos.length;a++){const n=trafegos[a].getAttribute("data-conc"),[i,d,c]=trafegos[a].getAttribute("data-id").split("|"),r=trafegos[a].getAttribute("data-trafego").split("|"),g=e===n||null==e||"ALL"==e,u=l===d&&o===c||null==l||"ALL"==l,p="ALL"==t||null==t||r.includes(t);g&&u&&p?(trafegos[a].style.display="block",s++):trafegos[a].style.display="none"}0==s?document.getElementById("noresults").classList.remove("hidden"):document.getElementById("noresults").classList.add("hidden")}