const btnCopiaTempoViagem = document.getElementById('btn__copia-tempo-viagem')
const txtTempoViagem = document.getElementById('text__tempo-viagem').innerText


    let myToast =  Toastify({
        text: 'Texto copiado',
        duration: 3000,
        destination: "#",
        newWindow: true,
        close: false,
        gravity: "bottom", // `top` or `bottom`
        position: "center", // `left`, `center` or `right`
        stopOnFocus: true, // Prevents dismissing of toast on hover
        style: {
            background: "#3730a3",
        },
    });

    btnCopiaTempoViagem.addEventListener('click', function() {
        navigator.clipboard.writeText(txtTempoViagem.replace(/\t/g,''));
        myToast.showToast();
    })