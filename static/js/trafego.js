
  topButton = document.getElementById("topButton");
  window.onscroll = function () { scrollFunction() };

  function scrollFunction() {
    if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
      topButton.style.display = "block";
    } else {
      topButton.style.display = "none";
    }
  }
  
  function topFunction() {
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
  }

  const trafegos = document.querySelectorAll("#trafego");
  const selRodoviaEl = document.getElementById("sel_rodovia");
  const selConcEl = document.getElementById("sel_conc");
  const selTrafegoEl = document.getElementById("sel_tipo_trafego");
  const divLimpaFiltro = document.getElementById("limpaFiltro");

  function selectTrafego(tipoTrafego) {
    for (let i = 0; i < selTrafegoEl.options.length; i++) {
      if (selTrafegoEl.options[i].value == tipoTrafego) {
        selTrafegoEl.options[i].selected = 'selected';
        filtraTrafego('trafego');
      }
    }
  }

  function filtraRodoviasSelect(concessionaria) {
    if (concessionaria === "ALL") {
      selRodoviaEl.options[0].selected = 'selected';
      return;
    }
    for (let i = 1; i < selRodoviaEl.options.length; i++) {
      const [rodovia, concs] = selRodoviaEl.options[i].value.split("|");
      const concs_arr = concs.split(",");
      if (!concs_arr.includes(concessionaria)) {
        selRodoviaEl.options[i].style.display = 'none';
      } else {
        selRodoviaEl.options[i].style.display = 'list-item';
      }
    }
  }

  function recarregaRodoviasSelect() {
    resetStyles();
    for (let i = 0; i < selRodoviaEl.options.length; i++) {
      selRodoviaEl.options[i].style.display = 'list-item';
    }
    selRodoviaEl.options[0].selected = 'selected';
  }

  function limpaFiltros() {
    divLimpaFiltro.classList.add("hidden");
    recarregaRodoviasSelect();
    selConcEl.options[0].selected = 'selected';
    selRodoviaEl.options[0].selected = 'selected';
    selTrafegoEl.options[0].selected = 'selected';
    filtraLista("ALL", "ALL", "ALL", "ALL");
    resetStyles();
  }

  function resetStyles() {
    // selConcEl.style.fontWeight = '300';
    selConcEl.style.color = 'rgb(0, 0, 0)';
    selConcEl.style.background = 'white';
    selConcEl.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)';
    // selRodoviaEl.style.fontWeight = '300';
    selRodoviaEl.style.color = 'rgb(0, 0, 0)';
    selRodoviaEl.style.background = 'white';
    selRodoviaEl.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)';
    // selTrafegoEl.style.fontWeight = '300';
    selTrafegoEl.style.color = 'rgb(0, 0, 0)';
    selTrafegoEl.style.background = 'white';
    selTrafegoEl.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)';
  }

  function filtraTrafego(tipo) {
    divLimpaFiltro.classList.remove("hidden");
    if (tipo === 'concessionaria') {
      recarregaRodoviasSelect();
      selTrafegoEl.options[0].selected = 'selected';
      filtraRodoviasSelect(selConcEl.value);
    }
    const [rodovia, nome] = selRodoviaEl.value.split("|")[0].split(" - ");
    if (selTrafegoEl.value != 'ALL') {
      // selTrafegoEl.style.fontWeight = '400';
      selTrafegoEl.style.background = 'rgba(243, 244, 246, 1)'
      selTrafegoEl.style.boxShadow = 'none'
    }
    if (selRodoviaEl.value != 'ALL - ALL|ALL') {
      // selRodoviaEl.style.fontWeight = '400';
      selRodoviaEl.style.background = 'rgba(243, 244, 246, 1)'
      selRodoviaEl.style.boxShadow = 'none'
    }
    if (selConcEl.value != 'ALL') {
      // selConcEl.style.fontWeight = '400';
      selConcEl.style.background = 'rgba(243, 244, 246, 1)'
      selConcEl.style.boxShadow = 'none'
    }
    filtraLista(selConcEl.value, rodovia, nome, selTrafegoEl.value);
  }

  function filtraLista(concessionaria, rodovia, nome, trafego) {
    let counter = 0;
    for (let i = 0; i < trafegos.length; i++) {
      const conc_value = trafegos[i].getAttribute('data-conc');
      const [conc, rodCod, rodNome] = trafegos[i].getAttribute('data-id').split("|");
      const trafTypesArr = trafegos[i].getAttribute('data-trafego').split("|");
      const cond1 = (concessionaria === conc_value) ||
        (concessionaria == undefined) ||
        (concessionaria == "ALL");
      const cond2 = (rodovia === rodCod && nome === rodNome) ||
        (rodovia == undefined) ||
        (rodovia == "ALL");
      const cond3 = (trafego == "ALL") || (trafego == undefined) || (trafTypesArr.includes(trafego));
      if (cond1 && cond2 && cond3) {
        trafegos[i].style.display = 'block';
        counter++;
      } else {
        trafegos[i].style.display = 'none';
      }
    }
    if (counter == 0) {
      document.getElementById("noresults").classList.remove("hidden");
    } else {
      document.getElementById("noresults").classList.add("hidden");
    }
  }