const defaultModalHTML =  `
            <div class="hystmodal" id="myModal" aria-hidden="true">
                <div class="hystmodal__wrap">
                    <div class="hystmodal__window" role="dialog" aria-modal="true">
                        <button data-hystclose class="hystmodal__close"><PERSON><PERSON><PERSON></button>
                        <div class="bg-white p-4 m-4 border border-gray-200" id="trafego-modal">
                        <h1 class="text-lg font-bold text-center">Carregando...</h1>
                        </div>
                    </div>
                </div>
            </div>
        `;

const myModal = new HystModal({
    linkAttributeName: "data-hystmodal",
    beforeOpen: function(modal){
        modal.innerHTML = defaultModalHTML
    },
    afterClose: function(modal){
        modal.innerHTML = defaultModalHTML
    },
});

const showToast = (message, isError = false, reload = false) => {
    let myToast =  Toastify({
        text: message,
        duration: 5000,
        destination: "#",
        newWindow: true,
        close: false,
        gravity: "bottom", // `top` or `bottom`
        position: "center", // `left`, `center` or `right`
        stopOnFocus: true, // Prevents dismissing of toast on hover
        style: {
            background: isError ? "#991B1B" : "#3730a3",
        },
    });
    myToast.showToast();
    if(reload) {
        location.reload()
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

const resetModal = () => {
    myModal.innerHTML = `
    <div class="hystmodal" id="myModal" aria-hidden="true">
        <div class="hystmodal__wrap">
            <div class="hystmodal__window" role="dialog" aria-modal="true">
                <button data-hystclose class="hystmodal__close">Fechar</button>
                <div class="bg-white p-4 m-4 border border-gray-200" id="trafego-modal">
                <h1 class="text-lg font-bold text-center">Carregando...</h1>
                </div>
            </div>
        </div>
    </div>
    `;
    myModal.close();
}

const updateTrafficInPlace = async (pk, id, formData) => {
    const el = document.querySelector(`#trafego-${id}`)
    const url = `/dashboard/trafego/${id}/inplace-edit-item`
    let data = {}
    formData.forEach(function(value, key) {
        data[key] = value;
    });
    data_json = JSON.stringify(data);
    try {
        const response = await fetch(url, {
            method: 'POST',
            mode: 'cors',
            cache: "no-cache",
            credentials: "same-origin",
            headers: {
                "Content-Type": "application/json",
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: data_json,
        })
        const result = await response.json();

        if (!result.success) {
            const strErrors = result.message
            let strResult = ''
            if ((strErrors instanceof Array) && (strErrors.length > 0)) {
                strErrors.forEach((value) => {
                    strResult += `${value.message}\n`
                });
            } else {
                strResult = result.message
            }
            if (strResult) {
                throw new Error(strResult)
            } else {
                throw new Error("Ocorreu um erro!");
            }
        }

        const fields = result.obj

        const { 
            id,
            slug,
            slug_obra,
            cor,
            trafego,
            pista,
            km_inicial, 
            km_final, 
            causa, 
            comentario, 
            cond_climaticas, 
            atualizado_em, 
            atualizado_por,
            fotos
        } = fields

        if (el) {

            el.remove();
            decrementTrafegoCounter(pk);
        }
        createTrafficDom(
            pk, id, cor, trafego, pista, km_inicial, km_final, causa, comentario, cond_climaticas, slug, slug_obra, 
            atualizado_em, atualizado_por, fotos);
        resetModal();
        showToast("Tráfego editado com sucesso!");
    } catch (error) {

        showToast(error.message, true);
    }
}

const editTraffic = async (el) => {
    const pk = el.getAttribute("data-rodovia-id");
    const id = el.getAttribute("data-trafego-id");
    const url = `/dashboard/trafego/${id}/inplace-edit-item`
    const modal = document.getElementById('trafego-modal');
    try {
        const response = await fetch(url)

        if (!response.ok && response.status === 404) {
            throw new Error('Este item foi editado ou encerrado e não pode ser alterado.');
        }
        if (!response.ok) {
            throw new Error('Ocorreu um erro!')
        }
        const result = await response.text();
        modal.innerHTML = result;
        const form = document.getElementById('form-trafego');
        form.addEventListener('submit', function(event) {
            let formData = new FormData(form);
            event.preventDefault();
            updateTrafficInPlace(pk, id, formData);
        });
        
    } catch (error) {
        const errorMsg = error.toString().replace('Error: ', '')
        resetModal();
        showToast(errorMsg, true);
    }

};

const createTrafficInPlace = async (pk, formData) => {
    const url = `/dashboard/trafego/${pk}/add-item-json`
    let data = {}
    formData.forEach(function(value, key) {
        data[key] = value;
    });

    data_json = JSON.stringify(data);
    try {
        const response = await fetch(url, {
            method: 'POST',
            mode: 'cors',
            cache: "no-cache",
            credentials: "same-origin",
            headers: {
                "Content-Type": "application/json",
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: data_json,
        })
        const result = await response.json();

        if (!result.success) {
            const strErrors = result.message
            let strResult = ""
            if ((strErrors instanceof Array) && (strErrors.length > 0)) {
                strErrors.forEach((value) => {
                    strResult += `${value.message}\n`
                });
            }
            throw new Error(strResult)
        }

        const fields = result.obj
        const { 
            id,
            cor,
            trafego,
            slug,
            slug_obra,
            pista,
            km_inicial, 
            km_final, 
            causa, 
            comentario, 
            cond_climaticas, 
            atualizado_em, 
            atualizado_por 
        } = fields

        createTrafficDom(pk, id, cor, trafego, pista, km_inicial, km_final, causa, comentario, cond_climaticas, slug, slug_obra, atualizado_em, atualizado_por);

        resetModal();
        showToast("Tráfego criado com sucesso!");
    } catch (error) {
        showToast(error.message, true);
    }
}

const createTraffic = async (el) => {
    const pk = el.getAttribute("data-rodovia-id");
    const url = `/dashboard/trafego/${pk}/add-item-json`
    const modal = document.getElementById('trafego-modal');

    try {
        const response = await fetch(url)

        if (response.status != 200) {
            throw new Error();
        }
        const result = await response.text();

        modal.innerHTML = result;
        const form = document.getElementById('form-trafego');
        form.addEventListener('submit', function(event) {
            let formData = new FormData(form);
            event.preventDefault();
            createTrafficInPlace(pk, formData);
        });
        
    } catch (error) {
        resetModal();
        showToast('Ocorreu um erro!', true);
    }
};

const updateWihtoutModifications = async (el) => {
    const pk = el.getAttribute("data-trafego-id");
    const url = `/dashboard/trafego/${pk}/no-modification-edit-item-json`
    const container_atualizado_em = document.querySelector(`[data-trafego-atualizado-em="${pk}"]`)
    const container_atualizado_por = document.querySelector(`[data-trafego-atualizado-por="${pk}"]`)
    const container_atualizacao = document.querySelector(`[data-trafego-atualizacao-container="${pk}"]`)

    let confirmation = confirm('Deseja atualizar sem alterar os dados?')
    if (confirmation) {

        try {       
            const result = await fetch(url);
            const {success, message, atualizado_em, atualizado_por} = await result.json();
            if (success) {
                container_atualizacao.className = `font-light text-black`
                container_atualizado_em.textContent = `${atualizado_em}`
                container_atualizado_por.textContent = `${atualizado_por}`
                showToast("Tráfego editado com sucesso!");
            } else {
                showToast(message, true);
            }
        } catch (error) {
            showToast(error.message, true);
        }
    }
}


const finish = async (el) => {
    const trafPk = el.getAttribute("data-trafego-id");
    const trafRodoviaPk = el.getAttribute("data-rodovia-id");
    const url = `/dashboard/trafego/${trafPk}/json-finish-item`
    const containerTrafego = document.querySelector(`#trafego-${trafPk}`)
    const dividerEl = document.querySelector(`#divider-${trafPk}`)
    
    // const trafRodoviaEl = document.querySelector(`[data-trafego-rodovia-id="${trafRodoviaPk}"]`);
    // let counter = trafRodoviaEl.getAttribute('data-trafego-counter');

    let confirmation = confirm('Atenção! Deseja realmente encerrar este tráfego? Esta ação é irreversível!')
    if (confirmation) {
        try {       
            const result = await fetch(url);
            const {success, message} = await result.json();

            if (success) {
                containerTrafego.remove();
                if (dividerEl) {

                    dividerEl.remove();
                }
                // counter -= 1;
                // trafRodoviaEl.setAttribute('data-trafego-counter', counter);
                let counter = decrementTrafegoCounter(trafRodoviaPk)

                if (counter == 0) {

                    let html = `
                        <div class="flex flex-col w-full align-middle md:flex-row" id="container-trafego-normal-${trafRodoviaPk}">
                            <div class="flex self-center mr-4">
                            <div class="flex w-full mt-4 md:mt-0">

                            </div>
                            </div>
                            <div class="flex flex-grow align-middle text-xs flex-wrap">
                            <div class="flex w-full m-2 lg:w-2/12">
                                <span style="height: 15px;
                                            width: 15px;
                                            background-color: #309143;
                                            border-radius: 50%;
                                            border-width: 1px;
                                            border-color: rgb(200, 200, 200);
                                            margin-right: 1rem;
                                            display: inline-block;"></span>
                                <span class="font-bold">tráfego:&nbsp;</span>normal
                            </div>
                            <div class="flex w-full m-2">
                                <a 
                                onclick=createTraffic(this) 
                                data-hystmodal="#myModal"
                                data-rodovia-id="${trafRodoviaPk}"
                                class="cursor-pointer text-indigo-600 hover:text-indigo-900">[
                                Status ágil ]</a>
                                &nbsp;ou&nbsp;<a 
                                href="/dashboard/trafego/${trafRodoviaPk}/add-item"
                                class="cursor-pointer text-indigo-600 hover:text-indigo-900">[
                                Novo status ]</a>
                            </div>
                            </div>
                        </div>
                    `
                    let refEl = document.getElementById(`new-trafego-${trafRodoviaPk}`);
                    let newTrafegoDomElement = document.createElement("div");
                    newTrafegoDomElement.innerHTML = html;
                    refEl.after(newTrafegoDomElement);
                    let containerNewStatus = document.querySelector(`#container-esquerda-novo-status-${trafRodoviaPk}`);
                    containerNewStatus.classList.add('hidden')

                }

                showToast('Tráfego encerrado com sucesso.');
            } else {
                showToast(message, true, true);
            }
        } catch (error) {
            showToast(error.message, true, true);
        }
    }
}

function decrementTrafegoCounter(pk) {
    const trafRodoviaEl = document.querySelector(`[data-trafego-rodovia-id="${pk}"]`);
    let counter = trafRodoviaEl.getAttribute('data-trafego-counter');
    counter -= 1;
    trafRodoviaEl.setAttribute('data-trafego-counter', counter);
    return counter;
}

function incrementTrafegoCounter(pk) {
    const trafRodoviaEl = document.querySelector(`[data-trafego-rodovia-id="${pk}"]`);
    let counter = trafRodoviaEl.getAttribute('data-trafego-counter');
    counter += 1;
    trafRodoviaEl.setAttribute('data-trafego-counter', counter);
    return counter;
}

function updateTrafficDom(el, pk, pista, km_inicial, km_final, causa, comentario, cond_climaticas, slug, slug_obra, atualizado_em, atualizado_por) {

    el.setAttribute("data-trafego-id", pk);
    let pistaTxt = document.querySelector(`[data-trafego-pista="${pk}"]`);
    let kmInicialTxt = document.querySelector(`[data-trafego-km-inicial="${pk}"]`);
    let kmFinalTxt = document.querySelector(`[data-trafego-km-final="${pk}"]`);
    let causaTxt = document.querySelector(`[data-trafego-causa="${pk}"]`);
    let comentarioTxt = document.querySelector(`[data-trafego-comentario="${pk}"]`);
    let tempoTxt = document.querySelector(`[data-trafego-tempo="${pk}"]`);
    let slugTxt = document.querySelector(`[data-trafego-slug="${pk}"]`);
    let slugContainer = document.querySelector(`[data-trafego-slug-container="${pk}"]`);
    let slugObraTxt = document.querySelector(`[data-trafego-slug-obra="${pk}"]`);
    let slugObraContainer = document.querySelector(`[data-trafego-slug-obra-container="${pk}"]`);
    let atualizadoEmTxt = document.querySelector(`[data-trafego-atualizado-em="${pk}"]`);
    let atualizadoPorTxt = document.querySelector(`[data-trafego-atualizado-por="${pk}"]`);
    let container_atualizacao = document.querySelector(`[data-trafego-atualizacao-container="${pk}"]`)


    pistaTxt.textContent = pista ? pista.toLowerCase() : "";
    kmInicialTxt.textContent = km_inicial ? parseFloat(km_inicial).toFixed(3) : "-";
    kmFinalTxt.textContent = km_final ? parseFloat(km_final).toFixed(3) : "-";
    causaTxt.textContent = causa ? causa.toLowerCase() : "-";
    comentarioTxt.textContent = comentario ? comentario.toLowerCase() : "";
    tempoTxt.textContent = cond_climaticas ? cond_climaticas.toLowerCase() : "-";
    updateSlug(slugTxt, slug, pk, slugContainer, 'ocorrencia');
    updateSlug(slugObraTxt, slug_obra, pk, slugObraContainer, 'obra');
    atualizadoEmTxt.textContent = atualizado_em ? atualizado_em : "-";
    atualizadoPorTxt.textContent = atualizado_por ? atualizado_por : "-";
    container_atualizacao.className = `font-light text-black`
}

function updateSlug(slugTxt, slug, pk, container, tipo) {
    if (slugTxt && slug) {
        slugTxt.textContent = slug.toUpperCase();
        slugTxt.href = `/${slug}`;
    }
    if (slugTxt && !slug) {
        slugTxt.remove();
    }
    if (!slugTxt && slug && tipo == 'ocorrencia') {
        let newHTMLnode = `
            <a class="flex justify-center items-center bg-gray-100 border-0 px-2 py-2 md:py-0 focus:outline-none hover:bg-gray-200 rounded text-xs text-center m-0 font-light text-indigo-600 shadow cursor-pointer"
                href="/${slug}"
                target="_blank"
                aria-label="clique para visualizar detalhes da ocorrência"
                data-microtip-position="top" role="tooltip"
                data-trafego-slug="${pk}">
                ${slug.toUpperCase()}
            </a>
        `;
        container.innerHTML = newHTMLnode;
        container.classList.remove('hidden');
    }
    if (!slugTxt && slug && tipo == 'obra') {
        let newHTMLnode = `
            <a class="flex justify-center items-center bg-gray-100 border-0 px-2 py-2 md:py-0 focus:outline-none hover:bg-gray-200 rounded text-xs text-center m-0 font-light text-indigo-600 shadow cursor-pointer"
                href="/intervencoes-viarias/${slug}"
                target="_blank"
                aria-label="clique para visualizar detalhes da ocorrência"
                data-microtip-position="top" role="tooltip"
                data-trafego-slug-obra="${pk}">
                ${slug.toUpperCase()}
            </a>
        `;
        container.innerHTML = newHTMLnode;
        container.classList.remove('hidden');
    }
}

function createTrafficDom(
    pk, id, cor, trafego, pista, km_inicial, km_final, causa, comentario, cond_climaticas, slug, slug_obra,
    atualizado_em, atualizado_por, fotos) {
    let refEl = document.getElementById(`new-trafego-${pk}`);

    let trafNormalEl = document.getElementById(`container-trafego-normal-${pk}`);
    let pistaTxt = pista ? pista.toLowerCase() : "-";
    let kmInicialTxt = km_inicial ? parseFloat(km_inicial).toFixed(3) : "-";
    let kmFinalTxt = km_final ? parseFloat(km_final).toFixed(3) : "-";
    let trafegoTxt = trafego ? trafego.toLowerCase() : "-";
    let causaTxt = causa ? causa.toLowerCase() : "-";
    let comentarioTxt = comentario ? comentario.toLowerCase() : "";
    let tempoTxt = cond_climaticas ? cond_climaticas.toLowerCase() : "";
    let atualizadoEmTxt = atualizado_em ? atualizado_em : "-";
    let atualizadoPorTxt = atualizado_por ? atualizado_por : "-";
    let html = `
        <div 
            class="flex flex-col w-full align-middle md:flex-row trafego-container mb-2" 
            id="trafego-${id}"
            data-parent-id="${pk}"
        >
        <div class="flex self-center mr-4">
            <div class="flex w-full mt-4 md:mt-0">

            </div>
        </div>
        <div class="flex flex-grow align-middle text-xs flex-wrap mt-2">
            <div class="flex w-full m-2 lg:m-0 lg:w-2/12">
            <span data-trafego-cor="${id}" class="" style="
                        min-height: 15px;
                        min-width: 15px;
                        max-height: 15px;
                        max-width: 15px;
                        background-color: ${cor ? cor : '#000000'};
                        border-radius: 50%;
                        margin-right: 1rem;
                        border-width: 1px;
                        border-color: rgb(200, 200, 200);
                        display: inline-block;"></span>
            <span class="font-bold">tráfego:&nbsp;</span>${trafegoTxt}
            </div>
            <div class="flex w-full m-2 lg:m-0 lg:w-2/12">
                <span class="font-bold">local:&nbsp;</span>
                <span data-trafego-pista="${id}">${pistaTxt}</span>
            </div>
            <div class="flex w-full m-2 lg:m-0 lg:w-2/12">
                <span class="font-bold">km inicial:&nbsp;</span>
                <span data-trafego-km-inicial="${id}">${kmInicialTxt}</span>
            </div>
            <div class="flex w-full m-2 lg:m-0 lg:w-2/12"><span class="font-bold">km
            final:&nbsp;</span>
            <span data-trafego-km-final="${id}">
                ${kmFinalTxt}
            </span>
            </div>
            <div class="flex w-full m-2 lg:m-0 lg:w-2/12">
            <span class="font-bold">causa:&nbsp;</span>
            <span data-trafego-causa="${id}">
                ${causaTxt}
            </span>
            </div>
            <div class="flex w-full m-2 lg:m-0 lg:w-2/12"><span class="font-bold">tempo:&nbsp;</span>
            <span data-trafego-tempo="${id}">
                ${tempoTxt}
            </span>
            </div>
            <div class="flex flex-col md:flex-row w-full m-2">
            <span data-trafego-comentario="${id}" class="font-bold">
                ${comentarioTxt}
            </span>
            <div data-trafego-slug-container="${id}" class="hidden"></div>
            <div data-trafego-slug-obra-container="${id}" class="hidden"></div>
            <span data-trafego-atualizacao-container="${id}" class="font-light">
                &nbsp;
                <span data-trafego-atualizado-em="${id}">${atualizadoEmTxt}</span>
            </span>
            &nbsp;por&nbsp;
            <span data-trafego-atualizado-por="${id}">${atualizadoPorTxt}</span>
            </div>
        `
    if (fotos) {
        fotos.forEach(foto => {

            let {trafego, foto_pk, foto_blob, foto_desatualizada, legenda, criado_em, criado_por} = foto
            let desatualizadaStyle = foto_desatualizada ? 'text-red-800' : ''
            let fotosHTML = `
                <div class="flex flex-col justify-center items-center mx-auto">
                    <a target="_blank" href="/trafego/${pk}/foto/${foto_pk}" aria-label="clique para ver em tamanho maior"
                    data-microtip-position="top" role="tooltip">
                    <img
                        class="object-fit object-center  border border-gray-200 rounded min-h-48 max-h-48 min-w-xs max-w-xs block my-2 mx-2"
                        src="data:image/png;base64,${foto_blob}" alt="${legenda ? legenda : ''}">
                    </a>
                    <div class="font-light text-center">${legenda ? legenda : ''}</div>
                    <div class="text-center font-medium ${desatualizadaStyle}">
                        ${criado_em} por ${criado_por}
                    </div>
                </div>
            `
            html += fotosHTML
        });
    }
    html += `
                <div class="flex w-full m-2">
                    <a 
                        onclick=updateWihtoutModifications(this) 
                        data-trafego-id="${id}" 
                        data-rodovia-id="${pk}"
                        class="cursor-pointer text-indigo-600 hover:text-indigo-900"
                    >Atualizar sem alterações</a>
                    <span>&nbsp;|&nbsp;</span>
                    <a 
                        onclick=editTraffic(this) 
                        data-hystmodal="#myModal"
                        data-trafego-id="${id}" 
                        data-rodovia-id="${pk}"
                        class="cursor-pointer text-indigo-600 hover:text-indigo-900"
                    >Editar aqui</a>
                    <span>&nbsp;|&nbsp;</span>
                    <a 
                        href="/dashboard/trafego/${id}/edit-item"
                        class="cursor-pointer text-indigo-600 hover:text-indigo-900"
                    >Editar </a>
                    <span>&nbsp;|&nbsp;</span>
                    <a href="/dashboard/trafego/${id}/add-photo"
                        class="text-indigo-600 hover:text-indigo-900">Fotos</a>
                    <span>&nbsp;|&nbsp;</span>
                    <a 
                        onclick=finish(this)
                        data-trafego-id="${id}" 
                        data-rodovia-id="${pk}"
                        class="cursor-pointer text-indigo-600 hover:text-indigo-900">Encerrar</a>
                </div>
            </div>
        </div>
    `
    if(trafNormalEl) {
        trafNormalEl.remove();
    } else {
        html += `<hr id="divider-${id}" />`
    }
    let newTrafegoDomElement = document.createElement("div");
    newTrafegoDomElement.innerHTML = html;
    refEl.after(newTrafegoDomElement);
    let slugTxt = document.querySelector(`[data-trafego-slug="${id}"]`);
    let slugContainer = document.querySelector(`[data-trafego-slug-container="${id}"]`);
    let slugTxtObra = document.querySelector(`[data-trafego-slug-obra="${id}"]`);
    let slugContainerObra = document.querySelector(`[data-trafego-slug-obra-container="${id}"]`);
    updateSlug(slugTxt, slug, id, slugContainer, 'ocorrencia');
    updateSlug(slugTxtObra, slug_obra, id, slugContainerObra, 'obra');
    incrementTrafegoCounter(pk);

    let containerNewStatus = document.querySelector(`#container-esquerda-novo-status-${pk}`);

    let newStatusHTML = `
        
        <a 
            onclick=createTraffic(this) 
            data-hystmodal="#myModal"
            data-rodovia-id="${pk}"
            class="text-xs cursor-pointer text-indigo-600 hover:text-indigo-900">[
            Status ágil ]</a>
        <br /><a 
            href="/dashboard/trafego/${pk}/add-item"
            class="text-xs cursor-pointer text-indigo-600 hover:text-indigo-900">[
            Novo status ]</a>
    `
    if (containerNewStatus) {
        containerNewStatus.classList.remove('hidden');
        containerNewStatus.innerHTML = newStatusHTML;
    }
    
}   

