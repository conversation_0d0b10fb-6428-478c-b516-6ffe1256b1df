@charset "UTF-8";
/*
 |  tail.datetime - The vanilla way to select dates and times!
 |  @file       ./less/tail.datetime-harx-light.less
 |  <AUTHOR> <<EMAIL>>
 |  @version    0.4.14 - Beta
 |
 |  @website    https://github.com/pytesNET/tail.DateTime
 |  @license    X11 / MIT License
 |  @copyright  Copyright © 2018 - 2019 SamBrishes, pytesNET <<EMAIL>>
 */

/* @start MAIN CALENDAR */
.tail-datetime-calendar, .tail-datetime-calendar *, .tail-datetime-calendar *:before,
.tail-datetime-calendar *:after{
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}
.tail-datetime-calendar{
    top: 0;
    left: 0;
    width: 360px;
    height: auto;
    margin: 15px;
    padding: 0;
    z-index: 3000;
    display: block;
    position: absolute;
    visibility: hidden;
    direction: ltr;
    border-collapse: separate;
    font-family: "Open Sans", Calibri, Aria<PERSON>, sans-serif;
    background-color: white;
    border-width: 0;
    border-style: solid;
    border-color: transparent;
    border-radius: 3px;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.15), 0 1px 2px 1px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.15), 0 1px 2px 1px rgba(0, 0, 0, 0.1);
}
.tail-datetime-calendar:after{
    clear: both;
    content: "";
    display: block;
    font-size: 0;
    visibility: hidden;
}
.tail-datetime-calendar.calendar-static{
    top: auto;
    left: auto;
    margin-left: auto;
    margin-right: auto;
    position: static;
    visibility: visible;
}
.tail-datetime-calendar button.calendar-close{
    top: 100%;
    right: 15px;
    color: #303438;
    width: 35px;
    height: 25px;
    margin: 1px 0 0 0;
    padding: 5px 10px;
    opacity: 0.5;
    display: inline-block;
    position: absolute;
    font-size: 14px;
    line-height: 1.125em;
    text-shadow: none;
    border: 0;
    outline: none;
    background-color: white;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9z\
                dmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDEyIDE2Ij48cGF0aCBmaWxsPSIjMzAzNDM4IiB\
                kPSJNNy40OCA4bDMuNzUgMy43NS0xLjQ4IDEuNDhMNiA5LjQ4bC0zLjc1IDMuNzUtMS40OC0xLjQ4TDQuNTIgOCAuNz\
                cgNC4yNWwxLjQ4LTEuNDhMNiA2LjUybDMuNzUtMy43NSAxLjQ4IDEuNDhMNy40OCA4eiIvPjwvc3ZnPg==");
    background-repeat: no-repeat;
    background-position: center center;
    border-width: 0;
    border-style: solid;
    border-color: transparent;
    border-radius: 0 0 3px 3px;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.15), 0 1px 2px 1px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.15), 0 1px 2px 1px rgba(0, 0, 0, 0.1);
    transition: opacity 142ms linear;
    -webkit-transition: opacity 142ms linear;
}
.tail-datetime-calendar button.calendar-close:hover{
    opacity: 1;
}
/* @end MAIN CALENDAR */

/* @start CALENDAR TOOLTIP */
.tail-datetime-calendar .calendar-tooltip{
    color: white;
    width: auto;
    margin: 0;
    padding: 0;
    display: block;
    position: absolute;
    background-color: #303438;
    border-radius: 3px;
}
.tail-datetime-calendar .calendar-tooltip:before{
    top: -7px;
    left: 50%;
    width: 0;
    height: 0;
    margin: 0 0 0 -6px;
    content: "";
    display: block;
    position: absolute;
    border-width: 0 7px 7px 7px;
    border-style: solid;
    border-color: transparent transparent #303438 transparent;
}
.tail-datetime-calendar .calendar-tooltip .tooltip-inner{
    width: auto;
    margin: 0;
    padding: 4px 7px;
    display: block;
    font-size: 12px;
    line-height: 14px;
}
/* @end CALENDAR TOOLTIP */

/* @start CALENDAR ACTIONs */
.tail-datetime-calendar .calendar-actions{
    color: #303438;
    width: 100%;
    height: 36px;
    margin: 0;
    padding: 0;
    display: table;
    overflow: hidden;
    border-spacing: 0;
    border-collapse: separate;
    background-color: white;
    border-width: 0;
    border-style: solid;
    border-color: transparent;
    border-radius: 3px 3px 0 0;
}
.tail-datetime-calendar .calendar-actions span{
    margin: 0;
    padding: 0;
    opacity: 0.5;
    display: table-cell;
    position: relative;
    text-align: center;
    line-height: 40px;
    text-shadow: none;
    background-repeat: no-repeat;
    background-position: center center;
    transition: opacity 142ms linear, background 142ms linear;
    -webkit-transition: opacity 142ms linear, background 142ms linear;
}
.tail-datetime-calendar .calendar-actions span[data-action]{
    cursor: pointer;
}
.tail-datetime-calendar .calendar-actions span.action{
    width: 50px;
    font-size: 22px;
}
.tail-datetime-calendar .calendar-actions span.label{
    width: auto;
    opacity: 1;
    font-size: 16px;
}
.tail-datetime-calendar .calendar-actions span:first-child{
    border-radius: 4px 0 0 0;
}
.tail-datetime-calendar .calendar-actions span:last-child{
    border-radius: 0 4px 0 0;
}
.tail-datetime-calendar .calendar-actions span:first-child:before,
.tail-datetime-calendar .calendar-actions span:last-child:before{
    top: 5px;
    bottom: 5px;
    width: 1px;
    height: auto;
    margin: 0;
    padding: 0;
    content: "";
    display: inline-block;
    position: absolute;
    background-color: #e6e6e6;
}
.tail-datetime-calendar .calendar-actions span:first-child:before{
    right: -1px;
}
.tail-datetime-calendar .calendar-actions span:last-child:before{
    left: -1px;
}
.tail-datetime-calendar .calendar-actions span:first-child:hover:before,
.tail-datetime-calendar .calendar-actions span:last-child:hover:before{
    display: none;
}
.tail-datetime-calendar .calendar-actions span[data-action]:hover{
    opacity: 0.95;
    background-color: #e6e6e6;
}
.tail-datetime-calendar .calendar-actions span.action-prev{
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zd\
                mciIHdpZHRoPSI2IiBoZWlnaHQ9IjE2IiB2aWV3Qm94PSIwIDAgNiAxNiI+PHBhdGggZD0iTTYgMkwwIDhsNiA2VjJ6\
                Ii8+PC9zdmc+");
}
.tail-datetime-calendar .calendar-actions span.action-next{
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9z\
                dmciIHdpZHRoPSI2IiBoZWlnaHQ9IjE2IiB2aWV3Qm94PSIwIDAgNiAxNiI+PHBhdGggZD0iTTAgMTRsNi02LTYtNnY\
                xMnoiLz48L3N2Zz4=");
}
.tail-datetime-calendar .calendar-actions span.action-submit{
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9z\
                dmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDEyIDE2Ij48cGF0aCBkPSJNMTIgNWwtOCA4LTQ\
                tNCAxLjUtMS41TDQgMTBsNi41LTYuNUwxMiA1eiIvPjwvc3ZnPg==");
}
.tail-datetime-calendar .calendar-actions span.action-cancel{
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9z\
                dmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDEyIDE2Ij48cGF0aCBmaWxsPSIjMzAzNDM4IiB\
                kPSJNNy40OCA4bDMuNzUgMy43NS0xLjQ4IDEuNDhMNiA5LjQ4bC0zLjc1IDMuNzUtMS40OC0xLjQ4TDQuNTIgOCAuNz\
                cgNC4yNWwxLjQ4LTEuNDhMNiA2LjUybDMuNzUtMy43NSAxLjQ4IDEuNDhMNy40OCA4eiIvPjwvc3ZnPg==");
}
/* @end CALENDAR ACTIONs */

/* @start CALENDAR DATEPICKER */
.tail-datetime-calendar .calendar-datepicker{
    width: 100%;
    margin: 0 0 15px 0;
    padding: 0;
    display: block;
    position: relative;
}
.tail-datetime-calendar .calendar-datepicker:after{
    top: -1px;
    left: 10px;
    right: 10px;
    content: "";
    position: absolute;
    border-width: 1px 0 0 0;
    border-style: solid;
    border-color: #e6e6e6;
}
.tail-datetime-calendar .calendar-datepicker table{
    width: 100%;
    margin: 0;
    padding: 0;
    border-spacing: 1px;
    border-collapse: separate;
}
.tail-datetime-calendar .calendar-datepicker table tr th,
.tail-datetime-calendar .calendar-datepicker table tr td{
    color: #303438;
    width: 50px;
    height: 35px;
    padding: 0;
    position: relative;
    font-size: 13px;
    text-align: center;
    font-weight: normal;
    text-shadow: none;
    line-height: 30px;
    background-color: transparent;
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    border-radius: 3px;
}
.tail-datetime-calendar .calendar-datepicker table tr td{
    cursor: pointer;
}
.tail-datetime-calendar .calendar-datepicker table tr td span.inner{
    margin: 0;
    padding: 0;
    display: inline-block;
}
.tail-datetime-calendar .calendar-datepicker table tr td.date-disabled{
    cursor: not-allowed;
    color: #A0A4A8;
}
.tail-datetime-calendar .calendar-datepicker table tr td.date-disabled:after{
    color: #A0A4A8;
    top: 0;
    left: 0;
    width: 49px;
    height: 35px;
    margin: 0;
    padding: 0;
    content: "✕";
    opacity: 0.25;
    display: inline-block;
    position: absolute;
    font-size: 30px;
    line-height: 35px;
}
.tail-datetime-calendar .calendar-datepicker table tr td.date-previous,
.tail-datetime-calendar .calendar-datepicker table tr td.date-next{
    color: #808488;
    background-color: transparent;
}
.tail-datetime-calendar .calendar-datepicker table tr td.date-today:before,
.tail-datetime-calendar .calendar-datepicker table tr td .tooltip-tick{
    top: 5px;
    width: 5px;
    height: 5px;
    margin: 0;
    padding: 0;
    z-index: 20;
    content: "";
    display: inline-block;
    position: absolute;
    border-width: 0;
    border-style: solid;
    border-color: transparent;
    border-radius: 50%;
}
.tail-datetime-calendar .calendar-datepicker table tr td.date-today:before{
    left: 5px;
    background-color: #32B93C;
}
.tail-datetime-calendar .calendar-datepicker table tr td .tooltip-tick{
    right: 5px;
    background-color: #303438;
}
.tail-datetime-calendar .calendar-datepicker table tr td.date-select.date-today:before,
.tail-datetime-calendar .calendar-datepicker table tr td.date-select .tooltip-tick{
    background-color: white;
}
.tail-datetime-calendar .calendar-datepicker table tr td .tooltip-tick:before,
.tail-datetime-calendar .calendar-datepicker table tr td .tooltip-tick:after{
    display: none;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-day,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-month,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-year,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade{
    width: 14.28571429%;
    height: 35px;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-day:hover,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-month:hover,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-year:hover,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade:hover{
    border-color: #e6e6e6;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-day.date-disabled:hover,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-month.date-disabled:hover,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-year.date-disabled:hover,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade.date-disabled:hover{
    border-color: white;
    background-color: white;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-day.date-today,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-month.date-today,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-year.date-today,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade.date-today{
    color: #32B93C;
    border-color: #32B93C;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-day.date-select,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-month.date-select,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-year.date-select,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade.date-select{
    border-color: #149BE6;
    background-color: #149BE6;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-day.date-select span,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-month.date-select span,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-year.date-select span,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade.date-select span{
    color: white;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-day span.inner,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-month span.inner,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-year span.inner,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade span.inner{
    border: 0;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-year,
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade{
    width: 25%;
}
.tail-datetime-calendar .calendar-datepicker table tr td.calendar-decade span.inner{
    height: 54px;
    padding: 7px 15px;
    text-align: center;
    line-height: 20px;
}
/* @end CALENDAR DATEPICKER */

/* @start CALENDAR TIMEPICKER */
.tail-datetime-calendar .calendar-timepicker{
    width: 100%;
    margin: 0;
    padding: 0;
    display: block;
    position: relative;
    border-top: 0;
    text-align: center;
}
.tail-datetime-calendar .calendar-timepicker:after{
    top: -1px;
    left: 10px;
    right: 10px;
    content: "";
    position: absolute;
    border-width: 1px 0 0 0;
    border-style: solid;
    border-color: #e6e6e6;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field{
    width: auto;
    margin: 0;
    padding: 20px 10px 10px 10px;
    display: inline-block;
    position: relative;
    text-align: center;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field:first-of-type{
    text-align: right;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field:last-of-type{
    text-align: left;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input[type="text"]{
    color: #606468;
    width: 75px;
    height: 35px;
    margin: 0;
    z-index: 4;
    padding: 3px 20px 3px 5px;
    display: inline-block;
    position: relative;
    font-size: 12px;
    text-align: center;
    appearance: textfield;
    -moz-appearance: textfield;
    -webkit-appearance: textfield;
    background-color: white;
    border-width: 1px;
    border-style: solid;
    border-color: #E0E0E0;
    border-radius: 3px;
    box-shadow: none;
    -webkit-box-shadow: none;
    transition: color 142ms linear, border 142ms linear, background 142ms linear;
    -webkit-transition: color 142ms linear, border 142ms linear, background 142ms linear;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input[type="text"]:hover{
    color: #404448;
    border-color: #D0D0D0;
    background-color: white;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input[type="text"]:focus{
    color: #303438;
    border-color: #149BE6;
    background-color: white;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input[type="text"]:disabled{
    cursor: not-allowed;
    color: #A0A4A8;
    border-color: #D0D0D0;
    background-color: #F0F0F0;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field button.picker-step{
    right: 11px;
    width: 20px;
    height: 17px;
    margin: 0;
    padding: 0;
    z-index: 15;
    display: inline-block;
    position: absolute;
    background-color: white;
    box-shadow: none;
    -webkit-box-shadow: none;
    transition: border 142ms linear, background 142ms linear;
    -webkit-transition: border 142ms linear, background 142ms linear;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field button.picker-step:before{
    top: 5px;
    left: 50%;
    width: 0;
    height: 0;
    margin: 0 0 0 -3px;
    padding: 0;
    content: "";
    display: inline-block;
    position: absolute;
    transition: border 142ms linear;
    -webkit-transition: border 142ms linear;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field button.picker-step.step-up{
    top: 21px;
    border-width: 0 0 1px 1px;
    border-style: solid;
    border-color: #E0E0E0;
    border-radius: 0 2px 0 0;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field button.picker-step.step-up:hover{
    background-color: white;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field button.picker-step.step-up:before{
    border-width: 0 4px 5px 4px;
    border-style: solid;
    border-color: transparent transparent #606468 transparent;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field button.picker-step.step-down{
    top: 37px;
    border-width: 1px 0 0 1px;
    border-style: solid;
    border-color: #E0E0E0;
    border-radius: 0 0 2px 0;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field button.picker-step.step-down:hover{
    background-color: white;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field button.picker-step.step-down:before{
    border-width: 5px 4px 0 4px;
    border-style: solid;
    border-color: #606468 transparent transparent transparent;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:focus + button.step-up{
    border-color: #149BE6;
    background-color: white;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:focus + button.step-up:before{
    border-bottom-color: #149BE6;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:focus + button.step-up:hover{
    color: white;
    background-color: #149BE6;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:focus + button.step-up:hover:before{
    border-bottom-color: white;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:focus + button + button.step-down{
    border-color: #149BE6;
    background-color: white;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:focus + button + button.step-down:before{
    border-top-color: #149BE6;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:focus + button + button.step-down:hover{
    color: white;
    background-color: #149BE6;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:focus + button + button.step-down:hover:before{
    border-top-color: white;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:disabled + button.step-up{
    cursor: not-allowed;
    border-color: #D0D0D0;
    background-color: #F0F0F0;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:disabled + button.step-up:hover{
    border-color: #D0D0D0;
    background-color: #F0F0F0;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:disabled + button.step-up:before{
    border-bottom-color: #A0A4A8;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:disabled + button + button.step-down{
    cursor: not-allowed;
    border-color: #D0D0D0;
    background-color: #F0F0F0;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:disabled + button + button.step-down:hover{
    border-color: #D0D0D0;
    background-color: #F0F0F0;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field input:disabled + button + button.step-down:before{
    border-top-color: #A0A4A8;
}
.tail-datetime-calendar .calendar-timepicker .timepicker-field label{
    color: #606468;
    margin: 0;
    padding: 0;
    display: block;
    font-size: 12px;
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch{
    cursor: pointer;
    margin: 0 0 -8px 0;
    padding: 15px 0 0 0;
    display: block;
    text-align: center;
    vertical-align: top;
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch:before,
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch:after{
    width: auto;
    margin: 0;
    padding: 0 5px;
    font-size: 12px;
    line-height: 16px;
    vertical-align: top;
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch:before{
    content: attr(data-am);
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch:after{
    content: attr(data-pm);
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch input[type="checkbox"]{
    display: none;
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch input[type="checkbox"] + span{
    display: inline-block;
    position: relative;
    vertical-align: top;
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch input[type="checkbox"] + span:before{
    width: 50px;
    height: 16px;
    content: "";
    display: inline-block;
    vertical-align: top;
    border-width: 1px;
    border-style: solid;
    border-color: #149BE6;
    border-radius: 14px;
    transition: border 284ms linear;
    -webkit-transition: border 284ms linear;
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch input[type="checkbox"] + span:after{
    top: 3px;
    left: 4px;
    right: 30px;
    width: auto;
    height: 10px;
    margin: 0;
    padding: 0;
    content: "";
    display: inline-block;
    position: absolute;
    background-color: #149BE6;
    border-radius: 15px;
    vertical-align: top;
    transition: left 284ms linear, right 284ms linear 284ms, background 284ms linear;
    -webkit-transition: left 284ms linear, right 284ms linear 284ms, background 284ms linear;
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch input[type="checkbox"]:checked + span:before{
    border-color: #32B93C;
}
.tail-datetime-calendar .calendar-timepicker label.timepicker-switch input[type="checkbox"]:checked + span:after{
    left: 30px;
    right: 4px;
    background-color: #32B93C;
    transition: right 284ms linear, left 284ms linear 284ms, background 284ms linear;
    -webkit-transition: right 284ms linear, left 284ms linear 284ms, background 284ms linear;
}
/* @end CALENDAR TIMEPICKER */

/* @start RTL */
.tail-datetime-calendar.rtl{
    direction: rtl;
}
.tail-datetime-calendar.rtl .calendar-actions span.action-next,
.tail-datetime-calendar.rtl .calendar-actions span.action-prev{
    transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
}
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.date-disabled:after{
    right: 3px;
    transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
}
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.date-today:before{
    right: 5px;
}
.tail-datetime-calendar.rtl .calendar-datepicker table tr td .tooltip-tick{
    left: 5px;
}
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-month.date-today:before,
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-year.date-today:before,
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-decade.date-today:before{
    right: 50%;
    margin-right: -2.5px;
}
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-month:hover span.inner:before,
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-year:hover span.inner:before,
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-decade:hover span.inner:before{
    right: 6px;
    border-right-color: #cccccc;
}
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-month span.inner:after,
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-year span.inner:after,
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-decade span.inner:after{
    left: 0;
}
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-month:hover span.inner:after,
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-year:hover span.inner:after,
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-decade:hover span.inner:after{
    left: 6px;
    border-left-color: #cccccc;
}
.tail-datetime-calendar.rtl .calendar-datepicker table tr td.calendar-decade span.inner{
    text-align: right;
}
.tail-datetime-calendar.rtl .calendar-timepicker .timepicker-field:first-child{
    text-align: left;
    padding-left: 0;
    padding-right: 25px;
}
.tail-datetime-calendar.rtl .calendar-timepicker .timepicker-field:last-child{
    text-align: right;
    padding-left: 25px;
    padding-right: 0;
}
.tail-datetime-calendar.rtl .calendar-timepicker .timepicker-field:first-child input[type="text"]{
    margin-left: -1px;
    margin-right: 0;
    border-radius: 0 3px 3px 0;
}
.tail-datetime-calendar.rtl .calendar-timepicker .timepicker-field:last-child input[type="text"]{
    margin-left: 0;
    margin-right: -1px;
    border-radius: 3px 0 0 3px;
}
/* @end RTL */

/*# sourceMappingURL=tail.datetime-harx-light.map */
