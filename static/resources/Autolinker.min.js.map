{"version": 3, "sources": ["es2015/utils.js", "es2015/html-tag.js", "es2015/anchor-tag-builder.js", "es2015/truncate/truncate-end.js", "es2015/truncate/truncate-smart.js", "es2015/truncate/truncate-middle.js", "es2015/match/match.js", "../node_modules/tslib/tslib.es6.js", "es2015/match/email-match.js", "es2015/match/hashtag-match.js", "es2015/match/mention-match.js", "es2015/match/phone-match.js", "es2015/match/url-match.js", "es2015/matcher/matcher.js", "es2015/regex-lib.js", "es2015/matcher/tld-regex.js", "es2015/matcher/email-matcher.js", "es2015/matcher/url-match-validator.js", "es2015/matcher/url-matcher.js", "es2015/matcher/hashtag-matcher.js", "es2015/matcher/phone-matcher.js", "es2015/matcher/mention-matcher.js", "es2015/htmlParser/parse-html.js", "es2015/autolinker.js"], "names": ["indexOf", "arr", "element", "Array", "prototype", "i", "len", "length", "remove", "fn", "splice", "throwUnhandledCaseError", "theValue", "Error", "HtmlTag", "cfg", "this", "tagName", "attrs", "innerHTML", "whitespaceRegex", "innerHtml", "setTagName", "getTagName", "setAttr", "attrName", "attrValue", "getAttrs", "getAttr", "setAttrs", "Object", "assign", "setClass", "cssClass", "addClass", "newClass", "classAttr", "getClass", "classes", "split", "newClasses", "shift", "push", "join", "removeClass", "removeClasses", "idx", "hasClass", "setInnerHTML", "html", "setInnerHtml", "getInnerHTML", "getInnerHtml", "toAnchorString", "attrsStr", "buildAttrsStr", "attrsArr", "prop", "hasOwnProperty", "AnchorTagBuilder", "newWindow", "truncate", "className", "build", "match", "createAttrs", "processAnchorText", "getAnchorText", "href", "getAnchorHref", "createCssClass", "returnClasses", "cssClassSuffixes", "getCssClassSuffixes", "anchorText", "doTruncate", "ellipsisChars", "str", "truncateLen", "ellipsis<PERSON><PERSON><PERSON>", "truncate<PERSON><PERSON>th", "truncateLocation", "location", "url", "ellipsisLengthBeforeParsing", "buildUrl", "url<PERSON>bj", "scheme", "host", "path", "query", "fragment", "buildSegment", "segment", "remainingAvailableLength", "remainingAvailableLengthHalf", "startOffset", "Math", "ceil", "endOffset", "floor", "end", "substr", "urlSub", "availableLength", "matchQuery", "replace", "pathAndQuery", "truncateSmart", "truncateMiddle", "substring", "Match", "__jsduckDummyDocProp", "matchedText", "offset", "tagBuilder", "getMatchedText", "setOffset", "getOffset", "getType", "buildTag", "extendStatics", "d", "b", "setPrototypeOf", "__proto__", "p", "__extends", "__", "constructor", "create", "__assign", "t", "s", "n", "arguments", "call", "apply", "EmailMatch", "_super", "_this", "email", "tslib_1.__extends", "getEmail", "HashtagMatch", "serviceName", "hashtag", "getServiceName", "getHashtag", "MentionMatch", "mention", "getMention", "PhoneMatch", "number", "plusSign", "getPhoneNumber", "getNumber", "UrlMatch", "urlMatchType", "protocolUrlMatch", "protocolRelativeMatch", "stripPrefix", "www", "stripTrailingSlash", "decodePercentEncoding", "schemePrefixRegex", "wwwPrefixRegex", "protocolRelativeRegex", "protocolPrepended", "getUrlMatchType", "getUrl", "stripProtocolRelativePrefix", "stripSchemePrefix", "stripWwwPrefix", "removeTrailingSlash", "removePercentEncoding", "text", "char<PERSON>t", "slice", "preProcessedEntityAnchorText", "decodeURIComponent", "e", "Matcher", "letterRe", "digitRe", "whitespaceRe", "quoteRe", "controlCharsRe", "alphaCharsStr", "source", "alphaCharsAndMarksStr", "decimalNumbersStr", "alphaNumericCharsStr", "alphaNumericAndMarksCharsStr", "ipStr", "domainLabelStr", "getDomainLabelStr", "group", "getDomainNameStr", "domainNameCharRegex", "RegExp", "tldRegex", "EmailMatcher", "localPartCharRegex", "strictTldRegex", "parseMatches", "matches", "noCurrentEmailMatch", "CurrentEmailMatch", "mailtoTransitions", "m", "a", "l", "o", "charIdx", "state", "currentEmailMatch", "char", "stateNonEmail<PERSON><PERSON><PERSON>", "stateMailTo", "stateLocalPart", "stateLocalPartDot", "stateAtSign", "stateDomainChar", "stateDomainHyphen", "stateDomainDot", "captureMatchIfValidAndReset", "beginEmailMatch", "test", "prevChar", "tslib_1.__assign", "hasMailtoPrefix", "resetToNonEmailMatchState", "hasDomainDot", "newState", "emailAddress", "emailAddressNormalized", "pop", "toLowerCase", "undefined", "UrlMatchValidator", "<PERSON><PERSON><PERSON><PERSON>", "urlMatch", "isValidUriScheme", "urlMatchDoesNotHaveProtocolOrDot", "urlMatchDoesNotHaveAtLeastOneWordChar", "isValidIpAddress", "containsMultipleDots", "uriSchemeMatch", "newRegex", "hasFullProtocolRegex", "ipRegex", "stringBeforeSlash", "uriSchemeMatchArr", "uriSchemeRegex", "uriScheme", "hasWordCharAfterProtocolRegex", "Url<PERSON><PERSON><PERSON>", "urlSuffixRegex", "matcherRegex", "wordCharRegExp", "_loop_1", "matchStr", "schemeUrlMatch", "wwwUrlMatch", "wwwProtocolRelativeMatch", "tldProtocolRelativeMatch", "index", "this_1", "matchHasUnbalancedClosingParen", "pos", "matchHasInvalidCharAfterTld", "foundCommonScheme", "find", "commonScheme", "indexOfSchemeStart", "exec", "startChar", "endChar", "numOpenBraces", "max", "res", "HashtagMatcher", "nonWordCharRegex", "PhoneMatcher", "cleanNumber", "before", "after", "contextClear", "testMatch", "MentionMatcher", "matcherRegexes", "twitter", "instagram", "soundcloud", "parseHtml", "_a", "onOpenTag", "onCloseTag", "onText", "onComment", "onDoctype", "noCurrentTag", "CurrentTag", "currentDataIdx", "currentTag", "stateData", "stateTagOpen", "stateEndTagOpen", "stateTagName", "stateBeforeAttributeName", "stateAttributeName", "stateAfterAttributeName", "stateBeforeAttributeValue", "stateAttributeValueDoubleQuoted", "stateAttributeValueSingleQuoted", "stateAttributeValueUnquoted", "stateAfterAttributeValueQuoted", "stateSelfClosingStartTag", "stateMarkupDeclarationOpen", "stateCommentStart", "stateCommentStartDash", "stateComment", "stateCommentEndDash", "stateCommentEnd", "stateCommentEndBang", "stateDoctype", "startNewTag", "isClosing", "isOpening", "name", "captureTagName", "emitTagAndPreviousTextNode", "resetToDataState", "type", "toUpperCase", "textBeforeTag", "startIdx", "Autolinker", "version", "urls", "phone", "replaceFn", "context", "matchers", "normalizeUrlsCfg", "normalizeStripPrefixCfg", "normalizeTruncateCfg", "link", "textOrHtml", "options", "parse", "schemeMatches", "wwwMatches", "tldMatches", "dest", "src", "defaults", "Number", "POSITIVE_INFINITY", "skipTagNames", "skipTagsStackCount", "textSplit", "splitRegex", "global", "result", "lastIdx", "splitAndCapture", "currentOffset_1", "for<PERSON>ach", "splitText", "textNodeMatches", "parseText", "compactMatches", "removeUnwantedMatches", "sort", "matchedTextLength", "endIdx", "removeIdx", "getMatchers", "numMatchers", "textMatches", "j", "numTextMatches", "newHtml", "lastIndex", "createMatchReturnVal", "replaceFnResult", "getTagBuilder", "matcher", "Email", "Hashtag", "Mention", "Phone", "Url"], "mappings": ";;;;;;;;;gMA+CO,SAASA,EAAQC,EAAKC,GACzB,GAAIC,MAAMC,UAAUJ,QAChB,OAAOC,EAAID,QAAQE,GAGnB,IAAK,IAAIG,EAAI,EAAGC,EAAML,EAAIM,OAAQF,EAAIC,EAAKD,IACvC,GAAIJ,EAAII,KAAOH,EACX,OAAOG,EAEf,OAAQ,EAiBT,SAASG,EAAOP,EAAKQ,GACxB,IAAK,IAAIJ,EAAIJ,EAAIM,OAAS,EAAQ,GAALF,EAAQA,KACd,IAAfI,EAAGR,EAAII,KACPJ,EAAIS,OAAOL,EAAG,GA2CnB,SAASM,EAAwBC,GACpC,MAAM,IAAIC,MAAM,8BAAgCD,EAAW,KC7C/D,IAAIE,EAAyB,WAKzB,SAASA,EAAQC,QACD,IAARA,IAAkBA,EAAM,IAS5BC,KAAKC,QAAU,GAOfD,KAAKE,MAAQ,GAMbF,KAAKG,UAAY,GAOjBH,KAAKI,gBAAkB,MACvBJ,KAAKC,QAAUF,EAAIE,SAAW,GAC9BD,KAAKE,MAAQH,EAAIG,OAAS,GAC1BF,KAAKG,UAAYJ,EAAIM,WAAaN,EAAII,WAAa,GAsLvD,OA9KAL,EAAQV,UAAUkB,WAAa,SAAUL,GAErC,OADAD,KAAKC,QAAUA,EACRD,MAOXF,EAAQV,UAAUmB,WAAa,WAC3B,OAAOP,KAAKC,SAAW,IAS3BH,EAAQV,UAAUoB,QAAU,SAAUC,EAAUC,GAG5C,OAFeV,KAAKW,WACXF,GAAYC,EACdV,MAQXF,EAAQV,UAAUwB,QAAU,SAAUH,GAClC,OAAOT,KAAKW,WAAWF,IAQ3BX,EAAQV,UAAUyB,SAAW,SAAUX,GAEnC,OADAY,OAAOC,OAAOf,KAAKW,WAAYT,GACxBF,MAOXF,EAAQV,UAAUuB,SAAW,WACzB,OAAOX,KAAKE,QAAUF,KAAKE,MAAQ,KAQvCJ,EAAQV,UAAU4B,SAAW,SAAUC,GACnC,OAAOjB,KAAKQ,QAAQ,QAASS,IAQjCnB,EAAQV,UAAU8B,SAAW,SAAUD,GAEnC,IADA,IAAuLE,EAAnLC,EAAYpB,KAAKqB,WAAYjB,EAAkBJ,KAAKI,gBAAiBkB,EAAYF,EAAkBA,EAAUG,MAAMnB,GAArB,GAAuCoB,EAAaP,EAASM,MAAMnB,GAC9Je,EAAWK,EAAWC,UACW,IAAhCzC,EAAQsC,EAASH,IACjBG,EAAQI,KAAKP,GAIrB,OADAnB,KAAKW,WAAkB,MAAIW,EAAQK,KAAK,KACjC3B,MAQXF,EAAQV,UAAUwC,YAAc,SAAUX,GAEtC,IADA,IAA0LW,EAAtLR,EAAYpB,KAAKqB,WAAYjB,EAAkBJ,KAAKI,gBAAiBkB,EAAYF,EAAkBA,EAAUG,MAAMnB,GAArB,GAAuCyB,EAAgBZ,EAASM,MAAMnB,GACjKkB,EAAQ/B,SAAWqC,EAAcC,EAAcJ,UAAU,CAC5D,IAAIK,EAAM9C,EAAQsC,EAASM,IACd,IAATE,GACAR,EAAQ5B,OAAOoC,EAAK,GAI5B,OADA9B,KAAKW,WAAkB,MAAIW,EAAQK,KAAK,KACjC3B,MAQXF,EAAQV,UAAUiC,SAAW,WACzB,OAAOrB,KAAKW,WAAkB,OAAK,IAQvCb,EAAQV,UAAU2C,SAAW,SAAUd,GACnC,OAAwE,KAAhE,IAAMjB,KAAKqB,WAAa,KAAKrC,QAAQ,IAAMiC,EAAW,MAQlEnB,EAAQV,UAAU4C,aAAe,SAAUC,GAEvC,OADAjC,KAAKG,UAAY8B,EACVjC,MAQXF,EAAQV,UAAU8C,aAAe,SAAUD,GACvC,OAAOjC,KAAKgC,aAAaC,IAO7BnC,EAAQV,UAAU+C,aAAe,WAC7B,OAAOnC,KAAKG,WAAa,IAO7BL,EAAQV,UAAUgD,aAAe,WAC7B,OAAOpC,KAAKmC,gBAOhBrC,EAAQV,UAAUiD,eAAiB,WAC/B,IAAIpC,EAAUD,KAAKO,aAAc+B,EAAWtC,KAAKuC,gBAEjD,MAAO,CAAC,IAAKtC,EADbqC,EAAW,EAAa,IAAMA,EAAW,GACT,IAAKtC,KAAKoC,eAAgB,KAAMnC,EAAS,KAAK0B,KAAK,KASvF7B,EAAQV,UAAUmD,cAAgB,WAC9B,IAAKvC,KAAKE,MACN,MAAO,GACX,IAAIA,EAAQF,KAAKW,WAAY6B,EAAW,GACxC,IAAK,IAAIC,KAAQvC,EACTA,EAAMwC,eAAeD,IACrBD,EAASd,KAAKe,EAAO,KAAOvC,EAAMuC,GAAQ,KAGlD,OAAOD,EAASb,KAAK,MAElB7B,EA5NA,GC7CX,IAAI6C,EAAkC,WAKlC,SAASA,EAAiB5C,QACV,IAARA,IAAkBA,EAAM,IAK5BC,KAAK4C,WAAY,EAKjB5C,KAAK6C,SAAW,GAKhB7C,KAAK8C,UAAY,GACjB9C,KAAK4C,UAAY7C,EAAI6C,YAAa,EAClC5C,KAAK6C,SAAW9C,EAAI8C,UAAY,GAChC7C,KAAK8C,UAAY/C,EAAI+C,WAAa,GAqHtC,OA3GAH,EAAiBvD,UAAU2D,MAAQ,SAAUC,GACzC,OAAO,IAAIlD,EAAQ,CACfG,QAAS,IACTC,MAAOF,KAAKiD,YAAYD,GACxB3C,UAAWL,KAAKkD,kBAAkBF,EAAMG,oBAYhDR,EAAiBvD,UAAU6D,YAAc,SAAUD,GAC/C,IAAI9C,EAAQ,CACRkD,KAAQJ,EAAMK,iBAEdpC,EAAWjB,KAAKsD,eAAeN,GAanC,OAZI/B,IACAf,EAAa,MAAIe,GAEjBjB,KAAK4C,YACL1C,EAAc,OAAI,SAClBA,EAAW,IAAI,uBAEfF,KAAK6C,UACD7C,KAAK6C,SAAStD,QAAUS,KAAK6C,SAAStD,OAASyD,EAAMG,gBAAgB5D,SACrEW,EAAa,MAAI8C,EAAMK,iBAGxBnD,GAsBXyC,EAAiBvD,UAAUkE,eAAiB,SAAUN,GAClD,IAAIF,EAAY9C,KAAK8C,UACrB,GAAKA,EAGA,CAED,IADA,IAAIS,EAAgB,CAACT,GAAYU,EAAmBR,EAAMS,sBACjDpE,EAAI,EAAGC,EAAMkE,EAAiBjE,OAAQF,EAAIC,EAAKD,IACpDkE,EAAc7B,KAAKoB,EAAY,IAAMU,EAAiBnE,IAE1D,OAAOkE,EAAc5B,KAAK,KAP1B,MAAO,IAmBfgB,EAAiBvD,UAAU8D,kBAAoB,SAAUQ,GAErD,OADAA,EAAa1D,KAAK2D,WAAWD,IAcjCf,EAAiBvD,UAAUuE,WAAa,SAAUD,GAC9C,IAAIb,EAAW7C,KAAK6C,SACpB,IAAKA,IAAaA,EAAStD,OACvB,OAAOmE,EACX,ICvJ6CE,EHiB5BC,EAAKC,EAAaF,EACnCG,EEqIIC,EAAiBnB,EAAStD,OAAQ0E,EAAmBpB,EAASqB,SAClE,MAAyB,UAArBD,EErJL,SAAuBE,EAAKL,EAAaF,GAC5C,IAAIQ,EACAL,EAIAK,EAHiB,MAAjBR,GACAA,EAAgB,WAChBG,EAAiB,EACa,IAG9BA,EAAiBH,EAAcrE,OACDqE,EAAcrE,QAEhD,IA8BI8E,EAAW,SAAUC,GACrB,IAAIH,EAAM,GAgBV,OAfIG,EAAOC,QAAUD,EAAOE,OACxBL,GAAOG,EAAOC,OAAS,OAEvBD,EAAOE,OACPL,GAAOG,EAAOE,MAEdF,EAAOG,OACPN,GAAO,IAAMG,EAAOG,MAEpBH,EAAOI,QACPP,GAAO,IAAMG,EAAOI,OAEpBJ,EAAOK,WACPR,GAAO,IAAMG,EAAOK,UAEjBR,GAEPS,EAAe,SAAUC,EAASC,GAClC,IAAIC,EAA+BD,EAA2B,EAAGE,EAAcC,KAAKC,KAAKH,GAA+BI,GAAc,EAAKF,KAAKG,MAAML,GAA+BM,EAAM,GAI3L,OAHIF,EAAY,IACZE,EAAMR,EAAQS,OAAOH,IAElBN,EAAQS,OAAO,EAAGN,GAAepB,EAAgByB,GAE5D,GAAIlB,EAAI5E,QAAUuE,EACd,OAAOK,EAEX,IA1DQG,EACAiB,EACAvC,EAwDJwC,EAAkB1B,EAAcC,EAChCO,GA3DIA,EAAS,IAETtB,GADAuC,EA0DepB,GAzDAnB,MAAM,sBAErBsB,EAAOC,OAASvB,EAAM,GACtBuC,EAASA,EAAOD,OAAOtC,EAAM,GAAGzD,UAEpCyD,EAAQuC,EAAOvC,MAAM,6BAEjBsB,EAAOE,KAAOxB,EAAM,GACpBuC,EAASA,EAAOD,OAAOtC,EAAM,GAAGzD,UAEpCyD,EAAQuC,EAAOvC,MAAM,4BAEjBsB,EAAOG,KAAOzB,EAAM,GACpBuC,EAASA,EAAOD,OAAOtC,EAAM,GAAGzD,UAEpCyD,EAAQuC,EAAOvC,MAAM,yBAEjBsB,EAAOI,MAAQ1B,EAAM,GACrBuC,EAASA,EAAOD,OAAOtC,EAAM,GAAGzD,UAEpCyD,EAAQuC,EAAOvC,MAAM,gBAEjBsB,EAAOK,SAAW3B,EAAM,IAGrBsB,GAkCX,GAAIA,EAAOI,MAAO,CACd,IAAIe,EAAanB,EAAOI,MAAM1B,MAAM,4BAChCyC,IAEAnB,EAAOI,MAAQJ,EAAOI,MAAMY,OAAO,EAAGG,EAAW,GAAGlG,QACpD4E,EAAME,EAASC,IAGvB,GAAIH,EAAI5E,QAAUuE,EACd,OAAOK,EAMX,GAJIG,EAAOE,OACPF,EAAOE,KAAOF,EAAOE,KAAKkB,QAAQ,SAAU,IAC5CvB,EAAME,EAASC,IAEfH,EAAI5E,QAAUuE,EACd,OAAOK,EAGX,IAAIN,EAAM,GAIV,GAHIS,EAAOE,OACPX,GAAOS,EAAOE,MAEdX,EAAItE,QAAUiG,EACd,OAAIlB,EAAOE,KAAKjF,QAAUuE,GACdQ,EAAOE,KAAKc,OAAO,EAAIxB,EAAcC,GAAmBH,GAAe0B,OAAO,EAAGE,EAAkBpB,GAExGQ,EAAaf,EAAK2B,GAAiBF,OAAO,EAAGE,EAAkBpB,GAE1E,IAAIuB,EAAe,GAOnB,GANIrB,EAAOG,OACPkB,GAAgB,IAAMrB,EAAOG,MAE7BH,EAAOI,QACPiB,GAAgB,IAAMrB,EAAOI,OAE7BiB,EACA,CAAA,GAAmCH,IAA9B3B,EAAM8B,GAAcpG,OACrB,OAAKsE,EAAM8B,GAAcpG,QAAUuE,GACvBD,EAAM8B,GAAcL,OAAO,EAAGxB,IAGlCD,EAAMe,EAAae,EADIH,EAAkB3B,EAAItE,SACe+F,OAAO,EAAGE,EAAkBpB,GAGhGP,GAAO8B,EAGf,GAAIrB,EAAOK,SAAU,CACjB,IAAIA,EAAW,IAAML,EAAOK,SAC5B,GAA+Ba,IAA1B3B,EAAMc,GAAUpF,OACjB,OAAKsE,EAAMc,GAAUpF,QAAUuE,GACnBD,EAAMc,GAAUW,OAAO,EAAGxB,IAG9BD,EAAMe,EAAaD,EADKa,EAAkB3B,EAAItE,SACW+F,OAAO,EAAGE,EAAkBpB,GAG7FP,GAAOc,EAGf,GAAIL,EAAOC,QAAUD,EAAOE,KAAM,CAC9B,IAAID,EAASD,EAAOC,OAAS,MAC7B,IAAKV,EAAMU,GAAQhF,OAASiG,EACxB,OAAQjB,EAASV,GAAKyB,OAAO,EAAGxB,GAGxC,GAAID,EAAItE,QAAUuE,EACd,OAAOD,EAEX,IAAIwB,EAAM,GAIV,OAHsB,EAAlBG,IACAH,EAAMxB,EAAIyB,QAAS,EAAKL,KAAKG,MAAMI,EAAkB,MAEjD3B,EAAIyB,OAAO,EAAGL,KAAKC,KAAKM,EAAkB,IAAM5B,EAAgByB,GAAKC,OAAO,EAAGE,EAAkBpB,GFE1FwB,CAAclC,EAAYM,GAEP,WAArBC,EGzJV,SAAwBE,EAAKL,EAAaF,GAC7C,GAAIO,EAAI5E,QAAUuE,EACd,OAAOK,EAEX,IAAIC,EAWAoB,EAAkB1B,GATD,MAAjBF,GACAA,EAAgB,WAChBQ,EAA8B,EACb,IAGjBA,EAA8BR,EAAcrE,OAC3BqE,EAAcrE,SAG/B8F,EAAM,GAIV,OAHsB,EAAlBG,IACAH,EAAMlB,EAAImB,QAAS,EAAKL,KAAKG,MAAMI,EAAkB,MAEjDrB,EAAImB,OAAO,EAAGL,KAAKC,KAAKM,EAAkB,IAAM5B,EAAgByB,GAAKC,OAAO,EAAGE,EAAkBpB,GHsI1FyB,CAAenC,EAAYM,IF3IhBF,EE8IaE,EF9IAJ,EGhBEA,GHgBpBC,EE8IMH,GF5InBnE,OAASuE,IAGTC,EAFiB,MAAjBH,GACAA,EAAgB,WACC,GAGAA,EAAcrE,OAEnCsE,EAAMA,EAAIiC,UAAU,EAAGhC,EAAcC,GAAkBH,GAEpDC,IEqIAlB,EA7IS,GIChBoD,EAAuB,WAOvB,SAASA,EAAMhG,GAOXC,KAAKgG,qBAAuB,KAM5BhG,KAAKiG,YAAc,GAMnBjG,KAAKkG,OAAS,EACdlG,KAAKmG,WAAapG,EAAIoG,WACtBnG,KAAKiG,YAAclG,EAAIkG,YACvBjG,KAAKkG,OAASnG,EAAImG,OA0FtB,OAnFAH,EAAM3G,UAAUgH,eAAiB,WAC7B,OAAOpG,KAAKiG,aAchBF,EAAM3G,UAAUiH,UAAY,SAAUH,GAClClG,KAAKkG,OAASA,GAQlBH,EAAM3G,UAAUkH,UAAY,WACxB,OAAOtG,KAAKkG,QAuBhBH,EAAM3G,UAAUqE,oBAAsB,WAClC,MAAO,CAACzD,KAAKuG,YA+BjBR,EAAM3G,UAAUoH,SAAW,WACvB,OAAOxG,KAAKmG,WAAWpD,MAAM/C,OAE1B+F,EAvHF,GCfLU,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgB3F,OAAO8F,gBAClB,CAAEC,UAAW,cAAgB1H,OAAS,SAAUuH,EAAGC,GAAKD,EAAEG,UAAYF,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIG,KAAKH,EAAOA,EAAEjE,eAAeoE,KAAIJ,EAAEI,GAAKH,EAAEG,MACpDJ,EAAGC,IAGrB,SAASI,EAAUL,EAAGC,GAEzB,SAASK,IAAOhH,KAAKiH,YAAcP,EADnCD,EAAcC,EAAGC,GAEjBD,EAAEtH,UAAkB,OAANuH,EAAa7F,OAAOoG,OAAOP,IAAMK,EAAG5H,UAAYuH,EAAEvH,UAAW,IAAI4H,GAG5E,IAAIG,EAAW,WAQlB,OAPAA,EAAWrG,OAAOC,QAAU,SAAkBqG,GAC1C,IAAK,IAAIC,EAAGhI,EAAI,EAAGiI,EAAIC,UAAUhI,OAAQF,EAAIiI,EAAGjI,IAE5C,IAAK,IAAIyH,KADTO,EAAIE,UAAUlI,GACOyB,OAAO1B,UAAUsD,eAAe8E,KAAKH,EAAGP,KAAIM,EAAEN,GAAKO,EAAEP,IAE9E,OAAOM,IAEKK,MAAMzH,KAAMuH,YC3B5BG,EAA4B,SAAUC,GAOtC,SAASD,EAAW3H,GAChB,IAAI6H,EAAQD,EAAOH,KAAKxH,KAAMD,IAAQC,KAQtC,OAFA4H,EAAMC,MAAQ,GACdD,EAAMC,MAAQ9H,EAAI8H,MACXD,EAmCX,OAlDAE,EAAkBJ,EAAYC,GAuB9BD,EAAWtI,UAAUmH,QAAU,WAC3B,MAAO,SAOXmB,EAAWtI,UAAU2I,SAAW,WAC5B,OAAO/H,KAAK6H,OAOhBH,EAAWtI,UAAUiE,cAAgB,WACjC,MAAO,UAAYrD,KAAK6H,OAO5BH,EAAWtI,UAAU+D,cAAgB,WACjC,OAAOnD,KAAK6H,OAETH,EAnDG,CAoDZ3B,GClDEiC,EAA8B,SAAUL,GAOxC,SAASK,EAAajI,GAClB,IAAI6H,EAAQD,EAAOH,KAAKxH,KAAMD,IAAQC,KAgBtC,OATA4H,EAAMK,YAAc,GAMpBL,EAAMM,QAAU,GAChBN,EAAMK,YAAclI,EAAIkI,YACxBL,EAAMM,QAAUnI,EAAImI,QACbN,EAsDX,OA7EAE,EAAkBE,EAAcL,GA+BhCK,EAAa5I,UAAUmH,QAAU,WAC7B,MAAO,WAQXyB,EAAa5I,UAAU+I,eAAiB,WACpC,OAAOnI,KAAKiI,aAOhBD,EAAa5I,UAAUgJ,WAAa,WAChC,OAAOpI,KAAKkI,SAOhBF,EAAa5I,UAAUiE,cAAgB,WACnC,IAAI4E,EAAcjI,KAAKiI,YAAaC,EAAUlI,KAAKkI,QACnD,OAAQD,GACJ,IAAK,UACD,MAAO,+BAAiCC,EAC5C,IAAK,WACD,MAAO,oCAAsCA,EACjD,IAAK,YACD,MAAO,sCAAwCA,EACnD,QACI,MAAM,IAAIrI,MAAM,6CAA+CoI,KAQ3ED,EAAa5I,UAAU+D,cAAgB,WACnC,MAAO,IAAMnD,KAAKkI,SAEfF,EA9EK,CA+EdjC,GCjFEsC,EAA8B,SAAUV,GAOxC,SAASU,EAAatI,GAClB,IAAI6H,EAAQD,EAAOH,KAAKxH,KAAMD,IAAQC,KAgBtC,OATA4H,EAAMK,YAAc,UAMpBL,EAAMU,QAAU,GAChBV,EAAMU,QAAUvI,EAAIuI,QACpBV,EAAMK,YAAclI,EAAIkI,YACjBL,EAmEX,OA1FAE,EAAkBO,EAAcV,GA+BhCU,EAAajJ,UAAUmH,QAAU,WAC7B,MAAO,WAOX8B,EAAajJ,UAAUmJ,WAAa,WAChC,OAAOvI,KAAKsI,SAQhBD,EAAajJ,UAAU+I,eAAiB,WACpC,OAAOnI,KAAKiI,aAOhBI,EAAajJ,UAAUiE,cAAgB,WACnC,OAAQrD,KAAKiI,aACT,IAAK,UACD,MAAO,uBAAyBjI,KAAKsI,QACzC,IAAK,YACD,MAAO,yBAA2BtI,KAAKsI,QAC3C,IAAK,aACD,MAAO,0BAA4BtI,KAAKsI,QAC5C,QACI,MAAM,IAAIzI,MAAM,6CAA+CG,KAAKiI,eAQhFI,EAAajJ,UAAU+D,cAAgB,WACnC,MAAO,IAAMnD,KAAKsI,SAStBD,EAAajJ,UAAUqE,oBAAsB,WACzC,IAAID,EAAmBmE,EAAOvI,UAAUqE,oBAAoB+D,KAAKxH,MAAOiI,EAAcjI,KAAKmI,iBAI3F,OAHIF,GACAzE,EAAiB9B,KAAKuG,GAEnBzE,GAEJ6E,EA3FK,CA4FdtC,GC1FEyC,EAA4B,SAAUb,GAOtC,SAASa,EAAWzI,GAChB,IAAI6H,EAAQD,EAAOH,KAAKxH,KAAMD,IAAQC,KAsBtC,OAbA4H,EAAMa,OAAS,GAUfb,EAAMc,UAAW,EACjBd,EAAMa,OAAS1I,EAAI0I,OACnBb,EAAMc,SAAW3I,EAAI2I,SACdd,EAiDX,OA9EAE,EAAkBU,EAAYb,GAqC9Ba,EAAWpJ,UAAUmH,QAAU,WAC3B,MAAO,SAUXiC,EAAWpJ,UAAUuJ,eAAiB,WAClC,OAAO3I,KAAKyI,QAUhBD,EAAWpJ,UAAUwJ,UAAY,WAC7B,OAAO5I,KAAK2I,kBAOhBH,EAAWpJ,UAAUiE,cAAgB,WACjC,MAAO,QAAUrD,KAAK0I,SAAW,IAAM,IAAM1I,KAAKyI,QAOtDD,EAAWpJ,UAAU+D,cAAgB,WACjC,OAAOnD,KAAKiG,aAETuC,EA/EG,CAgFZzC,GClFE8C,EAA0B,SAAUlB,GAOpC,SAASkB,EAAS9I,GACd,IAAI6H,EAAQD,EAAOH,KAAKxH,KAAMD,IAAQC,KAqFtC,OA/EA4H,EAAMzD,IAAM,GASZyD,EAAMkB,aAAe,SAOrBlB,EAAMmB,kBAAmB,EAQzBnB,EAAMoB,uBAAwB,EAM9BpB,EAAMqB,YAAc,CAAE1E,QAAQ,EAAM2E,KAAK,GAKzCtB,EAAMuB,oBAAqB,EAK3BvB,EAAMwB,uBAAwB,EAQ9BxB,EAAMyB,kBAAoB,mBAO1BzB,EAAM0B,eAAiB,2BAQvB1B,EAAM2B,sBAAwB,QAQ9B3B,EAAM4B,mBAAoB,EAC1B5B,EAAMkB,aAAe/I,EAAI+I,aACzBlB,EAAMzD,IAAMpE,EAAIoE,IAChByD,EAAMmB,iBAAmBhJ,EAAIgJ,iBAC7BnB,EAAMoB,sBAAwBjJ,EAAIiJ,sBAClCpB,EAAMqB,YAAclJ,EAAIkJ,YACxBrB,EAAMuB,mBAAqBpJ,EAAIoJ,mBAC/BvB,EAAMwB,sBAAwBrJ,EAAIqJ,sBAC3BxB,EAwJX,OApPAE,EAAkBe,EAAUlB,GAoG5BkB,EAASzJ,UAAUmH,QAAU,WACzB,MAAO,OAaXsC,EAASzJ,UAAUqK,gBAAkB,WACjC,OAAOzJ,KAAK8I,cAQhBD,EAASzJ,UAAUsK,OAAS,WACxB,IAAIvF,EAAMnE,KAAKmE,IAMf,OAJKnE,KAAKgJ,uBAA0BhJ,KAAK+I,kBAAqB/I,KAAKwJ,oBAC/DrF,EAAMnE,KAAKmE,IAAM,UAAYA,EAC7BnE,KAAKwJ,mBAAoB,GAEtBrF,GAOX0E,EAASzJ,UAAUiE,cAAgB,WAE/B,OADUrD,KAAK0J,SACJhE,QAAQ,SAAU,MAOjCmD,EAASzJ,UAAU+D,cAAgB,WAC/B,IAAIO,EAAa1D,KAAKoG,iBAiBtB,OAhBIpG,KAAKgJ,wBAELtF,EAAa1D,KAAK2J,4BAA4BjG,IAE9C1D,KAAKiJ,YAAY1E,SACjBb,EAAa1D,KAAK4J,kBAAkBlG,IAEpC1D,KAAKiJ,YAAYC,MACjBxF,EAAa1D,KAAK6J,eAAenG,IAEjC1D,KAAKmJ,qBACLzF,EAAa1D,KAAK8J,oBAAoBpG,IAEtC1D,KAAKoJ,wBACL1F,EAAa1D,KAAK+J,sBAAsBrG,IAErCA,GAaXmF,EAASzJ,UAAUwK,kBAAoB,SAAUzF,GAC7C,OAAOA,EAAIuB,QAAQ1F,KAAKqJ,kBAAmB,KAU/CR,EAASzJ,UAAUyK,eAAiB,SAAU1F,GAC1C,OAAOA,EAAIuB,QAAQ1F,KAAKsJ,eAAgB,OAU5CT,EAASzJ,UAAUuK,4BAA8B,SAAUK,GACvD,OAAOA,EAAKtE,QAAQ1F,KAAKuJ,sBAAuB,KAUpDV,EAASzJ,UAAU0K,oBAAsB,SAAUpG,GAI/C,MAHiD,MAA7CA,EAAWuG,OAAOvG,EAAWnE,OAAS,KACtCmE,EAAaA,EAAWwG,MAAM,GAAI,IAE/BxG,GAYXmF,EAASzJ,UAAU2K,sBAAwB,SAAUrG,GAIjD,IAAIyG,EAA+BzG,EAC9BgC,QAAQ,QAAS,UACjBA,QAAQ,QAAS,SACjBA,QAAQ,QAAS,SACjBA,QAAQ,QAAS,QACjBA,QAAQ,QAAS,QACtB,IAEI,OAAO0E,mBAAmBD,GAE9B,MAAOE,GACH,OAAOF,IAGRtB,EArPC,CAsPV9C,GCtPEuE,EAMA,SAAiBvK,GAObC,KAAKgG,qBAAuB,KAC5BhG,KAAKmG,WAAapG,EAAIoG,YCdnBoE,EAAW,WAIXC,EAAU,QAIVC,EAAe,KAIfC,EAAU,OAKVC,EAAiB,kBAyBjBC,EAAgB,4sIACtBC,OAyCMC,EAAwBF,EApCb,4eACjBC,OAwBiB,0hEACjBA,OAkCME,EAAoB,0dAC1BF,OASMG,EAAuBF,EAAwBC,EAU/CE,EAA+BH,EAAwBC,EAE9DG,EAAQ,OAASH,EAAoB,iBAAmBA,EAAoB,SAE5EI,EAAiB,IAAMF,EAA+B,QAAUA,EAA+B,cAAgBA,EAA+B,MAC9IG,EAAoB,SAAUC,GAC9B,MAAO,OAASF,EAAiB,OAASE,GAMnCC,EAAmB,SAAUD,GACpC,MAAO,MAAQD,EAAkBC,GAAS,SAAWD,EAAkBC,EAAQ,GAAK,YAAcH,EAAQ,KAWnGK,EAAsB,IAAIC,OAAO,IAAMP,EAA+B,KChKtEQ,EAAW,uuVCclBC,EAA8B,SAAU/D,GAExC,SAAS+D,IACL,IAAI9D,EAAmB,OAAXD,GAAmBA,EAAOF,MAAMzH,KAAMuH,YAAcvH,KAWhE,OANA4H,EAAM+D,mBAAqB,IAAIH,OAAO,IAAMP,EAA+B,wBAK3ErD,EAAMgE,eAAiB,IAAIJ,OAAO,IAAMC,EAASZ,OAAS,KACnDjD,EAoQX,OAjRAE,EAAkB4D,EAAc/D,GAkBhC+D,EAAatM,UAAUyM,aAAe,SAAU7B,GAgB5C,IAfA,IAAI7D,EAAanG,KAAKmG,WAAYwF,EAAqB3L,KAAK2L,mBAAoBC,EAAiB5L,KAAK4L,eAAgBE,EAAU,GAAIxM,EAAM0K,EAAKzK,OAAQwM,EAAsB,IAAIC,EAE7KC,EAAoB,CACpBC,EAAK,IACLC,EAAK,IACL9M,EAAK,IACL+M,EAAK,IACLhF,EAAK,IACLiF,EAAK,KAELC,EAAU,EAAGC,EAAQ,EAAuBC,EAAoBT,EAK7DO,EAAUhN,GAAK,CAClB,IAAImN,EAAOzC,EAAKC,OAAOqC,GAKvB,OAAQC,GACJ,KAAK,EACDG,EAAqBD,GACrB,MACJ,KAAK,EACDE,EAAY3C,EAAKC,OAAOqC,EAAU,GAAIG,GACtC,MACJ,KAAK,EACDG,EAAeH,GACf,MACJ,KAAK,EACDI,EAAkBJ,GAClB,MACJ,KAAK,EACDK,EAAYL,GACZ,MACJ,KAAK,EACDM,EAAgBN,GAChB,MACJ,KAAK,EACDO,EAAkBP,GAClB,MACJ,KAAK,EACDQ,EAAeR,GACf,MACJ,QACI9M,EAAwB4M,GAMhCD,IAMJ,OAHAY,IAGOpB,EAEP,SAASY,EAAqBD,GACb,MAATA,EACAU,EAAgB,GAEXxB,EAAmByB,KAAKX,IAC7BU,IAOR,SAASR,EAAYU,EAAUZ,GACV,MAAbY,EAEI1B,EAAmByB,KAAKX,IACxBF,EAAQ,EACRC,EAAoB,IAAIR,EAAkBsB,EAAiB,GAAId,EAAmB,CAAEe,iBAAiB,MAMrGC,IAGCvB,EAAkBoB,KAAcZ,IAIhCd,EAAmByB,KAAKX,GAG7BF,EAAQ,EAEM,MAATE,EAGLF,EAAQ,EAEM,MAATE,EAGLF,EAAQ,EAIRiB,KAKR,SAASZ,EAAeH,GACP,MAATA,EACAF,EAAQ,EAEM,MAATE,EACLF,EAAQ,EAEHZ,EAAmByB,KAAKX,IAK7Be,IAIR,SAASX,EAAkBJ,GACV,MAATA,EAGAe,IAEc,MAATf,EAGLe,IAEK7B,EAAmByB,KAAKX,GAC7BF,EAAQ,EAIRiB,IAGR,SAASV,EAAYL,GACblB,EAAoB6B,KAAKX,GACzBF,EAAQ,EAIRiB,IAGR,SAAST,EAAgBN,GACR,MAATA,EACAF,EAAQ,EAEM,MAATE,EACLF,EAAQ,EAEHhB,EAAoB6B,KAAKX,IAM9BS,IAGR,SAASF,EAAkBP,GACV,MAATA,GAAyB,MAATA,EAEhBS,IAEK3B,EAAoB6B,KAAKX,GAC9BF,EAAQ,EAIRW,IAGR,SAASD,EAAeR,GACP,MAATA,GAAyB,MAATA,EAEhBS,IAEK3B,EAAoB6B,KAAKX,IAC9BF,EAAQ,EAKRC,EAAoB,IAAIR,EAAkBsB,EAAiB,GAAId,EAAmB,CAAEiB,cAAc,MAIlGP,IAGR,SAASC,EAAgBO,QACJ,IAAbA,IAAuBA,EAAW,GACtCnB,EAAQmB,EACRlB,EAAoB,IAAIR,EAAkB,CAAElK,IAAKwK,IAErD,SAASkB,IACLjB,EAAQ,EACRC,EAAoBT,EAMxB,SAASmB,IACL,GAAIV,EAAkBiB,aAAc,CAChC,IAAIxH,EAAc+D,EAAKE,MAAMsC,EAAkB1K,IAAKwK,GAKhD,QAAQc,KAAKnH,KACbA,EAAcA,EAAYiE,MAAM,GAAI,IAExC,IAAIyD,EAAenB,EAAkBe,gBAC/BtH,EAAYiE,MAAM,UAAU3K,QAC5B0G,EAmBF2H,GAjBsBD,EAgBSpM,MAAM,KAAKsM,OAAS,IACVC,cAC5BlC,EAAewB,KAAKQ,IAjBjC9B,EAAQpK,KAAK,IAAIgG,EAAW,CACxBvB,WAAYA,EACZF,YAAaA,EACbC,OAAQsG,EAAkB1K,IAC1B+F,MAAO8F,KAUnB,IAEQC,EARRJ,MAcD9B,EAlRK,CAmRdpB,GAEE0B,EACA,SAA2BjM,QACX,IAARA,IAAkBA,EAAM,IAC5BC,KAAK8B,SAAkBiM,IAAZhO,EAAI+B,IAAoB/B,EAAI+B,KAAO,EAC9C9B,KAAKuN,kBAAoBxN,EAAIwN,gBAC7BvN,KAAKyN,eAAiB1N,EAAI0N,cC3R9BO,EAAmC,WACnC,SAASA,KA6IT,OAlHAA,EAAkBC,QAAU,SAAUC,EAAUnF,GAC5C,QAAKA,IAAqB/I,KAAKmO,iBAAiBpF,IAC5C/I,KAAKoO,iCAAiCF,EAAUnF,IAC/C/I,KAAKqO,sCAAsCH,EAAUnF,KACjD/I,KAAKsO,iBAAiBJ,IAC3BlO,KAAKuO,qBAAqBL,KAKlCF,EAAkBM,iBAAmB,SAAUE,GAC3C,IAAIC,EAAW,IAAIjD,OAAOxL,KAAK0O,qBAAqB7D,OAAS7K,KAAK2O,QAAQ9D,QAE1E,OAAqB,OADL2D,EAAexL,MAAMyL,IAGzCT,EAAkBO,qBAAuB,SAAUL,GAC/C,IAAIU,EAAoBV,EAIxB,OAHIlO,KAAK0O,qBAAqBtB,KAAKc,KAC/BU,EAAoBV,EAAS3M,MAAM,OAAO,KAEU,EAAjDqN,EAAkBrN,MAAM,KAAK,GAAGvC,QAAQ,OAWnDgP,EAAkBG,iBAAmB,SAAUK,GAC3C,IAAIK,EAAoBL,EAAexL,MAAMhD,KAAK8O,gBAAiBC,EAAYF,GAAqBA,EAAkB,GAAGf,cACzH,MAAsB,gBAAdiB,GAA6C,cAAdA,GAuB3Cf,EAAkBI,iCAAmC,SAAUF,EAAUnF,GACrE,SAAUmF,GAAcnF,GAAqB/I,KAAK0O,qBAAqBtB,KAAKrE,KAAiD,IAA3BmF,EAASlP,QAAQ,OAmBvHgP,EAAkBK,sCAAwC,SAAUH,EAAUnF,GAC1E,SAAImF,IAAYnF,KACJ/I,KAAKgP,8BAA8B5B,KAAKc,IAYxDF,EAAkBU,qBAAuB,gCASzCV,EAAkBc,eAAiB,4BAOnCd,EAAkBgB,8BAAgC,IAAIxD,OAAO,aAAeZ,EAAgB,KAO5FoD,EAAkBW,QAAU,2FACrBX,EA9IU,GCDjBiB,EAA4B,SAAUtH,GAOtC,SAASsH,EAAWlP,GAChB,IAwDImP,EAxDAtH,EAAQD,EAAOH,KAAKxH,KAAMD,IAAQC,KAkGtC,OA5FA4H,EAAMqB,YAAc,CAAE1E,QAAQ,EAAM2E,KAAK,GAKzCtB,EAAMuB,oBAAqB,EAK3BvB,EAAMwB,uBAAwB,EAmC9BxB,EAAMuH,cAKFD,EAAiB,IAAI1D,OAAO,YAAcP,EAA+B,qCAA6CA,EAA+B,gCAC9I,IAAIO,OAAO,CACd,MACA,IAPc,4FAQFX,OACZS,EAAiB,GACjB,IACA,IACA,IACA,QAZO,YAaET,OACTS,EAAiB,GACjB,IACA,IACA,IACA,QACAA,EAAiB,IAAM,MACvBG,EAASZ,OACT,QAAUG,EAAuB,KACjC,IACA,IACA,eACA,MAAQkE,EAAerE,OAAS,MAClClJ,KAAK,IAAK,OAehBiG,EAAMwH,eAAiB,IAAI5D,OAAO,IAAMP,EAA+B,KACvErD,EAAMqB,YAAclJ,EAAIkJ,YACxBrB,EAAMuB,mBAAqBpJ,EAAIoJ,mBAC/BvB,EAAMwB,sBAAwBrJ,EAAIqJ,sBAC3BxB,EAiLX,OA1RAE,EAAkBmH,EAAYtH,GA8G9BsH,EAAW7P,UAAUyM,aAAe,SAAU7B,GAsE1C,IArEA,IAAoNhH,EAAhNmM,EAAenP,KAAKmP,aAAclG,EAAcjJ,KAAKiJ,YAAaE,EAAqBnJ,KAAKmJ,mBAAoBC,EAAwBpJ,KAAKoJ,sBAAuBjD,EAAanG,KAAKmG,WAAY2F,EAAU,GAC5MuD,EAAU,WACV,IAAIC,EAAWtM,EAAM,GAAIuM,EAAiBvM,EAAM,GAAIwM,EAAcxM,EAAM,GAAIyM,EAA2BzM,EAAM,GAE7G0M,EAA2B1M,EAAM,GAAIkD,EAASlD,EAAM2M,MAAO3G,EAAwByG,GAA4BC,EAA0BrC,EAAWrD,EAAKC,OAAO/D,EAAS,GACzK,IAAK8H,EAAkBC,QAAQqB,EAAUC,GACrC,MAAO,WAIX,GAAa,EAATrJ,GAA2B,MAAbmH,EACd,MAAO,WAMX,GAAa,EAATnH,GAAc8C,GAAyB4G,EAAOR,eAAehC,KAAKC,GAClE,MAAO,WAWX,GANI,MAAMD,KAAKkC,KACXA,EAAWA,EAAShK,OAAO,EAAGgK,EAAS/P,OAAS,IAKhDqQ,EAAOC,+BAA+BP,GACtCA,EAAWA,EAAShK,OAAO,EAAGgK,EAAS/P,OAAS,OAE/C,CAED,IAAIuQ,EAAMF,EAAOG,4BAA4BT,EAAUC,IAC5C,EAAPO,IACAR,EAAWA,EAAShK,OAAO,EAAGwK,IAQtC,IAAIE,EAAoB,CAAC,UAAW,YAAYC,KAAK,SAAUC,GAAgB,QAASX,IAA4D,IAA1CA,EAAevQ,QAAQkR,KACjI,GAAIF,EAAmB,CAInB,IAAIG,EAAqBb,EAAStQ,QAAQgR,GAC1CV,EAAWA,EAAShK,OAAO6K,GAC3BZ,EAAiBA,EAAejK,OAAO6K,GACvCjK,GAAkBiK,EAEtB,IAAIrH,EAAeyG,EAAiB,SAAYC,EAAc,MAAQ,MAAQzG,IAAqBwG,EACnGzD,EAAQpK,KAAK,IAAImH,EAAS,CACtB1C,WAAYA,EACZF,YAAaqJ,EACbpJ,OAAQA,EACR4C,aAAcA,EACd3E,IAAKmL,EACLvG,iBAAkBA,EAClBC,wBAAyBA,EACzBC,YAAaA,EACbE,mBAAoBA,EACpBC,sBAAuBA,MAG3BwG,EAAS5P,KACgC,QAArCgD,EAAQmM,EAAaiB,KAAKpG,KAC9BqF,IAEJ,OAAOvD,GA4BXmD,EAAW7P,UAAUyQ,+BAAiC,SAAUP,GAC5D,IACIe,EADAC,EAAUhB,EAASrF,OAAOqF,EAAS/P,OAAS,GAEhD,GAAgB,MAAZ+Q,EACAD,EAAY,QAEX,CAAA,GAAgB,MAAZC,EAIL,OAAO,EAHPD,EAAY,IAShB,IADA,IAAIE,EAAgB,EACXlR,EAAI,EAAGC,EAAMgQ,EAAS/P,OAAS,EAAGF,EAAIC,EAAKD,IAAK,CACrD,IAAIoN,EAAO6C,EAASrF,OAAO5K,GACvBoN,IAAS4D,EACTE,IAEK9D,IAAS6D,IACdC,EAAgBtL,KAAKuL,IAAID,EAAgB,EAAG,IAQpD,OAAsB,IAAlBA,GAoBRtB,EAAW7P,UAAU2Q,4BAA8B,SAAU7B,EAAUqB,GACnE,IAAKrB,EACD,OAAQ,EAEZ,IAAIhI,EAAS,EACTqJ,IACArJ,EAASgI,EAASlP,QAAQ,KAC1BkP,EAAWA,EAAShE,MAAMhE,IAE9B,IACIuK,EADK,IAAIjF,OAAO,eAAmBP,EAA+B,OAASA,EAA+B,SAAWA,EAA+B,OAC3ImF,KAAKlC,GAClB,OAAY,OAARuC,GACQ,GAEZvK,GAAUuK,EAAI,GAAGlR,OACjB2O,EAAWA,EAAShE,MAAMuG,EAAI,GAAGlR,QAC7B,uBAAuB6N,KAAKc,GACrBhI,GAEH,IAEL+I,EA3RG,CA4RZ3E,GChSEoG,EAAgC,SAAU/I,GAO1C,SAAS+I,EAAe3Q,GACpB,IAAI6H,EAAQD,EAAOH,KAAKxH,KAAMD,IAAQC,KA4BtC,OArBA4H,EAAMK,YAAc,UASpBL,EAAMuH,aAAe,IAAI3D,OAAO,MAAQP,EAA+B,gBAAkBA,EAA+B,KAAM,KAU9HrD,EAAM+I,iBAAmB,IAAInF,OAAO,KAAOP,EAA+B,KAC1ErD,EAAMK,YAAclI,EAAIkI,YACjBL,EAyBX,OA5DAE,EAAkB4I,EAAgB/I,GAwClC+I,EAAetR,UAAUyM,aAAe,SAAU7B,GAE9C,IADA,IAA4JhH,EAAxJmM,EAAenP,KAAKmP,aAAcwB,EAAmB3Q,KAAK2Q,iBAAkB1I,EAAcjI,KAAKiI,YAAa9B,EAAanG,KAAKmG,WAAY2F,EAAU,GAC3G,QAArC9I,EAAQmM,EAAaiB,KAAKpG,KAAiB,CAC/C,IAAI9D,EAASlD,EAAM2M,MAAOtC,EAAWrD,EAAKC,OAAO/D,EAAS,GAI1D,GAAe,IAAXA,GAAgByK,EAAiBvD,KAAKC,GAAW,CACjD,IAAIpH,EAAcjD,EAAM,GAAIkF,EAAUlF,EAAM,GAAGkH,MAAM,GACrD4B,EAAQpK,KAAK,IAAIsG,EAAa,CAC1B7B,WAAYA,EACZF,YAAaA,EACbC,OAAQA,EACR+B,YAAaA,EACbC,QAASA,MAIrB,OAAO4D,GAEJ4E,EA7DO,CA8DhBpG,GC5DEsG,EAA8B,SAAUjJ,GAExC,SAASiJ,IACL,IAAIhJ,EAAmB,OAAXD,GAAmBA,EAAOF,MAAMzH,KAAMuH,YAAcvH,KAchE,OADA4H,EAAMuH,aAAe,wRACdvH,EA6BX,OA7CAE,EAAkB8I,EAAcjJ,GAuBhCiJ,EAAaxR,UAAUyM,aAAe,SAAU7B,GAE5C,IADA,IAAkFhH,EAA9EmM,EAAenP,KAAKmP,aAAchJ,EAAanG,KAAKmG,WAAY2F,EAAU,GACjC,QAArC9I,EAAQmM,EAAaiB,KAAKpG,KAAiB,CAE/C,IAAI/D,EAAcjD,EAAM,GAAI6N,EAAc5K,EAAYP,QAAQ,aAAc,IAC5EgD,KAAc1F,EAAM,KAAMA,EAAM,IAChC8N,EAAwB,GAAf9N,EAAM2M,MAAa,GAAK3F,EAAK1E,OAAOtC,EAAM2M,MAAQ,EAAG,GAAIoB,EAAQ/G,EAAK1E,OAAOtC,EAAM2M,MAAQ1J,EAAY1G,OAAQ,GAAIyR,GAAgBF,EAAO9N,MAAM,QAAU+N,EAAM/N,MAAM,MAC3KhD,KAAKiR,UAAUjO,EAAM,KAAOhD,KAAKiR,UAAUhL,IAAgB+K,GAC3DlF,EAAQpK,KAAK,IAAI8G,EAAW,CACxBrC,WAAYA,EACZF,YAAaA,EACbC,OAAQlD,EAAM2M,MACdlH,OAAQoI,EACRnI,SAAUA,KAItB,OAAOoD,GAEX8E,EAAaxR,UAAU6R,UAAY,SAAUjH,GACzC,MAAO,KAAKoD,KAAKpD,IAEd4G,EA9CK,CA+CdtG,GCjDE4G,EAAgC,SAAUvJ,GAO1C,SAASuJ,EAAenR,GACpB,IAAI6H,EAAQD,EAAOH,KAAKxH,KAAMD,IAAQC,KAiCtC,OAzBA4H,EAAMK,YAAc,UASpBL,EAAMuJ,eAAiB,CACnBC,QAAW,IAAI5F,OAAO,MAAQP,EAA+B,eAAiBA,EAA+B,KAAM,KACnHoG,UAAa,IAAI7F,OAAO,OAASP,EAA+B,eAAiBA,EAA+B,KAAM,KACtHqG,WAAc,IAAI9F,OAAO,QAAUP,EAA+B,gBAAkBA,EAA+B,KAAM,MAW7HrD,EAAM+I,iBAAmB,IAAInF,OAAO,KAAOP,EAA+B,KAC1ErD,EAAMK,YAAclI,EAAIkI,YACjBL,EA6BX,OArEAE,EAAkBoJ,EAAgBvJ,GA6ClCuJ,EAAe9R,UAAUyM,aAAe,SAAU7B,GAC9C,IAAgLhH,EAA5KiF,EAAcjI,KAAKiI,YAAakH,EAAenP,KAAKmR,eAAenR,KAAKiI,aAAc0I,EAAmB3Q,KAAK2Q,iBAAkBxK,EAAanG,KAAKmG,WAAY2F,EAAU,GAC5K,IAAKqD,EACD,OAAOrD,EAEX,KAA6C,QAArC9I,EAAQmM,EAAaiB,KAAKpG,KAAiB,CAC/C,IAAI9D,EAASlD,EAAM2M,MAAOtC,EAAWrD,EAAKC,OAAO/D,EAAS,GAI1D,GAAe,IAAXA,GAAgByK,EAAiBvD,KAAKC,GAAW,CACjD,IAAIpH,EAAcjD,EAAM,GAAG0C,QAAQ,QAAS,IAC5C4C,EAAUrC,EAAYiE,MAAM,GAC5B4B,EAAQpK,KAAK,IAAI2G,EAAa,CAC1BlC,WAAYA,EACZF,YAAaA,EACbC,OAAQA,EACR+B,YAAaA,EACbK,QAASA,MAIrB,OAAOwD,GAEJoF,EAtEO,CAuEhB5G,GCzBK,SAASiH,EAAUtP,EAAMuP,GAS5B,IARA,IA0hBQxH,EA1hBJyH,EAAYD,EAAGC,UAAWC,EAAaF,EAAGE,WAAYC,EAASH,EAAGG,OAAQC,EAAYJ,EAAGI,UAAWC,EAAYL,EAAGK,UACnHC,EAAe,IAAIC,EACnBzF,EAAU,EAAGhN,EAAM2C,EAAK1C,OAAQgN,EAAQ,EAAcyF,EAAiB,EAC3EC,EAAaH,EAKNxF,EAAUhN,GAAK,CAClB,IAAImN,EAAOxK,EAAKgI,OAAOqC,GAMvB,OAAQC,GACJ,KAAK,EACD2F,EAAUzF,GACV,MACJ,KAAK,EACD0F,EAAa1F,GACb,MACJ,KAAK,EACD2F,EAAgB3F,GAChB,MACJ,KAAK,EACD4F,EAAa5F,GACb,MACJ,KAAK,EACD6F,EAAyB7F,GACzB,MACJ,KAAK,EACD8F,EAAmB9F,GACnB,MACJ,KAAK,EACD+F,EAAwB/F,GACxB,MACJ,KAAK,EACDgG,EAA0BhG,GAC1B,MACJ,KAAK,EACDiG,EAAgCjG,GAChC,MACJ,KAAK,EACDkG,EAAgClG,GAChC,MACJ,KAAK,GACDmG,EAA4BnG,GAC5B,MACJ,KAAK,GACDoG,EAA+BpG,GAC/B,MACJ,KAAK,GACDqG,EAAyBrG,GACzB,MACJ,KAAK,GACDsG,EAA2BtG,GAC3B,MACJ,KAAK,GACDuG,EAAkBvG,GAClB,MACJ,KAAK,GACDwG,EAAsBxG,GACtB,MACJ,KAAK,GACDyG,EAAazG,GACb,MACJ,KAAK,GACD0G,EAAoB1G,GACpB,MACJ,KAAK,GACD2G,EAAgB3G,GAChB,MACJ,KAAK,GACD4G,EAAoB5G,GACpB,MACJ,KAAK,GACD6G,EAAa7G,GACb,MACJ,QACI9M,EAAwB4M,GAOhCD,IASJ,SAAS4F,EAAUzF,GACF,MAATA,GACA8G,IAKR,SAASpB,EAAa1F,GACL,MAATA,EACAF,EAAQ,GAEM,MAATE,GACLF,EAAQ,EACR0F,EAAa,IAAIF,EAAWzE,EAAiB,GAAI2E,EAAY,CAAEuB,WAAW,MAE5D,MAAT/G,EAEL8G,IAKAtB,EAHK1H,EAAS6C,KAAKX,IAEnBF,EAAQ,EACK,IAAIwF,EAAWzE,EAAiB,GAAI2E,EAAY,CAAEwB,WAAW,OAI1ElH,EAAQ,EACKuF,GAMrB,SAASO,EAAa5F,GACdhC,EAAa2C,KAAKX,IAClBwF,EAAa,IAAIF,EAAWzE,EAAiB,GAAI2E,EAAY,CAAEyB,KAAMC,OACrEpH,EAAQ,GAEM,MAATE,EAEL8G,IAEc,MAAT9G,GACLwF,EAAa,IAAIF,EAAWzE,EAAiB,GAAI2E,EAAY,CAAEyB,KAAMC,OACrEpH,EAAQ,IAEM,MAATE,GACLwF,EAAa,IAAIF,EAAWzE,EAAiB,GAAI2E,EAAY,CAAEyB,KAAMC,OACrEC,KAEMrJ,EAAS6C,KAAKX,IAAUjC,EAAQ4C,KAAKX,IAAkB,MAATA,GAGpDoH,IAQR,SAASzB,EAAgB3F,GACR,MAATA,EACAoH,IAEKtJ,EAAS6C,KAAKX,GACnBF,EAAQ,EAIRsH,IAIR,SAASvB,EAAyB7F,GAC1BhC,EAAa2C,KAAKX,KAGJ,MAATA,EACLF,EAAQ,GAEM,MAATE,EACLmH,IAEc,MAATnH,EAEL8G,IAEc,MAAT9G,GAAgB/B,EAAQ0C,KAAKX,IAAS9B,EAAeyC,KAAKX,GAI/DoH,IAIAtH,EAAQ,GAIhB,SAASgG,EAAmB9F,GACpBhC,EAAa2C,KAAKX,GAClBF,EAAQ,EAEM,MAATE,EACLF,EAAQ,GAEM,MAATE,EACLF,EAAQ,EAEM,MAATE,EACLmH,IAEc,MAATnH,EAEL8G,IAEK7I,EAAQ0C,KAAKX,IAIlBoH,IAOR,SAASrB,EAAwB/F,GACzBhC,EAAa2C,KAAKX,KAGJ,MAATA,EACLF,EAAQ,GAEM,MAATE,EACLF,EAAQ,EAEM,MAATE,EACLmH,IAEc,MAATnH,EAEL8G,IAEK7I,EAAQ0C,KAAKX,GAIlBoH,IAIAtH,EAAQ,GAIhB,SAASkG,EAA0BhG,GAC3BhC,EAAa2C,KAAKX,KAGJ,MAATA,EACLF,EAAQ,EAEM,MAATE,EACLF,EAAQ,EAEH,QAAQa,KAAKX,GAGlBoH,IAEc,MAATpH,EAEL8G,IAIAhH,EAAQ,IAIhB,SAASmG,EAAgCjG,GACxB,MAATA,IACAF,EAAQ,IAOhB,SAASoG,EAAgClG,GACxB,MAATA,IACAF,EAAQ,IAOhB,SAASqG,EAA4BnG,GAC7BhC,EAAa2C,KAAKX,GAClBF,EAAQ,EAEM,MAATE,EACLmH,IAEc,MAATnH,GAEL8G,IAOR,SAASV,EAA+BpG,GAChChC,EAAa2C,KAAKX,GAClBF,EAAQ,EAEM,MAATE,EACLF,EAAQ,GAEM,MAATE,EACLmH,IAEc,MAATnH,EAEL8G,KAMAhH,EAAQ,EAkOZD,KA3NJ,SAASwG,EAAyBrG,GACjB,MAATA,GACAwF,EAAa,IAAIF,EAAWzE,EAAiB,GAAI2E,EAAY,CAAEuB,WAAW,KAC1EI,KAGArH,EAAQ,EAKhB,SAASwG,EAA2BtG,GACA,OAA5BxK,EAAKqD,OAAOgH,EAAS,IACrBA,GAAW,EACX2F,EAAa,IAAIF,EAAWzE,EAAiB,GAAI2E,EAAY,CAAE6B,KAAM,aACrEvH,EAAQ,IAEuC,YAA1CtK,EAAKqD,OAAOgH,EAAS,GAAGyH,eAC7BzH,GAAW,EACX2F,EAAa,IAAIF,EAAWzE,EAAiB,GAAI2E,EAAY,CAAE6B,KAAM,aACrEvH,EAAQ,IAQRsH,IAKR,SAASb,EAAkBvG,GACV,MAATA,EAEAF,EAAQ,GAEM,MAATE,EAILoH,IAIAtH,EAAQ,GAKhB,SAAS0G,EAAsBxG,GACd,MAATA,EAEAF,EAAQ,GAEM,MAATE,EAILoH,IAIAtH,EAAQ,GAKhB,SAAS2G,EAAazG,GACL,MAATA,IACAF,EAAQ,IAShB,SAAS4G,EAAoB1G,GAErBF,EADS,MAATE,EACQ,GAIA,GAMhB,SAAS2G,EAAgB3G,GACR,MAATA,EACAmH,IAEc,MAATnH,EACLF,EAAQ,GAEM,MAATE,IAMLF,EAAQ,IAKhB,SAAS8G,EAAoB5G,GACZ,MAATA,EAGAF,EAAQ,GAEM,MAATE,EAELmH,IAKArH,EAAQ,GAahB,SAAS+G,EAAa7G,GACL,MAATA,EACAmH,IAEc,MAATnH,GACL8G,IAaR,SAASM,IACLtH,EAAQ,EACR0F,EAAaH,EAUjB,SAASyB,IACLhH,EAAQ,EACR0F,EAAa,IAAIF,EAAW,CAAEjQ,IAAKwK,IAMvC,SAASsH,IACL,IAAII,EAAgB/R,EAAKiI,MAAM8H,EAAgBC,EAAWnQ,KACtDkS,GAIArC,EAAOqC,EAAehC,GAEF,YAApBC,EAAW6B,KACXlC,EAAUK,EAAWnQ,KAEI,YAApBmQ,EAAW6B,KAChBjC,EAAUI,EAAWnQ,MAGjBmQ,EAAWwB,WACXhC,EAAUQ,EAAWyB,KAAMzB,EAAWnQ,KAEtCmQ,EAAWuB,WACX9B,EAAWO,EAAWyB,KAAMzB,EAAWnQ,MAI/C+R,IACA7B,EAAiB1F,EAAU,EAW/B,SAASqH,IACL,IAAIM,EAAWhC,EAAWnQ,KAAOmQ,EAAWuB,UAAY,EAAI,GAC5D,OAAOvR,EAAKiI,MAAM+J,EAAU3H,GAASwB,cA3crCkE,EAAiB1F,IAicbtC,EAAO/H,EAAKiI,MAAM8H,EAAgB1F,GACtCqF,EAAO3H,EAAMgI,GACbA,EAAiB1F,EAAU,GAmBnC,IAAIyF,EACA,SAAoBhS,QACJ,IAARA,IAAkBA,EAAM,IAC5BC,KAAK8B,SAAkBiM,IAAZhO,EAAI+B,IAAoB/B,EAAI+B,KAAO,EAC9C9B,KAAK8T,KAAO/T,EAAI+T,MAAQ,MACxB9T,KAAK0T,KAAO3T,EAAI2T,MAAQ,GACxB1T,KAAKyT,YAAc1T,EAAI0T,UACvBzT,KAAKwT,YAAczT,EAAIyT,kBCrfC,WAM5B,SAASU,EAAWnU,QACJ,IAARA,IAAkBA,EAAM,IAM5BC,KAAKmU,QAAUD,EAAWC,QAoC1BnU,KAAKoU,KAAO,GAOZpU,KAAK6H,OAAQ,EAOb7H,KAAKqU,OAAQ,EAabrU,KAAKkI,SAAU,EAaflI,KAAKsI,SAAU,EAMftI,KAAK4C,WAAY,EAkCjB5C,KAAKiJ,YAAc,CAAE1E,QAAQ,EAAM2E,KAAK,GAUxClJ,KAAKmJ,oBAAqB,EAU1BnJ,KAAKoJ,uBAAwB,EAgD7BpJ,KAAK6C,SAAW,CAAEtD,OAAQ,EAAG2E,SAAU,OAiBvClE,KAAK8C,UAAY,GAkBjB9C,KAAKsU,UAAY,KAQjBtU,KAAKuU,aAAUxG,EAUf/N,KAAKwU,SAAW,KAQhBxU,KAAKmG,WAAa,KAGlBnG,KAAKoU,KAAOpU,KAAKyU,iBAAiB1U,EAAIqU,MACtCpU,KAAK6H,MAA6B,kBAAd9H,EAAI8H,MAAsB9H,EAAI8H,MAAQ7H,KAAK6H,MAC/D7H,KAAKqU,MAA6B,kBAAdtU,EAAIsU,MAAsBtU,EAAIsU,MAAQrU,KAAKqU,MAC/DrU,KAAKkI,QAAUnI,EAAImI,SAAWlI,KAAKkI,QACnClI,KAAKsI,QAAUvI,EAAIuI,SAAWtI,KAAKsI,QACnCtI,KAAK4C,UAAqC,kBAAlB7C,EAAI6C,UAA0B7C,EAAI6C,UAAY5C,KAAK4C,UAC3E5C,KAAKiJ,YAAcjJ,KAAK0U,wBAAwB3U,EAAIkJ,aACpDjJ,KAAKmJ,mBAAuD,kBAA3BpJ,EAAIoJ,mBAAmCpJ,EAAIoJ,mBAAqBnJ,KAAKmJ,mBACtGnJ,KAAKoJ,sBAA6D,kBAA9BrJ,EAAIqJ,sBAAsCrJ,EAAIqJ,sBAAwBpJ,KAAKoJ,sBAE/G,IAAId,EAAUtI,KAAKsI,QACnB,IAAgB,IAAZA,GAAiC,YAAZA,GAAqC,cAAZA,GAAuC,eAAZA,EACzE,MAAM,IAAIzI,MAAM,oCAGpB,IAAIqI,EAAUlI,KAAKkI,QACnB,IAAgB,IAAZA,GAAiC,YAAZA,GAAqC,aAAZA,GAAsC,cAAZA,EACxE,MAAM,IAAIrI,MAAM,oCAEpBG,KAAK6C,SAAW7C,KAAK2U,qBAAqB5U,EAAI8C,UAC9C7C,KAAK8C,UAAY/C,EAAI+C,WAAa9C,KAAK8C,UACvC9C,KAAKsU,UAAYvU,EAAIuU,WAAatU,KAAKsU,UACvCtU,KAAKuU,QAAUxU,EAAIwU,SAAWvU,KAwdlC,OAhcAkU,EAAWU,KAAO,SAAUC,EAAYC,GAEpC,OADiB,IAAIZ,EAAWY,GACdF,KAAKC,IAmC3BX,EAAWa,MAAQ,SAAUF,EAAYC,GAErC,OADiB,IAAIZ,EAAWY,GACdC,MAAMF,IAY5BX,EAAW9U,UAAUqV,iBAAmB,SAAUL,GAG9C,OAFY,MAARA,IACAA,GAAO,GACS,kBAATA,EACA,CAAEY,cAAeZ,EAAMa,WAAYb,EAAMc,WAAYd,GAGrD,CACHY,cAA6C,kBAAvBZ,EAAKY,eAA8BZ,EAAKY,cAC9DC,WAAuC,kBAApBb,EAAKa,YAA2Bb,EAAKa,WACxDC,WAAuC,kBAApBd,EAAKc,YAA2Bd,EAAKc,aAcpEhB,EAAW9U,UAAUsV,wBAA0B,SAAUzL,GAGrD,OAFmB,MAAfA,IACAA,GAAc,GACS,kBAAhBA,EACA,CAAE1E,OAAQ0E,EAAaC,IAAKD,GAG5B,CACH1E,OAAsC,kBAAvB0E,EAAY1E,QAAuB0E,EAAY1E,OAC9D2E,IAAgC,kBAApBD,EAAYC,KAAoBD,EAAYC,MAcpEgL,EAAW9U,UAAUuV,qBAAuB,SAAU9R,GAClD,MAAwB,iBAAbA,EACA,CAAEtD,OAAQsD,EAAUqB,SAAU,OvBzgB1C,SAAkBiR,EAAMC,GAC3B,IAAK,IAAI3S,KAAQ2S,EACTA,EAAI1S,eAAeD,SAAwBsL,IAAfoH,EAAK1S,KACjC0S,EAAK1S,GAAQ2S,EAAI3S,IAGzB,OAAO0S,EuBsgBQE,CAASxS,GAAY,GAAI,CAC5BtD,OAAQ+V,OAAOC,kBACfrR,SAAU,SAmCtBgQ,EAAW9U,UAAU2V,MAAQ,SAAUF,GACnC,IAAIjN,EAAQ5H,KACRwV,EAAe,CAAC,IAAK,QAAS,UAAWC,EAAqB,EAClE3J,EAAU,GA+CV,OA5CAyF,EAAUsD,EAAY,CAClBpD,UAAW,SAAUxR,GACoB,GAAjCuV,EAAaxW,QAAQiB,IACrBwV,KAGR9D,OAAQ,SAAU3H,EAAM9D,GAEpB,GAA2B,IAAvBuP,EAA0B,CAM1B,IACIC,EvBvejB,SAAyB7R,EAAK8R,GACjC,IAAKA,EAAWC,OACZ,MAAM,IAAI/V,MAAM,2CAEpB,IADA,IAA8BmD,EAA1B6S,EAAS,GAAIC,EAAU,EACpB9S,EAAQ2S,EAAWvF,KAAKvM,IAC3BgS,EAAOnU,KAAKmC,EAAIiC,UAAUgQ,EAAS9S,EAAM2M,QACzCkG,EAAOnU,KAAKsB,EAAM,IAClB8S,EAAU9S,EAAM2M,MAAQ3M,EAAM,GAAGzD,OAGrC,OADAsW,EAAOnU,KAAKmC,EAAIiC,UAAUgQ,IACnBD,EuB6dyBE,CAAgB/L,EADC,8DAE7BgM,EAAkB9P,EACtBwP,EAAUO,QAAQ,SAAUC,EAAW7W,GAEnC,GAAIA,EAAI,GAAM,EAAG,CACb,IAAI8W,EAAkBvO,EAAMwO,UAAUF,EAAWF,GACjDlK,EAAQpK,KAAK+F,MAAMqE,EAASqK,GAEhCH,GAAmBE,EAAU3W,WAIzCmS,WAAY,SAAUzR,GACmB,GAAjCuV,EAAaxW,QAAQiB,KACrBwV,EAAqBxQ,KAAKuL,IAAIiF,EAAqB,EAAG,KAG9D7D,UAAW,SAAU1L,KACrB2L,UAAW,SAAU3L,OAKzB4F,EAAU9L,KAAKqW,eAAevK,GAK9BA,EAAU9L,KAAKsW,sBAAsBxK,IAczCoI,EAAW9U,UAAUiX,eAAiB,SAAUvK,GAE5CA,EAAQyK,KAAK,SAAUpK,EAAGxF,GAAK,OAAOwF,EAAE7F,YAAcK,EAAEL,cACxD,IAAK,IAAIjH,EAAI,EAAGA,EAAIyM,EAAQvM,OAAS,EAAGF,IAAK,CACzC,IAAI2D,EAAQ8I,EAAQzM,GAAI6G,EAASlD,EAAMsD,YAAakQ,EAAoBxT,EAAMoD,iBAAiB7G,OAAQkX,EAASvQ,EAASsQ,EACzH,GAAInX,EAAI,EAAIyM,EAAQvM,OAAQ,CAExB,GAAIuM,EAAQzM,EAAI,GAAGiH,cAAgBJ,EAAQ,CACvC,IAAIwQ,EAAY5K,EAAQzM,EAAI,GAAG+G,iBAAiB7G,OAASiX,EAAoBnX,EAAIA,EAAI,EACrFyM,EAAQpM,OAAOgX,EAAW,GAC1B,SAGA5K,EAAQzM,EAAI,GAAGiH,YAAcmQ,GAC7B3K,EAAQpM,OAAOL,EAAI,EAAG,IAIlC,OAAOyM,GAoBXoI,EAAW9U,UAAUkX,sBAAwB,SAAUxK,GAkBnD,OAjBK9L,KAAKkI,SACN1I,EAAOsM,EAAS,SAAU9I,GAAS,MAA2B,YAApBA,EAAMuD,YAC/CvG,KAAK6H,OACNrI,EAAOsM,EAAS,SAAU9I,GAAS,MAA2B,UAApBA,EAAMuD,YAC/CvG,KAAKqU,OACN7U,EAAOsM,EAAS,SAAU9I,GAAS,MAA2B,UAApBA,EAAMuD,YAC/CvG,KAAKsI,SACN9I,EAAOsM,EAAS,SAAU9I,GAAS,MAA2B,YAApBA,EAAMuD,YAC/CvG,KAAKoU,KAAKY,eACXxV,EAAOsM,EAAS,SAAUI,GAAK,MAAuB,QAAhBA,EAAE3F,WAA+C,WAAxB2F,EAAEzC,oBAEhEzJ,KAAKoU,KAAKa,YACXzV,EAAOsM,EAAS,SAAUI,GAAK,MAAuB,QAAhBA,EAAE3F,WAA+C,QAAxB2F,EAAEzC,oBAEhEzJ,KAAKoU,KAAKc,YACX1V,EAAOsM,EAAS,SAAUI,GAAK,MAAuB,QAAhBA,EAAE3F,WAA+C,QAAxB2F,EAAEzC,oBAE9DqC,GAuBXoI,EAAW9U,UAAUgX,UAAY,SAAUpM,EAAM9D,QAC9B,IAAXA,IAAqBA,EAAS,GAClCA,EAASA,GAAU,EAEnB,IADA,IAAIsO,EAAWxU,KAAK2W,cAAe7K,EAAU,GACpCzM,EAAI,EAAGuX,EAAcpC,EAASjV,OAAQF,EAAIuX,EAAavX,IAAK,CAMjE,IALA,IAAIwX,EAAcrC,EAASnV,GAAGwM,aAAa7B,GAKlC8M,EAAI,EAAGC,EAAiBF,EAAYtX,OAAQuX,EAAIC,EAAgBD,IACrED,EAAYC,GAAGzQ,UAAUH,EAAS2Q,EAAYC,GAAGxQ,aAErDwF,EAAQpK,KAAK+F,MAAMqE,EAAS+K,GAEhC,OAAO/K,GAoBXoI,EAAW9U,UAAUwV,KAAO,SAAUC,GAClC,IAAKA,EACD,MAAO,GAGX,IADA,IAAI/I,EAAU9L,KAAK+U,MAAMF,GAAamC,EAAU,GAAIC,EAAY,EACvD5X,EAAI,EAAGC,EAAMwM,EAAQvM,OAAQF,EAAIC,EAAKD,IAAK,CAChD,IAAI2D,EAAQ8I,EAAQzM,GACpB2X,EAAQtV,KAAKmT,EAAW/O,UAAUmR,EAAWjU,EAAMsD,cACnD0Q,EAAQtV,KAAK1B,KAAKkX,qBAAqBlU,IACvCiU,EAAYjU,EAAMsD,YAActD,EAAMoD,iBAAiB7G,OAG3D,OADAyX,EAAQtV,KAAKmT,EAAW/O,UAAUmR,IAC3BD,EAAQrV,KAAK,KAcxBuS,EAAW9U,UAAU8X,qBAAuB,SAAUlU,GAElD,IAAImU,EAIJ,OAHInX,KAAKsU,YACL6C,EAAkBnX,KAAKsU,UAAU9M,KAAKxH,KAAKuU,QAASvR,IAEzB,iBAApBmU,EACAA,GAEkB,IAApBA,EACEnU,EAAMoD,iBAER+Q,aAA2BrX,EACzBqX,EAAgB9U,iBAIPW,EAAMwD,WACLnE,kBAUzB6R,EAAW9U,UAAUuX,YAAc,WAC/B,GAAK3W,KAAKwU,SAYN,OAAOxU,KAAKwU,SAXZ,IAAIrO,EAAanG,KAAKoX,gBAClB5C,EAAW,CACX,IAAI9D,EAAe,CAAEvK,WAAYA,EAAY8B,YAAajI,KAAKkI,UAC/D,IAAIwD,EAAa,CAAEvF,WAAYA,IAC/B,IAAIyK,EAAa,CAAEzK,WAAYA,IAC/B,IAAI+K,EAAe,CAAE/K,WAAYA,EAAY8B,YAAajI,KAAKsI,UAC/D,IAAI2G,EAAW,CAAE9I,WAAYA,EAAY8C,YAAajJ,KAAKiJ,YAAaE,mBAAoBnJ,KAAKmJ,mBAAoBC,sBAAuBpJ,KAAKoJ,yBAErJ,OAAQpJ,KAAKwU,SAAWA,GAahCN,EAAW9U,UAAUgY,cAAgB,WACjC,IAAIjR,EAAanG,KAAKmG,WAQtB,OAPKA,IACDA,EAAanG,KAAKmG,WAAa,IAAIxD,EAAiB,CAChDC,UAAW5C,KAAK4C,UAChBC,SAAU7C,KAAK6C,SACfC,UAAW9C,KAAK8C,aAGjBqD,GAUX+N,EAAWC,QAAU,SAKrBD,EAAWvR,iBAAmBA,EAK9BuR,EAAWpU,QAAUA,EAKrBoU,EAAWmD,QAAU,CACjBC,MAAO5L,EACP6L,QAAS7G,EACTpG,QAASA,EACTkN,QAAStG,EACTuG,MAAO7G,EACP8G,IAAKzI,GAMTiF,EAAWlR,MAAQ,CACfsU,MAAO5P,EACP6P,QAASvP,EACTjC,MAAOA,EACPyR,QAASnP,EACToP,MAAOjP,EACPkP,IAAK7O,GAEFqL,EAnvBG", "file": "Autolinker.min.js", "sourcesContent": ["/**\n * Assigns (shallow copies) the properties of `src` onto `dest`, if the\n * corresponding property on `dest` === `undefined`.\n *\n * @param {Object} dest The destination object.\n * @param {Object} src The source object.\n * @return {Object} The destination object (`dest`)\n */\nexport function defaults(dest, src) {\n    for (var prop in src) {\n        if (src.hasOwnProperty(prop) && dest[prop] === undefined) {\n            dest[prop] = src[prop];\n        }\n    }\n    return dest;\n}\n/**\n * Truncates the `str` at `len - ellipsisChars.length`, and adds the `ellipsisChars` to the\n * end of the string (by default, two periods: '..'). If the `str` length does not exceed\n * `len`, the string will be returned unchanged.\n *\n * @param {String} str The string to truncate and add an ellipsis to.\n * @param {Number} truncateLen The length to truncate the string at.\n * @param {String} [ellipsisChars=...] The ellipsis character(s) to add to the end of `str`\n *   when truncated. Defaults to '...'\n */\nexport function ellipsis(str, truncateLen, ellipsisChars) {\n    var ellipsisLength;\n    if (str.length > truncateLen) {\n        if (ellipsisChars == null) {\n            ellipsisChars = '&hellip;';\n            ellipsisLength = 3;\n        }\n        else {\n            ellipsisLength = ellipsisChars.length;\n        }\n        str = str.substring(0, truncateLen - ellipsisLength) + ellipsisChars;\n    }\n    return str;\n}\n/**\n * Supports `Array.prototype.indexOf()` functionality for old IE (IE8 and below).\n *\n * @param {Array} arr The array to find an element of.\n * @param {*} element The element to find in the array, and return the index of.\n * @return {Number} The index of the `element`, or -1 if it was not found.\n */\nexport function indexOf(arr, element) {\n    if (Array.prototype.indexOf) {\n        return arr.indexOf(element);\n    }\n    else {\n        for (var i = 0, len = arr.length; i < len; i++) {\n            if (arr[i] === element)\n                return i;\n        }\n        return -1;\n    }\n}\n/**\n * Removes array elements based on a filtering function. Mutates the input\n * array.\n *\n * Using this instead of the ES5 Array.prototype.filter() function, to allow\n * Autolinker compatibility with IE8, and also to prevent creating many new\n * arrays in memory for filtering.\n *\n * @param {Array} arr The array to remove elements from. This array is\n *   mutated.\n * @param {Function} fn A function which should return `true` to\n *   remove an element.\n * @return {Array} The mutated input `arr`.\n */\nexport function remove(arr, fn) {\n    for (var i = arr.length - 1; i >= 0; i--) {\n        if (fn(arr[i]) === true) {\n            arr.splice(i, 1);\n        }\n    }\n}\n/**\n * Performs the functionality of what modern browsers do when `String.prototype.split()` is called\n * with a regular expression that contains capturing parenthesis.\n *\n * For example:\n *\n *     // Modern browsers:\n *     \"a,b,c\".split( /(,)/ );  // --> [ 'a', ',', 'b', ',', 'c' ]\n *\n *     // Old IE (including IE8):\n *     \"a,b,c\".split( /(,)/ );  // --> [ 'a', 'b', 'c' ]\n *\n * This method emulates the functionality of modern browsers for the old IE case.\n *\n * @param {String} str The string to split.\n * @param {RegExp} splitRegex The regular expression to split the input `str` on. The splitting\n *   character(s) will be spliced into the array, as in the \"modern browsers\" example in the\n *   description of this method.\n *   Note #1: the supplied regular expression **must** have the 'g' flag specified.\n *   Note #2: for simplicity's sake, the regular expression does not need\n *   to contain capturing parenthesis - it will be assumed that any match has them.\n * @return {String[]} The split array of strings, with the splitting character(s) included.\n */\nexport function splitAndCapture(str, splitRegex) {\n    if (!splitRegex.global)\n        throw new Error(\"`splitRegex` must have the 'g' flag set\");\n    var result = [], lastIdx = 0, match;\n    while (match = splitRegex.exec(str)) {\n        result.push(str.substring(lastIdx, match.index));\n        result.push(match[0]); // push the splitting char(s)\n        lastIdx = match.index + match[0].length;\n    }\n    result.push(str.substring(lastIdx));\n    return result;\n}\n/**\n * Function that should never be called but is used to check that every\n * enum value is handled using TypeScript's 'never' type.\n */\nexport function throwUnhandledCaseError(theValue) {\n    throw new Error(\"Unhandled case for value: '\" + theValue + \"'\");\n}\n\n//# sourceMappingURL=utils.js.map\n", "import { indexOf } from \"./utils\";\n/**\n * @class Autolinker.HtmlTag\n * @extends Object\n *\n * Represents an HTML tag, which can be used to easily build/modify HTML tags programmatically.\n *\n * Autolinker uses this abstraction to create HTML tags, and then write them out as strings. You may also use\n * this class in your code, especially within a {@link Autolinker#replaceFn replaceFn}.\n *\n * ## Examples\n *\n * Example instantiation:\n *\n *     var tag = new Autolinker.HtmlTag( {\n *         tagName : 'a',\n *         attrs   : { 'href': 'http://google.com', 'class': 'external-link' },\n *         innerHtml : 'Google'\n *     } );\n *\n *     tag.toAnchorString();  // <a href=\"http://google.com\" class=\"external-link\">Google</a>\n *\n *     // Individual accessor methods\n *     tag.getTagName();                 // 'a'\n *     tag.getAttr( 'href' );            // 'http://google.com'\n *     tag.hasClass( 'external-link' );  // true\n *\n *\n * Using mutator methods (which may be used in combination with instantiation config properties):\n *\n *     var tag = new Autolinker.HtmlTag();\n *     tag.setTagName( 'a' );\n *     tag.setAttr( 'href', 'http://google.com' );\n *     tag.addClass( 'external-link' );\n *     tag.setInnerHtml( 'Google' );\n *\n *     tag.getTagName();                 // 'a'\n *     tag.getAttr( 'href' );            // 'http://google.com'\n *     tag.hasClass( 'external-link' );  // true\n *\n *     tag.toAnchorString();  // <a href=\"http://google.com\" class=\"external-link\">Google</a>\n *\n *\n * ## Example use within a {@link Autolinker#replaceFn replaceFn}\n *\n *     var html = Autolinker.link( \"Test google.com\", {\n *         replaceFn : function( match ) {\n *             var tag = match.buildTag();  // returns an {@link Autolinker.HtmlTag} instance, configured with the Match's href and anchor text\n *             tag.setAttr( 'rel', 'nofollow' );\n *\n *             return tag;\n *         }\n *     } );\n *\n *     // generated html:\n *     //   Test <a href=\"http://google.com\" target=\"_blank\" rel=\"nofollow\">google.com</a>\n *\n *\n * ## Example use with a new tag for the replacement\n *\n *     var html = Autolinker.link( \"Test google.com\", {\n *         replaceFn : function( match ) {\n *             var tag = new Autolinker.HtmlTag( {\n *                 tagName : 'button',\n *                 attrs   : { 'title': 'Load URL: ' + match.getAnchorHref() },\n *                 innerHtml : 'Load URL: ' + match.getAnchorText()\n *             } );\n *\n *             return tag;\n *         }\n *     } );\n *\n *     // generated html:\n *     //   Test <button title=\"Load URL: http://google.com\">Load URL: google.com</button>\n */\nvar HtmlTag = /** @class */ (function () {\n    /**\n     * @method constructor\n     * @param {Object} [cfg] The configuration properties for this class, in an Object (map)\n     */\n    function HtmlTag(cfg) {\n        if (cfg === void 0) { cfg = {}; }\n        /**\n         * @cfg {String} tagName\n         *\n         * The tag name. Ex: 'a', 'button', etc.\n         *\n         * Not required at instantiation time, but should be set using {@link #setTagName} before {@link #toAnchorString}\n         * is executed.\n         */\n        this.tagName = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Object.<String, String>} attrs\n         *\n         * An key/value Object (map) of attributes to create the tag with. The keys are the attribute names, and the\n         * values are the attribute values.\n         */\n        this.attrs = {}; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {String} innerHTML\n         *\n         * The inner HTML for the tag.\n         */\n        this.innerHTML = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @protected\n         * @property {RegExp} whitespaceRegex\n         *\n         * Regular expression used to match whitespace in a string of CSS classes.\n         */\n        this.whitespaceRegex = /\\s+/; // default value just to get the above doc comment in the ES5 output and documentation generator\n        this.tagName = cfg.tagName || '';\n        this.attrs = cfg.attrs || {};\n        this.innerHTML = cfg.innerHtml || cfg.innerHTML || ''; // accept either the camelCased form or the fully capitalized acronym as in the DOM\n    }\n    /**\n     * Sets the tag name that will be used to generate the tag with.\n     *\n     * @param {String} tagName\n     * @return {Autolinker.HtmlTag} This HtmlTag instance, so that method calls may be chained.\n     */\n    HtmlTag.prototype.setTagName = function (tagName) {\n        this.tagName = tagName;\n        return this;\n    };\n    /**\n     * Retrieves the tag name.\n     *\n     * @return {String}\n     */\n    HtmlTag.prototype.getTagName = function () {\n        return this.tagName || '';\n    };\n    /**\n     * Sets an attribute on the HtmlTag.\n     *\n     * @param {String} attrName The attribute name to set.\n     * @param {String} attrValue The attribute value to set.\n     * @return {Autolinker.HtmlTag} This HtmlTag instance, so that method calls may be chained.\n     */\n    HtmlTag.prototype.setAttr = function (attrName, attrValue) {\n        var tagAttrs = this.getAttrs();\n        tagAttrs[attrName] = attrValue;\n        return this;\n    };\n    /**\n     * Retrieves an attribute from the HtmlTag. If the attribute does not exist, returns `undefined`.\n     *\n     * @param {String} attrName The attribute name to retrieve.\n     * @return {String} The attribute's value, or `undefined` if it does not exist on the HtmlTag.\n     */\n    HtmlTag.prototype.getAttr = function (attrName) {\n        return this.getAttrs()[attrName];\n    };\n    /**\n     * Sets one or more attributes on the HtmlTag.\n     *\n     * @param {Object.<String, String>} attrs A key/value Object (map) of the attributes to set.\n     * @return {Autolinker.HtmlTag} This HtmlTag instance, so that method calls may be chained.\n     */\n    HtmlTag.prototype.setAttrs = function (attrs) {\n        Object.assign(this.getAttrs(), attrs);\n        return this;\n    };\n    /**\n     * Retrieves the attributes Object (map) for the HtmlTag.\n     *\n     * @return {Object.<String, String>} A key/value object of the attributes for the HtmlTag.\n     */\n    HtmlTag.prototype.getAttrs = function () {\n        return this.attrs || (this.attrs = {});\n    };\n    /**\n     * Sets the provided `cssClass`, overwriting any current CSS classes on the HtmlTag.\n     *\n     * @param {String} cssClass One or more space-separated CSS classes to set (overwrite).\n     * @return {Autolinker.HtmlTag} This HtmlTag instance, so that method calls may be chained.\n     */\n    HtmlTag.prototype.setClass = function (cssClass) {\n        return this.setAttr('class', cssClass);\n    };\n    /**\n     * Convenience method to add one or more CSS classes to the HtmlTag. Will not add duplicate CSS classes.\n     *\n     * @param {String} cssClass One or more space-separated CSS classes to add.\n     * @return {Autolinker.HtmlTag} This HtmlTag instance, so that method calls may be chained.\n     */\n    HtmlTag.prototype.addClass = function (cssClass) {\n        var classAttr = this.getClass(), whitespaceRegex = this.whitespaceRegex, classes = (!classAttr) ? [] : classAttr.split(whitespaceRegex), newClasses = cssClass.split(whitespaceRegex), newClass;\n        while (newClass = newClasses.shift()) {\n            if (indexOf(classes, newClass) === -1) {\n                classes.push(newClass);\n            }\n        }\n        this.getAttrs()['class'] = classes.join(\" \");\n        return this;\n    };\n    /**\n     * Convenience method to remove one or more CSS classes from the HtmlTag.\n     *\n     * @param {String} cssClass One or more space-separated CSS classes to remove.\n     * @return {Autolinker.HtmlTag} This HtmlTag instance, so that method calls may be chained.\n     */\n    HtmlTag.prototype.removeClass = function (cssClass) {\n        var classAttr = this.getClass(), whitespaceRegex = this.whitespaceRegex, classes = (!classAttr) ? [] : classAttr.split(whitespaceRegex), removeClasses = cssClass.split(whitespaceRegex), removeClass;\n        while (classes.length && (removeClass = removeClasses.shift())) {\n            var idx = indexOf(classes, removeClass);\n            if (idx !== -1) {\n                classes.splice(idx, 1);\n            }\n        }\n        this.getAttrs()['class'] = classes.join(\" \");\n        return this;\n    };\n    /**\n     * Convenience method to retrieve the CSS class(es) for the HtmlTag, which will each be separated by spaces when\n     * there are multiple.\n     *\n     * @return {String}\n     */\n    HtmlTag.prototype.getClass = function () {\n        return this.getAttrs()['class'] || \"\";\n    };\n    /**\n     * Convenience method to check if the tag has a CSS class or not.\n     *\n     * @param {String} cssClass The CSS class to check for.\n     * @return {Boolean} `true` if the HtmlTag has the CSS class, `false` otherwise.\n     */\n    HtmlTag.prototype.hasClass = function (cssClass) {\n        return (' ' + this.getClass() + ' ').indexOf(' ' + cssClass + ' ') !== -1;\n    };\n    /**\n     * Sets the inner HTML for the tag.\n     *\n     * @param {String} html The inner HTML to set.\n     * @return {Autolinker.HtmlTag} This HtmlTag instance, so that method calls may be chained.\n     */\n    HtmlTag.prototype.setInnerHTML = function (html) {\n        this.innerHTML = html;\n        return this;\n    };\n    /**\n     * Backwards compatibility method name.\n     *\n     * @param {String} html The inner HTML to set.\n     * @return {Autolinker.HtmlTag} This HtmlTag instance, so that method calls may be chained.\n     */\n    HtmlTag.prototype.setInnerHtml = function (html) {\n        return this.setInnerHTML(html);\n    };\n    /**\n     * Retrieves the inner HTML for the tag.\n     *\n     * @return {String}\n     */\n    HtmlTag.prototype.getInnerHTML = function () {\n        return this.innerHTML || \"\";\n    };\n    /**\n     * Backward compatibility method name.\n     *\n     * @return {String}\n     */\n    HtmlTag.prototype.getInnerHtml = function () {\n        return this.getInnerHTML();\n    };\n    /**\n     * Override of superclass method used to generate the HTML string for the tag.\n     *\n     * @return {String}\n     */\n    HtmlTag.prototype.toAnchorString = function () {\n        var tagName = this.getTagName(), attrsStr = this.buildAttrsStr();\n        attrsStr = (attrsStr) ? ' ' + attrsStr : ''; // prepend a space if there are actually attributes\n        return ['<', tagName, attrsStr, '>', this.getInnerHtml(), '</', tagName, '>'].join(\"\");\n    };\n    /**\n     * Support method for {@link #toAnchorString}, returns the string space-separated key=\"value\" pairs, used to populate\n     * the stringified HtmlTag.\n     *\n     * @protected\n     * @return {String} Example return: `attr1=\"value1\" attr2=\"value2\"`\n     */\n    HtmlTag.prototype.buildAttrsStr = function () {\n        if (!this.attrs)\n            return \"\"; // no `attrs` Object (map) has been set, return empty string\n        var attrs = this.getAttrs(), attrsArr = [];\n        for (var prop in attrs) {\n            if (attrs.hasOwnProperty(prop)) {\n                attrsArr.push(prop + '=\"' + attrs[prop] + '\"');\n            }\n        }\n        return attrsArr.join(\" \");\n    };\n    return HtmlTag;\n}());\nexport { HtmlTag };\n\n//# sourceMappingURL=html-tag.js.map\n", "import { HtmlTag } from \"./html-tag\";\nimport { truncateSmart } from \"./truncate/truncate-smart\";\nimport { truncateMiddle } from \"./truncate/truncate-middle\";\nimport { truncateEnd } from \"./truncate/truncate-end\";\n/**\n * @protected\n * @class Autolinker.AnchorTagBuilder\n * @extends Object\n *\n * Builds anchor (&lt;a&gt;) tags for the Autolinker utility when a match is\n * found.\n *\n * Normally this class is instantiated, configured, and used internally by an\n * {@link Autolinker} instance, but may actually be used indirectly in a\n * {@link Autolinker#replaceFn replaceFn} to create {@link Autolinker.HtmlTag HtmlTag}\n * instances which may be modified before returning from the\n * {@link Autolinker#replaceFn replaceFn}. For example:\n *\n *     var html = Autolinker.link( \"Test google.com\", {\n *         replaceFn : function( match ) {\n *             var tag = match.buildTag();  // returns an {@link Autolinker.HtmlTag} instance\n *             tag.setAttr( 'rel', 'nofollow' );\n *\n *             return tag;\n *         }\n *     } );\n *\n *     // generated html:\n *     //   Test <a href=\"http://google.com\" target=\"_blank\" rel=\"nofollow\">google.com</a>\n */\nvar AnchorTagBuilder = /** @class */ (function () {\n    /**\n     * @method constructor\n     * @param {Object} [cfg] The configuration options for the AnchorTagBuilder instance, specified in an Object (map).\n     */\n    function AnchorTagBuilder(cfg) {\n        if (cfg === void 0) { cfg = {}; }\n        /**\n         * @cfg {Boolean} newWindow\n         * @inheritdoc Autolinker#newWindow\n         */\n        this.newWindow = false; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Object} truncate\n         * @inheritdoc Autolinker#truncate\n         */\n        this.truncate = {}; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {String} className\n         * @inheritdoc Autolinker#className\n         */\n        this.className = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        this.newWindow = cfg.newWindow || false;\n        this.truncate = cfg.truncate || {};\n        this.className = cfg.className || '';\n    }\n    /**\n     * Generates the actual anchor (&lt;a&gt;) tag to use in place of the\n     * matched text, via its `match` object.\n     *\n     * @param {Autolinker.match.Match} match The Match instance to generate an\n     *   anchor tag from.\n     * @return {Autolinker.HtmlTag} The HtmlTag instance for the anchor tag.\n     */\n    AnchorTagBuilder.prototype.build = function (match) {\n        return new HtmlTag({\n            tagName: 'a',\n            attrs: this.createAttrs(match),\n            innerHtml: this.processAnchorText(match.getAnchorText())\n        });\n    };\n    /**\n     * Creates the Object (map) of the HTML attributes for the anchor (&lt;a&gt;)\n     *   tag being generated.\n     *\n     * @protected\n     * @param {Autolinker.match.Match} match The Match instance to generate an\n     *   anchor tag from.\n     * @return {Object} A key/value Object (map) of the anchor tag's attributes.\n     */\n    AnchorTagBuilder.prototype.createAttrs = function (match) {\n        var attrs = {\n            'href': match.getAnchorHref() // we'll always have the `href` attribute\n        };\n        var cssClass = this.createCssClass(match);\n        if (cssClass) {\n            attrs['class'] = cssClass;\n        }\n        if (this.newWindow) {\n            attrs['target'] = \"_blank\";\n            attrs['rel'] = \"noopener noreferrer\"; // Issue #149. See https://mathiasbynens.github.io/rel-noopener/\n        }\n        if (this.truncate) {\n            if (this.truncate.length && this.truncate.length < match.getAnchorText().length) {\n                attrs['title'] = match.getAnchorHref();\n            }\n        }\n        return attrs;\n    };\n    /**\n     * Creates the CSS class that will be used for a given anchor tag, based on\n     * the `matchType` and the {@link #className} config.\n     *\n     * Example returns:\n     *\n     * - \"\"                                      // no {@link #className}\n     * - \"myLink myLink-url\"                     // url match\n     * - \"myLink myLink-email\"                   // email match\n     * - \"myLink myLink-phone\"                   // phone match\n     * - \"myLink myLink-hashtag\"                 // hashtag match\n     * - \"myLink myLink-mention myLink-twitter\"  // mention match with Twitter service\n     *\n     * @protected\n     * @param {Autolinker.match.Match} match The Match instance to generate an\n     *   anchor tag from.\n     * @return {String} The CSS class string for the link. Example return:\n     *   \"myLink myLink-url\". If no {@link #className} was configured, returns\n     *   an empty string.\n     */\n    AnchorTagBuilder.prototype.createCssClass = function (match) {\n        var className = this.className;\n        if (!className) {\n            return \"\";\n        }\n        else {\n            var returnClasses = [className], cssClassSuffixes = match.getCssClassSuffixes();\n            for (var i = 0, len = cssClassSuffixes.length; i < len; i++) {\n                returnClasses.push(className + '-' + cssClassSuffixes[i]);\n            }\n            return returnClasses.join(' ');\n        }\n    };\n    /**\n     * Processes the `anchorText` by truncating the text according to the\n     * {@link #truncate} config.\n     *\n     * @private\n     * @param {String} anchorText The anchor tag's text (i.e. what will be\n     *   displayed).\n     * @return {String} The processed `anchorText`.\n     */\n    AnchorTagBuilder.prototype.processAnchorText = function (anchorText) {\n        anchorText = this.doTruncate(anchorText);\n        return anchorText;\n    };\n    /**\n     * Performs the truncation of the `anchorText` based on the {@link #truncate}\n     * option. If the `anchorText` is longer than the length specified by the\n     * {@link #truncate} option, the truncation is performed based on the\n     * `location` property. See {@link #truncate} for details.\n     *\n     * @private\n     * @param {String} anchorText The anchor tag's text (i.e. what will be\n     *   displayed).\n     * @return {String} The truncated anchor text.\n     */\n    AnchorTagBuilder.prototype.doTruncate = function (anchorText) {\n        var truncate = this.truncate;\n        if (!truncate || !truncate.length)\n            return anchorText;\n        var truncateLength = truncate.length, truncateLocation = truncate.location;\n        if (truncateLocation === 'smart') {\n            return truncateSmart(anchorText, truncateLength);\n        }\n        else if (truncateLocation === 'middle') {\n            return truncateMiddle(anchorText, truncateLength);\n        }\n        else {\n            return truncateEnd(anchorText, truncateLength);\n        }\n    };\n    return AnchorTagBuilder;\n}());\nexport { AnchorTagBuilder };\n\n//# sourceMappingURL=anchor-tag-builder.js.map\n", "import { ellipsis } from \"../utils\";\n/**\n * A truncation feature where the ellipsis will be placed at the end of the URL.\n *\n * @param {String} anchorText\n * @param {Number} truncateLen The maximum length of the truncated output URL string.\n * @param {String} ellipsisChars The characters to place within the url, e.g. \"..\".\n * @return {String} The truncated URL.\n */\nexport function truncateEnd(anchorText, truncateLen, ellipsisChars) {\n    return ellipsis(anchorText, truncateLen, ellipsisChars);\n}\n\n//# sourceMappingURL=truncate-end.js.map\n", "/**\n * Date: 2015-10-05\n * Author: <PERSON><PERSON> <<EMAIL>> (https://github.com/kafoso)\n *\n * A truncation feature, where the ellipsis will be placed at a section within\n * the URL making it still somewhat human readable.\n *\n * @param {String} url\t\t\t\t\t\t A URL.\n * @param {Number} truncateLen\t\t The maximum length of the truncated output URL string.\n * @param {String} ellipsisChars\t The characters to place within the url, e.g. \"...\".\n * @return {String} The truncated URL.\n */\nexport function truncateSmart(url, truncateLen, ellipsisChars) {\n    var ellipsisLengthBeforeParsing;\n    var ellipsisLength;\n    if (ellipsisChars == null) {\n        ellipsisChars = '&hellip;';\n        ellipsisLength = 3;\n        ellipsisLengthBeforeParsing = 8;\n    }\n    else {\n        ellipsisLength = ellipsisChars.length;\n        ellipsisLengthBeforeParsing = ellipsisChars.length;\n    }\n    var parse_url = function (url) {\n        var urlObj = {};\n        var urlSub = url;\n        var match = urlSub.match(/^([a-z]+):\\/\\//i);\n        if (match) {\n            urlObj.scheme = match[1];\n            urlSub = urlSub.substr(match[0].length);\n        }\n        match = urlSub.match(/^(.*?)(?=(\\?|#|\\/|$))/i);\n        if (match) {\n            urlObj.host = match[1];\n            urlSub = urlSub.substr(match[0].length);\n        }\n        match = urlSub.match(/^\\/(.*?)(?=(\\?|#|$))/i);\n        if (match) {\n            urlObj.path = match[1];\n            urlSub = urlSub.substr(match[0].length);\n        }\n        match = urlSub.match(/^\\?(.*?)(?=(#|$))/i);\n        if (match) {\n            urlObj.query = match[1];\n            urlSub = urlSub.substr(match[0].length);\n        }\n        match = urlSub.match(/^#(.*?)$/i);\n        if (match) {\n            urlObj.fragment = match[1];\n            //urlSub = urlSub.substr(match[0].length);  -- not used. Uncomment if adding another block.\n        }\n        return urlObj;\n    };\n    var buildUrl = function (urlObj) {\n        var url = \"\";\n        if (urlObj.scheme && urlObj.host) {\n            url += urlObj.scheme + \"://\";\n        }\n        if (urlObj.host) {\n            url += urlObj.host;\n        }\n        if (urlObj.path) {\n            url += \"/\" + urlObj.path;\n        }\n        if (urlObj.query) {\n            url += \"?\" + urlObj.query;\n        }\n        if (urlObj.fragment) {\n            url += \"#\" + urlObj.fragment;\n        }\n        return url;\n    };\n    var buildSegment = function (segment, remainingAvailableLength) {\n        var remainingAvailableLengthHalf = remainingAvailableLength / 2, startOffset = Math.ceil(remainingAvailableLengthHalf), endOffset = (-1) * Math.floor(remainingAvailableLengthHalf), end = \"\";\n        if (endOffset < 0) {\n            end = segment.substr(endOffset);\n        }\n        return segment.substr(0, startOffset) + ellipsisChars + end;\n    };\n    if (url.length <= truncateLen) {\n        return url;\n    }\n    var availableLength = truncateLen - ellipsisLength;\n    var urlObj = parse_url(url);\n    // Clean up the URL\n    if (urlObj.query) {\n        var matchQuery = urlObj.query.match(/^(.*?)(?=(\\?|\\#))(.*?)$/i);\n        if (matchQuery) {\n            // Malformed URL; two or more \"?\". Removed any content behind the 2nd.\n            urlObj.query = urlObj.query.substr(0, matchQuery[1].length);\n            url = buildUrl(urlObj);\n        }\n    }\n    if (url.length <= truncateLen) {\n        return url;\n    }\n    if (urlObj.host) {\n        urlObj.host = urlObj.host.replace(/^www\\./, \"\");\n        url = buildUrl(urlObj);\n    }\n    if (url.length <= truncateLen) {\n        return url;\n    }\n    // Process and build the URL\n    var str = \"\";\n    if (urlObj.host) {\n        str += urlObj.host;\n    }\n    if (str.length >= availableLength) {\n        if (urlObj.host.length == truncateLen) {\n            return (urlObj.host.substr(0, (truncateLen - ellipsisLength)) + ellipsisChars).substr(0, availableLength + ellipsisLengthBeforeParsing);\n        }\n        return buildSegment(str, availableLength).substr(0, availableLength + ellipsisLengthBeforeParsing);\n    }\n    var pathAndQuery = \"\";\n    if (urlObj.path) {\n        pathAndQuery += \"/\" + urlObj.path;\n    }\n    if (urlObj.query) {\n        pathAndQuery += \"?\" + urlObj.query;\n    }\n    if (pathAndQuery) {\n        if ((str + pathAndQuery).length >= availableLength) {\n            if ((str + pathAndQuery).length == truncateLen) {\n                return (str + pathAndQuery).substr(0, truncateLen);\n            }\n            var remainingAvailableLength = availableLength - str.length;\n            return (str + buildSegment(pathAndQuery, remainingAvailableLength)).substr(0, availableLength + ellipsisLengthBeforeParsing);\n        }\n        else {\n            str += pathAndQuery;\n        }\n    }\n    if (urlObj.fragment) {\n        var fragment = \"#\" + urlObj.fragment;\n        if ((str + fragment).length >= availableLength) {\n            if ((str + fragment).length == truncateLen) {\n                return (str + fragment).substr(0, truncateLen);\n            }\n            var remainingAvailableLength2 = availableLength - str.length;\n            return (str + buildSegment(fragment, remainingAvailableLength2)).substr(0, availableLength + ellipsisLengthBeforeParsing);\n        }\n        else {\n            str += fragment;\n        }\n    }\n    if (urlObj.scheme && urlObj.host) {\n        var scheme = urlObj.scheme + \"://\";\n        if ((str + scheme).length < availableLength) {\n            return (scheme + str).substr(0, truncateLen);\n        }\n    }\n    if (str.length <= truncateLen) {\n        return str;\n    }\n    var end = \"\";\n    if (availableLength > 0) {\n        end = str.substr((-1) * Math.floor(availableLength / 2));\n    }\n    return (str.substr(0, Math.ceil(availableLength / 2)) + ellipsisChars + end).substr(0, availableLength + ellipsisLengthBeforeParsing);\n}\n\n//# sourceMappingURL=truncate-smart.js.map\n", "/**\n * Date: 2015-10-05\n * Author: <PERSON><PERSON> <<EMAIL>> (https://github.com/kafoso)\n *\n * A truncation feature, where the ellipsis will be placed in the dead-center of the URL.\n *\n * @param {String} url             A URL.\n * @param {Number} truncateLen     The maximum length of the truncated output URL string.\n * @param {String} ellipsisChars   The characters to place within the url, e.g. \"..\".\n * @return {String} The truncated URL.\n */\nexport function truncateMiddle(url, truncateLen, ellipsisChars) {\n    if (url.length <= truncateLen) {\n        return url;\n    }\n    var ellipsisLengthBeforeParsing;\n    var ellipsisLength;\n    if (ellipsisChars == null) {\n        ellipsisChars = '&hellip;';\n        ellipsisLengthBeforeParsing = 8;\n        ellipsisLength = 3;\n    }\n    else {\n        ellipsisLengthBeforeParsing = ellipsisChars.length;\n        ellipsisLength = ellipsisChars.length;\n    }\n    var availableLength = truncateLen - ellipsisLength;\n    var end = \"\";\n    if (availableLength > 0) {\n        end = url.substr((-1) * Math.floor(availableLength / 2));\n    }\n    return (url.substr(0, Math.ceil(availableLength / 2)) + ellipsisChars + end).substr(0, availableLength + ellipsisLengthBeforeParsing);\n}\n\n//# sourceMappingURL=truncate-middle.js.map\n", "/**\n * @abstract\n * @class Autolinker.match.Match\n *\n * Represents a match found in an input string which should be Autolinked. A Match object is what is provided in a\n * {@link Autolinker#replaceFn replaceFn}, and may be used to query for details about the match.\n *\n * For example:\n *\n *     var input = \"...\";  // string with URLs, Email Addresses, and Mentions (Twitter, Instagram, Soundcloud)\n *\n *     var linkedText = Autolinker.link( input, {\n *         replaceFn : function( match ) {\n *             console.log( \"href = \", match.getAnchorHref() );\n *             console.log( \"text = \", match.getAnchorText() );\n *\n *             switch( match.getType() ) {\n *                 case 'url' :\n *                     console.log( \"url: \", match.getUrl() );\n *\n *                 case 'email' :\n *                     console.log( \"email: \", match.getEmail() );\n *\n *                 case 'mention' :\n *                     console.log( \"mention: \", match.getMention() );\n *             }\n *         }\n *     } );\n *\n * See the {@link Autolinker} class for more details on using the {@link Autolinker#replaceFn replaceFn}.\n */\nvar Match = /** @class */ (function () {\n    /**\n     * @member Autolinker.match.Match\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match\n     *   instance, specified in an Object (map).\n     */\n    function Match(cfg) {\n        /**\n         * @cfg {Autolinker.AnchorTagBuilder} tagBuilder (required)\n         *\n         * Reference to the AnchorTagBuilder instance to use to generate an anchor\n         * tag for the Match.\n         */\n        this.__jsduckDummyDocProp = null; // property used just to get the above doc comment into the ES5 output and documentation generator\n        /**\n         * @cfg {String} matchedText (required)\n         *\n         * The original text that was matched by the {@link Autolinker.matcher.Matcher}.\n         */\n        this.matchedText = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Number} offset (required)\n         *\n         * The offset of where the match was made in the input string.\n         */\n        this.offset = 0; // default value just to get the above doc comment in the ES5 output and documentation generator\n        this.tagBuilder = cfg.tagBuilder;\n        this.matchedText = cfg.matchedText;\n        this.offset = cfg.offset;\n    }\n    /**\n     * Returns the original text that was matched.\n     *\n     * @return {String}\n     */\n    Match.prototype.getMatchedText = function () {\n        return this.matchedText;\n    };\n    /**\n     * Sets the {@link #offset} of where the match was made in the input string.\n     *\n     * A {@link Autolinker.matcher.Matcher} will be fed only HTML text nodes,\n     * and will therefore set an original offset that is relative to the HTML\n     * text node itself. However, we want this offset to be relative to the full\n     * HTML input string, and thus if using {@link Autolinker#parse} (rather\n     * than calling a {@link Autolinker.matcher.Matcher} directly), then this\n     * offset is corrected after the Matcher itself has done its job.\n     *\n     * @param {Number} offset\n     */\n    Match.prototype.setOffset = function (offset) {\n        this.offset = offset;\n    };\n    /**\n     * Returns the offset of where the match was made in the input string. This\n     * is the 0-based index of the match.\n     *\n     * @return {Number}\n     */\n    Match.prototype.getOffset = function () {\n        return this.offset;\n    };\n    /**\n     * Returns the CSS class suffix(es) for this match.\n     *\n     * A CSS class suffix is appended to the {@link Autolinker#className} in\n     * the {@link Autolinker.AnchorTagBuilder} when a match is translated into\n     * an anchor tag.\n     *\n     * For example, if {@link Autolinker#className} was configured as 'myLink',\n     * and this method returns `[ 'url' ]`, the final class name of the element\n     * will become: 'myLink myLink-url'.\n     *\n     * The match may provide multiple CSS class suffixes to be appended to the\n     * {@link Autolinker#className} in order to facilitate better styling\n     * options for different match criteria. See {@link Autolinker.match.Mention}\n     * for an example.\n     *\n     * By default, this method returns a single array with the match's\n     * {@link #getType type} name, but may be overridden by subclasses.\n     *\n     * @return {String[]}\n     */\n    Match.prototype.getCssClassSuffixes = function () {\n        return [this.getType()];\n    };\n    /**\n     * Builds and returns an {@link Autolinker.HtmlTag} instance based on the\n     * Match.\n     *\n     * This can be used to easily generate anchor tags from matches, and either\n     * return their HTML string, or modify them before doing so.\n     *\n     * Example Usage:\n     *\n     *     var tag = match.buildTag();\n     *     tag.addClass( 'cordova-link' );\n     *     tag.setAttr( 'target', '_system' );\n     *\n     *     tag.toAnchorString();  // <a href=\"http://google.com\" class=\"cordova-link\" target=\"_system\">Google</a>\n     *\n     * Example Usage in {@link Autolinker#replaceFn}:\n     *\n     *     var html = Autolinker.link( \"Test google.com\", {\n     *         replaceFn : function( match ) {\n     *             var tag = match.buildTag();  // returns an {@link Autolinker.HtmlTag} instance\n     *             tag.setAttr( 'rel', 'nofollow' );\n     *\n     *             return tag;\n     *         }\n     *     } );\n     *\n     *     // generated html:\n     *     //   Test <a href=\"http://google.com\" target=\"_blank\" rel=\"nofollow\">google.com</a>\n     */\n    Match.prototype.buildTag = function () {\n        return this.tagBuilder.build(this);\n    };\n    return Match;\n}());\nexport { Match };\n\n//# sourceMappingURL=match.js.map\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n", "import * as tslib_1 from \"tslib\";\nimport { Match } from \"./match\";\n/**\n * @class Autolinker.match.Email\n * @extends Autolinker.match.Match\n *\n * Represents a Email match found in an input string which should be Autolinked.\n *\n * See this class's superclass ({@link Autolinker.match.Match}) for more details.\n */\nvar EmailMatch = /** @class */ (function (_super) {\n    tslib_1.__extends(EmailMatch, _super);\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match\n     *   instance, specified in an Object (map).\n     */\n    function EmailMatch(cfg) {\n        var _this = _super.call(this, cfg) || this;\n        /**\n         * @cfg {String} email (required)\n         *\n         * The email address that was matched.\n         */\n        _this.email = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        _this.email = cfg.email;\n        return _this;\n    }\n    /**\n     * Returns a string name for the type of match that this class represents.\n     * For the case of EmailMatch, returns 'email'.\n     *\n     * @return {String}\n     */\n    EmailMatch.prototype.getType = function () {\n        return 'email';\n    };\n    /**\n     * Returns the email address that was matched.\n     *\n     * @return {String}\n     */\n    EmailMatch.prototype.getEmail = function () {\n        return this.email;\n    };\n    /**\n     * Returns the anchor href that should be generated for the match.\n     *\n     * @return {String}\n     */\n    EmailMatch.prototype.getAnchorHref = function () {\n        return 'mailto:' + this.email;\n    };\n    /**\n     * Returns the anchor text that should be generated for the match.\n     *\n     * @return {String}\n     */\n    EmailMatch.prototype.getAnchorText = function () {\n        return this.email;\n    };\n    return EmailMatch;\n}(Match));\nexport { EmailMatch };\n\n//# sourceMappingURL=email-match.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { Match } from \"./match\";\n/**\n * @class Autolinker.match.Hashtag\n * @extends Autolinker.match.Match\n *\n * Represents a Hashtag match found in an input string which should be\n * Autolinked.\n *\n * See this class's superclass ({@link Autolinker.match.Match}) for more\n * details.\n */\nvar HashtagMatch = /** @class */ (function (_super) {\n    tslib_1.__extends(HashtagMatch, _super);\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match\n     *   instance, specified in an Object (map).\n     */\n    function HashtagMatch(cfg) {\n        var _this = _super.call(this, cfg) || this;\n        /**\n         * @cfg {String} serviceName\n         *\n         * The service to point hashtag matches to. See {@link Autolinker#hashtag}\n         * for available values.\n         */\n        _this.serviceName = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {String} hashtag (required)\n         *\n         * The HashtagMatch that was matched, without the '#'.\n         */\n        _this.hashtag = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        _this.serviceName = cfg.serviceName;\n        _this.hashtag = cfg.hashtag;\n        return _this;\n    }\n    /**\n     * Returns a string name for the type of match that this class represents.\n     * For the case of HashtagMatch, returns 'hashtag'.\n     *\n     * @return {String}\n     */\n    HashtagMatch.prototype.getType = function () {\n        return 'hashtag';\n    };\n    /**\n     * Returns the configured {@link #serviceName} to point the HashtagMatch to.\n     * Ex: 'facebook', 'twitter'.\n     *\n     * @return {String}\n     */\n    HashtagMatch.prototype.getServiceName = function () {\n        return this.serviceName;\n    };\n    /**\n     * Returns the matched hashtag, without the '#' character.\n     *\n     * @return {String}\n     */\n    HashtagMatch.prototype.getHashtag = function () {\n        return this.hashtag;\n    };\n    /**\n     * Returns the anchor href that should be generated for the match.\n     *\n     * @return {String}\n     */\n    HashtagMatch.prototype.getAnchorHref = function () {\n        var serviceName = this.serviceName, hashtag = this.hashtag;\n        switch (serviceName) {\n            case 'twitter':\n                return 'https://twitter.com/hashtag/' + hashtag;\n            case 'facebook':\n                return 'https://www.facebook.com/hashtag/' + hashtag;\n            case 'instagram':\n                return 'https://instagram.com/explore/tags/' + hashtag;\n            default: // Shouldn't happen because Autolinker's constructor should block any invalid values, but just in case.\n                throw new Error('Unknown service name to point hashtag to: ' + serviceName);\n        }\n    };\n    /**\n     * Returns the anchor text that should be generated for the match.\n     *\n     * @return {String}\n     */\n    HashtagMatch.prototype.getAnchorText = function () {\n        return '#' + this.hashtag;\n    };\n    return HashtagMatch;\n}(Match));\nexport { HashtagMatch };\n\n//# sourceMappingURL=hashtag-match.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { Match } from \"./match\";\n/**\n * @class Autolinker.match.Mention\n * @extends Autolinker.match.Match\n *\n * Represents a Mention match found in an input string which should be Autolinked.\n *\n * See this class's superclass ({@link Autolinker.match.Match}) for more details.\n */\nvar MentionMatch = /** @class */ (function (_super) {\n    tslib_1.__extends(MentionMatch, _super);\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match\n     *   instance, specified in an Object (map).\n     */\n    function MentionMatch(cfg) {\n        var _this = _super.call(this, cfg) || this;\n        /**\n         * @cfg {String} serviceName\n         *\n         * The service to point mention matches to. See {@link Autolinker#mention}\n         * for available values.\n         */\n        _this.serviceName = 'twitter'; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {String} mention (required)\n         *\n         * The Mention that was matched, without the '@' character.\n         */\n        _this.mention = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        _this.mention = cfg.mention;\n        _this.serviceName = cfg.serviceName;\n        return _this;\n    }\n    /**\n     * Returns a string name for the type of match that this class represents.\n     * For the case of MentionMatch, returns 'mention'.\n     *\n     * @return {String}\n     */\n    MentionMatch.prototype.getType = function () {\n        return 'mention';\n    };\n    /**\n     * Returns the mention, without the '@' character.\n     *\n     * @return {String}\n     */\n    MentionMatch.prototype.getMention = function () {\n        return this.mention;\n    };\n    /**\n     * Returns the configured {@link #serviceName} to point the mention to.\n     * Ex: 'instagram', 'twitter', 'soundcloud'.\n     *\n     * @return {String}\n     */\n    MentionMatch.prototype.getServiceName = function () {\n        return this.serviceName;\n    };\n    /**\n     * Returns the anchor href that should be generated for the match.\n     *\n     * @return {String}\n     */\n    MentionMatch.prototype.getAnchorHref = function () {\n        switch (this.serviceName) {\n            case 'twitter':\n                return 'https://twitter.com/' + this.mention;\n            case 'instagram':\n                return 'https://instagram.com/' + this.mention;\n            case 'soundcloud':\n                return 'https://soundcloud.com/' + this.mention;\n            default: // Shouldn't happen because Autolinker's constructor should block any invalid values, but just in case.\n                throw new Error('Unknown service name to point mention to: ' + this.serviceName);\n        }\n    };\n    /**\n     * Returns the anchor text that should be generated for the match.\n     *\n     * @return {String}\n     */\n    MentionMatch.prototype.getAnchorText = function () {\n        return '@' + this.mention;\n    };\n    /**\n     * Returns the CSS class suffixes that should be used on a tag built with\n     * the match. See {@link Autolinker.match.Match#getCssClassSuffixes} for\n     * details.\n     *\n     * @return {String[]}\n     */\n    MentionMatch.prototype.getCssClassSuffixes = function () {\n        var cssClassSuffixes = _super.prototype.getCssClassSuffixes.call(this), serviceName = this.getServiceName();\n        if (serviceName) {\n            cssClassSuffixes.push(serviceName);\n        }\n        return cssClassSuffixes;\n    };\n    return MentionMatch;\n}(Match));\nexport { MentionMatch };\n\n//# sourceMappingURL=mention-match.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { Match } from \"./match\";\n/**\n * @class Autolinker.match.Phone\n * @extends Autolinker.match.Match\n *\n * Represents a Phone number match found in an input string which should be\n * Autolinked.\n *\n * See this class's superclass ({@link Autolinker.match.Match}) for more\n * details.\n */\nvar PhoneMatch = /** @class */ (function (_super) {\n    tslib_1.__extends(PhoneMatch, _super);\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match\n     *   instance, specified in an Object (map).\n     */\n    function PhoneMatch(cfg) {\n        var _this = _super.call(this, cfg) || this;\n        /**\n         * @protected\n         * @property {String} number (required)\n         *\n         * The phone number that was matched, without any delimiter characters.\n         *\n         * Note: This is a string to allow for prefixed 0's.\n         */\n        _this.number = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @protected\n         * @property  {Boolean} plusSign (required)\n         *\n         * `true` if the matched phone number started with a '+' sign. We'll include\n         * it in the `tel:` URL if so, as this is needed for international numbers.\n         *\n         * Ex: '+****************'\n         */\n        _this.plusSign = false; // default value just to get the above doc comment in the ES5 output and documentation generator\n        _this.number = cfg.number;\n        _this.plusSign = cfg.plusSign;\n        return _this;\n    }\n    /**\n     * Returns a string name for the type of match that this class represents.\n     * For the case of PhoneMatch, returns 'phone'.\n     *\n     * @return {String}\n     */\n    PhoneMatch.prototype.getType = function () {\n        return 'phone';\n    };\n    /**\n     * Returns the phone number that was matched as a string, without any\n     * delimiter characters.\n     *\n     * Note: This is a string to allow for prefixed 0's.\n     *\n     * @return {String}\n     */\n    PhoneMatch.prototype.getPhoneNumber = function () {\n        return this.number;\n    };\n    /**\n     * Alias of {@link #getPhoneNumber}, returns the phone number that was\n     * matched as a string, without any delimiter characters.\n     *\n     * Note: This is a string to allow for prefixed 0's.\n     *\n     * @return {String}\n     */\n    PhoneMatch.prototype.getNumber = function () {\n        return this.getPhoneNumber();\n    };\n    /**\n     * Returns the anchor href that should be generated for the match.\n     *\n     * @return {String}\n     */\n    PhoneMatch.prototype.getAnchorHref = function () {\n        return 'tel:' + (this.plusSign ? '+' : '') + this.number;\n    };\n    /**\n     * Returns the anchor text that should be generated for the match.\n     *\n     * @return {String}\n     */\n    PhoneMatch.prototype.getAnchorText = function () {\n        return this.matchedText;\n    };\n    return PhoneMatch;\n}(Match));\nexport { PhoneMatch };\n\n//# sourceMappingURL=phone-match.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { Match } from \"./match\";\n/**\n * @class Autolinker.match.Url\n * @extends Autolinker.match.Match\n *\n * Represents a Url match found in an input string which should be Autolinked.\n *\n * See this class's superclass ({@link Autolinker.match.Match}) for more details.\n */\nvar UrlMatch = /** @class */ (function (_super) {\n    tslib_1.__extends(UrlMatch, _super);\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match\n     *   instance, specified in an Object (map).\n     */\n    function UrlMatch(cfg) {\n        var _this = _super.call(this, cfg) || this;\n        /**\n         * @cfg {String} url (required)\n         *\n         * The url that was matched.\n         */\n        _this.url = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {\"scheme\"/\"www\"/\"tld\"} urlMatchType (required)\n         *\n         * The type of URL match that this class represents. This helps to determine\n         * if the match was made in the original text with a prefixed scheme (ex:\n         * 'http://www.google.com'), a prefixed 'www' (ex: 'www.google.com'), or\n         * was matched by a known top-level domain (ex: 'google.com').\n         */\n        _this.urlMatchType = 'scheme'; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} protocolUrlMatch (required)\n         *\n         * `true` if the URL is a match which already has a protocol (i.e.\n         * 'http://'), `false` if the match was from a 'www' or known TLD match.\n         */\n        _this.protocolUrlMatch = false; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} protocolRelativeMatch (required)\n         *\n         * `true` if the URL is a protocol-relative match. A protocol-relative match\n         * is a URL that starts with '//', and will be either http:// or https://\n         * based on the protocol that the site is loaded under.\n         */\n        _this.protocolRelativeMatch = false; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Object} stripPrefix (required)\n         *\n         * The Object form of {@link Autolinker#cfg-stripPrefix}.\n         */\n        _this.stripPrefix = { scheme: true, www: true }; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} stripTrailingSlash (required)\n         * @inheritdoc Autolinker#cfg-stripTrailingSlash\n         */\n        _this.stripTrailingSlash = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} decodePercentEncoding (required)\n         * @inheritdoc Autolinker#cfg-decodePercentEncoding\n         */\n        _this.decodePercentEncoding = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @private\n         * @property {RegExp} schemePrefixRegex\n         *\n         * A regular expression used to remove the 'http://' or 'https://' from\n         * URLs.\n         */\n        _this.schemePrefixRegex = /^(https?:\\/\\/)?/i;\n        /**\n         * @private\n         * @property {RegExp} wwwPrefixRegex\n         *\n         * A regular expression used to remove the 'www.' from URLs.\n         */\n        _this.wwwPrefixRegex = /^(https?:\\/\\/)?(www\\.)?/i;\n        /**\n         * @private\n         * @property {RegExp} protocolRelativeRegex\n         *\n         * The regular expression used to remove the protocol-relative '//' from the {@link #url} string, for purposes\n         * of {@link #getAnchorText}. A protocol-relative URL is, for example, \"//yahoo.com\"\n         */\n        _this.protocolRelativeRegex = /^\\/\\//;\n        /**\n         * @private\n         * @property {Boolean} protocolPrepended\n         *\n         * Will be set to `true` if the 'http://' protocol has been prepended to the {@link #url} (because the\n         * {@link #url} did not have a protocol)\n         */\n        _this.protocolPrepended = false;\n        _this.urlMatchType = cfg.urlMatchType;\n        _this.url = cfg.url;\n        _this.protocolUrlMatch = cfg.protocolUrlMatch;\n        _this.protocolRelativeMatch = cfg.protocolRelativeMatch;\n        _this.stripPrefix = cfg.stripPrefix;\n        _this.stripTrailingSlash = cfg.stripTrailingSlash;\n        _this.decodePercentEncoding = cfg.decodePercentEncoding;\n        return _this;\n    }\n    /**\n     * Returns a string name for the type of match that this class represents.\n     * For the case of UrlMatch, returns 'url'.\n     *\n     * @return {String}\n     */\n    UrlMatch.prototype.getType = function () {\n        return 'url';\n    };\n    /**\n     * Returns a string name for the type of URL match that this class\n     * represents.\n     *\n     * This helps to determine if the match was made in the original text with a\n     * prefixed scheme (ex: 'http://www.google.com'), a prefixed 'www' (ex:\n     * 'www.google.com'), or was matched by a known top-level domain (ex:\n     * 'google.com').\n     *\n     * @return {\"scheme\"/\"www\"/\"tld\"}\n     */\n    UrlMatch.prototype.getUrlMatchType = function () {\n        return this.urlMatchType;\n    };\n    /**\n     * Returns the url that was matched, assuming the protocol to be 'http://' if the original\n     * match was missing a protocol.\n     *\n     * @return {String}\n     */\n    UrlMatch.prototype.getUrl = function () {\n        var url = this.url;\n        // if the url string doesn't begin with a protocol, assume 'http://'\n        if (!this.protocolRelativeMatch && !this.protocolUrlMatch && !this.protocolPrepended) {\n            url = this.url = 'http://' + url;\n            this.protocolPrepended = true;\n        }\n        return url;\n    };\n    /**\n     * Returns the anchor href that should be generated for the match.\n     *\n     * @return {String}\n     */\n    UrlMatch.prototype.getAnchorHref = function () {\n        var url = this.getUrl();\n        return url.replace(/&amp;/g, '&'); // any &amp;'s in the URL should be converted back to '&' if they were displayed as &amp; in the source html\n    };\n    /**\n     * Returns the anchor text that should be generated for the match.\n     *\n     * @return {String}\n     */\n    UrlMatch.prototype.getAnchorText = function () {\n        var anchorText = this.getMatchedText();\n        if (this.protocolRelativeMatch) {\n            // Strip off any protocol-relative '//' from the anchor text\n            anchorText = this.stripProtocolRelativePrefix(anchorText);\n        }\n        if (this.stripPrefix.scheme) {\n            anchorText = this.stripSchemePrefix(anchorText);\n        }\n        if (this.stripPrefix.www) {\n            anchorText = this.stripWwwPrefix(anchorText);\n        }\n        if (this.stripTrailingSlash) {\n            anchorText = this.removeTrailingSlash(anchorText); // remove trailing slash, if there is one\n        }\n        if (this.decodePercentEncoding) {\n            anchorText = this.removePercentEncoding(anchorText);\n        }\n        return anchorText;\n    };\n    // ---------------------------------------\n    // Utility Functionality\n    /**\n     * Strips the scheme prefix (such as \"http://\" or \"https://\") from the given\n     * `url`.\n     *\n     * @private\n     * @param {String} url The text of the anchor that is being generated, for\n     *   which to strip off the url scheme.\n     * @return {String} The `url`, with the scheme stripped.\n     */\n    UrlMatch.prototype.stripSchemePrefix = function (url) {\n        return url.replace(this.schemePrefixRegex, '');\n    };\n    /**\n     * Strips the 'www' prefix from the given `url`.\n     *\n     * @private\n     * @param {String} url The text of the anchor that is being generated, for\n     *   which to strip off the 'www' if it exists.\n     * @return {String} The `url`, with the 'www' stripped.\n     */\n    UrlMatch.prototype.stripWwwPrefix = function (url) {\n        return url.replace(this.wwwPrefixRegex, '$1'); // leave any scheme ($1), it one exists\n    };\n    /**\n     * Strips any protocol-relative '//' from the anchor text.\n     *\n     * @private\n     * @param {String} text The text of the anchor that is being generated, for which to strip off the\n     *   protocol-relative prefix (such as stripping off \"//\")\n     * @return {String} The `anchorText`, with the protocol-relative prefix stripped.\n     */\n    UrlMatch.prototype.stripProtocolRelativePrefix = function (text) {\n        return text.replace(this.protocolRelativeRegex, '');\n    };\n    /**\n     * Removes any trailing slash from the given `anchorText`, in preparation for the text to be displayed.\n     *\n     * @private\n     * @param {String} anchorText The text of the anchor that is being generated, for which to remove any trailing\n     *   slash ('/') that may exist.\n     * @return {String} The `anchorText`, with the trailing slash removed.\n     */\n    UrlMatch.prototype.removeTrailingSlash = function (anchorText) {\n        if (anchorText.charAt(anchorText.length - 1) === '/') {\n            anchorText = anchorText.slice(0, -1);\n        }\n        return anchorText;\n    };\n    /**\n     * Decodes percent-encoded characters from the given `anchorText`, in\n     * preparation for the text to be displayed.\n     *\n     * @private\n     * @param {String} anchorText The text of the anchor that is being\n     *   generated, for which to decode any percent-encoded characters.\n     * @return {String} The `anchorText`, with the percent-encoded characters\n     *   decoded.\n     */\n    UrlMatch.prototype.removePercentEncoding = function (anchorText) {\n        // First, convert a few of the known % encodings to the corresponding\n        // HTML entities that could accidentally be interpretted as special\n        // HTML characters\n        var preProcessedEntityAnchorText = anchorText\n            .replace(/%22/gi, '&quot;') // \" char\n            .replace(/%26/gi, '&amp;') // & char\n            .replace(/%27/gi, '&#39;') // ' char\n            .replace(/%3C/gi, '&lt;') // < char\n            .replace(/%3E/gi, '&gt;'); // > char\n        try {\n            // Now attempt to decode the rest of the anchor text\n            return decodeURIComponent(preProcessedEntityAnchorText);\n        }\n        catch (e) { // Invalid % escape sequence in the anchor text\n            return preProcessedEntityAnchorText;\n        }\n    };\n    return UrlMatch;\n}(Match));\nexport { UrlMatch };\n\n//# sourceMappingURL=url-match.js.map\n", "/**\n * @abstract\n * @class Autolinker.matcher.Matcher\n *\n * An abstract class and interface for individual matchers to find matches in\n * an input string with linkified versions of them.\n *\n * Note that Matchers do not take HTML into account - they must be fed the text\n * nodes of any HTML string, which is handled by {@link Autolinker#parse}.\n */\nvar Matcher = /** @class */ (function () {\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Matcher\n     *   instance, specified in an Object (map).\n     */\n    function Matcher(cfg) {\n        /**\n         * @cfg {Autolinker.AnchorTagBuilder} tagBuilder (required)\n         *\n         * Reference to the AnchorTagBuilder instance to use to generate HTML tags\n         * for {@link Autolinker.match.Match Matches}.\n         */\n        this.__jsduckDummyDocProp = null; // property used just to get the above doc comment into the ES5 output and documentation generator\n        this.tagBuilder = cfg.tagBuilder;\n    }\n    return Matcher;\n}());\nexport { Matcher };\n\n//# sourceMappingURL=matcher.js.map\n", "/*\n * This file builds and stores a library of the common regular expressions used\n * by the Autolinker utility.\n *\n * Other regular expressions may exist ad-hoc, but these are generally the\n * regular expressions that are shared between source files.\n */\n/**\n * Regular expression to match upper and lowercase ASCII letters\n */\nexport var letterRe = /[A-Za-z]/;\n/**\n * Regular expression to match ASCII digits\n */\nexport var digitRe = /[0-9]/;\n/**\n * Regular expression to match whitespace\n */\nexport var whitespaceRe = /\\s/;\n/**\n * Regular expression to match quote characters\n */\nexport var quoteRe = /['\"]/;\n/**\n * Regular expression to match the range of ASCII control characters (0-31), and\n * the backspace char (127)\n */\nexport var controlCharsRe = /[\\x00-\\x1F\\x7F]/;\n/**\n * The string form of a regular expression that would match all of the\n * alphabetic (\"letter\") chars in the unicode character set when placed in a\n * RegExp character class (`[]`). This includes all international alphabetic\n * characters.\n *\n * These would be the characters matched by unicode regex engines `\\p{L}`\n * escape (\"all letters\").\n *\n * Taken from the XRegExp library: http://xregexp.com/ (thanks @https://github.com/slevithan)\n * Specifically: http://xregexp.com/v/3.2.0/xregexp-all.js, the 'Letter'\n *   regex's bmp\n *\n * VERY IMPORTANT: This set of characters is defined inside of a Regular\n *   Expression literal rather than a string literal to prevent UglifyJS from\n *   compressing the unicode escape sequences into their actual unicode\n *   characters. If Uglify compresses these into the unicode characters\n *   themselves, this results in the error \"Range out of order in character\n *   class\" when these characters are used inside of a Regular Expression\n *   character class (`[]`). See usages of this const. Alternatively, we can set\n *   the UglifyJS option `ascii_only` to true for the build, but that doesn't\n *   help others who are pulling in Autolinker into their own build and running\n *   UglifyJS themselves.\n */\nexport var alphaCharsStr = /A-Za-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FD5\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC/\n    .source; // see note in above variable description\n/**\n * The string form of a regular expression that would match all emoji characters\n * Based on the emoji regex defined in this article: https://thekevinscott.com/emojis-in-javascript/\n */\nexport var emojiStr = /\\u2700-\\u27bf\\udde6-\\uddff\\ud800-\\udbff\\udc00-\\udfff\\ufe0e\\ufe0f\\u0300-\\u036f\\ufe20-\\ufe23\\u20d0-\\u20f0\\ud83c\\udffb-\\udfff\\u200d\\u3299\\u3297\\u303d\\u3030\\u24c2\\ud83c\\udd70-\\udd71\\udd7e-\\udd7f\\udd8e\\udd91-\\udd9a\\udde6-\\uddff\\ude01-\\ude02\\ude1a\\ude2f\\ude32-\\ude3a\\ude50-\\ude51\\u203c\\u2049\\u25aa-\\u25ab\\u25b6\\u25c0\\u25fb-\\u25fe\\u00a9\\u00ae\\u2122\\u2139\\udc04\\u2600-\\u26FF\\u2b05\\u2b06\\u2b07\\u2b1b\\u2b1c\\u2b50\\u2b55\\u231a\\u231b\\u2328\\u23cf\\u23e9-\\u23f3\\u23f8-\\u23fa\\udccf\\u2935\\u2934\\u2190-\\u21ff/\n    .source;\n/**\n * The string form of a regular expression that would match all of the\n * combining mark characters in the unicode character set when placed in a\n * RegExp character class (`[]`).\n *\n * These would be the characters matched by unicode regex engines `\\p{M}`\n * escape (\"all marks\").\n *\n * Taken from the XRegExp library: http://xregexp.com/ (thanks @https://github.com/slevithan)\n * Specifically: http://xregexp.com/v/3.2.0/xregexp-all.js, the 'Mark'\n *   regex's bmp\n *\n * VERY IMPORTANT: This set of characters is defined inside of a Regular\n *   Expression literal rather than a string literal to prevent UglifyJS from\n *   compressing the unicode escape sequences into their actual unicode\n *   characters. If Uglify compresses these into the unicode characters\n *   themselves, this results in the error \"Range out of order in character\n *   class\" when these characters are used inside of a Regular Expression\n *   character class (`[]`). See usages of this const. Alternatively, we can set\n *   the UglifyJS option `ascii_only` to true for the build, but that doesn't\n *   help others who are pulling in Autolinker into their own build and running\n *   UglifyJS themselves.\n */\nexport var marksStr = /\\u0300-\\u036F\\u0483-\\u0489\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u0610-\\u061A\\u064B-\\u065F\\u0670\\u06D6-\\u06DC\\u06DF-\\u06E4\\u06E7\\u06E8\\u06EA-\\u06ED\\u0711\\u0730-\\u074A\\u07A6-\\u07B0\\u07EB-\\u07F3\\u0816-\\u0819\\u081B-\\u0823\\u0825-\\u0827\\u0829-\\u082D\\u0859-\\u085B\\u08D4-\\u08E1\\u08E3-\\u0903\\u093A-\\u093C\\u093E-\\u094F\\u0951-\\u0957\\u0962\\u0963\\u0981-\\u0983\\u09BC\\u09BE-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CD\\u09D7\\u09E2\\u09E3\\u0A01-\\u0A03\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A70\\u0A71\\u0A75\\u0A81-\\u0A83\\u0ABC\\u0ABE-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AE2\\u0AE3\\u0B01-\\u0B03\\u0B3C\\u0B3E-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B62\\u0B63\\u0B82\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD7\\u0C00-\\u0C03\\u0C3E-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C62\\u0C63\\u0C81-\\u0C83\\u0CBC\\u0CBE-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CE2\\u0CE3\\u0D01-\\u0D03\\u0D3E-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4D\\u0D57\\u0D62\\u0D63\\u0D82\\u0D83\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DF2\\u0DF3\\u0E31\\u0E34-\\u0E3A\\u0E47-\\u0E4E\\u0EB1\\u0EB4-\\u0EB9\\u0EBB\\u0EBC\\u0EC8-\\u0ECD\\u0F18\\u0F19\\u0F35\\u0F37\\u0F39\\u0F3E\\u0F3F\\u0F71-\\u0F84\\u0F86\\u0F87\\u0F8D-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u102B-\\u103E\\u1056-\\u1059\\u105E-\\u1060\\u1062-\\u1064\\u1067-\\u106D\\u1071-\\u1074\\u1082-\\u108D\\u108F\\u109A-\\u109D\\u135D-\\u135F\\u1712-\\u1714\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17B4-\\u17D3\\u17DD\\u180B-\\u180D\\u1885\\u1886\\u18A9\\u1920-\\u192B\\u1930-\\u193B\\u1A17-\\u1A1B\\u1A55-\\u1A5E\\u1A60-\\u1A7C\\u1A7F\\u1AB0-\\u1ABE\\u1B00-\\u1B04\\u1B34-\\u1B44\\u1B6B-\\u1B73\\u1B80-\\u1B82\\u1BA1-\\u1BAD\\u1BE6-\\u1BF3\\u1C24-\\u1C37\\u1CD0-\\u1CD2\\u1CD4-\\u1CE8\\u1CED\\u1CF2-\\u1CF4\\u1CF8\\u1CF9\\u1DC0-\\u1DF5\\u1DFB-\\u1DFF\\u20D0-\\u20F0\\u2CEF-\\u2CF1\\u2D7F\\u2DE0-\\u2DFF\\u302A-\\u302F\\u3099\\u309A\\uA66F-\\uA672\\uA674-\\uA67D\\uA69E\\uA69F\\uA6F0\\uA6F1\\uA802\\uA806\\uA80B\\uA823-\\uA827\\uA880\\uA881\\uA8B4-\\uA8C5\\uA8E0-\\uA8F1\\uA926-\\uA92D\\uA947-\\uA953\\uA980-\\uA983\\uA9B3-\\uA9C0\\uA9E5\\uAA29-\\uAA36\\uAA43\\uAA4C\\uAA4D\\uAA7B-\\uAA7D\\uAAB0\\uAAB2-\\uAAB4\\uAAB7\\uAAB8\\uAABE\\uAABF\\uAAC1\\uAAEB-\\uAAEF\\uAAF5\\uAAF6\\uABE3-\\uABEA\\uABEC\\uABED\\uFB1E\\uFE00-\\uFE0F\\uFE20-\\uFE2F/\n    .source; // see note in above variable description\n/**\n * The string form of a regular expression that would match all of the\n * alphabetic (\"letter\") chars, emoji, and combining marks in the unicode character set\n * when placed in a RegExp character class (`[]`). This includes all\n * international alphabetic characters.\n *\n * These would be the characters matched by unicode regex engines `\\p{L}\\p{M}`\n * escapes and emoji characters.\n */\nexport var alphaCharsAndMarksStr = alphaCharsStr + emojiStr + marksStr;\n/**\n * The string form of a regular expression that would match all of the\n * decimal number chars in the unicode character set when placed in a RegExp\n * character class (`[]`).\n *\n * These would be the characters matched by unicode regex engines `\\p{Nd}`\n * escape (\"all decimal numbers\")\n *\n * Taken from the XRegExp library: http://xregexp.com/ (thanks @https://github.com/slevithan)\n * Specifically: http://xregexp.com/v/3.2.0/xregexp-all.js, the 'Decimal_Number'\n *   regex's bmp\n *\n * VERY IMPORTANT: This set of characters is defined inside of a Regular\n *   Expression literal rather than a string literal to prevent UglifyJS from\n *   compressing the unicode escape sequences into their actual unicode\n *   characters. If Uglify compresses these into the unicode characters\n *   themselves, this results in the error \"Range out of order in character\n *   class\" when these characters are used inside of a Regular Expression\n *   character class (`[]`). See usages of this const. Alternatively, we can set\n *   the UglifyJS option `ascii_only` to true for the build, but that doesn't\n *   help others who are pulling in Autolinker into their own build and running\n *   UglifyJS themselves.\n */\nexport var decimalNumbersStr = /0-9\\u0660-\\u0669\\u06F0-\\u06F9\\u07C0-\\u07C9\\u0966-\\u096F\\u09E6-\\u09EF\\u0A66-\\u0A6F\\u0AE6-\\u0AEF\\u0B66-\\u0B6F\\u0BE6-\\u0BEF\\u0C66-\\u0C6F\\u0CE6-\\u0CEF\\u0D66-\\u0D6F\\u0DE6-\\u0DEF\\u0E50-\\u0E59\\u0ED0-\\u0ED9\\u0F20-\\u0F29\\u1040-\\u1049\\u1090-\\u1099\\u17E0-\\u17E9\\u1810-\\u1819\\u1946-\\u194F\\u19D0-\\u19D9\\u1A80-\\u1A89\\u1A90-\\u1A99\\u1B50-\\u1B59\\u1BB0-\\u1BB9\\u1C40-\\u1C49\\u1C50-\\u1C59\\uA620-\\uA629\\uA8D0-\\uA8D9\\uA900-\\uA909\\uA9D0-\\uA9D9\\uA9F0-\\uA9F9\\uAA50-\\uAA59\\uABF0-\\uABF9\\uFF10-\\uFF19/\n    .source; // see note in above variable description\n/**\n * The string form of a regular expression that would match all of the\n * letters and decimal number chars in the unicode character set when placed in\n * a RegExp character class (`[]`).\n *\n * These would be the characters matched by unicode regex engines\n * `[\\p{L}\\p{Nd}]` escape (\"all letters and decimal numbers\")\n */\nexport var alphaNumericCharsStr = alphaCharsAndMarksStr + decimalNumbersStr;\n/**\n * The string form of a regular expression that would match all of the\n * letters, combining marks, and decimal number chars in the unicode character\n * set when placed in a RegExp character class (`[]`).\n *\n * These would be the characters matched by unicode regex engines\n * `[\\p{L}\\p{M}\\p{Nd}]` escape (\"all letters, combining marks, and decimal\n * numbers\")\n */\nexport var alphaNumericAndMarksCharsStr = alphaCharsAndMarksStr + decimalNumbersStr;\n// Simplified IP regular expression\nvar ipStr = '(?:[' + decimalNumbersStr + ']{1,3}\\\\.){3}[' + decimalNumbersStr + ']{1,3}';\n// Protected domain label which do not allow \"-\" character on the beginning and the end of a single label\nvar domainLabelStr = '[' + alphaNumericAndMarksCharsStr + '](?:[' + alphaNumericAndMarksCharsStr + '\\\\-]{0,61}[' + alphaNumericAndMarksCharsStr + '])?';\nvar getDomainLabelStr = function (group) {\n    return '(?=(' + domainLabelStr + '))\\\\' + group;\n};\n/**\n * A function to match domain names of a URL or email address.\n * Ex: 'google', 'yahoo', 'some-other-company', etc.\n */\nexport var getDomainNameStr = function (group) {\n    return '(?:' + getDomainLabelStr(group) + '(?:\\\\.' + getDomainLabelStr(group + 1) + '){0,126}|' + ipStr + ')';\n};\n/**\n * A regular expression to match domain names of a URL or email address.\n * Ex: 'google', 'yahoo', 'some-other-company', etc.\n */\nexport var domainNameRegex = new RegExp('[' + alphaNumericAndMarksCharsStr + '.\\\\-]*[' + alphaNumericAndMarksCharsStr + '\\\\-]');\n/**\n * A regular expression that is simply the character class of the characters\n * that may be used in a domain name, minus the '-' or '.'\n */\nexport var domainNameCharRegex = new RegExp(\"[\" + alphaNumericAndMarksCharsStr + \"]\");\n\n//# sourceMappingURL=regex-lib.js.map\n", "// NOTE: THIS IS A GENERATED FILE\n// To update with the latest TLD list, run `npm run update-tld-regex` or `yarn update-tld-regex` (depending on which you have installed)\nexport var tldRegex = /(?:xn--vermgensberatung-pwb|xn--vermgensberater-ctb|xn--clchc0ea0b2g2a9gcd|xn--w4r85el8fhu5dnra|northwesternmutual|travelersinsurance|vermögensberatung|xn--3oq18vl8pn36a|xn--5su34j936bgsg|xn--bck1b9a5dre4c|xn--mgbai9azgqp6j|xn--mgberp4a5d4ar|xn--xkc2dl3a5ee0h|vermögensberater|xn--fzys8d69uvgm|xn--mgba7c0bbn0a|xn--xkc2al3hye2a|americanexpress|kerryproperties|sandvikcoromant|xn--i1b6b1a6a2e|xn--kcrx77d1x4a|xn--lgbbat1ad8j|xn--mgba3a4f16a|xn--mgbaakc7dvf|xn--mgbc0a9azcg|xn--nqv7fs00ema|afamilycompany|americanfamily|bananarepublic|cancerresearch|cookingchannel|kerrylogistics|weatherchannel|xn--54b7fta0cc|xn--6qq986b3xl|xn--80aqecdr1a|xn--b4w605ferd|xn--fiq228c5hs|xn--h2breg3eve|xn--jlq61u9w7b|xn--mgba3a3ejt|xn--mgbaam7a8h|xn--mgbayh7gpa|xn--mgbb9fbpob|xn--mgbbh1a71e|xn--mgbca7dzdo|xn--mgbi4ecexp|xn--mgbx4cd0ab|xn--rvc1e0am3e|international|lifeinsurance|spreadbetting|travelchannel|wolterskluwer|xn--eckvdtc9d|xn--fpcrj9c3d|xn--fzc2c9e2c|xn--h2brj9c8c|xn--tiq49xqyj|xn--yfro4i67o|xn--ygbi2ammx|construction|lplfinancial|scholarships|versicherung|xn--3e0b707e|xn--45br5cyl|xn--80adxhks|xn--80asehdb|xn--8y0a063a|xn--gckr3f0f|xn--mgb9awbf|xn--mgbab2bd|xn--mgbgu82a|xn--mgbpl2fh|xn--mgbt3dhd|xn--mk1bu44c|xn--ngbc5azd|xn--ngbe9e0a|xn--ogbpf8fl|xn--qcka1pmc|accountants|barclaycard|blackfriday|blockbuster|bridgestone|calvinklein|contractors|creditunion|engineering|enterprises|foodnetwork|investments|kerryhotels|lamborghini|motorcycles|olayangroup|photography|playstation|productions|progressive|redumbrella|rightathome|williamhill|xn--11b4c3d|xn--1ck2e1b|xn--1qqw23a|xn--2scrj9c|xn--3bst00m|xn--3ds443g|xn--3hcrj9c|xn--42c2d9a|xn--45brj9c|xn--55qw42g|xn--6frz82g|xn--80ao21a|xn--9krt00a|xn--cck2b3b|xn--czr694b|xn--d1acj3b|xn--efvy88h|xn--estv75g|xn--fct429k|xn--fjq720a|xn--flw351e|xn--g2xx48c|xn--gecrj9c|xn--gk3at1e|xn--h2brj9c|xn--hxt814e|xn--imr513n|xn--j6w193g|xn--jvr189m|xn--kprw13d|xn--kpry57d|xn--kpu716f|xn--mgbbh1a|xn--mgbtx2b|xn--mix891f|xn--nyqy26a|xn--otu796d|xn--pbt977c|xn--pgbs0dh|xn--q9jyb4c|xn--rhqv96g|xn--rovu88b|xn--s9brj9c|xn--ses554g|xn--t60b56a|xn--vuq861b|xn--w4rs40l|xn--xhq521b|xn--zfr164b|சிங்கப்பூர்|accountant|apartments|associates|basketball|bnpparibas|boehringer|capitalone|consulting|creditcard|cuisinella|eurovision|extraspace|foundation|healthcare|immobilien|industries|management|mitsubishi|nationwide|newholland|nextdirect|onyourside|properties|protection|prudential|realestate|republican|restaurant|schaeffler|swiftcover|tatamotors|technology|telefonica|university|vistaprint|vlaanderen|volkswagen|xn--30rr7y|xn--3pxu8k|xn--45q11c|xn--4gbrim|xn--55qx5d|xn--5tzm5g|xn--80aswg|xn--90a3ac|xn--9dbq2a|xn--9et52u|xn--c2br7g|xn--cg4bki|xn--czrs0t|xn--czru2d|xn--fiq64b|xn--fiqs8s|xn--fiqz9s|xn--io0a7i|xn--kput3i|xn--mxtq1m|xn--o3cw4h|xn--pssy2u|xn--unup4y|xn--wgbh1c|xn--wgbl6a|xn--y9a3aq|accenture|alfaromeo|allfinanz|amsterdam|analytics|aquarelle|barcelona|bloomberg|christmas|community|directory|education|equipment|fairwinds|financial|firestone|fresenius|frontdoor|fujixerox|furniture|goldpoint|hisamitsu|homedepot|homegoods|homesense|honeywell|institute|insurance|kuokgroup|ladbrokes|lancaster|landrover|lifestyle|marketing|marshalls|melbourne|microsoft|panasonic|passagens|pramerica|richardli|scjohnson|shangrila|solutions|statebank|statefarm|stockholm|travelers|vacations|xn--90ais|xn--c1avg|xn--d1alf|xn--e1a4c|xn--fhbei|xn--j1aef|xn--j1amh|xn--l1acc|xn--ngbrx|xn--nqv7f|xn--p1acf|xn--tckwe|xn--vhquv|yodobashi|abudhabi|airforce|allstate|attorney|barclays|barefoot|bargains|baseball|boutique|bradesco|broadway|brussels|budapest|builders|business|capetown|catering|catholic|chrysler|cipriani|cityeats|cleaning|clinique|clothing|commbank|computer|delivery|deloitte|democrat|diamonds|discount|discover|download|engineer|ericsson|esurance|etisalat|everbank|exchange|feedback|fidelity|firmdale|football|frontier|goodyear|grainger|graphics|guardian|hdfcbank|helsinki|holdings|hospital|infiniti|ipiranga|istanbul|jpmorgan|lighting|lundbeck|marriott|maserati|mckinsey|memorial|merckmsd|mortgage|movistar|observer|partners|pharmacy|pictures|plumbing|property|redstone|reliance|saarland|samsclub|security|services|shopping|showtime|softbank|software|stcgroup|supplies|symantec|training|uconnect|vanguard|ventures|verisign|woodside|xn--90ae|xn--node|xn--p1ai|xn--qxam|yokohama|السعودية|abogado|academy|agakhan|alibaba|android|athleta|auction|audible|auspost|avianca|banamex|bauhaus|bentley|bestbuy|booking|brother|bugatti|capital|caravan|careers|cartier|channel|charity|chintai|citadel|clubmed|college|cologne|comcast|company|compare|contact|cooking|corsica|country|coupons|courses|cricket|cruises|dentist|digital|domains|exposed|express|farmers|fashion|ferrari|ferrero|finance|fishing|fitness|flights|florist|flowers|forsale|frogans|fujitsu|gallery|genting|godaddy|grocery|guitars|hamburg|hangout|hitachi|holiday|hosting|hoteles|hotmail|hyundai|iselect|ismaili|jewelry|juniper|kitchen|komatsu|lacaixa|lancome|lanxess|lasalle|latrobe|leclerc|liaison|limited|lincoln|markets|metlife|monster|netbank|netflix|network|neustar|okinawa|oldnavy|organic|origins|philips|pioneer|politie|realtor|recipes|rentals|reviews|rexroth|samsung|sandvik|schmidt|schwarz|science|shiksha|shriram|singles|staples|starhub|storage|support|surgery|systems|temasek|theater|theatre|tickets|tiffany|toshiba|trading|walmart|wanggou|watches|weather|website|wedding|whoswho|windows|winners|xfinity|yamaxun|youtube|zuerich|католик|اتصالات|الجزائر|العليان|پاکستان|كاثوليك|موبايلي|இந்தியா|abarth|abbott|abbvie|active|africa|agency|airbus|airtel|alipay|alsace|alstom|anquan|aramco|author|bayern|beauty|berlin|bharti|blanco|bostik|boston|broker|camera|career|caseih|casino|center|chanel|chrome|church|circle|claims|clinic|coffee|comsec|condos|coupon|credit|cruise|dating|datsun|dealer|degree|dental|design|direct|doctor|dunlop|dupont|durban|emerck|energy|estate|events|expert|family|flickr|futbol|gallup|garden|george|giving|global|google|gratis|health|hermes|hiphop|hockey|hotels|hughes|imamat|insure|intuit|jaguar|joburg|juegos|kaufen|kinder|kindle|kosher|lancia|latino|lawyer|lefrak|living|locker|london|luxury|madrid|maison|makeup|market|mattel|mobile|mobily|monash|mormon|moscow|museum|mutual|nagoya|natura|nissan|nissay|norton|nowruz|office|olayan|online|oracle|orange|otsuka|pfizer|photos|physio|piaget|pictet|quebec|racing|realty|reisen|repair|report|review|rocher|rogers|ryukyu|safety|sakura|sanofi|school|schule|search|secure|select|shouji|soccer|social|stream|studio|supply|suzuki|swatch|sydney|taipei|taobao|target|tattoo|tennis|tienda|tjmaxx|tkmaxx|toyota|travel|unicom|viajes|viking|villas|virgin|vision|voting|voyage|vuelos|walter|warman|webcam|xihuan|yachts|yandex|zappos|москва|онлайн|ابوظبي|ارامكو|الاردن|المغرب|امارات|فلسطين|مليسيا|भारतम्|இலங்கை|ファッション|actor|adult|aetna|amfam|amica|apple|archi|audio|autos|azure|baidu|beats|bible|bingo|black|boats|bosch|build|canon|cards|chase|cheap|cisco|citic|click|cloud|coach|codes|crown|cymru|dabur|dance|deals|delta|dodge|drive|dubai|earth|edeka|email|epost|epson|faith|fedex|final|forex|forum|gallo|games|gifts|gives|glade|glass|globo|gmail|green|gripe|group|gucci|guide|homes|honda|horse|house|hyatt|ikano|intel|irish|iveco|jetzt|koeln|kyoto|lamer|lease|legal|lexus|lilly|linde|lipsy|lixil|loans|locus|lotte|lotto|lupin|macys|mango|media|miami|money|mopar|movie|nadex|nexus|nikon|ninja|nokia|nowtv|omega|osaka|paris|parts|party|phone|photo|pizza|place|poker|praxi|press|prime|promo|quest|radio|rehab|reise|ricoh|rocks|rodeo|rugby|salon|sener|seven|sharp|shell|shoes|skype|sling|smart|smile|solar|space|sport|stada|store|study|style|sucks|swiss|tatar|tires|tirol|tmall|today|tokyo|tools|toray|total|tours|trade|trust|tunes|tushu|ubank|vegas|video|vodka|volvo|wales|watch|weber|weibo|works|world|xerox|yahoo|zippo|ایران|بازار|بھارت|سودان|سورية|همراه|भारोत|संगठन|বাংলা|భారత్|ഭാരതം|嘉里大酒店|aarp|able|adac|aero|aigo|akdn|ally|amex|arab|army|arpa|arte|asda|asia|audi|auto|baby|band|bank|bbva|beer|best|bike|bing|blog|blue|bofa|bond|book|buzz|cafe|call|camp|care|cars|casa|case|cash|cbre|cern|chat|citi|city|club|cool|coop|cyou|data|date|dclk|deal|dell|desi|diet|dish|docs|doha|duck|duns|dvag|erni|fage|fail|fans|farm|fast|fiat|fido|film|fire|fish|flir|food|ford|free|fund|game|gbiz|gent|ggee|gift|gmbh|gold|golf|goog|guge|guru|hair|haus|hdfc|help|here|hgtv|host|hsbc|icbc|ieee|imdb|immo|info|itau|java|jeep|jobs|jprs|kddi|kiwi|kpmg|kred|land|lego|lgbt|lidl|life|like|limo|link|live|loan|loft|love|ltda|luxe|maif|meet|meme|menu|mini|mint|mobi|moda|moto|name|navy|news|next|nico|nike|ollo|open|page|pars|pccw|pics|ping|pink|play|plus|pohl|porn|post|prod|prof|qpon|raid|read|reit|rent|rest|rich|rmit|room|rsvp|ruhr|safe|sale|sarl|save|saxo|scor|scot|seat|seek|sexy|shaw|shia|shop|show|silk|sina|site|skin|sncf|sohu|song|sony|spot|star|surf|talk|taxi|team|tech|teva|tiaa|tips|town|toys|tube|vana|visa|viva|vivo|vote|voto|wang|weir|wien|wiki|wine|work|xbox|yoga|zara|zero|zone|дети|сайт|بارت|بيتك|ڀارت|تونس|شبكة|عراق|عمان|موقع|भारत|ভারত|ভাৰত|ਭਾਰਤ|ભારત|ଭାରତ|ಭಾರತ|ලංකා|グーグル|クラウド|ポイント|大众汽车|组织机构|電訊盈科|香格里拉|aaa|abb|abc|aco|ads|aeg|afl|aig|anz|aol|app|art|aws|axa|bar|bbc|bbt|bcg|bcn|bet|bid|bio|biz|bms|bmw|bnl|bom|boo|bot|box|buy|bzh|cab|cal|cam|car|cat|cba|cbn|cbs|ceb|ceo|cfa|cfd|com|crs|csc|dad|day|dds|dev|dhl|diy|dnp|dog|dot|dtv|dvr|eat|eco|edu|esq|eus|fan|fit|fly|foo|fox|frl|ftr|fun|fyi|gal|gap|gdn|gea|gle|gmo|gmx|goo|gop|got|gov|hbo|hiv|hkt|hot|how|ibm|ice|icu|ifm|inc|ing|ink|int|ist|itv|jcb|jcp|jio|jll|jmp|jnj|jot|joy|kfh|kia|kim|kpn|krd|lat|law|lds|llc|lol|lpl|ltd|man|map|mba|med|men|mil|mit|mlb|mls|mma|moe|moi|mom|mov|msd|mtn|mtr|nab|nba|nec|net|new|nfl|ngo|nhk|now|nra|nrw|ntt|nyc|obi|off|one|ong|onl|ooo|org|ott|ovh|pay|pet|phd|pid|pin|pnc|pro|pru|pub|pwc|qvc|red|ren|ril|rio|rip|run|rwe|sap|sas|sbi|sbs|sca|scb|ses|sew|sex|sfr|ski|sky|soy|srl|srt|stc|tab|tax|tci|tdk|tel|thd|tjx|top|trv|tui|tvs|ubs|uno|uol|ups|vet|vig|vin|vip|wed|win|wme|wow|wtc|wtf|xin|xxx|xyz|you|yun|zip|бел|ком|қаз|мкд|мон|орг|рус|срб|укр|հայ|קום|عرب|قطر|كوم|مصر|कॉम|नेट|คอม|ไทย|ストア|セール|みんな|中文网|天主教|我爱你|新加坡|淡马锡|诺基亚|飞利浦|ac|ad|ae|af|ag|ai|al|am|ao|aq|ar|as|at|au|aw|ax|az|ba|bb|bd|be|bf|bg|bh|bi|bj|bm|bn|bo|br|bs|bt|bv|bw|by|bz|ca|cc|cd|cf|cg|ch|ci|ck|cl|cm|cn|co|cr|cu|cv|cw|cx|cy|cz|de|dj|dk|dm|do|dz|ec|ee|eg|er|es|et|eu|fi|fj|fk|fm|fo|fr|ga|gb|gd|ge|gf|gg|gh|gi|gl|gm|gn|gp|gq|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|in|io|iq|ir|is|it|je|jm|jo|jp|ke|kg|kh|ki|km|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|na|nc|ne|nf|ng|ni|nl|no|np|nr|nu|nz|om|pa|pe|pf|pg|ph|pk|pl|pm|pn|pr|ps|pt|pw|py|qa|re|ro|rs|ru|rw|sa|sb|sc|sd|se|sg|sh|si|sj|sk|sl|sm|sn|so|sr|st|su|sv|sx|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|to|tr|tt|tv|tw|tz|ua|ug|uk|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|za|zm|zw|ελ|бг|ею|рф|გე|닷넷|닷컴|삼성|한국|コム|世界|中信|中国|中國|企业|佛山|信息|健康|八卦|公司|公益|台湾|台灣|商城|商店|商标|嘉里|在线|大拿|娱乐|家電|工行|广东|微博|慈善|手机|手表|招聘|政务|政府|新闻|时尚|書籍|机构|游戏|澳門|点看|珠宝|移动|网址|网店|网站|网络|联通|谷歌|购物|通販|集团|食品|餐厅|香港)/;\n\n//# sourceMappingURL=tld-regex.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { <PERSON><PERSON> } from \"./matcher\";\nimport { alphaNumericAndMarksCharsStr, domainNameCharRegex } from \"../regex-lib\";\nimport { EmailMatch } from \"../match/email-match\";\nimport { throwUnhandledCaseError } from '../utils';\nimport { tldRegex } from \"./tld-regex\";\n// For debugging: search for other \"For debugging\" lines\n// import CliTable from 'cli-table';\n/**\n * @class Autolinker.matcher.Email\n * @extends Autolinker.matcher.Matcher\n *\n * Matcher to find email matches in an input string.\n *\n * See this class's superclass ({@link Autolinker.matcher.Matcher}) for more details.\n */\nvar EmailMatcher = /** @class */ (function (_super) {\n    tslib_1.__extends(EmailMatcher, _super);\n    function EmailMatcher() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        /**\n         * Valid characters that can be used in the \"local\" part of an email address,\n         * i.e. the \"name\" part of \"<EMAIL>\"\n         */\n        _this.localPartCharRegex = new RegExp(\"[\" + alphaNumericAndMarksCharsStr + \"!#$%&'*+/=?^_`{|}~-]\");\n        /**\n         * Stricter TLD regex which adds a beginning and end check to ensure\n         * the string is a valid TLD\n         */\n        _this.strictTldRegex = new RegExp(\"^\" + tldRegex.source + \"$\");\n        return _this;\n    }\n    /**\n     * @inheritdoc\n     */\n    EmailMatcher.prototype.parseMatches = function (text) {\n        var tagBuilder = this.tagBuilder, localPartCharRegex = this.localPartCharRegex, strictTldRegex = this.strictTldRegex, matches = [], len = text.length, noCurrentEmailMatch = new CurrentEmailMatch();\n        // for matching a 'mailto:' prefix\n        var mailtoTransitions = {\n            'm': 'a',\n            'a': 'i',\n            'i': 'l',\n            'l': 't',\n            't': 'o',\n            'o': ':',\n        };\n        var charIdx = 0, state = 0 /* NonEmailMatch */, currentEmailMatch = noCurrentEmailMatch;\n        // For debugging: search for other \"For debugging\" lines\n        // const table = new CliTable( {\n        // \thead: [ 'charIdx', 'char', 'state', 'charIdx', 'currentEmailAddress.idx', 'hasDomainDot' ]\n        // } );\n        while (charIdx < len) {\n            var char = text.charAt(charIdx);\n            // For debugging: search for other \"For debugging\" lines\n            // table.push( \n            // \t[ charIdx, char, State[ state ], charIdx, currentEmailAddress.idx, currentEmailAddress.hasDomainDot ] \n            // );\n            switch (state) {\n                case 0 /* NonEmailMatch */:\n                    stateNonEmailAddress(char);\n                    break;\n                case 1 /* Mailto */:\n                    stateMailTo(text.charAt(charIdx - 1), char);\n                    break;\n                case 2 /* LocalPart */:\n                    stateLocalPart(char);\n                    break;\n                case 3 /* LocalPartDot */:\n                    stateLocalPartDot(char);\n                    break;\n                case 4 /* AtSign */:\n                    stateAtSign(char);\n                    break;\n                case 5 /* DomainChar */:\n                    stateDomainChar(char);\n                    break;\n                case 6 /* DomainHyphen */:\n                    stateDomainHyphen(char);\n                    break;\n                case 7 /* DomainDot */:\n                    stateDomainDot(char);\n                    break;\n                default:\n                    throwUnhandledCaseError(state);\n            }\n            // For debugging: search for other \"For debugging\" lines\n            // table.push( \n            // \t[ charIdx, char, State[ state ], charIdx, currentEmailAddress.idx, currentEmailAddress.hasDomainDot ] \n            // );\n            charIdx++;\n        }\n        // Capture any valid match at the end of the string\n        captureMatchIfValidAndReset();\n        // For debugging: search for other \"For debugging\" lines\n        //console.log( '\\n' + table.toString() );\n        return matches;\n        // Handles the state when we're not in an email address\n        function stateNonEmailAddress(char) {\n            if (char === 'm') {\n                beginEmailMatch(1 /* Mailto */);\n            }\n            else if (localPartCharRegex.test(char)) {\n                beginEmailMatch();\n            }\n            else {\n                // not an email address character, continue\n            }\n        }\n        // Handles if we're reading a 'mailto:' prefix on the string\n        function stateMailTo(prevChar, char) {\n            if (prevChar === ':') {\n                // We've reached the end of the 'mailto:' prefix\n                if (localPartCharRegex.test(char)) {\n                    state = 2 /* LocalPart */;\n                    currentEmailMatch = new CurrentEmailMatch(tslib_1.__assign({}, currentEmailMatch, { hasMailtoPrefix: true }));\n                }\n                else {\n                    // we've matched 'mailto:' but didn't get anything meaningful\n                    // immediately afterwards (for example, we encountered a \n                    // space character, or an '@' character which formed 'mailto:@'\n                    resetToNonEmailMatchState();\n                }\n            }\n            else if (mailtoTransitions[prevChar] === char) {\n                // We're currently reading the 'mailto:' prefix, stay in\n                // Mailto state\n            }\n            else if (localPartCharRegex.test(char)) {\n                // We we're reading a prefix of 'mailto:', but encountered a\n                // different character that didn't continue the prefix\n                state = 2 /* LocalPart */;\n            }\n            else if (char === '.') {\n                // We we're reading a prefix of 'mailto:', but encountered a\n                // dot character\n                state = 3 /* LocalPartDot */;\n            }\n            else if (char === '@') {\n                // We we're reading a prefix of 'mailto:', but encountered a\n                // an @ character\n                state = 4 /* AtSign */;\n            }\n            else {\n                // not an email address character, return to \"NonEmailAddress\" state\n                resetToNonEmailMatchState();\n            }\n        }\n        // Handles the state when we're currently in the \"local part\" of an \n        // email address (as opposed to the \"domain part\")\n        function stateLocalPart(char) {\n            if (char === '.') {\n                state = 3 /* LocalPartDot */;\n            }\n            else if (char === '@') {\n                state = 4 /* AtSign */;\n            }\n            else if (localPartCharRegex.test(char)) {\n                // stay in the \"local part\" of the email address\n            }\n            else {\n                // not an email address character, return to \"NonEmailAddress\" state\n                resetToNonEmailMatchState();\n            }\n        }\n        // Handles the state where we've read \n        function stateLocalPartDot(char) {\n            if (char === '.') {\n                // We read a second '.' in a row, not a valid email address \n                // local part\n                resetToNonEmailMatchState();\n            }\n            else if (char === '@') {\n                // We read the '@' character immediately after a dot ('.'), not \n                // an email address\n                resetToNonEmailMatchState();\n            }\n            else if (localPartCharRegex.test(char)) {\n                state = 2 /* LocalPart */;\n            }\n            else {\n                // Anything else, not an email address\n                resetToNonEmailMatchState();\n            }\n        }\n        function stateAtSign(char) {\n            if (domainNameCharRegex.test(char)) {\n                state = 5 /* DomainChar */;\n            }\n            else {\n                // Anything else, not an email address\n                resetToNonEmailMatchState();\n            }\n        }\n        function stateDomainChar(char) {\n            if (char === '.') {\n                state = 7 /* DomainDot */;\n            }\n            else if (char === '-') {\n                state = 6 /* DomainHyphen */;\n            }\n            else if (domainNameCharRegex.test(char)) {\n                // Stay in the DomainChar state\n            }\n            else {\n                // Anything else, we potentially matched if the criteria has\n                // been met\n                captureMatchIfValidAndReset();\n            }\n        }\n        function stateDomainHyphen(char) {\n            if (char === '-' || char === '.') {\n                // Not valid to have two hyphens (\"--\") or hypen+dot (\"-.\")\n                captureMatchIfValidAndReset();\n            }\n            else if (domainNameCharRegex.test(char)) {\n                state = 5 /* DomainChar */;\n            }\n            else {\n                // Anything else\n                captureMatchIfValidAndReset();\n            }\n        }\n        function stateDomainDot(char) {\n            if (char === '.' || char === '-') {\n                // not valid to have two dots (\"..\") or dot+hypen (\".-\")\n                captureMatchIfValidAndReset();\n            }\n            else if (domainNameCharRegex.test(char)) {\n                state = 5 /* DomainChar */;\n                // After having read a '.' and then a valid domain character,\n                // we now know that the domain part of the email is valid, and\n                // we have found at least a partial EmailMatch (however, the\n                // email address may have additional characters from this point)\n                currentEmailMatch = new CurrentEmailMatch(tslib_1.__assign({}, currentEmailMatch, { hasDomainDot: true }));\n            }\n            else {\n                // Anything else\n                captureMatchIfValidAndReset();\n            }\n        }\n        function beginEmailMatch(newState) {\n            if (newState === void 0) { newState = 2 /* LocalPart */; }\n            state = newState;\n            currentEmailMatch = new CurrentEmailMatch({ idx: charIdx });\n        }\n        function resetToNonEmailMatchState() {\n            state = 0 /* NonEmailMatch */;\n            currentEmailMatch = noCurrentEmailMatch;\n        }\n        /*\n         * Captures the current email address as an EmailMatch if it's valid,\n         * and resets the state to read another email address.\n         */\n        function captureMatchIfValidAndReset() {\n            if (currentEmailMatch.hasDomainDot) { // we need at least one dot in the domain to be considered a valid email address\n                var matchedText = text.slice(currentEmailMatch.idx, charIdx);\n                // If we read a '.' or '-' char that ended the email address\n                // (valid domain name characters, but only valid email address\n                // characters if they are followed by something else), strip \n                // it off now\n                if (/[-.]$/.test(matchedText)) {\n                    matchedText = matchedText.slice(0, -1);\n                }\n                var emailAddress = currentEmailMatch.hasMailtoPrefix\n                    ? matchedText.slice('mailto:'.length)\n                    : matchedText;\n                // if the email address has a valid TLD, add it to the list of matches\n                if (doesEmailHaveValidTld(emailAddress)) {\n                    matches.push(new EmailMatch({\n                        tagBuilder: tagBuilder,\n                        matchedText: matchedText,\n                        offset: currentEmailMatch.idx,\n                        email: emailAddress\n                    }));\n                }\n            }\n            resetToNonEmailMatchState();\n            /**\n             * Determines if the given email address has a valid TLD or not\n             * @param {string} emailAddress - email address\n             * @return {Boolean} - true is email have valid TLD, false otherwise\n             */\n            function doesEmailHaveValidTld(emailAddress) {\n                var emailAddressTld = emailAddress.split('.').pop() || '';\n                var emailAddressNormalized = emailAddressTld.toLowerCase();\n                var isValidTld = strictTldRegex.test(emailAddressNormalized);\n                return isValidTld;\n            }\n        }\n    };\n    return EmailMatcher;\n}(Matcher));\nexport { EmailMatcher };\nvar CurrentEmailMatch = /** @class */ (function () {\n    function CurrentEmailMatch(cfg) {\n        if (cfg === void 0) { cfg = {}; }\n        this.idx = cfg.idx !== undefined ? cfg.idx : -1;\n        this.hasMailtoPrefix = !!cfg.hasMailtoPrefix;\n        this.hasDomainDot = !!cfg.hasDomainDot;\n    }\n    return CurrentEmailMatch;\n}());\n\n//# sourceMappingURL=email-matcher.js.map\n", "import { alphaCharsStr } from \"../regex-lib\";\n/**\n * @private\n * @class Autolinker.matcher.UrlMatchValidator\n * @singleton\n *\n * Used by Autolinker to filter out false URL positives from the\n * {@link Autolinker.matcher.Url UrlMatcher}.\n *\n * Due to the limitations of regular expressions (including the missing feature\n * of look-behinds in JS regular expressions), we cannot always determine the\n * validity of a given match. This class applies a bit of additional logic to\n * filter out any false positives that have been matched by the\n * {@link Autolinker.matcher.Url UrlMatcher}.\n */\nvar UrlMatchValidator = /** @class */ (function () {\n    function UrlMatchValidator() {\n    }\n    /**\n     * Determines if a given URL match found by the {@link Autolinker.matcher.Url UrlMatcher}\n     * is valid. Will return `false` for:\n     *\n     * 1) URL matches which do not have at least have one period ('.') in the\n     *    domain name (effectively skipping over matches like \"abc:def\").\n     *    However, URL matches with a protocol will be allowed (ex: 'http://localhost')\n     * 2) URL matches which do not have at least one word character in the\n     *    domain name (effectively skipping over matches like \"git:1.0\").\n     * 3) A protocol-relative url match (a URL beginning with '//') whose\n     *    previous character is a word character (effectively skipping over\n     *    strings like \"abc//google.com\")\n     *\n     * Otherwise, returns `true`.\n     *\n     * @param {String} urlMatch The matched URL, if there was one. Will be an\n     *   empty string if the match is not a URL match.\n     * @param {String} protocolUrlMatch The match URL string for a protocol\n     *   match. Ex: 'http://yahoo.com'. This is used to match something like\n     *   'http://localhost', where we won't double check that the domain name\n     *   has at least one '.' in it.\n     * @return {Boolean} `true` if the match given is valid and should be\n     *   processed, or `false` if the match is invalid and/or should just not be\n     *   processed.\n     */\n    UrlMatchValidator.isValid = function (urlMatch, protocolUrlMatch) {\n        if ((protocolUrlMatch && !this.isValidUriScheme(protocolUrlMatch)) ||\n            this.urlMatchDoesNotHaveProtocolOrDot(urlMatch, protocolUrlMatch) || // At least one period ('.') must exist in the URL match for us to consider it an actual URL, *unless* it was a full protocol match (like 'http://localhost')\n            (this.urlMatchDoesNotHaveAtLeastOneWordChar(urlMatch, protocolUrlMatch) && // At least one letter character must exist in the domain name after a protocol match. Ex: skip over something like \"git:1.0\"\n                !this.isValidIpAddress(urlMatch)) || // Except if it's an IP address\n            this.containsMultipleDots(urlMatch)) {\n            return false;\n        }\n        return true;\n    };\n    UrlMatchValidator.isValidIpAddress = function (uriSchemeMatch) {\n        var newRegex = new RegExp(this.hasFullProtocolRegex.source + this.ipRegex.source);\n        var uriScheme = uriSchemeMatch.match(newRegex);\n        return uriScheme !== null;\n    };\n    UrlMatchValidator.containsMultipleDots = function (urlMatch) {\n        var stringBeforeSlash = urlMatch;\n        if (this.hasFullProtocolRegex.test(urlMatch)) {\n            stringBeforeSlash = urlMatch.split('://')[1];\n        }\n        return stringBeforeSlash.split('/')[0].indexOf(\"..\") > -1;\n    };\n    /**\n     * Determines if the URI scheme is a valid scheme to be autolinked. Returns\n     * `false` if the scheme is 'javascript:' or 'vbscript:'\n     *\n     * @private\n     * @param {String} uriSchemeMatch The match URL string for a full URI scheme\n     *   match. Ex: 'http://yahoo.com' or 'mailto:<EMAIL>'.\n     * @return {Boolean} `true` if the scheme is a valid one, `false` otherwise.\n     */\n    UrlMatchValidator.isValidUriScheme = function (uriSchemeMatch) {\n        var uriSchemeMatchArr = uriSchemeMatch.match(this.uriSchemeRegex), uriScheme = uriSchemeMatchArr && uriSchemeMatchArr[0].toLowerCase();\n        return (uriScheme !== 'javascript:' && uriScheme !== 'vbscript:');\n    };\n    /**\n     * Determines if a URL match does not have either:\n     *\n     * a) a full protocol (i.e. 'http://'), or\n     * b) at least one dot ('.') in the domain name (for a non-full-protocol\n     *    match).\n     *\n     * Either situation is considered an invalid URL (ex: 'git:d' does not have\n     * either the '://' part, or at least one dot in the domain name. If the\n     * match was 'git:abc.com', we would consider this valid.)\n     *\n     * @private\n     * @param {String} urlMatch The matched URL, if there was one. Will be an\n     *   empty string if the match is not a URL match.\n     * @param {String} protocolUrlMatch The match URL string for a protocol\n     *   match. Ex: 'http://yahoo.com'. This is used to match something like\n     *   'http://localhost', where we won't double check that the domain name\n     *   has at least one '.' in it.\n     * @return {Boolean} `true` if the URL match does not have a full protocol,\n     *   or at least one dot ('.') in a non-full-protocol match.\n     */\n    UrlMatchValidator.urlMatchDoesNotHaveProtocolOrDot = function (urlMatch, protocolUrlMatch) {\n        return (!!urlMatch && (!protocolUrlMatch || !this.hasFullProtocolRegex.test(protocolUrlMatch)) && urlMatch.indexOf('.') === -1);\n    };\n    /**\n     * Determines if a URL match does not have at least one word character after\n     * the protocol (i.e. in the domain name).\n     *\n     * At least one letter character must exist in the domain name after a\n     * protocol match. Ex: skip over something like \"git:1.0\"\n     *\n     * @private\n     * @param {String} urlMatch The matched URL, if there was one. Will be an\n     *   empty string if the match is not a URL match.\n     * @param {String} protocolUrlMatch The match URL string for a protocol\n     *   match. Ex: 'http://yahoo.com'. This is used to know whether or not we\n     *   have a protocol in the URL string, in order to check for a word\n     *   character after the protocol separator (':').\n     * @return {Boolean} `true` if the URL match does not have at least one word\n     *   character in it after the protocol, `false` otherwise.\n     */\n    UrlMatchValidator.urlMatchDoesNotHaveAtLeastOneWordChar = function (urlMatch, protocolUrlMatch) {\n        if (urlMatch && protocolUrlMatch) {\n            return !this.hasWordCharAfterProtocolRegex.test(urlMatch);\n        }\n        else {\n            return false;\n        }\n    };\n    /**\n     * Regex to test for a full protocol, with the two trailing slashes. Ex: 'http://'\n     *\n     * @private\n     * @property {RegExp} hasFullProtocolRegex\n     */\n    UrlMatchValidator.hasFullProtocolRegex = /^[A-Za-z][-.+A-Za-z0-9]*:\\/\\//;\n    /**\n     * Regex to find the URI scheme, such as 'mailto:'.\n     *\n     * This is used to filter out 'javascript:' and 'vbscript:' schemes.\n     *\n     * @private\n     * @property {RegExp} uriSchemeRegex\n     */\n    UrlMatchValidator.uriSchemeRegex = /^[A-Za-z][-.+A-Za-z0-9]*:/;\n    /**\n     * Regex to determine if at least one word char exists after the protocol (i.e. after the ':')\n     *\n     * @private\n     * @property {RegExp} hasWordCharAfterProtocolRegex\n     */\n    UrlMatchValidator.hasWordCharAfterProtocolRegex = new RegExp(\":[^\\\\s]*?[\" + alphaCharsStr + \"]\");\n    /**\n     * Regex to determine if the string is a valid IP address\n     *\n     * @private\n     * @property {RegExp} ipRegex\n     */\n    UrlMatchValidator.ipRegex = /[0-9][0-9]?[0-9]?\\.[0-9][0-9]?[0-9]?\\.[0-9][0-9]?[0-9]?\\.[0-9][0-9]?[0-9]?(:[0-9]*)?\\/?$/;\n    return UrlMatchValidator;\n}());\nexport { UrlMatchValidator };\n\n//# sourceMappingURL=url-match-validator.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { Match<PERSON> } from \"./matcher\";\nimport { alphaNumericCharsStr, alphaNumericAndMarksCharsStr, getDomainNameStr } from \"../regex-lib\";\nimport { tldRegex } from \"./tld-regex\";\nimport { UrlMatch } from \"../match/url-match\";\nimport { UrlMatchValidator } from \"./url-match-validator\";\n/**\n * @class Autolinker.matcher.Url\n * @extends Autolinker.matcher.Matcher\n *\n * Matcher to find URL matches in an input string.\n *\n * See this class's superclass ({@link Autolinker.matcher.Matcher}) for more details.\n */\nvar UrlMatcher = /** @class */ (function (_super) {\n    tslib_1.__extends(UrlMatcher, _super);\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match instance,\n     *   specified in an Object (map).\n     */\n    function UrlMatcher(cfg) {\n        var _this = _super.call(this, cfg) || this;\n        /**\n         * @cfg {Object} stripPrefix (required)\n         *\n         * The Object form of {@link Autolinker#cfg-stripPrefix}.\n         */\n        _this.stripPrefix = { scheme: true, www: true }; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} stripTrailingSlash (required)\n         * @inheritdoc Autolinker#stripTrailingSlash\n         */\n        _this.stripTrailingSlash = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} decodePercentEncoding (required)\n         * @inheritdoc Autolinker#decodePercentEncoding\n         */\n        _this.decodePercentEncoding = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @protected\n         * @property {RegExp} matcherRegex\n         *\n         * The regular expression to match URLs with an optional scheme, port\n         * number, path, query string, and hash anchor.\n         *\n         * Example matches:\n         *\n         *     http://google.com\n         *     www.google.com\n         *     google.com/path/to/file?q1=1&q2=2#myAnchor\n         *\n         *\n         * This regular expression will have the following capturing groups:\n         *\n         * 1.  Group that matches a scheme-prefixed URL (i.e. 'http://google.com').\n         *     This is used to match scheme URLs with just a single word, such as\n         *     'http://localhost', where we won't double check that the domain name\n         *     has at least one dot ('.') in it.\n         * 2.  Group that matches a 'www.' prefixed URL. This is only matched if the\n         *     'www.' text was not prefixed by a scheme (i.e.: not prefixed by\n         *     'http://', 'ftp:', etc.)\n         * 3.  A protocol-relative ('//') match for the case of a 'www.' prefixed\n         *     URL. Will be an empty string if it is not a protocol-relative match.\n         *     We need to know the character before the '//' in order to determine\n         *     if it is a valid match or the // was in a string we don't want to\n         *     auto-link.\n         * 4.  Group that matches a known TLD (top level domain), when a scheme\n         *     or 'www.'-prefixed domain is not matched.\n         * 5.  A protocol-relative ('//') match for the case of a known TLD prefixed\n         *     URL. Will be an empty string if it is not a protocol-relative match.\n         *     See #3 for more info.\n         */\n        _this.matcherRegex = (function () {\n            var schemeRegex = /(?:[A-Za-z][-.+A-Za-z0-9]{0,63}:(?![A-Za-z][-.+A-Za-z0-9]{0,63}:\\/\\/)(?!\\d+\\/?)(?:\\/\\/)?)/, // match protocol, allow in format \"http://\" or \"mailto:\". However, do not match the first part of something like 'link:http://www.google.com' (i.e. don't match \"link:\"). Also, make sure we don't interpret 'google.com:8000' as if 'google.com' was a protocol here (i.e. ignore a trailing port number in this regex)\n            wwwRegex = /(?:www\\.)/, // starting with 'www.'\n            // Allow optional path, query string, and hash anchor, not ending in the following characters: \"?!:,.;\"\n            // http://blog.codinghorror.com/the-problem-with-urls/\n            urlSuffixRegex = new RegExp('[/?#](?:[' + alphaNumericAndMarksCharsStr + '\\\\-+&@#/%=~_()|\\'$*\\\\[\\\\]?!:,.;\\u2713]*[' + alphaNumericAndMarksCharsStr + '\\\\-+&@#/%=~_()|\\'$*\\\\[\\\\]\\u2713])?');\n            return new RegExp([\n                '(?:',\n                '(',\n                schemeRegex.source,\n                getDomainNameStr(2),\n                ')',\n                '|',\n                '(',\n                '(//)?',\n                wwwRegex.source,\n                getDomainNameStr(6),\n                ')',\n                '|',\n                '(',\n                '(//)?',\n                getDomainNameStr(10) + '\\\\.',\n                tldRegex.source,\n                '(?![-' + alphaNumericCharsStr + '])',\n                ')',\n                ')',\n                '(?::[0-9]+)?',\n                '(?:' + urlSuffixRegex.source + ')?' // match for path, query string, and/or hash anchor - optional\n            ].join(\"\"), 'gi');\n        })();\n        /**\n         * A regular expression to use to check the character before a protocol-relative\n         * URL match. We don't want to match a protocol-relative URL if it is part\n         * of another word.\n         *\n         * For example, we want to match something like \"Go to: //google.com\",\n         * but we don't want to match something like \"abc//google.com\"\n         *\n         * This regular expression is used to test the character before the '//'.\n         *\n         * @protected\n         * @type {RegExp} wordCharRegExp\n         */\n        _this.wordCharRegExp = new RegExp('[' + alphaNumericAndMarksCharsStr + ']');\n        _this.stripPrefix = cfg.stripPrefix;\n        _this.stripTrailingSlash = cfg.stripTrailingSlash;\n        _this.decodePercentEncoding = cfg.decodePercentEncoding;\n        return _this;\n    }\n    /**\n     * @inheritdoc\n     */\n    UrlMatcher.prototype.parseMatches = function (text) {\n        var matcherRegex = this.matcherRegex, stripPrefix = this.stripPrefix, stripTrailingSlash = this.stripTrailingSlash, decodePercentEncoding = this.decodePercentEncoding, tagBuilder = this.tagBuilder, matches = [], match;\n        var _loop_1 = function () {\n            var matchStr = match[0], schemeUrlMatch = match[1], wwwUrlMatch = match[4], wwwProtocolRelativeMatch = match[5], \n            //tldUrlMatch = match[ 8 ],  -- not needed at the moment\n            tldProtocolRelativeMatch = match[9], offset = match.index, protocolRelativeMatch = wwwProtocolRelativeMatch || tldProtocolRelativeMatch, prevChar = text.charAt(offset - 1);\n            if (!UrlMatchValidator.isValid(matchStr, schemeUrlMatch)) {\n                return \"continue\";\n            }\n            // If the match is preceded by an '@' character, then it is either\n            // an email address or a username. Skip these types of matches.\n            if (offset > 0 && prevChar === '@') {\n                return \"continue\";\n            }\n            // If it's a protocol-relative '//' match, but the character before the '//'\n            // was a word character (i.e. a letter/number), then we found the '//' in the\n            // middle of another word (such as \"asdf//asdf.com\"). In this case, skip the\n            // match.\n            if (offset > 0 && protocolRelativeMatch && this_1.wordCharRegExp.test(prevChar)) {\n                return \"continue\";\n            }\n            // If the URL ends with a question mark, don't include the question\n            // mark as part of the URL. We'll assume the question mark was the\n            // end of a sentence, such as: \"Going to google.com?\"\n            if (/\\?$/.test(matchStr)) {\n                matchStr = matchStr.substr(0, matchStr.length - 1);\n            }\n            // Handle a closing parenthesis or square bracket at the end of the \n            // match, and exclude it if there is not a matching open parenthesis \n            // or square bracket in the match itself.\n            if (this_1.matchHasUnbalancedClosingParen(matchStr)) {\n                matchStr = matchStr.substr(0, matchStr.length - 1); // remove the trailing \")\"\n            }\n            else {\n                // Handle an invalid character after the TLD\n                var pos = this_1.matchHasInvalidCharAfterTld(matchStr, schemeUrlMatch);\n                if (pos > -1) {\n                    matchStr = matchStr.substr(0, pos); // remove the trailing invalid chars\n                }\n            }\n            // The autolinker accepts many characters in a url's scheme (like `fake://test.com`).\n            // However, in cases where a URL is missing whitespace before an obvious link,\n            // (for example: `nowhitespacehttp://www.test.com`), we only want the match to start\n            // at the http:// part. We will check if the match contains a common scheme and then \n            // shift the match to start from there. \t\t\n            var foundCommonScheme = ['http://', 'https://'].find(function (commonScheme) { return !!schemeUrlMatch && schemeUrlMatch.indexOf(commonScheme) !== -1; });\n            if (foundCommonScheme) {\n                // If we found an overmatched URL, we want to find the index\n                // of where the match should start and shift the match to\n                // start from the beginning of the common scheme\n                var indexOfSchemeStart = matchStr.indexOf(foundCommonScheme);\n                matchStr = matchStr.substr(indexOfSchemeStart);\n                schemeUrlMatch = schemeUrlMatch.substr(indexOfSchemeStart);\n                offset = offset + indexOfSchemeStart;\n            }\n            var urlMatchType = schemeUrlMatch ? 'scheme' : (wwwUrlMatch ? 'www' : 'tld'), protocolUrlMatch = !!schemeUrlMatch;\n            matches.push(new UrlMatch({\n                tagBuilder: tagBuilder,\n                matchedText: matchStr,\n                offset: offset,\n                urlMatchType: urlMatchType,\n                url: matchStr,\n                protocolUrlMatch: protocolUrlMatch,\n                protocolRelativeMatch: !!protocolRelativeMatch,\n                stripPrefix: stripPrefix,\n                stripTrailingSlash: stripTrailingSlash,\n                decodePercentEncoding: decodePercentEncoding,\n            }));\n        };\n        var this_1 = this;\n        while ((match = matcherRegex.exec(text)) !== null) {\n            _loop_1();\n        }\n        return matches;\n    };\n    /**\n     * Determines if a match found has an unmatched closing parenthesis or\n     * square bracket. If so, the parenthesis or square bracket will be removed\n     * from the match itself, and appended after the generated anchor tag.\n     *\n     * A match may have an extra closing parenthesis at the end of the match\n     * because the regular expression must include parenthesis for URLs such as\n     * \"wikipedia.com/something_(disambiguation)\", which should be auto-linked.\n     *\n     * However, an extra parenthesis *will* be included when the URL itself is\n     * wrapped in parenthesis, such as in the case of:\n     *     \"(wikipedia.com/something_(disambiguation))\"\n     * In this case, the last closing parenthesis should *not* be part of the\n     * URL itself, and this method will return `true`.\n     *\n     * For square brackets in URLs such as in PHP arrays, the same behavior as\n     * parenthesis discussed above should happen:\n     *     \"[http://www.example.com/foo.php?bar[]=1&bar[]=2&bar[]=3]\"\n     * The closing square bracket should not be part of the URL itself, and this\n     * method will return `true`.\n     *\n     * @protected\n     * @param {String} matchStr The full match string from the {@link #matcherRegex}.\n     * @return {Boolean} `true` if there is an unbalanced closing parenthesis or\n     *   square bracket at the end of the `matchStr`, `false` otherwise.\n     */\n    UrlMatcher.prototype.matchHasUnbalancedClosingParen = function (matchStr) {\n        var endChar = matchStr.charAt(matchStr.length - 1);\n        var startChar;\n        if (endChar === ')') {\n            startChar = '(';\n        }\n        else if (endChar === ']') {\n            startChar = '[';\n        }\n        else {\n            return false; // not a close parenthesis or square bracket\n        }\n        // Find if there are the same number of open braces as close braces in\n        // the URL string, minus the last character (which we have already \n        // determined to be either ')' or ']'\n        var numOpenBraces = 0;\n        for (var i = 0, len = matchStr.length - 1; i < len; i++) {\n            var char = matchStr.charAt(i);\n            if (char === startChar) {\n                numOpenBraces++;\n            }\n            else if (char === endChar) {\n                numOpenBraces = Math.max(numOpenBraces - 1, 0);\n            }\n        }\n        // If the number of open braces matches the number of close braces in\n        // the URL minus the last character, then the match has *unbalanced*\n        // braces because of the last character. Example of unbalanced braces\n        // from the regex match:\n        //     \"http://example.com?a[]=1]\"\n        if (numOpenBraces === 0) {\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Determine if there's an invalid character after the TLD in a URL. Valid\n     * characters after TLD are ':/?#'. Exclude scheme matched URLs from this\n     * check.\n     *\n     * @protected\n     * @param {String} urlMatch The matched URL, if there was one. Will be an\n     *   empty string if the match is not a URL match.\n     * @param {String} schemeUrlMatch The match URL string for a scheme\n     *   match. Ex: 'http://yahoo.com'. This is used to match something like\n     *   'http://localhost', where we won't double check that the domain name\n     *   has at least one '.' in it.\n     * @return {Number} the position where the invalid character was found. If\n     *   no such character was found, returns -1\n     */\n    UrlMatcher.prototype.matchHasInvalidCharAfterTld = function (urlMatch, schemeUrlMatch) {\n        if (!urlMatch) {\n            return -1;\n        }\n        var offset = 0;\n        if (schemeUrlMatch) {\n            offset = urlMatch.indexOf(':');\n            urlMatch = urlMatch.slice(offset);\n        }\n        var re = new RegExp(\"^((.?\\/\\/)?[-.\" + alphaNumericAndMarksCharsStr + \"]*[-\" + alphaNumericAndMarksCharsStr + \"]\\\\.[-\" + alphaNumericAndMarksCharsStr + \"]+)\");\n        var res = re.exec(urlMatch);\n        if (res === null) {\n            return -1;\n        }\n        offset += res[1].length;\n        urlMatch = urlMatch.slice(res[1].length);\n        if (/^[^-.A-Za-z0-9:\\/?#]/.test(urlMatch)) {\n            return offset;\n        }\n        return -1;\n    };\n    return UrlMatcher;\n}(Matcher));\nexport { UrlMatcher };\n\n//# sourceMappingURL=url-matcher.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { <PERSON><PERSON> } from \"./matcher\";\nimport { alphaNumericAndMarksCharsStr } from \"../regex-lib\";\nimport { HashtagMatch } from \"../match/hashtag-match\";\n/**\n * @class Autolinker.matcher.Hashtag\n * @extends Autolinker.matcher.Matcher\n *\n * Matcher to find HashtagMatch matches in an input string.\n */\nvar HashtagMatcher = /** @class */ (function (_super) {\n    tslib_1.__extends(HashtagMatcher, _super);\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match instance,\n     *   specified in an Object (map).\n     */\n    function HashtagMatcher(cfg) {\n        var _this = _super.call(this, cfg) || this;\n        /**\n         * @cfg {String} serviceName\n         *\n         * The service to point hashtag matches to. See {@link Autolinker#hashtag}\n         * for available values.\n         */\n        _this.serviceName = 'twitter'; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * The regular expression to match Hashtags. Example match:\n         *\n         *     #asdf\n         *\n         * @protected\n         * @property {RegExp} matcherRegex\n         */\n        _this.matcherRegex = new RegExp(\"#[_\" + alphaNumericAndMarksCharsStr + \"]{1,139}(?![_\" + alphaNumericAndMarksCharsStr + \"])\", 'g'); // lookahead used to make sure we don't match something above 139 characters\n        /**\n         * The regular expression to use to check the character before a username match to\n         * make sure we didn't accidentally match an email address.\n         *\n         * For example, the string \"<EMAIL>\" should not match \"@asdf\" as a username.\n         *\n         * @protected\n         * @property {RegExp} nonWordCharRegex\n         */\n        _this.nonWordCharRegex = new RegExp('[^' + alphaNumericAndMarksCharsStr + ']');\n        _this.serviceName = cfg.serviceName;\n        return _this;\n    }\n    /**\n     * @inheritdoc\n     */\n    HashtagMatcher.prototype.parseMatches = function (text) {\n        var matcherRegex = this.matcherRegex, nonWordCharRegex = this.nonWordCharRegex, serviceName = this.serviceName, tagBuilder = this.tagBuilder, matches = [], match;\n        while ((match = matcherRegex.exec(text)) !== null) {\n            var offset = match.index, prevChar = text.charAt(offset - 1);\n            // If we found the match at the beginning of the string, or we found the match\n            // and there is a whitespace char in front of it (meaning it is not a '#' char\n            // in the middle of a word), then it is a hashtag match.\n            if (offset === 0 || nonWordCharRegex.test(prevChar)) {\n                var matchedText = match[0], hashtag = match[0].slice(1); // strip off the '#' character at the beginning\n                matches.push(new HashtagMatch({\n                    tagBuilder: tagBuilder,\n                    matchedText: matchedText,\n                    offset: offset,\n                    serviceName: serviceName,\n                    hashtag: hashtag\n                }));\n            }\n        }\n        return matches;\n    };\n    return HashtagMatcher;\n}(Matcher));\nexport { HashtagMatcher };\n\n//# sourceMappingURL=hashtag-matcher.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { Match<PERSON> } from \"./matcher\";\nimport { PhoneMatch } from \"../match/phone-match\";\n/**\n * @class Autolinker.matcher.Phone\n * @extends Autolinker.matcher.Matcher\n *\n * Matcher to find Phone number matches in an input string.\n *\n * See this class's superclass ({@link Autolinker.matcher.Matcher}) for more\n * details.\n */\nvar PhoneMatcher = /** @class */ (function (_super) {\n    tslib_1.__extends(PhoneMatcher, _super);\n    function PhoneMatcher() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        /**\n         * The regular expression to match Phone numbers. Example match:\n         *\n         *     (*************\n         *\n         * This regular expression has the following capturing groups:\n         *\n         * 1 or 2. The prefixed '+' sign, if there is one.\n         *\n         * @protected\n         * @property {RegExp} matcherRegex\n         */\n        _this.matcherRegex = /(?:(?:(?:(\\+)?\\d{1,3}[-\\040.]?)?\\(?\\d{3}\\)?[-\\040.]?\\d{3}[-\\040.]?\\d{4})|(?:(\\+)(?:9[976]\\d|8[987530]\\d|6[987]\\d|5[90]\\d|42\\d|3[875]\\d|2[98654321]\\d|9[8543210]|8[6421]|6[6543210]|5[87654321]|4[987654310]|3[9643210]|2[70]|7|1)[-\\040.]?(?:\\d[-\\040.]?){6,12}\\d+))([,;]+[0-9]+#?)*/g;\n        return _this;\n    }\n    // ex: (*************, ************, ************, +18004441234,,;,10226420346#,\n    // +****************, 10226420346#, **************,1022,64,20346#\n    /**\n     * @inheritdoc\n     */\n    PhoneMatcher.prototype.parseMatches = function (text) {\n        var matcherRegex = this.matcherRegex, tagBuilder = this.tagBuilder, matches = [], match;\n        while ((match = matcherRegex.exec(text)) !== null) {\n            // Remove non-numeric values from phone number string\n            var matchedText = match[0], cleanNumber = matchedText.replace(/[^0-9,;#]/g, ''), // strip out non-digit characters exclude comma semicolon and #\n            plusSign = !!(match[1] || match[2]), // match[ 1 ] or match[ 2 ] is the prefixed plus sign, if there is one\n            before = match.index == 0 ? '' : text.substr(match.index - 1, 1), after = text.substr(match.index + matchedText.length, 1), contextClear = !before.match(/\\d/) && !after.match(/\\d/);\n            if (this.testMatch(match[3]) && this.testMatch(matchedText) && contextClear) {\n                matches.push(new PhoneMatch({\n                    tagBuilder: tagBuilder,\n                    matchedText: matchedText,\n                    offset: match.index,\n                    number: cleanNumber,\n                    plusSign: plusSign\n                }));\n            }\n        }\n        return matches;\n    };\n    PhoneMatcher.prototype.testMatch = function (text) {\n        return /\\D/.test(text);\n    };\n    return PhoneMatcher;\n}(Matcher));\nexport { PhoneMatcher };\n\n//# sourceMappingURL=phone-matcher.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { <PERSON><PERSON> } from \"./matcher\";\nimport { alphaNumericAndMarksCharsStr } from \"../regex-lib\";\nimport { MentionMatch } from \"../match/mention-match\";\n/**\n * @class Autolinker.matcher.Mention\n * @extends Autolinker.matcher.Matcher\n *\n * Matcher to find/replace username matches in an input string.\n */\nvar MentionMatcher = /** @class */ (function (_super) {\n    tslib_1.__extends(MentionMatcher, _super);\n    /**\n     * @method constructor\n     * @param {Object} cfg The configuration properties for the Match instance,\n     *   specified in an Object (map).\n     */\n    function MentionMatcher(cfg) {\n        var _this = _super.call(this, cfg) || this;\n        /**\n         * @cfg {'twitter'/'instagram'/'soundcloud'} protected\n         *\n         * The name of service to link @mentions to.\n         *\n         * Valid values are: 'twitter', 'instagram', or 'soundcloud'\n         */\n        _this.serviceName = 'twitter'; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * Hash of regular expression to match username handles. Example match:\n         *\n         *     @asdf\n         *\n         * @private\n         * @property {Object} matcherRegexes\n         */\n        _this.matcherRegexes = {\n            'twitter': new RegExp(\"@[_\" + alphaNumericAndMarksCharsStr + \"]{1,50}(?![_\" + alphaNumericAndMarksCharsStr + \"])\", 'g'),\n            'instagram': new RegExp(\"@[_.\" + alphaNumericAndMarksCharsStr + \"]{1,30}(?![_\" + alphaNumericAndMarksCharsStr + \"])\", 'g'),\n            'soundcloud': new RegExp(\"@[-_.\" + alphaNumericAndMarksCharsStr + \"]{1,50}(?![-_\" + alphaNumericAndMarksCharsStr + \"])\", 'g') // lookahead used to make sure we don't match something above 50 characters\n        };\n        /**\n         * The regular expression to use to check the character before a username match to\n         * make sure we didn't accidentally match an email address.\n         *\n         * For example, the string \"<EMAIL>\" should not match \"@asdf\" as a username.\n         *\n         * @private\n         * @property {RegExp} nonWordCharRegex\n         */\n        _this.nonWordCharRegex = new RegExp('[^' + alphaNumericAndMarksCharsStr + ']');\n        _this.serviceName = cfg.serviceName;\n        return _this;\n    }\n    /**\n     * @inheritdoc\n     */\n    MentionMatcher.prototype.parseMatches = function (text) {\n        var serviceName = this.serviceName, matcherRegex = this.matcherRegexes[this.serviceName], nonWordCharRegex = this.nonWordCharRegex, tagBuilder = this.tagBuilder, matches = [], match;\n        if (!matcherRegex) {\n            return matches;\n        }\n        while ((match = matcherRegex.exec(text)) !== null) {\n            var offset = match.index, prevChar = text.charAt(offset - 1);\n            // If we found the match at the beginning of the string, or we found the match\n            // and there is a whitespace char in front of it (meaning it is not an email\n            // address), then it is a username match.\n            if (offset === 0 || nonWordCharRegex.test(prevChar)) {\n                var matchedText = match[0].replace(/\\.+$/g, ''), // strip off trailing .\n                mention = matchedText.slice(1); // strip off the '@' character at the beginning\n                matches.push(new MentionMatch({\n                    tagBuilder: tagBuilder,\n                    matchedText: matchedText,\n                    offset: offset,\n                    serviceName: serviceName,\n                    mention: mention\n                }));\n            }\n        }\n        return matches;\n    };\n    return MentionMatcher;\n}(Matcher));\nexport { MentionMatcher };\n\n//# sourceMappingURL=mention-matcher.js.map\n", "import * as tslib_1 from \"tslib\";\nimport { letterRe, digitRe, whitespaceRe, quoteRe, controlCharsRe } from '../regex-lib';\nimport { throwUnhandledCaseError } from '../utils';\n// For debugging: search for other \"For debugging\" lines\n// import CliTable from 'cli-table';\n/**\n * Parses an HTML string, calling the callbacks to notify of tags and text.\n *\n * ## History\n *\n * This file previously used a regular expression to find html tags in the input\n * text. Unfortunately, we ran into a bunch of catastrophic backtracking issues\n * with certain input text, causing Autolinker to either hang or just take a\n * really long time to parse the string.\n *\n * The current code is intended to be a O(n) algorithm that walks through\n * the string in one pass, and tries to be as cheap as possible. We don't need\n * to implement the full HTML spec, but rather simply determine where the string\n * looks like an HTML tag, and where it looks like text (so that we can autolink\n * that).\n *\n * This state machine parser is intended just to be a simple but performant\n * parser of HTML for the subset of requirements we have. We simply need to:\n *\n * 1. Determine where HTML tags are\n * 2. Determine the tag name (<PERSON><PERSON><PERSON> specifically only cares about <a>,\n *    <script>, and <style> tags, so as not to link any text within them)\n *\n * We don't need to:\n *\n * 1. Create a parse tree\n * 2. Auto-close tags with invalid markup\n * 3. etc.\n *\n * The other intention behind this is that we didn't want to add external\n * dependencies on the Autolinker utility which would increase its size. For\n * instance, adding htmlparser2 adds 125kb to the minified output file,\n * increasing its final size from 47kb to 172kb (at the time of writing). It\n * also doesn't work exactly correctly, treating the string \"<3 blah blah blah\"\n * as an HTML tag.\n *\n * Reference for HTML spec:\n *\n *     https://www.w3.org/TR/html51/syntax.html#sec-tokenization\n *\n * @param {String} html The HTML to parse\n * @param {Object} callbacks\n * @param {Function} callbacks.onOpenTag Callback function to call when an open\n *   tag is parsed. Called with the tagName as its argument.\n * @param {Function} callbacks.onCloseTag Callback function to call when a close\n *   tag is parsed. Called with the tagName as its argument. If a self-closing\n *   tag is found, `onCloseTag` is called immediately after `onOpenTag`.\n * @param {Function} callbacks.onText Callback function to call when text (i.e\n *   not an HTML tag) is parsed. Called with the text (string) as its first\n *   argument, and offset (number) into the string as its second.\n */\nexport function parseHtml(html, _a) {\n    var onOpenTag = _a.onOpenTag, onCloseTag = _a.onCloseTag, onText = _a.onText, onComment = _a.onComment, onDoctype = _a.onDoctype;\n    var noCurrentTag = new CurrentTag();\n    var charIdx = 0, len = html.length, state = 0 /* Data */, currentDataIdx = 0, // where the current data start index is\n    currentTag = noCurrentTag; // describes the current tag that is being read\n    // For debugging: search for other \"For debugging\" lines\n    // const table = new CliTable( {\n    // \thead: [ 'charIdx', 'char', 'state', 'currentDataIdx', 'currentOpenTagIdx', 'tag.type' ]\n    // } );\n    while (charIdx < len) {\n        var char = html.charAt(charIdx);\n        // For debugging: search for other \"For debugging\" lines\n        // ALSO: Temporarily remove the 'const' keyword on the State enum\n        // table.push( \n        // \t[ charIdx, char, State[ state ], currentDataIdx, currentTag.idx, currentTag.idx === -1 ? '' : currentTag.type ] \n        // );\n        switch (state) {\n            case 0 /* Data */:\n                stateData(char);\n                break;\n            case 1 /* TagOpen */:\n                stateTagOpen(char);\n                break;\n            case 2 /* EndTagOpen */:\n                stateEndTagOpen(char);\n                break;\n            case 3 /* TagName */:\n                stateTagName(char);\n                break;\n            case 4 /* BeforeAttributeName */:\n                stateBeforeAttributeName(char);\n                break;\n            case 5 /* AttributeName */:\n                stateAttributeName(char);\n                break;\n            case 6 /* AfterAttributeName */:\n                stateAfterAttributeName(char);\n                break;\n            case 7 /* BeforeAttributeValue */:\n                stateBeforeAttributeValue(char);\n                break;\n            case 8 /* AttributeValueDoubleQuoted */:\n                stateAttributeValueDoubleQuoted(char);\n                break;\n            case 9 /* AttributeValueSingleQuoted */:\n                stateAttributeValueSingleQuoted(char);\n                break;\n            case 10 /* AttributeValueUnquoted */:\n                stateAttributeValueUnquoted(char);\n                break;\n            case 11 /* AfterAttributeValueQuoted */:\n                stateAfterAttributeValueQuoted(char);\n                break;\n            case 12 /* SelfClosingStartTag */:\n                stateSelfClosingStartTag(char);\n                break;\n            case 13 /* MarkupDeclarationOpenState */:\n                stateMarkupDeclarationOpen(char);\n                break;\n            case 14 /* CommentStart */:\n                stateCommentStart(char);\n                break;\n            case 15 /* CommentStartDash */:\n                stateCommentStartDash(char);\n                break;\n            case 16 /* Comment */:\n                stateComment(char);\n                break;\n            case 17 /* CommentEndDash */:\n                stateCommentEndDash(char);\n                break;\n            case 18 /* CommentEnd */:\n                stateCommentEnd(char);\n                break;\n            case 19 /* CommentEndBang */:\n                stateCommentEndBang(char);\n                break;\n            case 20 /* Doctype */:\n                stateDoctype(char);\n                break;\n            default:\n                throwUnhandledCaseError(state);\n        }\n        // For debugging: search for other \"For debugging\" lines\n        // ALSO: Temporarily remove the 'const' keyword on the State enum\n        // table.push( \n        // \t[ charIdx, char, State[ state ], currentDataIdx, currentTag.idx, currentTag.idx === -1 ? '' : currentTag.type ] \n        // );\n        charIdx++;\n    }\n    if (currentDataIdx < charIdx) {\n        emitText();\n    }\n    // For debugging: search for other \"For debugging\" lines\n    // console.log( '\\n' + table.toString() );\n    // Called when non-tags are being read (i.e. the text around HTML †ags)\n    // https://www.w3.org/TR/html51/syntax.html#data-state\n    function stateData(char) {\n        if (char === '<') {\n            startNewTag();\n        }\n    }\n    // Called after a '<' is read from the Data state\n    // https://www.w3.org/TR/html51/syntax.html#tag-open-state\n    function stateTagOpen(char) {\n        if (char === '!') {\n            state = 13 /* MarkupDeclarationOpenState */;\n        }\n        else if (char === '/') {\n            state = 2 /* EndTagOpen */;\n            currentTag = new CurrentTag(tslib_1.__assign({}, currentTag, { isClosing: true }));\n        }\n        else if (char === '<') {\n            // start of another tag (ignore the previous, incomplete one)\n            startNewTag();\n        }\n        else if (letterRe.test(char)) {\n            // tag name start (and no '/' read)\n            state = 3 /* TagName */;\n            currentTag = new CurrentTag(tslib_1.__assign({}, currentTag, { isOpening: true }));\n        }\n        else {\n            // Any other \n            state = 0 /* Data */;\n            currentTag = noCurrentTag;\n        }\n    }\n    // After a '<x', '</x' sequence is read (where 'x' is a letter character), \n    // this is to continue reading the tag name\n    // https://www.w3.org/TR/html51/syntax.html#tag-name-state\n    function stateTagName(char) {\n        if (whitespaceRe.test(char)) {\n            currentTag = new CurrentTag(tslib_1.__assign({}, currentTag, { name: captureTagName() }));\n            state = 4 /* BeforeAttributeName */;\n        }\n        else if (char === '<') {\n            // start of another tag (ignore the previous, incomplete one)\n            startNewTag();\n        }\n        else if (char === '/') {\n            currentTag = new CurrentTag(tslib_1.__assign({}, currentTag, { name: captureTagName() }));\n            state = 12 /* SelfClosingStartTag */;\n        }\n        else if (char === '>') {\n            currentTag = new CurrentTag(tslib_1.__assign({}, currentTag, { name: captureTagName() }));\n            emitTagAndPreviousTextNode(); // resets to Data state as well\n        }\n        else if (!letterRe.test(char) && !digitRe.test(char) && char !== ':') {\n            // Anything else that does not form an html tag. Note: the colon \n            // character is accepted for XML namespaced tags\n            resetToDataState();\n        }\n        else {\n            // continue reading tag name\n        }\n    }\n    // Called after the '/' is read from a '</' sequence\n    // https://www.w3.org/TR/html51/syntax.html#end-tag-open-state\n    function stateEndTagOpen(char) {\n        if (char === '>') { // parse error. Encountered \"</>\". Skip it without treating as a tag\n            resetToDataState();\n        }\n        else if (letterRe.test(char)) {\n            state = 3 /* TagName */;\n        }\n        else {\n            // some other non-tag-like character, don't treat this as a tag\n            resetToDataState();\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#before-attribute-name-state\n    function stateBeforeAttributeName(char) {\n        if (whitespaceRe.test(char)) {\n            // stay in BeforeAttributeName state - continue reading chars\n        }\n        else if (char === '/') {\n            state = 12 /* SelfClosingStartTag */;\n        }\n        else if (char === '>') {\n            emitTagAndPreviousTextNode(); // resets to Data state as well\n        }\n        else if (char === '<') {\n            // start of another tag (ignore the previous, incomplete one)\n            startNewTag();\n        }\n        else if (char === \"=\" || quoteRe.test(char) || controlCharsRe.test(char)) {\n            // \"Parse error\" characters that, according to the spec, should be\n            // appended to the attribute name, but we'll treat these characters\n            // as not forming a real HTML tag\n            resetToDataState();\n        }\n        else {\n            // Any other char, start of a new attribute name\n            state = 5 /* AttributeName */;\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#attribute-name-state\n    function stateAttributeName(char) {\n        if (whitespaceRe.test(char)) {\n            state = 6 /* AfterAttributeName */;\n        }\n        else if (char === '/') {\n            state = 12 /* SelfClosingStartTag */;\n        }\n        else if (char === '=') {\n            state = 7 /* BeforeAttributeValue */;\n        }\n        else if (char === '>') {\n            emitTagAndPreviousTextNode(); // resets to Data state as well\n        }\n        else if (char === '<') {\n            // start of another tag (ignore the previous, incomplete one)\n            startNewTag();\n        }\n        else if (quoteRe.test(char)) {\n            // \"Parse error\" characters that, according to the spec, should be\n            // appended to the attribute name, but we'll treat these characters\n            // as not forming a real HTML tag\n            resetToDataState();\n        }\n        else {\n            // anything else: continue reading attribute name\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#after-attribute-name-state\n    function stateAfterAttributeName(char) {\n        if (whitespaceRe.test(char)) {\n            // ignore the character - continue reading\n        }\n        else if (char === '/') {\n            state = 12 /* SelfClosingStartTag */;\n        }\n        else if (char === '=') {\n            state = 7 /* BeforeAttributeValue */;\n        }\n        else if (char === '>') {\n            emitTagAndPreviousTextNode();\n        }\n        else if (char === '<') {\n            // start of another tag (ignore the previous, incomplete one)\n            startNewTag();\n        }\n        else if (quoteRe.test(char)) {\n            // \"Parse error\" characters that, according to the spec, should be\n            // appended to the attribute name, but we'll treat these characters\n            // as not forming a real HTML tag\n            resetToDataState();\n        }\n        else {\n            // Any other character, start a new attribute in the current tag\n            state = 5 /* AttributeName */;\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#before-attribute-value-state\n    function stateBeforeAttributeValue(char) {\n        if (whitespaceRe.test(char)) {\n            // ignore the character - continue reading\n        }\n        else if (char === \"\\\"\") {\n            state = 8 /* AttributeValueDoubleQuoted */;\n        }\n        else if (char === \"'\") {\n            state = 9 /* AttributeValueSingleQuoted */;\n        }\n        else if (/[>=`]/.test(char)) {\n            // Invalid chars after an '=' for an attribute value, don't count \n            // the current tag as an HTML tag\n            resetToDataState();\n        }\n        else if (char === '<') {\n            // start of another tag (ignore the previous, incomplete one)\n            startNewTag();\n        }\n        else {\n            // Any other character, consider it an unquoted attribute value\n            state = 10 /* AttributeValueUnquoted */;\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#attribute-value-double-quoted-state\n    function stateAttributeValueDoubleQuoted(char) {\n        if (char === \"\\\"\") { // end the current double-quoted attribute\n            state = 11 /* AfterAttributeValueQuoted */;\n        }\n        else {\n            // consume the character as part of the double-quoted attribute value\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#attribute-value-single-quoted-state\n    function stateAttributeValueSingleQuoted(char) {\n        if (char === \"'\") { // end the current single-quoted attribute\n            state = 11 /* AfterAttributeValueQuoted */;\n        }\n        else {\n            // consume the character as part of the double-quoted attribute value\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#attribute-value-unquoted-state\n    function stateAttributeValueUnquoted(char) {\n        if (whitespaceRe.test(char)) {\n            state = 4 /* BeforeAttributeName */;\n        }\n        else if (char === '>') {\n            emitTagAndPreviousTextNode();\n        }\n        else if (char === '<') {\n            // start of another tag (ignore the previous, incomplete one)\n            startNewTag();\n        }\n        else {\n            // Any other character, treat it as part of the attribute value\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#after-attribute-value-quoted-state\n    function stateAfterAttributeValueQuoted(char) {\n        if (whitespaceRe.test(char)) {\n            state = 4 /* BeforeAttributeName */;\n        }\n        else if (char === '/') {\n            state = 12 /* SelfClosingStartTag */;\n        }\n        else if (char === '>') {\n            emitTagAndPreviousTextNode();\n        }\n        else if (char === '<') {\n            // start of another tag (ignore the previous, incomplete one)\n            startNewTag();\n        }\n        else {\n            // Any other character, \"parse error\". Spec says to switch to the\n            // BeforeAttributeState and re-consume the character, as it may be\n            // the start of a new attribute name\n            state = 4 /* BeforeAttributeName */;\n            reconsumeCurrentCharacter();\n        }\n    }\n    // A '/' has just been read in the current tag (presumably for '/>'), and \n    // this handles the next character\n    // https://www.w3.org/TR/html51/syntax.html#self-closing-start-tag-state\n    function stateSelfClosingStartTag(char) {\n        if (char === '>') {\n            currentTag = new CurrentTag(tslib_1.__assign({}, currentTag, { isClosing: true }));\n            emitTagAndPreviousTextNode(); // resets to Data state as well\n        }\n        else {\n            state = 4 /* BeforeAttributeName */;\n        }\n    }\n    // https://www.w3.org/TR/html51/syntax.html#markup-declaration-open-state\n    // (HTML Comments or !DOCTYPE)\n    function stateMarkupDeclarationOpen(char) {\n        if (html.substr(charIdx, 2) === '--') { // html comment\n            charIdx += 2; // \"consume\" characters\n            currentTag = new CurrentTag(tslib_1.__assign({}, currentTag, { type: 'comment' }));\n            state = 14 /* CommentStart */;\n        }\n        else if (html.substr(charIdx, 7).toUpperCase() === 'DOCTYPE') {\n            charIdx += 7; // \"consume\" characters\n            currentTag = new CurrentTag(tslib_1.__assign({}, currentTag, { type: 'doctype' }));\n            state = 20 /* Doctype */;\n        }\n        else {\n            // At this point, the spec specifies that the state machine should\n            // enter the \"bogus comment\" state, in which case any character(s) \n            // after the '<!' that were read should become an HTML comment up\n            // until the first '>' that is read (or EOF). Instead, we'll assume\n            // that a user just typed '<!' as part of text data\n            resetToDataState();\n        }\n    }\n    // Handles after the sequence '<!--' has been read\n    // https://www.w3.org/TR/html51/syntax.html#comment-start-state\n    function stateCommentStart(char) {\n        if (char === '-') {\n            // We've read the sequence '<!---' at this point (3 dashes)\n            state = 15 /* CommentStartDash */;\n        }\n        else if (char === '>') {\n            // At this point, we'll assume the comment wasn't a real comment\n            // so we'll just emit it as data. We basically read the sequence \n            // '<!-->'\n            resetToDataState();\n        }\n        else {\n            // Any other char, take it as part of the comment\n            state = 16 /* Comment */;\n        }\n    }\n    // We've read the sequence '<!---' at this point (3 dashes)\n    // https://www.w3.org/TR/html51/syntax.html#comment-start-dash-state\n    function stateCommentStartDash(char) {\n        if (char === '-') {\n            // We've read '<!----' (4 dashes) at this point\n            state = 18 /* CommentEnd */;\n        }\n        else if (char === '>') {\n            // At this point, we'll assume the comment wasn't a real comment\n            // so we'll just emit it as data. We basically read the sequence \n            // '<!--->'\n            resetToDataState();\n        }\n        else {\n            // Anything else, take it as a valid comment\n            state = 16 /* Comment */;\n        }\n    }\n    // Currently reading the comment's text (data)\n    // https://www.w3.org/TR/html51/syntax.html#comment-state\n    function stateComment(char) {\n        if (char === '-') {\n            state = 17 /* CommentEndDash */;\n        }\n        else {\n            // Any other character, stay in the Comment state\n        }\n    }\n    // When we we've read the first dash inside a comment, it may signal the\n    // end of the comment if we read another dash\n    // https://www.w3.org/TR/html51/syntax.html#comment-end-dash-state\n    function stateCommentEndDash(char) {\n        if (char === '-') {\n            state = 18 /* CommentEnd */;\n        }\n        else {\n            // Wasn't a dash, must still be part of the comment\n            state = 16 /* Comment */;\n        }\n    }\n    // After we've read two dashes inside a comment, it may signal the end of \n    // the comment if we then read a '>' char\n    // https://www.w3.org/TR/html51/syntax.html#comment-end-state\n    function stateCommentEnd(char) {\n        if (char === '>') {\n            emitTagAndPreviousTextNode();\n        }\n        else if (char === '!') {\n            state = 19 /* CommentEndBang */;\n        }\n        else if (char === '-') {\n            // A 3rd '-' has been read: stay in the CommentEnd state\n        }\n        else {\n            // Anything else, switch back to the comment state since we didn't\n            // read the full \"end comment\" sequence (i.e. '-->')\n            state = 16 /* Comment */;\n        }\n    }\n    // We've read the sequence '--!' inside of a comment\n    // https://www.w3.org/TR/html51/syntax.html#comment-end-bang-state\n    function stateCommentEndBang(char) {\n        if (char === '-') {\n            // We read the sequence '--!-' inside of a comment. The last dash\n            // could signify that the comment is going to close\n            state = 17 /* CommentEndDash */;\n        }\n        else if (char === '>') {\n            // End of comment with the sequence '--!>'\n            emitTagAndPreviousTextNode();\n        }\n        else {\n            // The '--!' was not followed by a '>', continue reading the \n            // comment's text\n            state = 16 /* Comment */;\n        }\n    }\n    /**\n     * For DOCTYPES in particular, we don't care about the attributes. Just\n     * advance to the '>' character and emit the tag, unless we find a '<'\n     * character in which case we'll start a new tag.\n     *\n     * Example doctype tag:\n     *    <!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01//EN\" \"http://www.w3.org/TR/html4/strict.dtd\">\n     *\n     * Actual spec: https://www.w3.org/TR/html51/syntax.html#doctype-state\n     */\n    function stateDoctype(char) {\n        if (char === '>') {\n            emitTagAndPreviousTextNode();\n        }\n        else if (char === '<') {\n            startNewTag();\n        }\n        else {\n            // stay in the Doctype state\n        }\n    }\n    /**\n     * Resets the state back to the Data state, and removes the current tag.\n     *\n     * We'll generally run this function whenever a \"parse error\" is\n     * encountered, where the current tag that is being read no longer looks\n     * like a real HTML tag.\n     */\n    function resetToDataState() {\n        state = 0 /* Data */;\n        currentTag = noCurrentTag;\n    }\n    /**\n     * Starts a new HTML tag at the current index, ignoring any previous HTML\n     * tag that was being read.\n     *\n     * We'll generally run this function whenever we read a new '<' character,\n     * including when we read a '<' character inside of an HTML tag that we were\n     * previously reading.\n     */\n    function startNewTag() {\n        state = 1 /* TagOpen */;\n        currentTag = new CurrentTag({ idx: charIdx });\n    }\n    /**\n     * Once we've decided to emit an open tag, that means we can also emit the\n     * text node before it.\n     */\n    function emitTagAndPreviousTextNode() {\n        var textBeforeTag = html.slice(currentDataIdx, currentTag.idx);\n        if (textBeforeTag) {\n            // the html tag was the first element in the html string, or two \n            // tags next to each other, in which case we should not emit a text \n            // node\n            onText(textBeforeTag, currentDataIdx);\n        }\n        if (currentTag.type === 'comment') {\n            onComment(currentTag.idx);\n        }\n        else if (currentTag.type === 'doctype') {\n            onDoctype(currentTag.idx);\n        }\n        else {\n            if (currentTag.isOpening) {\n                onOpenTag(currentTag.name, currentTag.idx);\n            }\n            if (currentTag.isClosing) { // note: self-closing tags will emit both opening and closing\n                onCloseTag(currentTag.name, currentTag.idx);\n            }\n        }\n        // Since we just emitted a tag, reset to the data state for the next char\n        resetToDataState();\n        currentDataIdx = charIdx + 1;\n    }\n    function emitText() {\n        var text = html.slice(currentDataIdx, charIdx);\n        onText(text, currentDataIdx);\n        currentDataIdx = charIdx + 1;\n    }\n    /**\n     * Captures the tag name from the start of the tag to the current character\n     * index, and converts it to lower case\n     */\n    function captureTagName() {\n        var startIdx = currentTag.idx + (currentTag.isClosing ? 2 : 1);\n        return html.slice(startIdx, charIdx).toLowerCase();\n    }\n    /**\n     * Causes the main loop to re-consume the current character, such as after\n     * encountering a \"parse error\" that changed state and needs to reconsume\n     * the same character in that new state.\n     */\n    function reconsumeCurrentCharacter() {\n        charIdx--;\n    }\n}\nvar CurrentTag = /** @class */ (function () {\n    function CurrentTag(cfg) {\n        if (cfg === void 0) { cfg = {}; }\n        this.idx = cfg.idx !== undefined ? cfg.idx : -1;\n        this.type = cfg.type || 'tag';\n        this.name = cfg.name || '';\n        this.isOpening = !!cfg.isOpening;\n        this.isClosing = !!cfg.isClosing;\n    }\n    return CurrentTag;\n}());\n\n//# sourceMappingURL=parse-html.js.map\n", "import { defaults, remove, splitAndCapture } from \"./utils\";\nimport { AnchorTagBuilder } from \"./anchor-tag-builder\";\nimport { Match } from \"./match/match\";\nimport { EmailMatch } from \"./match/email-match\";\nimport { HashtagMatch } from \"./match/hashtag-match\";\nimport { MentionMatch } from \"./match/mention-match\";\nimport { PhoneMatch } from \"./match/phone-match\";\nimport { UrlMatch } from \"./match/url-match\";\nimport { Matcher } from \"./matcher/matcher\";\nimport { HtmlTag } from \"./html-tag\";\nimport { EmailMatcher } from \"./matcher/email-matcher\";\nimport { UrlMatcher } from \"./matcher/url-matcher\";\nimport { HashtagMatcher } from \"./matcher/hashtag-matcher\";\nimport { PhoneMatcher } from \"./matcher/phone-matcher\";\nimport { MentionMatcher } from \"./matcher/mention-matcher\";\nimport { parseHtml } from './htmlParser/parse-html';\n/**\n * @class Autolinker\n * @extends Object\n *\n * Utility class used to process a given string of text, and wrap the matches in\n * the appropriate anchor (&lt;a&gt;) tags to turn them into links.\n *\n * Any of the configuration options may be provided in an Object provided\n * to the Autolinker constructor, which will configure how the {@link #link link()}\n * method will process the links.\n *\n * For example:\n *\n *     var autolinker = new Autolinker( {\n *         newWindow : false,\n *         truncate  : 30\n *     } );\n *\n *     var html = autolinker.link( \"Joe went to www.yahoo.com\" );\n *     // produces: 'Joe went to <a href=\"http://www.yahoo.com\">yahoo.com</a>'\n *\n *\n * The {@link #static-link static link()} method may also be used to inline\n * options into a single call, which may be more convenient for one-off uses.\n * For example:\n *\n *     var html = Autolinker.link( \"Joe went to www.yahoo.com\", {\n *         newWindow : false,\n *         truncate  : 30\n *     } );\n *     // produces: 'Joe went to <a href=\"http://www.yahoo.com\">yahoo.com</a>'\n *\n *\n * ## Custom Replacements of Links\n *\n * If the configuration options do not provide enough flexibility, a {@link #replaceFn}\n * may be provided to fully customize the output of Autolinker. This function is\n * called once for each URL/Email/Phone#/Hashtag/Mention (Twitter, Instagram, Soundcloud)\n * match that is encountered.\n *\n * For example:\n *\n *     var input = \"...\";  // string with URLs, Email Addresses, Phone #s, Hashtags, and Mentions (Twitter, Instagram, Soundcloud)\n *\n *     var linkedText = Autolinker.link( input, {\n *         replaceFn : function( match ) {\n *             console.log( \"href = \", match.getAnchorHref() );\n *             console.log( \"text = \", match.getAnchorText() );\n *\n *             switch( match.getType() ) {\n *                 case 'url' :\n *                     console.log( \"url: \", match.getUrl() );\n *\n *                     if( match.getUrl().indexOf( 'mysite.com' ) === -1 ) {\n *                         var tag = match.buildTag();  // returns an `Autolinker.HtmlTag` instance, which provides mutator methods for easy changes\n *                         tag.setAttr( 'rel', 'nofollow' );\n *                         tag.addClass( 'external-link' );\n *\n *                         return tag;\n *\n *                     } else {\n *                         return true;  // let Autolinker perform its normal anchor tag replacement\n *                     }\n *\n *                 case 'email' :\n *                     var email = match.getEmail();\n *                     console.log( \"email: \", email );\n *\n *                     if( email === \"<EMAIL>\" ) {\n *                         return false;  // don't auto-link this particular email address; leave as-is\n *                     } else {\n *                         return;  // no return value will have Autolinker perform its normal anchor tag replacement (same as returning `true`)\n *                     }\n *\n *                 case 'phone' :\n *                     var phoneNumber = match.getPhoneNumber();\n *                     console.log( phoneNumber );\n *\n *                     return '<a href=\"http://newplace.to.link.phone.numbers.to/\">' + phoneNumber + '</a>';\n *\n *                 case 'hashtag' :\n *                     var hashtag = match.getHashtag();\n *                     console.log( hashtag );\n *\n *                     return '<a href=\"http://newplace.to.link.hashtag.handles.to/\">' + hashtag + '</a>';\n *\n *                 case 'mention' :\n *                     var mention = match.getMention();\n *                     console.log( mention );\n *\n *                     return '<a href=\"http://newplace.to.link.mention.to/\">' + mention + '</a>';\n *             }\n *         }\n *     } );\n *\n *\n * The function may return the following values:\n *\n * - `true` (Boolean): Allow Autolinker to replace the match as it normally\n *   would.\n * - `false` (Boolean): Do not replace the current match at all - leave as-is.\n * - Any String: If a string is returned from the function, the string will be\n *   used directly as the replacement HTML for the match.\n * - An {@link Autolinker.HtmlTag} instance, which can be used to build/modify\n *   an HTML tag before writing out its HTML text.\n */\nvar Autolinker = /** @class */ (function () {\n    /**\n     * @method constructor\n     * @param {Object} [cfg] The configuration options for the Autolinker instance,\n     *   specified in an Object (map).\n     */\n    function Autolinker(cfg) {\n        if (cfg === void 0) { cfg = {}; }\n        /**\n         * The Autolinker version number exposed on the instance itself.\n         *\n         * Ex: 0.25.1\n         */\n        this.version = Autolinker.version;\n        /**\n         * @cfg {Boolean/Object} [urls]\n         *\n         * `true` if URLs should be automatically linked, `false` if they should not\n         * be. Defaults to `true`.\n         *\n         * Examples:\n         *\n         *     urls: true\n         *\n         *     // or\n         *\n         *     urls: {\n         *         schemeMatches : true,\n         *         wwwMatches    : true,\n         *         tldMatches    : true\n         *     }\n         *\n         * As shown above, this option also accepts an Object form with 3 properties\n         * to allow for more customization of what exactly gets linked. All default\n         * to `true`:\n         *\n         * @cfg {Boolean} [urls.schemeMatches] `true` to match URLs found prefixed\n         *   with a scheme, i.e. `http://google.com`, or `other+scheme://google.com`,\n         *   `false` to prevent these types of matches.\n         * @cfg {Boolean} [urls.wwwMatches] `true` to match urls found prefixed with\n         *   `'www.'`, i.e. `www.google.com`. `false` to prevent these types of\n         *   matches. Note that if the URL had a prefixed scheme, and\n         *   `schemeMatches` is true, it will still be linked.\n         * @cfg {Boolean} [urls.tldMatches] `true` to match URLs with known top\n         *   level domains (.com, .net, etc.) that are not prefixed with a scheme or\n         *   `'www.'`. This option attempts to match anything that looks like a URL\n         *   in the given text. Ex: `google.com`, `asdf.org/?page=1`, etc. `false`\n         *   to prevent these types of matches.\n         */\n        this.urls = {}; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} [email=true]\n         *\n         * `true` if email addresses should be automatically linked, `false` if they\n         * should not be.\n         */\n        this.email = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} [phone=true]\n         *\n         * `true` if Phone numbers (\"(555)555-5555\") should be automatically linked,\n         * `false` if they should not be.\n         */\n        this.phone = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean/String} [hashtag=false]\n         *\n         * A string for the service name to have hashtags (ex: \"#myHashtag\")\n         * auto-linked to. The currently-supported values are:\n         *\n         * - 'twitter'\n         * - 'facebook'\n         * - 'instagram'\n         *\n         * Pass `false` to skip auto-linking of hashtags.\n         */\n        this.hashtag = false; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {String/Boolean} [mention=false]\n         *\n         * A string for the service name to have mentions (ex: \"@myuser\")\n         * auto-linked to. The currently supported values are:\n         *\n         * - 'twitter'\n         * - 'instagram'\n         * - 'soundcloud'\n         *\n         * Defaults to `false` to skip auto-linking of mentions.\n         */\n        this.mention = false; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} [newWindow=true]\n         *\n         * `true` if the links should open in a new window, `false` otherwise.\n         */\n        this.newWindow = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean/Object} [stripPrefix=true]\n         *\n         * `true` if 'http://' (or 'https://') and/or the 'www.' should be stripped\n         * from the beginning of URL links' text, `false` otherwise. Defaults to\n         * `true`.\n         *\n         * Examples:\n         *\n         *     stripPrefix: true\n         *\n         *     // or\n         *\n         *     stripPrefix: {\n         *         scheme : true,\n         *         www    : true\n         *     }\n         *\n         * As shown above, this option also accepts an Object form with 2 properties\n         * to allow for more customization of what exactly is prevented from being\n         * displayed. Both default to `true`:\n         *\n         * @cfg {Boolean} [stripPrefix.scheme] `true` to prevent the scheme part of\n         *   a URL match from being displayed to the user. Example:\n         *   `'http://google.com'` will be displayed as `'google.com'`. `false` to\n         *   not strip the scheme. NOTE: Only an `'http://'` or `'https://'` scheme\n         *   will be removed, so as not to remove a potentially dangerous scheme\n         *   (such as `'file://'` or `'javascript:'`)\n         * @cfg {Boolean} [stripPrefix.www] www (Boolean): `true` to prevent the\n         *   `'www.'` part of a URL match from being displayed to the user. Ex:\n         *   `'www.google.com'` will be displayed as `'google.com'`. `false` to not\n         *   strip the `'www'`.\n         */\n        this.stripPrefix = { scheme: true, www: true }; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} [stripTrailingSlash=true]\n         *\n         * `true` to remove the trailing slash from URL matches, `false` to keep\n         *  the trailing slash.\n         *\n         *  Example when `true`: `http://google.com/` will be displayed as\n         *  `http://google.com`.\n         */\n        this.stripTrailingSlash = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Boolean} [decodePercentEncoding=true]\n         *\n         * `true` to decode percent-encoded characters in URL matches, `false` to keep\n         *  the percent-encoded characters.\n         *\n         *  Example when `true`: `https://en.wikipedia.org/wiki/San_Jos%C3%A9` will\n         *  be displayed as `https://en.wikipedia.org/wiki/San_José`.\n         */\n        this.decodePercentEncoding = true; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Number/Object} [truncate=0]\n         *\n         * ## Number Form\n         *\n         * A number for how many characters matched text should be truncated to\n         * inside the text of a link. If the matched text is over this number of\n         * characters, it will be truncated to this length by adding a two period\n         * ellipsis ('..') to the end of the string.\n         *\n         * For example: A url like 'http://www.yahoo.com/some/long/path/to/a/file'\n         * truncated to 25 characters might look something like this:\n         * 'yahoo.com/some/long/pat..'\n         *\n         * Example Usage:\n         *\n         *     truncate: 25\n         *\n         *\n         *  Defaults to `0` for \"no truncation.\"\n         *\n         *\n         * ## Object Form\n         *\n         * An Object may also be provided with two properties: `length` (Number) and\n         * `location` (String). `location` may be one of the following: 'end'\n         * (default), 'middle', or 'smart'.\n         *\n         * Example Usage:\n         *\n         *     truncate: { length: 25, location: 'middle' }\n         *\n         * @cfg {Number} [truncate.length=0] How many characters to allow before\n         *   truncation will occur. Defaults to `0` for \"no truncation.\"\n         * @cfg {\"end\"/\"middle\"/\"smart\"} [truncate.location=\"end\"]\n         *\n         * - 'end' (default): will truncate up to the number of characters, and then\n         *   add an ellipsis at the end. Ex: 'yahoo.com/some/long/pat..'\n         * - 'middle': will truncate and add the ellipsis in the middle. Ex:\n         *   'yahoo.com/s..th/to/a/file'\n         * - 'smart': for URLs where the algorithm attempts to strip out unnecessary\n         *   parts first (such as the 'www.', then URL scheme, hash, etc.),\n         *   attempting to make the URL human-readable before looking for a good\n         *   point to insert the ellipsis if it is still too long. Ex:\n         *   'yahoo.com/some..to/a/file'. For more details, see\n         *   {@link Autolinker.truncate.TruncateSmart}.\n         */\n        this.truncate = { length: 0, location: 'end' }; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {String} className\n         *\n         * A CSS class name to add to the generated links. This class will be added\n         * to all links, as well as this class plus match suffixes for styling\n         * url/email/phone/hashtag/mention links differently.\n         *\n         * For example, if this config is provided as \"myLink\", then:\n         *\n         * - URL links will have the CSS classes: \"myLink myLink-url\"\n         * - Email links will have the CSS classes: \"myLink myLink-email\", and\n         * - Phone links will have the CSS classes: \"myLink myLink-phone\"\n         * - Hashtag links will have the CSS classes: \"myLink myLink-hashtag\"\n         * - Mention links will have the CSS classes: \"myLink myLink-mention myLink-[type]\"\n         *   where [type] is either \"instagram\", \"twitter\" or \"soundcloud\"\n         */\n        this.className = ''; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Function} replaceFn\n         *\n         * A function to individually process each match found in the input string.\n         *\n         * See the class's description for usage.\n         *\n         * The `replaceFn` can be called with a different context object (`this`\n         * reference) using the {@link #context} cfg.\n         *\n         * This function is called with the following parameter:\n         *\n         * @cfg {Autolinker.match.Match} replaceFn.match The Match instance which\n         *   can be used to retrieve information about the match that the `replaceFn`\n         *   is currently processing. See {@link Autolinker.match.Match} subclasses\n         *   for details.\n         */\n        this.replaceFn = null; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @cfg {Object} context\n         *\n         * The context object (`this` reference) to call the `replaceFn` with.\n         *\n         * Defaults to this Autolinker instance.\n         */\n        this.context = undefined; // default value just to get the above doc comment in the ES5 output and documentation generator\n        /**\n         * @private\n         * @property {Autolinker.matcher.Matcher[]} matchers\n         *\n         * The {@link Autolinker.matcher.Matcher} instances for this Autolinker\n         * instance.\n         *\n         * This is lazily created in {@link #getMatchers}.\n         */\n        this.matchers = null;\n        /**\n         * @private\n         * @property {Autolinker.AnchorTagBuilder} tagBuilder\n         *\n         * The AnchorTagBuilder instance used to build match replacement anchor tags.\n         * Note: this is lazily instantiated in the {@link #getTagBuilder} method.\n         */\n        this.tagBuilder = null;\n        // Note: when `this.something` is used in the rhs of these assignments,\n        //       it refers to the default values set above the constructor\n        this.urls = this.normalizeUrlsCfg(cfg.urls);\n        this.email = typeof cfg.email === 'boolean' ? cfg.email : this.email;\n        this.phone = typeof cfg.phone === 'boolean' ? cfg.phone : this.phone;\n        this.hashtag = cfg.hashtag || this.hashtag;\n        this.mention = cfg.mention || this.mention;\n        this.newWindow = typeof cfg.newWindow === 'boolean' ? cfg.newWindow : this.newWindow;\n        this.stripPrefix = this.normalizeStripPrefixCfg(cfg.stripPrefix);\n        this.stripTrailingSlash = typeof cfg.stripTrailingSlash === 'boolean' ? cfg.stripTrailingSlash : this.stripTrailingSlash;\n        this.decodePercentEncoding = typeof cfg.decodePercentEncoding === 'boolean' ? cfg.decodePercentEncoding : this.decodePercentEncoding;\n        // Validate the value of the `mention` cfg\n        var mention = this.mention;\n        if (mention !== false && mention !== 'twitter' && mention !== 'instagram' && mention !== 'soundcloud') {\n            throw new Error(\"invalid `mention` cfg - see docs\");\n        }\n        // Validate the value of the `hashtag` cfg\n        var hashtag = this.hashtag;\n        if (hashtag !== false && hashtag !== 'twitter' && hashtag !== 'facebook' && hashtag !== 'instagram') {\n            throw new Error(\"invalid `hashtag` cfg - see docs\");\n        }\n        this.truncate = this.normalizeTruncateCfg(cfg.truncate);\n        this.className = cfg.className || this.className;\n        this.replaceFn = cfg.replaceFn || this.replaceFn;\n        this.context = cfg.context || this;\n    }\n    /**\n     * Automatically links URLs, Email addresses, Phone Numbers, Twitter handles,\n     * Hashtags, and Mentions found in the given chunk of HTML. Does not link URLs\n     * found within HTML tags.\n     *\n     * For instance, if given the text: `You should go to http://www.yahoo.com`,\n     * then the result will be `You should go to &lt;a href=\"http://www.yahoo.com\"&gt;http://www.yahoo.com&lt;/a&gt;`\n     *\n     * Example:\n     *\n     *     var linkedText = Autolinker.link( \"Go to google.com\", { newWindow: false } );\n     *     // Produces: \"Go to <a href=\"http://google.com\">google.com</a>\"\n     *\n     * @static\n     * @param {String} textOrHtml The HTML or text to find matches within (depending\n     *   on if the {@link #urls}, {@link #email}, {@link #phone}, {@link #mention},\n     *   {@link #hashtag}, and {@link #mention} options are enabled).\n     * @param {Object} [options] Any of the configuration options for the Autolinker\n     *   class, specified in an Object (map). See the class description for an\n     *   example call.\n     * @return {String} The HTML text, with matches automatically linked.\n     */\n    Autolinker.link = function (textOrHtml, options) {\n        var autolinker = new Autolinker(options);\n        return autolinker.link(textOrHtml);\n    };\n    /**\n     * Parses the input `textOrHtml` looking for URLs, email addresses, phone\n     * numbers, username handles, and hashtags (depending on the configuration\n     * of the Autolinker instance), and returns an array of {@link Autolinker.match.Match}\n     * objects describing those matches (without making any replacements).\n     *\n     * Note that if parsing multiple pieces of text, it is slightly more efficient\n     * to create an Autolinker instance, and use the instance-level {@link #parse}\n     * method.\n     *\n     * Example:\n     *\n     *     var matches = Autolinker.parse( \"Hello google.com, <NAME_EMAIL>\", {\n     *         urls: true,\n     *         email: true\n     *     } );\n     *\n     *     console.log( matches.length );           // 2\n     *     console.log( matches[ 0 ].getType() );   // 'url'\n     *     console.log( matches[ 0 ].getUrl() );    // 'google.com'\n     *     console.log( matches[ 1 ].getType() );   // 'email'\n     *     console.log( matches[ 1 ].getEmail() );  // '<EMAIL>'\n     *\n     * @static\n     * @param {String} textOrHtml The HTML or text to find matches within\n     *   (depending on if the {@link #urls}, {@link #email}, {@link #phone},\n     *   {@link #hashtag}, and {@link #mention} options are enabled).\n     * @param {Object} [options] Any of the configuration options for the Autolinker\n     *   class, specified in an Object (map). See the class description for an\n     *   example call.\n     * @return {Autolinker.match.Match[]} The array of Matches found in the\n     *   given input `textOrHtml`.\n     */\n    Autolinker.parse = function (textOrHtml, options) {\n        var autolinker = new Autolinker(options);\n        return autolinker.parse(textOrHtml);\n    };\n    /**\n     * Normalizes the {@link #urls} config into an Object with 3 properties:\n     * `schemeMatches`, `wwwMatches`, and `tldMatches`, all Booleans.\n     *\n     * See {@link #urls} config for details.\n     *\n     * @private\n     * @param {Boolean/Object} urls\n     * @return {Object}\n     */\n    Autolinker.prototype.normalizeUrlsCfg = function (urls) {\n        if (urls == null)\n            urls = true; // default to `true`\n        if (typeof urls === 'boolean') {\n            return { schemeMatches: urls, wwwMatches: urls, tldMatches: urls };\n        }\n        else { // object form\n            return {\n                schemeMatches: typeof urls.schemeMatches === 'boolean' ? urls.schemeMatches : true,\n                wwwMatches: typeof urls.wwwMatches === 'boolean' ? urls.wwwMatches : true,\n                tldMatches: typeof urls.tldMatches === 'boolean' ? urls.tldMatches : true\n            };\n        }\n    };\n    /**\n     * Normalizes the {@link #stripPrefix} config into an Object with 2\n     * properties: `scheme`, and `www` - both Booleans.\n     *\n     * See {@link #stripPrefix} config for details.\n     *\n     * @private\n     * @param {Boolean/Object} stripPrefix\n     * @return {Object}\n     */\n    Autolinker.prototype.normalizeStripPrefixCfg = function (stripPrefix) {\n        if (stripPrefix == null)\n            stripPrefix = true; // default to `true`\n        if (typeof stripPrefix === 'boolean') {\n            return { scheme: stripPrefix, www: stripPrefix };\n        }\n        else { // object form\n            return {\n                scheme: typeof stripPrefix.scheme === 'boolean' ? stripPrefix.scheme : true,\n                www: typeof stripPrefix.www === 'boolean' ? stripPrefix.www : true\n            };\n        }\n    };\n    /**\n     * Normalizes the {@link #truncate} config into an Object with 2 properties:\n     * `length` (Number), and `location` (String).\n     *\n     * See {@link #truncate} config for details.\n     *\n     * @private\n     * @param {Number/Object} truncate\n     * @return {Object}\n     */\n    Autolinker.prototype.normalizeTruncateCfg = function (truncate) {\n        if (typeof truncate === 'number') {\n            return { length: truncate, location: 'end' };\n        }\n        else { // object, or undefined/null\n            return defaults(truncate || {}, {\n                length: Number.POSITIVE_INFINITY,\n                location: 'end'\n            });\n        }\n    };\n    /**\n     * Parses the input `textOrHtml` looking for URLs, email addresses, phone\n     * numbers, username handles, and hashtags (depending on the configuration\n     * of the Autolinker instance), and returns an array of {@link Autolinker.match.Match}\n     * objects describing those matches (without making any replacements).\n     *\n     * This method is used by the {@link #link} method, but can also be used to\n     * simply do parsing of the input in order to discover what kinds of links\n     * there are and how many.\n     *\n     * Example usage:\n     *\n     *     var autolinker = new Autolinker( {\n     *         urls: true,\n     *         email: true\n     *     } );\n     *\n     *     var matches = autolinker.parse( \"Hello google.com, <NAME_EMAIL>\" );\n     *\n     *     console.log( matches.length );           // 2\n     *     console.log( matches[ 0 ].getType() );   // 'url'\n     *     console.log( matches[ 0 ].getUrl() );    // 'google.com'\n     *     console.log( matches[ 1 ].getType() );   // 'email'\n     *     console.log( matches[ 1 ].getEmail() );  // '<EMAIL>'\n     *\n     * @param {String} textOrHtml The HTML or text to find matches within\n     *   (depending on if the {@link #urls}, {@link #email}, {@link #phone},\n     *   {@link #hashtag}, and {@link #mention} options are enabled).\n     * @return {Autolinker.match.Match[]} The array of Matches found in the\n     *   given input `textOrHtml`.\n     */\n    Autolinker.prototype.parse = function (textOrHtml) {\n        var _this = this;\n        var skipTagNames = ['a', 'style', 'script'], skipTagsStackCount = 0, // used to only Autolink text outside of anchor/script/style tags. We don't want to autolink something that is already linked inside of an <a> tag, for instance\n        matches = [];\n        // Find all matches within the `textOrHtml` (but not matches that are\n        // already nested within <a>, <style> and <script> tags)\n        parseHtml(textOrHtml, {\n            onOpenTag: function (tagName) {\n                if (skipTagNames.indexOf(tagName) >= 0) {\n                    skipTagsStackCount++;\n                }\n            },\n            onText: function (text, offset) {\n                // Only process text nodes that are not within an <a>, <style> or <script> tag\n                if (skipTagsStackCount === 0) {\n                    // \"Walk around\" common HTML entities. An '&nbsp;' (for example)\n                    // could be at the end of a URL, but we don't want to \n                    // include the trailing '&' in the URL. See issue #76\n                    // TODO: Handle HTML entities separately in parseHtml() and\n                    // don't emit them as \"text\" except for &amp; entities\n                    var htmlCharacterEntitiesRegex = /(&nbsp;|&#160;|&lt;|&#60;|&gt;|&#62;|&quot;|&#34;|&#39;)/gi;\n                    var textSplit = splitAndCapture(text, htmlCharacterEntitiesRegex);\n                    var currentOffset_1 = offset;\n                    textSplit.forEach(function (splitText, i) {\n                        // even number matches are text, odd numbers are html entities\n                        if (i % 2 === 0) {\n                            var textNodeMatches = _this.parseText(splitText, currentOffset_1);\n                            matches.push.apply(matches, textNodeMatches);\n                        }\n                        currentOffset_1 += splitText.length;\n                    });\n                }\n            },\n            onCloseTag: function (tagName) {\n                if (skipTagNames.indexOf(tagName) >= 0) {\n                    skipTagsStackCount = Math.max(skipTagsStackCount - 1, 0); // attempt to handle extraneous </a> tags by making sure the stack count never goes below 0\n                }\n            },\n            onComment: function (offset) { },\n            onDoctype: function (offset) { },\n        });\n        // After we have found all matches, remove subsequent matches that\n        // overlap with a previous match. This can happen for instance with URLs,\n        // where the url 'google.com/#link' would match '#link' as a hashtag.\n        matches = this.compactMatches(matches);\n        // And finally, remove matches for match types that have been turned\n        // off. We needed to have all match types turned on initially so that\n        // things like hashtags could be filtered out if they were really just\n        // part of a URL match (for instance, as a named anchor).\n        matches = this.removeUnwantedMatches(matches);\n        return matches;\n    };\n    /**\n     * After we have found all matches, we need to remove matches that overlap\n     * with a previous match. This can happen for instance with URLs, where the\n     * url 'google.com/#link' would match '#link' as a hashtag. Because the\n     * '#link' part is contained in a larger match that comes before the HashTag\n     * match, we'll remove the HashTag match.\n     *\n     * @private\n     * @param {Autolinker.match.Match[]} matches\n     * @return {Autolinker.match.Match[]}\n     */\n    Autolinker.prototype.compactMatches = function (matches) {\n        // First, the matches need to be sorted in order of offset\n        matches.sort(function (a, b) { return a.getOffset() - b.getOffset(); });\n        for (var i = 0; i < matches.length - 1; i++) {\n            var match = matches[i], offset = match.getOffset(), matchedTextLength = match.getMatchedText().length, endIdx = offset + matchedTextLength;\n            if (i + 1 < matches.length) {\n                // Remove subsequent matches that equal offset with current match\n                if (matches[i + 1].getOffset() === offset) {\n                    var removeIdx = matches[i + 1].getMatchedText().length > matchedTextLength ? i : i + 1;\n                    matches.splice(removeIdx, 1);\n                    continue;\n                }\n                // Remove subsequent matches that overlap with the current match\n                if (matches[i + 1].getOffset() < endIdx) {\n                    matches.splice(i + 1, 1);\n                }\n            }\n        }\n        return matches;\n    };\n    /**\n     * Removes matches for matchers that were turned off in the options. For\n     * example, if {@link #hashtag hashtags} were not to be matched, we'll\n     * remove them from the `matches` array here.\n     *\n     * Note: we *must* use all Matchers on the input string, and then filter\n     * them out later. For example, if the options were `{ url: false, hashtag: true }`,\n     * we wouldn't want to match the text '#link' as a HashTag inside of the text\n     * 'google.com/#link'. The way the algorithm works is that we match the full\n     * URL first (which prevents the accidental HashTag match), and then we'll\n     * simply throw away the URL match.\n     *\n     * @private\n     * @param {Autolinker.match.Match[]} matches The array of matches to remove\n     *   the unwanted matches from. Note: this array is mutated for the\n     *   removals.\n     * @return {Autolinker.match.Match[]} The mutated input `matches` array.\n     */\n    Autolinker.prototype.removeUnwantedMatches = function (matches) {\n        if (!this.hashtag)\n            remove(matches, function (match) { return match.getType() === 'hashtag'; });\n        if (!this.email)\n            remove(matches, function (match) { return match.getType() === 'email'; });\n        if (!this.phone)\n            remove(matches, function (match) { return match.getType() === 'phone'; });\n        if (!this.mention)\n            remove(matches, function (match) { return match.getType() === 'mention'; });\n        if (!this.urls.schemeMatches) {\n            remove(matches, function (m) { return m.getType() === 'url' && m.getUrlMatchType() === 'scheme'; });\n        }\n        if (!this.urls.wwwMatches) {\n            remove(matches, function (m) { return m.getType() === 'url' && m.getUrlMatchType() === 'www'; });\n        }\n        if (!this.urls.tldMatches) {\n            remove(matches, function (m) { return m.getType() === 'url' && m.getUrlMatchType() === 'tld'; });\n        }\n        return matches;\n    };\n    /**\n     * Parses the input `text` looking for URLs, email addresses, phone\n     * numbers, username handles, and hashtags (depending on the configuration\n     * of the Autolinker instance), and returns an array of {@link Autolinker.match.Match}\n     * objects describing those matches.\n     *\n     * This method processes a **non-HTML string**, and is used to parse and\n     * match within the text nodes of an HTML string. This method is used\n     * internally by {@link #parse}.\n     *\n     * @private\n     * @param {String} text The text to find matches within (depending on if the\n     *   {@link #urls}, {@link #email}, {@link #phone},\n     *   {@link #hashtag}, and {@link #mention} options are enabled). This must be a non-HTML string.\n     * @param {Number} [offset=0] The offset of the text node within the\n     *   original string. This is used when parsing with the {@link #parse}\n     *   method to generate correct offsets within the {@link Autolinker.match.Match}\n     *   instances, but may be omitted if calling this method publicly.\n     * @return {Autolinker.match.Match[]} The array of Matches found in the\n     *   given input `text`.\n     */\n    Autolinker.prototype.parseText = function (text, offset) {\n        if (offset === void 0) { offset = 0; }\n        offset = offset || 0;\n        var matchers = this.getMatchers(), matches = [];\n        for (var i = 0, numMatchers = matchers.length; i < numMatchers; i++) {\n            var textMatches = matchers[i].parseMatches(text);\n            // Correct the offset of each of the matches. They are originally\n            // the offset of the match within the provided text node, but we\n            // need to correct them to be relative to the original HTML input\n            // string (i.e. the one provided to #parse).\n            for (var j = 0, numTextMatches = textMatches.length; j < numTextMatches; j++) {\n                textMatches[j].setOffset(offset + textMatches[j].getOffset());\n            }\n            matches.push.apply(matches, textMatches);\n        }\n        return matches;\n    };\n    /**\n     * Automatically links URLs, Email addresses, Phone numbers, Hashtags,\n     * and Mentions (Twitter, Instagram, Soundcloud) found in the given chunk of HTML. Does not link\n     * URLs found within HTML tags.\n     *\n     * For instance, if given the text: `You should go to http://www.yahoo.com`,\n     * then the result will be `You should go to\n     * &lt;a href=\"http://www.yahoo.com\"&gt;http://www.yahoo.com&lt;/a&gt;`\n     *\n     * This method finds the text around any HTML elements in the input\n     * `textOrHtml`, which will be the text that is processed. Any original HTML\n     * elements will be left as-is, as well as the text that is already wrapped\n     * in anchor (&lt;a&gt;) tags.\n     *\n     * @param {String} textOrHtml The HTML or text to autolink matches within\n     *   (depending on if the {@link #urls}, {@link #email}, {@link #phone}, {@link #hashtag}, and {@link #mention} options are enabled).\n     * @return {String} The HTML, with matches automatically linked.\n     */\n    Autolinker.prototype.link = function (textOrHtml) {\n        if (!textOrHtml) {\n            return \"\";\n        } // handle `null` and `undefined`\n        var matches = this.parse(textOrHtml), newHtml = [], lastIndex = 0;\n        for (var i = 0, len = matches.length; i < len; i++) {\n            var match = matches[i];\n            newHtml.push(textOrHtml.substring(lastIndex, match.getOffset()));\n            newHtml.push(this.createMatchReturnVal(match));\n            lastIndex = match.getOffset() + match.getMatchedText().length;\n        }\n        newHtml.push(textOrHtml.substring(lastIndex)); // handle the text after the last match\n        return newHtml.join('');\n    };\n    /**\n     * Creates the return string value for a given match in the input string.\n     *\n     * This method handles the {@link #replaceFn}, if one was provided.\n     *\n     * @private\n     * @param {Autolinker.match.Match} match The Match object that represents\n     *   the match.\n     * @return {String} The string that the `match` should be replaced with.\n     *   This is usually the anchor tag string, but may be the `matchStr` itself\n     *   if the match is not to be replaced.\n     */\n    Autolinker.prototype.createMatchReturnVal = function (match) {\n        // Handle a custom `replaceFn` being provided\n        var replaceFnResult;\n        if (this.replaceFn) {\n            replaceFnResult = this.replaceFn.call(this.context, match); // Autolinker instance is the context\n        }\n        if (typeof replaceFnResult === 'string') {\n            return replaceFnResult; // `replaceFn` returned a string, use that\n        }\n        else if (replaceFnResult === false) {\n            return match.getMatchedText(); // no replacement for the match\n        }\n        else if (replaceFnResult instanceof HtmlTag) {\n            return replaceFnResult.toAnchorString();\n        }\n        else { // replaceFnResult === true, or no/unknown return value from function\n            // Perform Autolinker's default anchor tag generation\n            var anchorTag = match.buildTag(); // returns an Autolinker.HtmlTag instance\n            return anchorTag.toAnchorString();\n        }\n    };\n    /**\n     * Lazily instantiates and returns the {@link Autolinker.matcher.Matcher}\n     * instances for this Autolinker instance.\n     *\n     * @private\n     * @return {Autolinker.matcher.Matcher[]}\n     */\n    Autolinker.prototype.getMatchers = function () {\n        if (!this.matchers) {\n            var tagBuilder = this.getTagBuilder();\n            var matchers = [\n                new HashtagMatcher({ tagBuilder: tagBuilder, serviceName: this.hashtag }),\n                new EmailMatcher({ tagBuilder: tagBuilder }),\n                new PhoneMatcher({ tagBuilder: tagBuilder }),\n                new MentionMatcher({ tagBuilder: tagBuilder, serviceName: this.mention }),\n                new UrlMatcher({ tagBuilder: tagBuilder, stripPrefix: this.stripPrefix, stripTrailingSlash: this.stripTrailingSlash, decodePercentEncoding: this.decodePercentEncoding })\n            ];\n            return (this.matchers = matchers);\n        }\n        else {\n            return this.matchers;\n        }\n    };\n    /**\n     * Returns the {@link #tagBuilder} instance for this Autolinker instance,\n     * lazily instantiating it if it does not yet exist.\n     *\n     * @private\n     * @return {Autolinker.AnchorTagBuilder}\n     */\n    Autolinker.prototype.getTagBuilder = function () {\n        var tagBuilder = this.tagBuilder;\n        if (!tagBuilder) {\n            tagBuilder = this.tagBuilder = new AnchorTagBuilder({\n                newWindow: this.newWindow,\n                truncate: this.truncate,\n                className: this.className\n            });\n        }\n        return tagBuilder;\n    };\n    /**\n     * @static\n     * @property {String} version\n     *\n     * The Autolinker version number in the form major.minor.patch\n     *\n     * Ex: 0.25.1\n     */\n    Autolinker.version = '3.11.1';\n    /**\n     * For backwards compatibility with Autolinker 1.x, the AnchorTagBuilder\n     * class is provided as a static on the Autolinker class.\n     */\n    Autolinker.AnchorTagBuilder = AnchorTagBuilder;\n    /**\n     * For backwards compatibility with Autolinker 1.x, the HtmlTag class is\n     * provided as a static on the Autolinker class.\n     */\n    Autolinker.HtmlTag = HtmlTag;\n    /**\n     * For backwards compatibility with Autolinker 1.x, the Matcher classes are\n     * provided as statics on the Autolinker class.\n     */\n    Autolinker.matcher = {\n        Email: EmailMatcher,\n        Hashtag: HashtagMatcher,\n        Matcher: Matcher,\n        Mention: MentionMatcher,\n        Phone: PhoneMatcher,\n        Url: UrlMatcher\n    };\n    /**\n     * For backwards compatibility with Autolinker 1.x, the Match classes are\n     * provided as statics on the Autolinker class.\n     */\n    Autolinker.match = {\n        Email: EmailMatch,\n        Hashtag: HashtagMatch,\n        Match: Match,\n        Mention: MentionMatch,\n        Phone: PhoneMatch,\n        Url: UrlMatch\n    };\n    return Autolinker;\n}());\nexport default Autolinker;\n\n//# sourceMappingURL=autolinker.js.map\n"]}