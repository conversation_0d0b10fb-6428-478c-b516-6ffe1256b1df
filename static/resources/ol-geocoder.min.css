/*!
 * ol-geocoder - v4.3.4
 * A geocoder extension compatible with OpenLayers v6.x to v9.0
 * https://github.com/Dominique92/ol-geocoder
 * Built: 15/03/2024 09:40:16
 */
 
 .ol-touch .ol-control.gcd-gl-control button{font-size:1.14em}.ol-touch .ol-geocoder.gcd-gl-container{font-size:1.1em}.ol-geocoder.gcd-gl-container{box-sizing:border-box;font-size:.9em;left:.5em;position:absolute;top:4.875em}.ol-geocoder.gcd-gl-container *,.ol-geocoder.gcd-gl-container :after,.ol-geocoder.gcd-gl-container :before{box-sizing:inherit}.ol-geocoder .gcd-gl-control{height:2.1875em;overflow:hidden;transition:width .2s,height .2s;width:2.1875em}.ol-geocoder .gcd-gl-expanded{height:2.1875em;width:15.625em}.ol-geocoder .gcd-gl-input{background-color:#fff;border:1px solid #ccc;color:#222;font-family:inherit;font-size:.875em;left:2.5em;padding:5px;position:absolute;top:.25em;width:14.84375em;z-index:99}.ol-geocoder .gcd-gl-input:focus{border:none;box-shadow:inset 0 0 0 1px #4d90fe,inset 0 0 5px #4d90fe;outline:none}.ol-geocoder .gcd-gl-search{background-color:transparent;border:none;cursor:pointer;display:inline-block;height:100%;line-height:1.4;outline:0;position:absolute;right:0;top:0;width:1.5625em;z-index:100}.ol-geocoder .gcd-gl-search:after{color:#333;content:"\2386";cursor:pointer;display:inline-block;font-size:1.5em}.ol-geocoder .gcd-gl-btn{cursor:pointer;height:1.5625em;left:.125em;position:absolute;top:.125em;width:1.5625em}.ol-geocoder .gcd-gl-btn:after{content:"\1F50D"}.ol-geocoder ul.gcd-gl-result{background-color:#fff;border-radius:4px;border-top:none;border-top-left-radius:0;border-top-right-radius:0;box-shadow:0 1px 7px rgba(0,0,0,.8);left:2em;list-style:none;margin:0;max-height:18.75em;overflow-x:hidden;overflow-y:auto;padding:0;position:absolute;top:2.1875em;transition:max-height .3s ease-in;white-space:normal;width:16.25em}.ol-geocoder ul.gcd-gl-result>li{border-bottom:1px solid #eee;line-height:.875rem;overflow:hidden;padding:0;width:100%}.ol-geocoder ul.gcd-gl-result>li>a{display:block;padding:3px 5px;text-decoration:none}.ol-geocoder ul.gcd-gl-result>li>a:hover{background-color:#d4d4d4}.ol-geocoder ul.gcd-gl-result>li:nth-child(odd){background-color:#e0ffe0}.ol-geocoder ul.gcd-gl-result:empty{display:none}.ol-geocoder.gcd-txt-container{box-sizing:border-box;height:4.375em;left:calc(50% - 12.5em);position:absolute;top:.5em;width:25em}.ol-geocoder.gcd-txt-container *,.ol-geocoder.gcd-txt-container :after,.ol-geocoder.gcd-txt-container :before{box-sizing:inherit}.ol-geocoder .gcd-txt-control{background-color:#fff;border:1px solid #ccc;height:4.375em;overflow:hidden;position:relative;width:100%}.ol-geocoder .gcd-txt-label{display:inline-block;text-align:center;width:100%}.ol-geocoder .gcd-txt-input{background-color:transparent;border:none;font-family:inherit;font-size:.875em;height:100%;left:0;padding:5px 30px 5px 40px;position:absolute;text-indent:6px;top:0;width:100%;z-index:99}.ol-geocoder .gcd-txt-input:focus{box-shadow:inset 0 0 0 1px #4d90fe,inset 0 0 6px #4d90fe;outline:none}.ol-geocoder .gcd-txt-search{background-color:transparent;border:none;cursor:pointer;display:inline-block;height:100%;line-height:100%;outline:0;position:absolute;right:0;top:0;vertical-align:middle;width:2.5em;z-index:100}.ol-geocoder .gcd-txt-search:after{color:#333;content:"\2386";cursor:pointer;display:inline-block;font-size:2em}.ol-geocoder .gcd-txt-glass{display:inline-block;height:100%;left:9px;position:absolute;top:26px;width:2.5em;z-index:100}.ol-geocoder .gcd-txt-glass:after{content:"\1F50D"}.ol-geocoder ul.gcd-txt-result{background-color:#fff;border-radius:4px;border-top:none;border-top-left-radius:0;border-top-right-radius:0;box-shadow:0 1px 7px rgba(0,0,0,.8);left:0;list-style:none;margin:0;max-height:18.75em;overflow-x:hidden;overflow-y:auto;padding:0;position:absolute;top:4.575em;transition:max-height .3s ease-in;white-space:normal;width:25em}.ol-geocoder ul.gcd-txt-result>li{border-bottom:1px solid #eee;line-height:.875rem;overflow:hidden;padding:0;width:100%}.ol-geocoder ul.gcd-txt-result>li>a{display:block;padding:3px 5px;text-decoration:none}.ol-geocoder ul.gcd-txt-result>li>a:hover{background-color:#d4d4d4}.ol-geocoder ul.gcd-txt-result>li:nth-child(odd){background-color:#e0ffe0}.ol-geocoder ul.gcd-txt-result:empty{display:none}.ol-geocoder .gcd-hidden{opacity:0;visibility:hidden}.ol-geocoder .gcd-pseudo-rotate:after{animation:spin .7s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}.gcd-address,.gcd-road{color:#333;font-size:.875em;font-weight:500}.gcd-city{font-weight:400}.gcd-city,.gcd-country{color:#333;font-size:.75em}.gcd-country{font-weight:lighter}