from django.contrib.auth import get_user_model

from rest_framework import serializers


from ocorrencias.models import Congestionamento, Interdicao, Ocorrencia, Foto, OcorrenciaAtualizacao, OcorrenciaFim, OcorrenciaVeiculo

from municipios_api.serializers import MunicipioSerializer
from concessionarias_api.serializers import ConcessionariaSerializer
from rodovias_api.serializers import RodoviaSerializer, TrechoSerializer


class FotoSerializer(serializers.ModelSerializer):
    adicionada_por = serializers.CharField(source='adicionada_por.first_name', read_only=True)
    ocid = serializers.IntegerField(source='ocorrencia.pk')
    class Meta:
        model = Foto
        fields = ['id', 'ocid', 'foto', 'legenda', 'adicionada_por']


class CongestionamentoSerializer(serializers.ModelSerializer):
    ocid = serializers.IntegerField(source='ocorrencia.pk')
    class Meta:
        model = Congestionamento
        fields = ['id', 'ocid', 'pista', 'sentido', 'hora_inicio', 'hora_termino', 'km_inicial', 'km_final', 'obs']


class OcorrenciaFimSerializer(serializers.ModelSerializer):
    # ocid = serializers.IntegerField(source='ocorrencia.pk')

    class Meta:
        model = OcorrenciaFim
        # fields = ['id', 'ocid', 'tipo', 'qtd', 'obs']
        fields = ['tipo', 'qtd']


class OcorrenciaVeiculoSerializer(serializers.ModelSerializer):
    ocid = serializers.IntegerField(source='ocorrencia.pk')

    class Meta:
        model = OcorrenciaVeiculo
        exclude = ['publica', 'ocorrencia']

class OcorrenciaAtualizacaoSerializer(serializers.ModelSerializer):
    ocid = serializers.IntegerField(source='ocorrencia.pk')
    oc_atualizada_por = serializers.CharField(source='oc_atualizada_por.first_name', read_only=True)

    class Meta:
        model = OcorrenciaAtualizacao
        fields = ['id', 'ocid', 'atualizacao', 'oc_atualizada_em', 'oc_atualizada_por']

class InterdicaoSerializer(serializers.ModelSerializer):
    ocid = serializers.IntegerField(source='ocorrencia.pk')

    class Meta:
        model = Interdicao
        exclude = ['ocorrencia']


class OcorrenciaSerializer(serializers.ModelSerializer):
    criado_por = serializers.CharField(source='criado_por.first_name', read_only=True)
    atualizado_por = serializers.CharField(source='atualizado_por.first_name', read_only=True)
    # rodovia = serializers.CharField(source='rodovia.codigo', read_only=True)
    # municipios = serializers.StringRelatedField(many=True, read_only=True)
    # concessionarias = serializers.StringRelatedField(many=True, read_only=True)
    # fotos = FotoSerializer(many=True, read_only=True)
    # congestionamento = CongestionamentoSerializer(many=True, read_only=True)
    # interdicao = InterdicaoSerializer(many=True, read_only=True)
    # fim = OcorrenciaFimSerializer(many=True, read_only=True)
    # atualizacao = OcorrenciaAtualizacaoSerializer(many=True, read_only=True)
    # veiculos = OcorrenciaVeiculoSerializer(many=True, read_only=True)

    class Meta:
        model = Ocorrencia
        fields = [
            'id',
            'classe', 'subclasse_oc', 'subclasse_ac',
            'tipo_ac', 'dinamica_oc', 'cod_conc',
            'slug', 'rodovia', 'concessionarias',
            'km_inicial', 'km_final', 'cond_climaticas',
            'dt_hr_conhecimento', 'dt_hr_oc', 'dt_hr_termino',
            'municipios', 'origem_comunicacao_conc', 'origem_comunicacao_cci',
            'pista', 'sentido', 'lat', 'lng', 'sinalizacao', 'comunicacao_usuario',
            'danos_patrimonio', 'danos_patrimonio_obs', 'recursos_internos',
            'recursos_externos', 'acoes_operacionais', 'observacoes_cci',
            'criado_em', 'criado_por', 'atualizado_em', 'atualizado_por',
            'fim',
        ]


class OcorrenciaPublicSerializer(serializers.ModelSerializer):
    criado_por = serializers.CharField(source='criado_por.first_name', read_only=True)
    atualizado_por = serializers.CharField(source='atualizado_por.first_name', read_only=True)
    concessionarias = serializers.StringRelatedField(many=True, read_only=True)
    rodovia = serializers.CharField(source='rodovia.codigo', read_only=True)
    municipios = serializers.StringRelatedField(many=True, read_only=True)
    # fotos = FotoSerializer(many=True, read_only=True)
    # congestionamento = CongestionamentoSerializer(many=True, read_only=True)
    # interdicao = InterdicaoSerializer(many=True, read_only=True)
    fim = OcorrenciaFimSerializer(many=True, read_only=True)
    # atualizacao = OcorrenciaAtualizacaoSerializer(many=True, read_only=True)
    # veiculos = OcorrenciaVeiculoSerializer(many=True, read_only=True)


    class Meta:
        model = Ocorrencia
        fields = [
            'id',
            'classe', 'subclasse_oc', 'subclasse_ac',
            'tipo_ac', 'dinamica_oc', 'cod_conc',
            'slug', 'rodovia', 'concessionarias',
            'km_inicial', 'km_final', 'cond_climaticas',
            'dt_hr_conhecimento', 'dt_hr_oc', 'dt_hr_termino',
            'municipios', 'origem_comunicacao_conc', 'origem_comunicacao_cci',
            'pista', 'sentido', 'lat', 'lng', 'sinalizacao', 'comunicacao_usuario',
            'danos_patrimonio', 'danos_patrimonio_obs', 'recursos_internos',
            'recursos_externos', 'acoes_operacionais', 'observacoes_cci',
            'criado_em', 'criado_por', 'atualizado_em', 'atualizado_por',
            'fim',
        ]
        #depth = 1
    #     extra_fields = ['fotos']

    # def get_field_names(self, declared_fields, info):
    #     expanded_fields = super(OcorrenciaSerializer, self).get_field_names(declared_fields, info)

    #     if getattr(self.Meta, 'extra_fields', None):
    #         return expanded_fields + self.Meta.extra_fields
    #     else:
    #         return expanded_fields
