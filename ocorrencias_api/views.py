import datetime
from rest_framework.permissions import BasePermission, IsAuthenticated, SAFE_METHODS
from rest_framework import viewsets
from rest_framework import pagination
from rest_framework.serializers import HyperlinkedRelated<PERSON>ield
from rest_framework.permissions import DjangoModelPermissions
from django.db.models import Q

from .serializers import CongestionamentoSerializer, InterdicaoSerializer, OcorrenciaAtualizacaoSerializer, OcorrenciaPublicSerializer, OcorrenciaSerializer, FotoSerializer, OcorrenciaFimSerializer, \
    OcorrenciaVeiculoSerializer
from ocorrencias.models import Congestionamento, Interdicao, Ocorrencia, Foto, OcorrenciaAtualizacao, OcorrenciaFim, OcorrenciaVeiculo

class ReadOnly(BasePermission):
    def has_permission(self, request, view):
        return request.method in SAFE_METHODS

class LargeResultsSetPagination(pagination.LimitOffsetPagination):
    default_limit = 10
    limit_query_param = 'limit'
    offset_query_param = 'offset'
    max_limit = 1000


class FotoListViewSet(viewsets.ModelViewSet):
    queryset = Foto.objects.all()
    serializer_class = FotoSerializer


class OcorrenciaFimListViewSet(viewsets.ModelViewSet):
    queryset = OcorrenciaFim.objects.all()
    serializer_class = OcorrenciaFimSerializer


class OcorrenciaVeiculoListViewSet(viewsets.ModelViewSet):
    queryset = OcorrenciaVeiculo.objects.all()
    serializer_class = OcorrenciaVeiculoSerializer


class OcorrenciaViewSet(viewsets.ModelViewSet):
    fotos = HyperlinkedRelatedField(many=True, read_only=True, view_name='api-fotos-detail')
    permission_classes = [DjangoModelPermissions]
    serializer_class = OcorrenciaSerializer
    pagination_class = LargeResultsSetPagination


    def get_queryset(self):
        dias = self.request.query_params.get('dias', 30)
        dias = int(dias)
        _now = datetime.datetime.now()
        nro_dias_atras = _now + datetime.timedelta(days=-dias)
        ocorrencias_qs = Ocorrencia.objects\
            .filter(Q(dt_hr_termino__gt=nro_dias_atras) | Q(dt_hr_termino__isnull=True))\
                .filter(publicada=True)

        ocorrencias_qs = ocorrencias_qs.prefetch_related(
            'rodovia','concessionarias','municipios','atualizacao', 'atualizado_por', 'interdicao')\
                .order_by('-dt_hr_termino', '-atualizado_em').filter(publicada=True)
        return ocorrencias_qs


class OcorrenciaPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    serializer_class = OcorrenciaPublicSerializer
    pagination_class = LargeResultsSetPagination
    


    def get_queryset(self):
        try:
            dias = self.request.query_params.get('dias', 30)
            dias = int(dias)

            data = self.request.query_params.get('data', None)
            if data:
                data = datetime.datetime.strptime(data, '%Y-%m-%d')
                data_plus_one = data + datetime.timedelta(days=1)

            data_inicial = self.request.query_params.get('dataInicial', None)
            data_final = self.request.query_params.get('dataFinal', None)


            if data_inicial and data_final:
                data_inicial = datetime.datetime.strptime(data_inicial, '%Y-%m-%d')
                data_final = datetime.datetime.strptime(data_final, '%Y-%m-%d')

        except Exception as err:
            print('ERRO! ', err)
            data = None
            data_inicial = None
            data_final = None
            dias = 30

        if data_inicial and data_final:
            if data_inicial > data_final:
                data_inicial, data_final = data_final, data_inicial
            return Ocorrencia.objects.prefetch_related(
                    'rodovia','concessionarias','municipios', 'atualizado_por', 'criado_por')\
                        .filter(Q(dt_hr_oc__gte=data_inicial) & (Q(dt_hr_oc__lte=data_final)))\
                            .filter(publicada=True)
        if data:
            return Ocorrencia.objects.prefetch_related(
                    'rodovia','concessionarias','municipios', 'atualizado_por', 'criado_por')\
                        .filter(Q(dt_hr_oc__gte=data) & (Q(dt_hr_oc__lt=data_plus_one)))\
                            .filter(publicada=True)

        _now = datetime.datetime.now()
        nro_dias_atras = _now + datetime.timedelta(days=-dias)
        ocorrencias_qs = Ocorrencia.objects.prefetch_related(
        'rodovia','concessionarias','municipios','atualizado_por', 'criado_por')\
            .filter(Q(dt_hr_termino__gt=nro_dias_atras) | Q(dt_hr_termino__isnull=True))\
                .filter(publicada=True)

        return ocorrencias_qs


class OcorrenciaFotosPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    serializer_class = FotoSerializer
    

    def get_queryset(self):
        dias = self.request.query_params.get('dias', 30)
        dias = int(dias)
        _now = datetime.datetime.now()
        nro_dias_atras = _now + datetime.timedelta(days=-dias)
        ocorrencias_qs = Ocorrencia.objects\
            .filter(Q(dt_hr_termino__gt=nro_dias_atras) | Q(dt_hr_termino__isnull=True))\
                .filter(publicada=True)

        fotos = Foto.objects.filter(ocorrencia__in=ocorrencias_qs)
        return fotos


class OcorrenciaFimPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    serializer_class = OcorrenciaFimSerializer
    lookup_url_kwarg = "dias"
    

    def get_queryset(self):
        dias = self.request.query_params.get('dias', 30)
        dias = int(dias)
        _now = datetime.datetime.now()
        nro_dias_atras = _now + datetime.timedelta(days=-dias)
        ocorrencias_qs = Ocorrencia.objects\
            .filter(Q(dt_hr_termino__gt=nro_dias_atras) | Q(dt_hr_termino__isnull=True))\
                .filter(publicada=True)

        fim = OcorrenciaFim.objects.filter(ocorrencia__in=ocorrencias_qs)
        return fim


class OcorrenciaCongestionamentoPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    serializer_class = CongestionamentoSerializer
    

    def get_queryset(self):
        dias = self.request.query_params.get('dias', 30)
        dias = int(dias)
        _now = datetime.datetime.now()
        nro_dias_atras = _now + datetime.timedelta(days=-dias)
        ocorrencias_qs = Ocorrencia.objects\
            .filter(Q(dt_hr_termino__gt=nro_dias_atras) | Q(dt_hr_termino__isnull=True))\
                .filter(publicada=True)

        congestionamento = Congestionamento.objects.filter(ocorrencia__in=ocorrencias_qs)
        return congestionamento


class OcorrenciaVeiculoPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    serializer_class = OcorrenciaVeiculoSerializer
    

    def get_queryset(self):
        dias = self.request.query_params.get('dias', 30)
        dias = int(dias)
        _now = datetime.datetime.now()
        nro_dias_atras = _now + datetime.timedelta(days=-dias)
        ocorrencias_qs = Ocorrencia.objects\
            .filter(Q(dt_hr_termino__gt=nro_dias_atras) | Q(dt_hr_termino__isnull=True))\
                .filter(publicada=True)

        veiculos = OcorrenciaVeiculo.objects.filter(ocorrencia__in=ocorrencias_qs)
        return veiculos


class OcorrenciaAtualizacaoPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    serializer_class = OcorrenciaAtualizacaoSerializer
    

    def get_queryset(self):
        dias = self.request.query_params.get('dias', 30)
        dias = int(dias)
        _now = datetime.datetime.now()
        nro_dias_atras = _now + datetime.timedelta(days=-dias)
        ocorrencias_qs = Ocorrencia.objects\
            .filter(Q(dt_hr_termino__gt=nro_dias_atras) | Q(dt_hr_termino__isnull=True))\
                .filter(publicada=True)

        atualizacoes = OcorrenciaAtualizacao.objects\
            .filter(ocorrencia__in=ocorrencias_qs)\
            .filter(atualizacao_apagada_em__isnull=True)
        return atualizacoes


class OcorrenciaInterdicaoPublicViewSet(viewsets.ModelViewSet):
    permission_classes = [ReadOnly]
    http_method_names = ['get']
    serializer_class = InterdicaoSerializer
    

    def get_queryset(self):
        dias = self.request.query_params.get('dias', 30)
        dias = int(dias)
        _now = datetime.datetime.now()
        nro_dias_atras = _now + datetime.timedelta(days=-dias)
        ocorrencias_qs = Ocorrencia.objects\
            .filter(Q(dt_hr_termino__gt=nro_dias_atras) | Q(dt_hr_termino__isnull=True))\
                .filter(publicada=True)

        interdicoes = Interdicao.objects.filter(ocorrencia__in=ocorrencias_qs)
        return interdicoes