from django.db import models
import hashlib

from concessionarias.models import Concessionaria

class Noticia(models.Model):
    title = models.CharField(max_length=255)
    publication_date = models.DateTimeField(
        "Data publicação", auto_now=False, auto_now_add=False, blank=False, null=False)
    link = models.URLField(
        "Link", max_length=500, null=False, blank=False, unique=True,
        help_text="Link da notícia"
    )
    concessionaria = models.ForeignKey(
        Concessionaria, null=False, blank=False, on_delete=models.CASCADE,
        verbose_name='Concessionária', related_name='news'
    )
    slug = models.SlugField("Slug", blank=True,
                            null=True, max_length=255, unique=True, db_index=True,)

    def save(self, *args, **kwargs):
        # Generate hash before saving
        self.hash = self.generate_hash()
        super().save(*args, **kwargs)

    def generate_hash(self):
        return hashlib.sha256(self.link.encode()).hexdigest()
    
    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('noticias-redirect', args=[str(self.slug)])
    
    class Meta:
        ordering: ['-publication_date']
        verbose_name = 'Notícia'
        verbose_name_plural = 'Notícias'
