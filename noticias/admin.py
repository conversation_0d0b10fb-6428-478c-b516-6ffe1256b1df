from django.contrib import admin
from .models import Noticia

class NoticiaAdmin(admin.ModelAdmin):
    list_display = ('title', 'publication_date', 'concessionaria', 'link',)
    list_filter = ('publication_date', 'concessionaria')
    search_fields = ('title', 'link')
    prepopulated_fields = {'slug': ('title',)}  # Automatically populate the slug field from the title
    ordering = ['-publication_date']

admin.site.register(Noticia, NoticiaAdmin)
