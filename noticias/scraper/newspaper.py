import csv
from pathlib import Path
import datetime as dt
from dataclasses import dataclass
from typing import List, Optional
import dateparser


def is_valid(article) -> bool:
    return all(
        [
            article.link,
            article.publication_date,
            article.title,
            article.conc is not None,
        ]
    )


class NewsArticleList:
    def __init__(self, news):
        self.news = self._filter_unique_valid_news(news)
        self._make_all_dates_naive()

    def _filter_unique_valid_news(self, news):
        unique_news = {}
        for article in news:
            if is_valid(article) and article.link not in unique_news:
                unique_news[article.link] = article
        return list(unique_news.values())

    def _make_all_dates_naive(self):
        for article in self.news:
            if article.publication_date and hasattr(article.publication_date, "tzinfo"):
                if article.publication_date.tzinfo is not None:
                    article.publication_date = article.publication_date.replace(tzinfo=None)

    def filter_by_year(self, year) -> "NewsArticleList":
        return NewsArticleList(
            [
                n
                for n in self.news
                if n.publication_date and n.publication_date.year == year
            ]
        )

    def filter_by_min_date(self, min_date: dt.datetime) -> "NewsArticleList":
        if (hasattr(min_date, "tzinfo") and min_date.tzinfo is not None) or not isinstance(min_date, str):
            min_date = min_date.replace(tzinfo=None)
        else:
            min_date = dateparser.parse(min_date, languages=['pt'], settings={'RELATIVE_BASE': dt.datetime.now()}) if min_date else None
        for _n in self.news:
            assert isinstance(_n.publication_date, dt.datetime), f'{_n.publication_date} is not a datetime ({type(_n.publication_date)}) \n {_n}'
        return NewsArticleList(
            [
                n
                for n in self.news
                if n.publication_date and n.publication_date >= min_date
            ]
        )

    def __iter__(self):
        return iter(self.news)
    
    def __len__(self):
        return len(self.news)

    def __str__(self) -> str:
        return "\n".join([str(n) for n in self.news])

    def write_news_to_csv_file(self, filename: str | Path):
        with open(filename, mode="w", newline="", encoding="utf-8") as file:
            writer = csv.writer(file)
            writer.writerow(["title", "link", "publication_date", "conc"])
            for item in self.news:
                pub_date = (
                    item.publication_date.isoformat() if item.publication_date else ""
                )
                writer.writerow([item.title, item.link, pub_date, item.conc])
