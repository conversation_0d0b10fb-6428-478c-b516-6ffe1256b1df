import datetime as dt


from .newspaper import NewsArticleList
from ..scrapers.l88_artesp import scraper as ArtespScraper
from ..scrapers.l00_der import scraper as Der<PERSON>craper
from ..scrapers.l01_autoban import scraper as AutobanScraper
from ..scrapers.l03_tebe import scraper as TebeScraper
from ..scrapers.l06_intervias import scraper as InterviasScraper
from ..scrapers.l11_renovias import scraper as RenoviasScraper
from ..scrapers.l12_viaoeste import scraper as ViaoesteScraper
from ..scrapers.l13_colinas import scraper as ColinasScraper
from ..scrapers.l16_cart import scraper as CartScraper
from ..scrapers.l19_viarondon import scraper as ViarondonScraper
from ..scrapers.l20_spvias import scraper as SpviasScraper
from ..scrapers.l21_tiete import scraper as TieteScraper
from ..scrapers.l22_ecovias import scraper as EcoviasScraper
from ..scrapers.l23_ecopistas import scraper as EcopistasScraper
from ..scrapers.l24_rodoanel import scraper as RodoanelScraper
from ..scrapers.l25_spmar import scraper as <PERSON>pmarScraper
from ..scrapers.l27_tamoios import scraper as TamoiosScraper
from ..scrapers.l28_entrevias import scraper as EntreviasScraper
from ..scrapers.l29_viapaulista import scraper as ViapaulistaScraper
from ..scrapers.l30_eixosp import scraper as EixospScraper
from ..scrapers.l31_econoroeste import scraper as EconoroesteScraper
from ..scrapers.l32_novolitoral import scraper as NovoLitoralScraper



def get_all_news_articles(scrapers):
    news = []
    for scraper in scrapers:
        try:
            scraper_instance = scraper.start()
            news.extend(scraper_instance)
        except Exception as err:
            print(err)

    return news

scrapers = [
    ArtespScraper,
    DerScraper,
    AutobanScraper, 
    #TebeScraper, 
    InterviasScraper, RenoviasScraper,
    ViaoesteScraper, ColinasScraper, CartScraper, ViarondonScraper, SpviasScraper,
    TieteScraper, EcoviasScraper, EcopistasScraper, RodoanelScraper, SpmarScraper,
    TamoiosScraper, EntreviasScraper, ViapaulistaScraper, EixospScraper, EconoroesteScraper,
    NovoLitoralScraper,
]

def get_news():
    news = get_all_news_articles(scrapers)
    current_date = dt.datetime.now()
    last_date_to_include_news = current_date - dt.timedelta(days=365)
    journal = NewsArticleList(news).filter_by_min_date(last_date_to_include_news)
    return journal