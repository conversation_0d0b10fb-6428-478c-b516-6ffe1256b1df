import requests
from bs4 import BeautifulSoup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=84)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class ViarondonScraper:
    def __init__(self, url: str):
        self.url = url
        self.base_domain = "https://www.viarondon.com.br"
        self.items: List[NewsItem] = []

    def fetch_html(self) -> Optional[str]:
        try:
            response = requests.get(self.url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML. Error: {e}")
            return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        containers = soup.find_all('a', class_='vr-noticias__item')

        for container in containers:
            title_tag = container.find('p', class_='vr-noticias__item--title')
            date_tag = container.find('span', class_='vr-noticias__item--data')

            title = title_tag.get_text(strip=True) if title_tag else ''
            link = f"{self.base_domain}{container['href']}" if container else ''
            publication_date = None
            if date_tag:
                date_text = date_tag.get_text(strip=True)
                try:
                    publication_date = datetime.strptime(date_text, '%d/%m/%Y')
                except ValueError as e:
                    print(f"Failed to parse publication date for title '{title}': {e}")
                    print(f"Problematic date text: '{date_text}'")

            article = NewsItem(
                title=title,
                link=link,
                publication_date=publication_date
            )
            self.items.append(article)

    def start(self):
        html = self.fetch_html()
        if html:
            self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"ViarondonScraper(url={self.url}, items_count={len(self.items)})"


url = "https://www.viarondon.com.br/noticias"
scraper = ViarondonScraper(url)


if __name__ == "__main__":
    scraper.start()
    for item in scraper:
        print(item)
