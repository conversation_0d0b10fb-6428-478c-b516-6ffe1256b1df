import requests
import xml.etree.ElementTree as ET
import re
from dataclasses import dataclass, field
from typing import Optional, List, Iterator
from datetime import datetime

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=1)
    
    def is_valid(self) -> bool:
        return all([self.link, self.publication_date, self.title, self.conc is not None])


@dataclass
class Noticia:
    NOT_CODIGO: Optional[str] = None
    NOT_TITULO: Optional[str] = None
    NOT_DATA_CRIACAO: Optional[datetime] = None
    NOT_DATA_VENCIMENTO: Optional[datetime] = None
    NOT_ATIVO: bool = False
    NOT_TEXTO_LINK: Optional[str] = None
    link: Optional[str] = None

    def __post_init__(self):
        if self.NOT_CODIGO:
            self.link = f"https://www.der.sp.gov.br/WebSite/Comunicacao/SalaImprensa.aspx?idNoticia={self.NOT_CODIGO}"
        
        try:
            if isinstance(self.NOT_DATA_CRIACAO, str):
                self.NOT_DATA_CRIACAO = datetime.strptime(self.NOT_DATA_CRIACAO[:19], "%Y-%m-%dT%H:%M:%S")
            if isinstance(self.NOT_DATA_VENCIMENTO, str):
                self.NOT_DATA_VENCIMENTO = datetime.strptime(self.NOT_DATA_VENCIMENTO[:19], "%Y-%m-%dT%H:%M:%S")
        except (ValueError, TypeError):
            self.NOT_DATA_CRIACAO = None
            self.NOT_DATA_VENCIMENTO = None

    def __str__(self):
        return (f"Noticia(CODIGO={self.NOT_CODIGO}, TITULO={self.NOT_TITULO}, "
                f"DATA_CRIACAO={self.NOT_DATA_CRIACAO}, DATA_VENCIMENTO={self.NOT_DATA_VENCIMENTO}, "
                f"ATIVO={self.NOT_ATIVO}, TEXTO_LINK={self.NOT_TEXTO_LINK}, link={self.link})")

class DerNewsScraper:
    def __init__(self, url: str):
        self.url = url
        self.articles: List[NewsItem] = []

    def fetch_xml(self) -> Optional[str]:
        response = requests.get(self.url)
        if response.status_code == 200:
            return response.content
        else:
            print(f"Failed to fetch the XML file. Status code: {response.status_code}")
            return None

    def parse_xml(self, xml_content: str):
        root = ET.fromstring(xml_content)
        for noticia in root.findall('Noticia'):
            noticia_data = {}
            for tag in ['NOT_CODIGO', 'NOT_TITULO', 'NOT_DATA_CRIACAO', 'NOT_DATA_VENCIMENTO', 'NOT_ATIVO']:
                element = noticia.find(tag)
                if element is not None:
                    if tag == 'NOT_ATIVO':
                        noticia_data[tag] = element.text.lower() == 'true'
                    else:
                        noticia_data[tag] = element.text
            
            # Extract link if present in <NOT_TEXTO>
            not_texto_elem = noticia.find('NOT_TEXTO')
            if not_texto_elem is not None:
                not_texto = not_texto_elem.text
                # Find the link using a regular expression
                link_match = re.search(r'(https?://[^\s]+)', not_texto)
                if link_match:
                    noticia_data['NOT_TEXTO_LINK'] = link_match.group(0)
            
            # Create a Noticia instance and add to the Noticias wrapper
            no = Noticia(**noticia_data)
            news_article = NewsItem(
                    title=no.NOT_TITULO,
                    link=no.link,
                    publication_date=no.NOT_DATA_CRIACAO,
                    conc=1
                )   
            self.articles.append(news_article)

    def start(self):
        xml_content = self.fetch_xml()
        if xml_content:
            self.parse_xml(xml_content)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.articles)

    def __len__(self) -> int:
        return len(self.articles)

    def __getitem__(self, index: int) -> NewsItem:
        return self.articles[index]

    def __str__(self) -> str:
        return f"DERScraper(url={self.url}, articles_count={len(self.articles)})"


url = "https://www.der.sp.gov.br/Upload/XML/noticiaHome.xml"
scraper = DerNewsScraper(url)