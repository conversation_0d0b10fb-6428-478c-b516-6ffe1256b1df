import requests
import xml.etree.ElementTree as ET
from dataclasses import dataclass, field
from typing import Optional, List, Iterator
from datetime import datetime

@dataclass
class NewsItem:
    title: Optional[str] = None
    link: Optional[str] = None
    publication_date: Optional[datetime] = None
    conc: int = field(default=79)

    def __post_init__(self):
        # Try to parse the publication date
        if isinstance(self.publication_date, str):
            try:
                self.publication_date = datetime.strptime(self.publication_date, "%a, %d %b %Y %H:%M:%S %z")
            except ValueError:
                self.publication_date = None

    def __str__(self):
        return f"NewsItem(title={self.title}, link={self.link}, publication_date={self.publication_date},conc={self.conc})"

class NewsFeed:
    def __init__(self):
        self._news_items: List[NewsItem] = []

    def add_news_item(self, news_item: NewsItem):
        self._news_items.append(news_item)

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self._news_items)

    def __len__(self) -> int:
        return len(self._news_items)

    def __getitem__(self, index: int) -> NewsItem:
        return self._news_items[index]

    def __str__(self) -> str:
        return f"NewsFeed(total={len(self._news_items)})"

class TebeScraper:
    def __init__(self, url: str):
        self.url = url
        self.items = NewsFeed()

    def fetch_xml(self) -> Optional[str]:
        response = requests.get(self.url)
        if response.status_code == 200:
            return response.content
        else:
            print(f"Failed to fetch the XML file. Status code: {response.status_code}")
            return None

    def parse_xml(self, xml_content: str):
        root = ET.fromstring(xml_content)
        for item in root.findall('./channel/item'):
            news_item_data = {}
            for tag in ['title', 'link', 'pubDate']:
                element = item.find(tag)
                if element is not None:
                    news_item_data[tag] = element.text
            
            self.items.add_news_item(NewsItem(
                title=news_item_data['title'],
                link=news_item_data['link'],
                publication_date=news_item_data['pubDate']
            ))

    def start(self):
        xml_content = self.fetch_xml()
        if xml_content:
            self.parse_xml(xml_content)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"TebeScraper(url={self.url}, items_count={len(self.items)})"

url = "https://tebe.com.br/noticias/feed/"
scraper = TebeScraper(url)