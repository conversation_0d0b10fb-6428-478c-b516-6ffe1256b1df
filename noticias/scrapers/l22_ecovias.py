import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from bs4 import BeautifulSoup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime
import dateparser

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=68)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class EcoviasScraper:
    def __init__(self, urls: List[str], pages: int = 3):
        self.urls = urls
        self.pages = pages
        self.base_domain = "https://www.ecoviaslestepaulista.com.br"
        self.items: List[NewsItem] = []
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'})
        retries = Retry(total=5, backoff_factor=1, status_forcelist=[502, 503, 504])
        self.session.mount('https://', HTTPAdapter(max_retries=retries))

    def fetch_html(self, url: str, page: int) -> Optional[str]:
        try:
            full_url = f"{url}?pagina={page}"
            response = self.session.get(full_url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML for {full_url}. Error: {e}")
            return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        containers = soup.find_all('div', class_='news-item')

        for container in containers:
            link_tag = container.find('a', href=True)
            link = f"{self.base_domain}{link_tag['href']}" if link_tag else ''
            
            if "boletins-de-trafego" in link:
                continue
            
            title_tag = container.find('h4')
            title = title_tag.get_text(strip=True) if title_tag else ''
            
            date_tag = container.find('p', class_='date')
            publication_date = None

            if date_tag:
                date_text = date_tag.get_text(strip=True)
                date_prefix = "Publicado em "
                if date_prefix in date_text:
                    date_text = date_text.split(date_prefix)[-1].strip()
                    publication_date = dateparser.parse(date_text, languages=['pt'])

            article = NewsItem(
                title=title,
                link=link,
                publication_date=publication_date
            )
            self.items.append(article)

    def start(self):
        for url in self.urls:
            for page in range(1, self.pages + 1):
                html = self.fetch_html(url, page)
                if html:
                    self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"EcoviasScraper(urls={self.urls}, pages={self.pages}, items_count={len(self.items)})"

urls = [
    "https://www.ecoviaslestepaulista.com.br/noticias/releases-institucionais",
    "https://www.ecoviaslestepaulista.com.br/noticias/releases-de-obras",
]
scraper = EcoviasScraper(urls)