import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from bs4 import BeautifulSoup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime

# Dictionary to map Brazilian Portuguese month names to numbers
MONTHS = {
    'janeiro': 1,
    'fevereiro': 2,
    'março': 3,
    'abril': 4,
    'maio': 5,
    'junho': 6,
    'julho': 7,
    'agosto': 8,
    'setembro': 9,
    'outubro': 10,
    'novembro': 11,
    'dezembro': 12
}

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=62)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class CartScraper:
    def __init__(self, url: str):
        self.url = url
        self.items: List[NewsItem] = []
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'})
        retries = Retry(total=5, backoff_factor=1, status_forcelist=[502, 503, 504])
        self.session.mount('https://', HTTPAdapter(max_retries=retries))

    def fetch_html(self, url: str) -> Optional[str]:
        try:
            response = self.session.get(url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML from {url}. Error: {e}")
            return None

    def parse_date(self, date_str: str) -> Optional[datetime]:
        try:
            date_str = date_str.strip()
            parts = date_str.split(' ')
            day = int(parts[1].replace(',', ''))
            month = MONTHS[parts[0].lower()]
            year = int(parts[2])
            return datetime(year, month, day)
        except (IndexError, ValueError, KeyError) as e:
            print(f"Failed to parse date: {date_str}. Error: {e}")
            return None

    def fetch_publication_date(self, link: str) -> Optional[datetime]:
        html = self.fetch_html(link)
        if html:
            soup = BeautifulSoup(html, 'html.parser')
            date_tag = soup.find('div', class_='data').find('a')
            if date_tag:
                return self.parse_date(date_tag.get_text(strip=True))
        return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        containers = soup.find_all('div', class_='data-content')

        for container in containers:
            title_tag = container.find('h1')
            link_tag = container.find_all('p')[-1].find('a', string='LEIA MAIS')

            title = title_tag.get_text(strip=True) if title_tag else ''
            link = link_tag['href'] if link_tag else ''
            publication_date = self.fetch_publication_date(link) if link else None

            article = NewsItem(
                title=title,
                link=link,
                publication_date=publication_date
            )
            self.items.append(article)

    def start(self):
        html = self.fetch_html(self.url)
        if html:
            self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"CartScraper(url={self.url}, items_count={len(self.items)})"

# Example usage

url = "https://cartsp.com.br/noticias/"
scraper = CartScraper(url)


if __name__ == "__main__":
    scraper.start()
    for item in scraper:
        print(item)
