import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from bs4 import <PERSON>Soup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime
import dateparser

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=87)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class EconoroesteScraper:
    def __init__(self, url: str):
        self.url = url
        self.base_domain = "https://www.ecoviasnoroestepaulista.com.br"
        self.items: List[NewsItem] = []
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'})
        retries = Retry(total=5, backoff_factor=1, status_forcelist=[502, 503, 504])
        self.session.mount('https://', HTTPAdapter(max_retries=retries))

    def fetch_html(self) -> Optional[str]:
        try:
            response = self.session.get(self.url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML. Error: {e}")
            return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        news_items = soup.find_all('div', class_='content-box noticia')
        for news_item in news_items:
            # Extracting link
            title_div = news_item.find('div', class_='title')
            link_tag = title_div.find('a') if title_div else None
            link = f"{link_tag['href']}" if link_tag and link_tag.has_attr('href') else ''
            
            # Extracting title
            title_tag = link_tag.find('h3') if link_tag else None
            title = title_tag.get_text(strip=True) if title_tag else ''
            
            # Extracting publication date
            date_div = news_item.find('div', class_='data')
            date_text = date_div.get_text(strip=True).replace('Publicado em: ', '') if date_div else ''
            publication_date = dateparser.parse(date_text, languages=['pt']) if date_text else None

            # Creating the NewsItem instance
            article = NewsItem(
                title=title,
                link=link,
                publication_date=publication_date
            )
            self.items.append(article)

    def start(self):
        html = self.fetch_html()
        if html:
            self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"EconoroesteScraper(url={self.url}, items_count={len(self.items)})"

url = "https://www.ecoviasnoroestepaulista.com.br/todas-as-noticias/"
scraper = EconoroesteScraper(url)
