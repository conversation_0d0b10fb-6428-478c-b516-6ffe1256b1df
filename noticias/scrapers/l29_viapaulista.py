import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from bs4 import BeautifulSoup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime
import re

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=83)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class ViapaulistaScraper:
    def __init__(self, base_url: str, pages: int = 4):
        self.base_url = base_url
        self.pages = pages
        self.items: List[NewsItem] = []
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'})
        retries = Retry(total=5, backoff_factor=1, status_forcelist=[502, 503, 504])
        self.session.mount('https://', HTTPAdapter(max_retries=retries))

    def fetch_html(self, url: str) -> Optional[str]:
        try:
            response = self.session.get(url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML. Error: {e}")
            return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        container = soup.find('div', class_='cards-publicacoes')
        if container:
            news_items = container.find_all('a')
            for item in news_items:
                link = item['href'] if item and item.has_attr('href') else ''
                link = f"https://www.arteris.com.br{link}"  # Add base URL to the link
                
                # Extracting publication date
                date_tag = item.find('span', class_='card-publicacao-data') or item.find('span', class_='card-publicacao-imagem-data')
                date_text = date_tag.get_text(strip=True) if date_tag else ''
                publication_date = datetime.strptime(date_text, '%d/%m/%y') if date_text else None

                # Extracting title
                title_tag = item.find('p')
                title = title_tag.get_text(strip=True) if title_tag else ''
                
                txt_to_ignore = 'Condições de tráfego'.upper()
                
                if txt_to_ignore in title.upper():
                    continue

                if re.search(r'\bvia paulista\b', title, re.IGNORECASE) or re.search(r'\bvia paulista\b', link, re.IGNORECASE) or re.search(r'\bviapaulista\b', title, re.IGNORECASE) or re.search(r'\bviapaulista\b', link, re.IGNORECASE):
                    article = NewsItem(
                        title=title,
                        link=link,
                        publication_date=publication_date
                    )
                    self.items.append(article)

    def start(self):
        for page in range(1, self.pages + 1):
            url = f"{self.base_url}?page={page}"
            html = self.fetch_html(url)
            if html:
                self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"ViapaulistaScraper(base_url={self.base_url}, items_count={len(self.items)})"

base_url = "https://www.arteris.com.br/fique-por-dentro/noticias-e-releases/"
scraper = ViapaulistaScraper(base_url)

if __name__ == "__main__":
    scraper.start()
    for item in scraper:
        print(item)
