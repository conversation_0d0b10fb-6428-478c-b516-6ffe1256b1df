import requests
from bs4 import BeautifulSoup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime

@dataclass
class NewsItem:
    link: str
    publication_date: Optional[datetime]
    title: str
    conc: int = field(default=70)

    def __str__(self):
        return (f"NewsItem(link={self.link}, publication_date={self.publication_date}, "
                f"title={self.title}, conc={self.conc})")

class ArterisScraper:
    def __init__(self, url: str):
        self.url = url
        self.articles: List[NewsItem] = []

    def fetch_html(self) -> Optional[str]:
        try:
            response = requests.get(self.url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML. Error: {e}")
            return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        container = soup.find(class_="cards-publicacoes")

        if container:
            for link_tag in container.find_all('a'):
                link = f"https://www.arteris.com.br{link_tag['href']}" if link_tag else ''
                date_tag = link_tag.find('span', class_="card-publicacao-imagem-data")
                
                # If the date_tag is not found with the first class, try the second class
                if not date_tag:
                    date_tag = link_tag.find('span', class_="card-publicacao-data")
                
                title_tag = link_tag.find('p')

                title = title_tag.get_text(strip=True) if title_tag else ''
                
                txt_to_ignore = 'Condições de tráfego'.upper()
                
                if txt_to_ignore in title.upper():
                    continue

                publication_date = None
                if date_tag:
                    try:
                        publication_date = datetime.strptime(date_tag.get_text(strip=True), '%d/%m/%y')
                    except ValueError as e:
                        print(f"Failed to parse publication date for title '{title}': {e}")
                        print(f"Problematic date text: '{date_tag.get_text(strip=True)}'")

                article = NewsItem(
                    link=link,
                    publication_date=publication_date,
                    title=title
                )
                self.articles.append(article)

    def start(self):
        html = self.fetch_html()
        if html:
            self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.articles)

    def __len__(self) -> int:
        return len(self.articles)

    def __getitem__(self, index: int) -> NewsItem:
        return self.articles[index]

    def __str__(self) -> str:
        return f"ArterisScraper(url={self.url}, articles_count={len(self.articles)})"
    

url = "https://www.arteris.com.br/fique-por-dentro/noticias-e-releases/?categoria=5&query="
scraper = ArterisScraper(url)

if __name__ == "__main__":
    scraper.start()
    for item in scraper:
        print(item)
