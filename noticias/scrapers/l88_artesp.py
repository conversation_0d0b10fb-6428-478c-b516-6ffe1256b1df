import requests
import xml.etree.ElementTree as ET
from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, List


@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=88)


class NewsScraper:
    URL = "http://www.artesp.sp.gov.br/_api/web/lists/getbytitle('Noticias')/items?$orderby=Data%20desc"
    NAMESPACES = {
        'atom': 'http://www.w3.org/2005/Atom',
        'd': 'http://schemas.microsoft.com/ado/2007/08/dataservices',
        'm': 'http://schemas.microsoft.com/ado/2007/08/dataservices/metadata'
    }

    def fetch_data(self) -> str:
        response = requests.get(self.URL)
        response.raise_for_status()
        return response.content

    def parse_xml(self, xml_content: str) -> List[NewsItem]:
        root = ET.fromstring(xml_content)
        news_items = []
        for entry in root.findall('.//atom:entry', self.NAMESPACES):
            title = entry.find('.//d:Title', self.NAMESPACES).text
            news_item = self.create_news_item(entry, title)
            if news_item:
                news_items.append(news_item)
        return news_items

    def create_news_item(self, entry: ET.Element, title: str) -> Optional[NewsItem]:
        id_tag = entry.find('.//d:Id', self.NAMESPACES)
        created_tag = entry.find('.//d:Created', self.NAMESPACES)

        if id_tag is not None and created_tag is not None:
            parsed_id = id_tag.text
            publication_date_str = created_tag.text
            publication_date = datetime.strptime(publication_date_str, '%Y-%m-%dT%H:%M:%SZ')
            link = f"http://www.artesp.sp.gov.br/Style%20Library/extranet/noticias/noticia-detalhes.aspx?id={parsed_id}"
            return NewsItem(title=title, link=link, publication_date=publication_date)
        return None

    def start(self) -> List[NewsItem]:
        xml_content = self.fetch_data()
        return self.parse_xml(xml_content)



scraper = NewsScraper()
