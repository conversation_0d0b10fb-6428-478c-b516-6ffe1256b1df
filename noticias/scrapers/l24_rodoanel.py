import requests
import json
import logging
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=63)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class GrupoCcrScraper:
    def __init__(self, name: str):
        self.name = name
        self.url = self.get_url_by_name(name)
        self.items: List[NewsItem] = []

    def get_url_by_name(self, name: str) -> str:
        urls = {
            "autoban": "https://rodovias.grupoccr.com.br/bin/getNewsPageList/?concessionary=autoban&language=pt",
            "viaoeste": "https://rodovias.grupoccr.com.br/bin/getNewsPageList/?concessionary=viaoeste&language=pt",
            "spvias": "https://rodovias.grupoccr.com.br/bin/getNewsPageList/?concessionary=spvias&language=pt",
            "rodoanel": "https://rodovias.grupoccr.com.br/bin/getNewsPageList/?concessionary=rodoanel&language=pt"
        }
        return urls.get(name, '')

    def fetch_json(self) -> Optional[List[dict]]:
        try:
            response = requests.get(self.url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.json()
        except requests.RequestException as e:
            print(f"Failed to fetch the JSON. Error: {e}")
            logging.error(f"Failed to fetch the JSON from {self.url}. Error: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"Failed to decode JSON. Error: {e}")
            logging.error(f"Failed to decode JSON from {self.url}. Error: {e}")
            return None

    def parse_json(self, data: List[dict]):
        for entry in data:
            title = entry.get('jcr:title', '')

            # Ensure title is decoded correctly if it's not already a string
            if isinstance(title, bytes):
                title = title.decode('utf-8', errors='replace')
            
            path = entry.get('path', '')
            publication_date_data = entry.get('publicationDate', {})
            
            # Convert publicationDate to datetime
            publication_date = None
            try:
                publication_date = datetime(
                    publication_date_data.get('year'),
                    publication_date_data.get('month') + 1,  # Adjust month value by adding 1
                    publication_date_data.get('dayOfMonth'),
                    publication_date_data.get('hourOfDay'),
                    publication_date_data.get('minute'),
                    publication_date_data.get('second')
                )
            except Exception as e:
                logging.error(f"Failed to parse publication date for title '{title}': {e}")
                # Log the publication date that couldn't be parsed
                logging.error(f"Publication date data: {publication_date_data}")

            # Construct URL from path
            link = self.extract_segments_and_construct_url(path)
            
            # Create NewsItem and add to items
            news_item = NewsItem(title=title, link=link, publication_date=publication_date, )
            self.items.append(news_item)

    def extract_segments_and_construct_url(self, path: str) -> str:
        # Split the path by slashes
        segments = path.split('/')
        
        # Extract the relevant segments for final URL
        try:
            project = segments[8]  # 'autoban'
            news_type = segments[9]  # 'noticias'
            date_segment = segments[10]  # '2024-07'
            news_title = segments[11]  # 'ccr-autoban-informa-cronograma--semanal-de-obras-de-22-a-28-de-j'
            final_path = f"/{project}/{news_type}/{date_segment}/{news_title}/"
        except IndexError:
            final_path = ''
            logging.error(f"Failed to parse path: {path}")
        
        base_domain = "https://rodovias.grupoccr.com.br"
        final_url = f"{base_domain}{final_path}"
        
        return final_url

    def start(self):
        data = self.fetch_json()
        if data:
            self.parse_json(data)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"GrupoCcrScraper(name={self.name}, items_count={len(self.items)})"


name = "rodoanel"
scraper = GrupoCcrScraper(name)