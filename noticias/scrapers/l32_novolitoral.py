import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from bs4 import BeautifulSoup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
import datetime as dt
import dateparser

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[dt.datetime]
    conc: int = field(default=89)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class NovoLitoralScraper:
    def __init__(self, url: str):
        self.url = url
        self.items: List[NewsItem] = []
        self.session = requests.Session()
        # self.session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'})
        retries = Retry(total=5, backoff_factor=1, status_forcelist=[502, 503, 504])
        self.session.mount('https://', HTTPAdapter(max_retries=retries))

    def fetch_json(self) -> Optional[str]:
        try:
            response = self.session.get(self.url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.json()
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML. Error: {e}")
            return None

    def parse_json(self, json_data: str):
        for item in json_data['items']:
            title = item['title']
            link = 'https://www.novolitoral.com.br/pt/noticias/' +item['meta']['slug']
            publication_date = item['meta']['first_published_at']
            publication_date = dateparser.parse(publication_date) if publication_date else None
            if not isinstance(publication_date, dt.datetime):
                raise ValueError(f"Invalid publication date format: {publication_date}")
            article = NewsItem(title, link, publication_date)
            self.items.append(article)

    def start(self):
        html = self.fetch_json()
        print(html)
        if html:
            self.parse_json(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"NovoLitoralScraper(url={self.url}, items_count={len(self.items)})"

url = "https://api.novolitoral.com.br/api/v2/pages/?type=imprensa.Noticia&fields=category,image,summary,date&order=-date&offset=0&limit=16"
scraper = NovoLitoralScraper(url)


if __name__ == "__main__":
    scraper.start()
    for item in scraper:
        print(item)
