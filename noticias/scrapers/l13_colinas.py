import requests
from bs4 import BeautifulSoup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime

# Dictionary to map Brazilian Portuguese month names to numbers
MONTHS = {
    'janeiro': 1,
    'fevereiro': 2,
    'março': 3,
    'abril': 4,
    'maio': 5,
    'junho': 6,
    'julho': 7,
    'agosto': 8,
    'setembro': 9,
    'outubro': 10,
    'novembro': 11,
    'dezembro': 12
}

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=65)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class ColinasScraper:
    def __init__(self, url: str):
        self.url = url
        self.items: List[NewsItem] = []

    def fetch_html(self) -> Optional[str]:
        try:
            response = requests.get(self.url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML. Error: {e}")
            return None

    def parse_date(self, date_str: str) -> Optional[datetime]:
        try:
            parts = date_str.split(' ')
            day = int(parts[0])
            month = MONTHS[parts[2].lower()]
            year = int(parts[4])
            return datetime(year, month, day)
        except (IndexError, ValueError, KeyError) as e:
            print(f"Failed to parse date: {date_str}. Error: {e}")
            return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        containers = soup.find_all('div', class_='elementor-post__card')

        for container in containers:
            title_tag = container.find('h3', class_='elementor-post__title')
            link_tag = title_tag.find('a') if title_tag else None
            date_tag = container.find('span', class_='elementor-post-date')

            title = title_tag.get_text(strip=True) if title_tag else ''
            link = link_tag['href'] if link_tag else ''
            publication_date = self.parse_date(date_tag.get_text(strip=True)) if date_tag else None

            article = NewsItem(
                title=title,
                link=link,
                publication_date=publication_date
            )
            self.items.append(article)

    def start(self):
        html = self.fetch_html()
        if html:
            self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"ColinasScraper(url={self.url}, items_count={len(self.items)})"


url = "https://viaappia.com.br/noticias-viacolinas/"
scraper = ColinasScraper(url)

if __name__ == "__main__":
    scraper.start()
    for item in scraper:
        print(item)
