import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from bs4 import <PERSON>Soup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime
import dateparser

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=76)

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class SpmarScraper:
    def __init__(self, url: str):
        self.url = url
        self.items: List[NewsItem] = []
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'})
        retries = Retry(total=5, backoff_factor=1, status_forcelist=[502, 503, 504])
        self.session.mount('https://', HTTPAdapter(max_retries=retries))

    def fetch_html(self) -> Optional[str]:
        try:
            response = self.session.get(self.url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML. Error: {e}")
            return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        loop_items = soup.find_all(attrs={"data-elementor-type": "loop-item"})
        if loop_items:
            for item in loop_items:
                # Extract the title from the <h1> tag with the specified class
                title_tag = item.find('h1', class_='elementor-heading-title')
                title = title_tag.get_text(strip=True) if title_tag else None

                # Extract the publication date from the <time> tag
                time_tag = item.find('time')
                publication_date = dateparser.parse(time_tag.get_text(strip=True), languages=['pt'], settings={'RELATIVE_BASE': datetime.now()}) if time_tag else None

                # Extract the link from the <a> tag's href attribute
                a_tag = item.find('a')
                link = a_tag['href'] if a_tag and a_tag.has_attr('href') else None
                if '/servicos/' in link:
                    continue

                # Save the extracted values in a dictionary
                article = NewsItem(title, link, publication_date)
                self.items.append(article)

    def start(self):
        html = self.fetch_html()
        if html:
            self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"SpmarScraper(url={self.url}, items_count={len(self.items)})"

url = "https://www.spmar.com.br/web/"
scraper = SpmarScraper(url)


if __name__ == "__main__":
    scraper.start()
    for item in scraper:
        print(item)
