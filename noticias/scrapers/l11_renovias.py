import requests
from bs4 import BeautifulSoup
from dataclasses import dataclass, field
from typing import List, Optional, Iterator
from datetime import datetime

@dataclass
class NewsItem:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = 73

    def __str__(self):
        return (f"NewsItem(title={self.title}, link={self.link}, "
                f"publication_date={self.publication_date}, conc={self.conc})")

class RenoviasScraper:
    def __init__(self, base_url: str, pages: int):
        self.base_url = base_url
        self.pages = pages
        self.items: List[NewsItem] = []

    def fetch_html(self, url: str) -> Optional[str]:
        try:
            response = requests.get(url)
            response.raise_for_status()  # Raise an error for HTTP requests that return a 4xx or 5xx status code
            return response.text
        except requests.RequestException as e:
            print(f"Failed to fetch the HTML from {url}. Error: {e}")
            return None

    def parse_html(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        containers = soup.find_all('div', class_='blog-post_wrapper')

        for container in containers:
            title_tag = container.find('h4', class_='blog-post_title')
            link_tag = title_tag.find('a') if title_tag else None
            date_tag = container.find('span', class_='date_post')

            title = title_tag.get_text(strip=True) if title_tag else ''
            link = link_tag['href'] if link_tag else ''
            publication_date = None
            if date_tag:
                date_text = date_tag.get_text(strip=True)
                try:
                    publication_date = datetime.strptime(date_text, '%d/%m/%Y')
                except ValueError as e:
                    print(f"Failed to parse publication date for title '{title}': {e}")
                    print(f"Problematic date text: '{date_text}'")

            article = NewsItem(
                title=title,
                link=link,
                publication_date=publication_date
            )
            self.items.append(article)

    def start(self):
        for page in range(1, self.pages + 1):
            if page == 1:
                url = self.base_url
            else:
                url = f"{self.base_url}/page/{page}/"
            
            html = self.fetch_html(url)
            if html:
                self.parse_html(html)
        return self

    def __iter__(self) -> Iterator[NewsItem]:
        return iter(self.items)

    def __len__(self) -> int:
        return len(self.items)

    def __getitem__(self, index: int) -> NewsItem:
        return self.items[index]

    def __str__(self) -> str:
        return f"RenoviasScraper(url={self.base_url}, pages={self.pages}, items_count={len(self.items)})"


base_url = "https://renovias.com.br/noticias"
scraper = RenoviasScraper(base_url, 3)

if __name__ == "__main__":
    scraper.start()
    for item in scraper:
        print(item)
