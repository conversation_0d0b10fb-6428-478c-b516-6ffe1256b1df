import time
import datetime as dt
from typing import List, Optional
from django.utils.text import slugify
from django.utils.html import strip_tags
from django.utils import timezone
from dataclasses import asdict, dataclass, field
from datetime import datetime

from celery import shared_task
from .models import Noticia
from .scraper.scrape import get_news

@dataclass
class NewsItemWrapper:
    title: str
    link: str
    publication_date: Optional[datetime]
    conc: int = field(default=76)
    slug: str = ''

def insert_message_orm(title, publication_date, link, conc, slug):
    start_time = time.time()
    message_instance = Noticia(
        title=title, publication_date=publication_date, link=link, concessionaria_id=conc, slug=slug)
    _now = dt.datetime.now()
    n_days = 3
    max_timedelta = dt.timedelta(days=n_days)
    n_days_before = _now - max_timedelta
    if publication_date >= n_days_before:
        try:
            message_instance.save()
            print("Message inserted")
        except Exception as e:
            print(f"Duplicate message: {str(e)}")
        print("ORM Method took", time.time() - start_time, "seconds")
    else:
        print(f"Message from {publication_date} < {n_days_before}")

def delete_old_news():
    one_year_ago = timezone.now() - dt.timedelta(days=365)
    deleted_count, _ = Noticia.objects.filter(publication_date__lt=one_year_ago).delete()
    print(f"Deleted {deleted_count} news items older than 1 year")

@shared_task
def retrieve_news():
    print('iniciando...')
    news = get_news()
    news_items = []
    print(f'len news: {len(news)}')
    
    # Collect all incoming links from the news feed
    incoming_links = {n.link for n in news if n.link}
    # Query existing Noticia objects that have these links
    existing_links = set(Noticia.objects.filter(link__in=incoming_links).values_list('link', flat=True))

    for n in news:
        if n.title and n.link and n.link not in existing_links:
            title = strip_tags(n.title)
            slug = slugify(title)[:255]
            noticia = Noticia(
                title=title,
                link=n.link,
                publication_date=n.publication_date,
                concessionaria_id=n.conc,
                slug=slug,
            )
            news_items.append(noticia)
    
    Noticia.objects.bulk_create(news_items, ignore_conflicts=True)
    delete_old_news()