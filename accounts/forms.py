import re
from accounts.models import Account
from django import forms
from django.contrib import admin
from django.contrib.auth import get_user_model
from django.contrib.admin.widgets import FilteredSelectMultiple
from django.contrib.auth.models import Group
from django.utils.safestring import mark_safe
from captcha.fields import Captcha<PERSON>ield, CaptchaTextInput

from validate_docbr import CNPJ, CPF
from disposable_email_domains import blocklist

from concessionarias.models import Concessionaria

cnpj_validator = CNPJ()
cpf_validator = CPF()

User = get_user_model()

def validate_email_address(email_address):
   if not re.search(r"^[A-Za-z0-9_!#$%&'*+\/=?`{|}~^.-]+@[A-Za-z0-9.-]+$", email_address):
       return False

class CustomCaptchaTextInput(CaptchaTextInput):
    template_name = 'accounts/custom-captcha.html'


class CadastroForm(forms.Form):
    CHOICES = (('<PERSON><PERSON>', '<PERSON><PERSON>'),('Foto<PERSON>', 'Fotos'),('<PERSON>oto<PERSON> e Dados', '<PERSON>oto<PERSON> e Dad<PERSON>'),)
    nome = forms.CharField(
        label="Nome", max_length=100)
    email = forms.EmailField(
        label="E-mail", 
        max_length=100,
        help_text='',
        required=True,
        widget=forms.TextInput(attrs={'autocomplete': 'off'}),
    )
    email_confirmation = forms.EmailField(
        label="Repita seu e-mail", 
        max_length=100,
        help_text='',
        required=True,
        widget=forms.TextInput(attrs={'autocomplete': 'off'}),
    )
    motivacao = forms.ChoiceField(label='Tenho interesse em:', choices=CHOICES)
    recebe_avisos = forms.BooleanField(
        label="Deseja receber nossos boletins informativos?",
        required=False,
        help_text="Sim, gostaria de receber os boletins informativos do Centro de Controle de Informações da ARTESP"    
    )
    termos_de_uso = forms.BooleanField(
        label="Termos de Uso e Política de Privacidade",
        help_text=mark_safe(
            "Li e estou de acordo com o <a class='text-indigo-600 cursor-pointer' href='/termos-de-uso' target='_blank'>Termo de Uso</a> e a <a class='text-indigo-600 cursor-pointer' href='/politica-de-privacidade' target='_blank'>Política de Privacidade</a>")
    )
    captcha = CaptchaField(widget=CustomCaptchaTextInput)

    def clean_nome(self):
        nome = self.cleaned_data.get('nome', '').strip()
        if len(nome) < 3:
            raise forms.ValidationError('Por favor, digite seu nome.')
        return nome
    
    def clean_email(self):
        email = self.cleaned_data.get('email', '').strip()
        if validate_email_address(email):
            raise forms.ValidationError('Por favor, digite um endereço de e-mail válido.')
        if email.partition('@')[2] in blocklist:
            raise forms.ValidationError('E-mail inválido')
        email_exists = Account.objects.filter(email=email).first()
        if email_exists:
            raise forms.ValidationError('E-mail já cadastrado')
        return email
    
    def clean_email_confirmation(self):
        email_confirmation = self.cleaned_data.get('email_confirmation', '').strip()
        return email_confirmation

    def clean_termos_de_uso(self):
        termos_de_uso = self.cleaned_data.get('termos_de_uso', '')
        if not termos_de_uso:
            raise forms.ValidationError(
                'Por favor, assinale a opção que leu e está de acordo com o Termo de Uso e a '\
                    'Política de Privacidade')
        return termos_de_uso
    
    def clean(self):
        cleaned_data = super().clean()
        email = cleaned_data.get('email', '').strip()
        email_confirmation = cleaned_data.get('email_confirmation', '').strip()
        if email and email_confirmation:
            if email != email_confirmation:
                raise forms.ValidationError('E-mail e confirmação de e-mail diferentes')


# Create ModelForm based on the Group model.
class GroupAdminForm(forms.ModelForm):
    class Meta:
        model = Group
        exclude = []

    # Add the users field.
    users = forms.ModelMultipleChoiceField(
         queryset=User.objects.all(), 
         required=False,
         # Use the pretty 'filter_horizontal widget'.
         widget=FilteredSelectMultiple('users', False)
    )

    def __init__(self, *args, **kwargs):
        # Do the normal form initialisation.
        super(GroupAdminForm, self).__init__(*args, **kwargs)
        # If it is an existing group (saved objects have a pk).
        if self.instance.pk:
            # Populate the users field with the current Group users.
            self.fields['users'].initial = self.instance.user_set.all()

    def save_m2m(self):
        # Add the users to the Group.
        self.instance.user_set.set(self.cleaned_data['users'])

    def save(self, *args, **kwargs):
        # Default save
        instance = super(GroupAdminForm, self).save()
        # Save many-to-many data
        self.save_m2m()
        return instance


cnpj_validator = CNPJ()
cpf_validator = CPF()

User = get_user_model()

def validate_email_address(email_address):
   if not re.search(r"^[A-Za-z0-9_!#$%&'*+\/=?`{|}~^.-]+@[A-Za-z0-9.-]+$", email_address):
       return False

class CadastroAdminForm(forms.Form):
    concessionarias = forms.ModelMultipleChoiceField(
        Concessionaria.objects.filter(lote__gt=0).order_by('lote'), required=False)
    is_artesp = forms.BooleanField(
        label="ARTESP", required=False)
    is_imprensa = forms.BooleanField(
        label="IMPRENSA ARTESP", required=False)
    is_governo_mun = forms.BooleanField(
        label="GOVERNO MUNICIPAL", required=False)
    is_governo_est = forms.BooleanField(
        label="GOVERNO ESTADUAL", required=False)
    is_governo_fed = forms.BooleanField(
        label="GOVERNO FEDERAL", required=False)
    is_conc = forms.BooleanField(
        label="CONCESSIONÁRIA(S)", required=False)
    is_externo = forms.BooleanField(
        label="EXTERNO", required=False)
    motivo_reprovacao = forms.CharField(
        required=False,
        widget=forms.Textarea(
            attrs={"class": "resize-none px-2 py-3 w-96 h-48 border border-gray-200 shadow-sm"}
        ) 
    )



class AprovaTermosDeUsoForm(forms.Form):
    termos_de_uso = forms.BooleanField(
        initial=False,
        required=False,
        label="",
        help_text=mark_safe(
            "Li e estou de acordo com o <a class='text-indigo-600 cursor-pointer' href='/termos-de-uso' target='_blank'>Termo de Uso</a> e a <a class='text-indigo-600 cursor-pointer' href='/politica-de-privacidade' target='_blank'>Política de Privacidade</a>")
    )

    def clean_termos_de_uso(self):
        termos_de_uso = self.cleaned_data.get('termos_de_uso', False)
        if not termos_de_uso:
            raise forms.ValidationError(
                'Por favor, assinale a opção que leu e está de acordo com o Termo de Uso e a '\
                    'Política de Privacidade')
        return termos_de_uso



class ExcluirContaForm(forms.Form):
    apagar_minha_conta = forms.BooleanField(
        initial=False,
        required=False,
        label="",
        help_text=mark_safe(
            "Sim, excluir minha conta")
    )

    def clean_apagar_minha_conta(self):
        apagar_minha_conta = self.cleaned_data.get('apagar_minha_conta', False)
        if not apagar_minha_conta:
            raise forms.ValidationError(
                'Por favor, assinale a opção "Sim, excluir minha conta"')
        return apagar_minha_conta

