import debug_toolbar
from django.contrib import admin
from django.urls import path, include
from django.views.generic import TemplateView

from .views import *

app_name = 'accounts'

urlpatterns = [
    # ************************************************* views ******************************************************
    # path('trigger-email/', trigger_email, name='trigger_email'),
    path('cadastro-solicitacao/', solicita_cadastro, name='solicitacao-cadastro'),
    path('aceitar-termos-de-uso/', user_must_accept_terms_of_use, name='user-must-accept-terms-of-use'),
    path('excluir-minha-conta/', delete_my_account, name='delete-my-account'),
    path('minha-conta/', minha_conta, name='minha-conta'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('change-password/', PasswordChangeView.as_view(), name='change-password'),
    path('password-reset/', MyPasswordResetView.as_view(), name='password_reset'),
    path('password_reset/done/', MyPasswordResetDoneView.as_view(), name='password_reset_done'),
    path('reset/<uidb64>/<token>/', MyPasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path('reset/done/', MyPasswordResetCompleteView.as_view(), name='password_reset_complete'),
    
]