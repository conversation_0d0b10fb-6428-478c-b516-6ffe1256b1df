import concurrent.futures

from django.http import HttpResponseRedirect
from django.urls import reverse_lazy, reverse
from django.shortcuts import render, redirect
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser

from .models import PathAudit



class RegisterUserMiddleware(MiddlewareMixin):
    def __init__(self, get_response):
        self.get_response = get_response
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)

    def __call__(self, request):
        response = self.get_response(request)

        user = request.user if request.user.is_authenticated else None
        path = request.path

        ip = request.META.get(
            "HTTP_X_FORWARDED_FOR", request.META.get("REMOTE_ADDR")
        ).split(",")[0]

        if any(kw in path for kw in ["/dados/", "/oc"]) and not any(
            kw in path for kw in ["/dashboard/", "cci-adm-mgr", "print", "foto"]
        ):
            self.executor.submit(self.log_path, user, ip, path)

        return response

    @staticmethod
    def log_path(user, ip, path):
        PathAudit.objects.create(user=user, ip=ip, caminho=path)


class CheckUserAcceptedTerms:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        path = request.path

        if "aceitar-termos-de-uso" in path:
            return response

        user = None
        if request.user.is_authenticated:
            user = request.user
        if user and user.is_authenticated and user.is_active and not user.is_superuser:
            if user and not user.aceite_termos_uso_em:
                return redirect("accounts:user-must-accept-terms-of-use")
        return response
