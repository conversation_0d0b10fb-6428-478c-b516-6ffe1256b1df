import re
from itertools import cycle
import datetime as dt
from django.contrib.auth.models import Group
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.forms import ReadOnlyPasswordHashField
from django.forms import TextInput, Textarea, CharField
from django import forms
from django.db import models
from django.core.exceptions import ValidationError

from .forms import GroupAdminForm
from .models import Account, Profile, LoginAudit, PathAudit, SolicitacaoCadastro

from import_export import resources
from import_export.admin import ImportExportMixin, ExportMixin

from validate_docbr import CNPJ, CPF

cnpj_validator = CNPJ()
cpf_validator = CPF()


class AccountResource(resources.ModelResource):
    class Meta:
        model = Account
        fields = [
            "email",
            "user_name",
            "first_name",
            "nome_publico",
            "recebe_avisos",
            "cpf",
            "dt_nascimento",
            "cnpj",
            "funcao",
            "telefone",
            "empresa",
            "end_web",
            "motivacao",
            "aceite_termos_uso_em",
            "aceite_termos_uso_ip",
            "recebe_avisos",
            "concessionarias",
            "start_date",
            "about",
            "is_staff",
            "is_active",
            "is_artesp",
            "is_concessionaria",
            "is_imprensa",
            "is_governo",
            "is_externo",
        ]


class AuditResource(resources.ModelResource):
    class Meta:
        model = LoginAudit
        fields = ["user__first_name", "user__email", "ip", "logged_in_at"]


class PathResource(resources.ModelResource):
    class Meta:
        model = PathAudit
        fields = ["user__first_name", "user__email", "ip", "caminho", "acessado_em"]


class ProfileAdminConfig(admin.ModelAdmin):
    model = Profile
    ordering = ("to_delete", "-log_count", "-last_login")
    list_display = (
        "user",
        "log_count",
        "last_login",
        "to_delete",
    )


class LoginAuditAdminConfig(ExportMixin, admin.ModelAdmin):
    resource_classes = [AuditResource]
    model = LoginAudit
    readonly_fields = (
        "user",
        "ip",
        "logged_in_at",
    )
    ordering = ("-logged_in_at",)
    search_fields = (
        "user__email",
        "user__first_name",
        "logged_in_at",
    )
    list_filter = (
        "logged_in_at",
        "user__empresa",
        "user__first_name",
    )
    list_display = ("nome", "logged_in_at", "ip")

    @admin.display(description="Nome")
    def nome(self, object):
        return f"{object.user.first_name} ({object.user.email})"
    
    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name="lgpd").exists()
        if is_superuser or is_lgpd:
            return True
        else:
            return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name="lgpd").exists()
        if is_superuser or is_lgpd:
            return True
        else:
            return False
    
    def has_view_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name="lgpd").exists()
        if is_superuser or is_lgpd:
            return True
        else:
            return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name='lgpd').exists()
        if is_superuser or is_lgpd:
            return True
        return False


class SolicitaoCadastroAdmin(admin.ModelAdmin):
    model = SolicitacaoCadastro
    ordering = ("-criada_em",)
    list_display = (
        "email",
        "first_name",
        "criada_em",
        "status",
    )
    search_fields = (
        "email",
        "first_name",
        "aprovada_por__first_name",
    )
    list_filter = (
        "aprovada_em",
        "eliminada_em",
    )

    @admin.display(description="status")
    def status(self, object):
        if object.aprovada_em:
            _dt = dt.datetime.strftime(object.aprovada_em, "%Y-%m-%d %H:%M")
            return f"APROVADA: {_dt} ({object.aprovada_por})"
        if object.eliminada_em:
            _dt = dt.datetime.strftime(object.eliminada_em, "%Y-%m-%d %H:%M")
            return f"REPROVADA: {_dt} ({object.eliminada_por})"
        else:
            return "-"


class PathAuditAdminConfig(ExportMixin, admin.ModelAdmin):
    resource_classes = [PathResource]
    model = PathAudit
    readonly_fields = (
        "user",
        "caminho",
        "ip",
        "acessado_em",
    )
    ordering = ("-acessado_em",)
    search_fields = (
        "user__email",
        "user__first_name",
        "caminho",
        "acessado_em",
    )
    # list_filter = ('user',)
    list_display = (
        "nome",
        "ip",
        "caminho",
        "acessado_em",
    )

    @admin.display(description="Nome")
    def nome(self, object):
        if object.user:
            return f"{object.user.first_name} ({object.user.email})"
        else:
            return "Anônimo"


class UserCreationForm(forms.ModelForm):
    """A form for creating new users. Includes all the required
    fields, plus a repeated password."""

    password1 = forms.CharField(label="Password", widget=forms.PasswordInput)
    password2 = forms.CharField(
        label="Password confirmation", widget=forms.PasswordInput
    )

    class Meta:
        model = Account
        fields = [
            "email",
            "user_name",
            "first_name",
            "nome_publico",
            "recebe_avisos",
            "cpf",
            "dt_nascimento",
            "cnpj",
            "funcao",
            "telefone",
            "empresa",
            "end_web",
            "motivacao",
            "concessionarias",
            "start_date",
            "about",
            "is_staff",
            "is_active",
            "is_artesp",
            "is_concessionaria",
            "is_imprensa",
            "is_governo",
            "is_externo",
        ]

    def clean_password2(self):
        # Check that the two password entries match
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise ValidationError("Passwords don't match")
        return password2

    def clean_cpf(self):
        cpf = self.cleaned_data.get("cpf")
        if cpf and not cpf_validator.validate(cpf):
            raise ValidationError("CPF inválido")
        return cpf

    def clean_cnpj(self):
        cnpj = self.cleaned_data.get("cnpj")
        if cnpj and not cnpj_validator.validate(cnpj):
            raise ValidationError("cnpj inválido")
        return cnpj

    def save(self, commit=True):
        # Save the provided password in hashed format
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password1"])
        if commit:
            user.save()
        return user


class UserChangeForm(forms.ModelForm):
    """A form for updating users. Includes all the fields on
    the user, but replaces the password field with admin's
    disabled password hash display field.
    """

    password = ReadOnlyPasswordHashField()

    class Meta:
        model = Account
        fields = [
            "email",
            "user_name",
            "first_name",
            "nome_publico",
            "recebe_avisos",
            "cpf",
            "dt_nascimento",
            "cnpj",
            "funcao",
            "telefone",
            "empresa",
            "end_web",
            "motivacao",
            "concessionarias",
            "start_date",
            "about",
            "is_staff",
            "is_active",
            "password",
            "is_artesp",
            "is_concessionaria",
            "is_imprensa",
            "is_governo",
            "is_externo",
        ]

    def clean_cpf(self):
        cpf = self.cleaned_data.get("cpf")
        if cpf and not cpf_validator.validate(cpf):
            raise ValidationError("CPF inválido")
        return cpf

    def clean_cnpj(self):
        cnpj = self.cleaned_data.get("cnpj")
        if cnpj and not cnpj_validator.validate(cnpj):
            raise ValidationError("cnpj inválido")
        return cnpj


class AccountAdminConfig(ExportMixin, UserAdmin):
    resource_classes = [AccountResource]
    form = UserChangeForm
    add_form = UserCreationForm
    # model = Account
    search_fields = (
        "email",
        "first_name",
        "empresa",
    )
    list_filter = (
        "groups",
        "empresa",
        "is_active",
        "is_staff",
        "is_imprensa",
        "is_governo",
        "is_concessionaria",
        "is_externo",
    )
    ordering = ("-start_date",)

    def group(self, user):
        return list(user.groups.values_list("name", flat=True))
        # groups = []
        # for group in user.groups.all():
        #     groups.append(group.name)
        # return ' '.join(groups)

    group.short_description = "Grupos acesso"
    list_display = (
        "email",
        "first_name",
        "empresa",
        "start_date",
        "group",
        "is_active",
        "is_staff",
    )
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "email",
                    "user_name",
                    "first_name",
                    "nome_publico",
                    "recebe_avisos",
                    "cpf",
                    "dt_nascimento",
                    "cnpj",
                    "funcao",
                    "telefone",
                    "empresa",
                    "end_web",
                    "motivacao",
                    "concessionarias",
                    "password",
                    "aceite_termos_uso_em",
                    "aceite_termos_uso_ip"
                )
            },
        ),
        (
            "Origem",
            {
                "fields": (
                    "is_staff",
                    "is_artesp",
                    "is_imprensa",
                    "is_governo",
                    "is_concessionaria",
                    "is_externo",
                )
            },
        ),
        ("Status do usuário", {"fields": ("is_active",)}),
        ("Perfil", {"fields": ("about",)}),
    )
    formfield_overrides = {
        models.TextField: {"widget": Textarea(attrs={"rows": 20, "cols": 60})},
    }
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "user_name",
                    "first_name",
                    "nome_publico",
                    "recebe_avisos",
                    "password1",
                    "password2",
                    "cpf",
                    "dt_nascimento",
                    "cnpj",
                    "funcao",
                    "telefone",
                    "empresa",
                    "end_web",
                    "motivacao",
                    "concessionarias",
                    "is_active",
                    "is_staff",
                    "is_artesp",
                    "is_governo",
                    "is_concessionaria",
                    "is_externo",
                    "aceite_termos_uso_em",
                    "aceite_termos_uso_ip"
                ),
            },
        ),
    )

    def has_add_permission(self, request):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name="lgpd").exists()
        if is_superuser or is_lgpd:
            return True
        else:
            return False

    def has_change_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name="lgpd").exists()
        if is_superuser or is_lgpd:
            return True
        else:
            return False
    
    def has_view_permission(self, request, obj=None):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name="lgpd").exists()
        if is_superuser or is_lgpd:
            return True
        else:
            return False

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name='lgpd').exists()
        if is_superuser or is_lgpd:
            return True
        return False


admin.site.register(Account, AccountAdminConfig)
admin.site.register(Profile, ProfileAdminConfig)
admin.site.register(LoginAudit, LoginAuditAdminConfig)
admin.site.register(PathAudit, PathAuditAdminConfig)
admin.site.register(SolicitacaoCadastro, SolicitaoCadastroAdmin)


# Unregister the original Group admin.
admin.site.unregister(Group)


# Create a new Group admin.
class GroupAdmin(admin.ModelAdmin):
    # Use our custom form.
    form = GroupAdminForm
    # Filter permissions horizontal as well.
    filter_horizontal = ["permissions"]

    def has_module_permission(self, request):
        is_superuser = request.user.is_superuser
        is_lgpd = request.user.groups.filter(name='lgpd').exists()
        if is_superuser or is_lgpd:
            return True
        return False


# Register the new Group ModelAdmin.
admin.site.register(Group, GroupAdmin)

# Text to put at the end of each page's <title>.
admin.site.site_title = "Portal CCI"

# Text to put in each page's <h1> (and above login form).
admin.site.site_header = "Portal CCI"

# Text to put at the top of the admin index page.
admin.site.index_title = "Administração"
