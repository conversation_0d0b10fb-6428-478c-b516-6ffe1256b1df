# views.py
import datetime
import logging
from typing import Tuple
from django.conf import settings
from django.urls import reverse_lazy
from django.contrib.auth.models import Group
from django.contrib.auth import authenticate, login
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.views import TokenRefreshView, TokenObtainPairView
from rest_framework_simplejwt.serializers import TokenRefreshSerializer
from rest_framework_simplejwt.exceptions import InvalidToken
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from django.http import HttpResponseRedirect, JsonResponse
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.response import Response
from django.utils import timezone
from django.shortcuts import render, redirect
from django.core.exceptions import PermissionDenied
from django.contrib.auth import logout


from django.contrib.auth import views as auth_views

from .models import LoginAudit

from .tasks import send_mail_cadastro_validado, send_test_email

from .forms import CadastroForm, AprovaTermosDeUsoForm, ExcluirContaForm

logger = logging.getLogger("django_file")

# def solicita_cadastro_realizada(request):
#     if request.user.is_authenticated:
#         return redirect('home')
#     return render(request, 'accounts/solicitacao-cadastro-realizada.html')

def trigger_email(request):
    if not request.user.is_superuser:
        return redirect('accounts:login')
    email = request.GET.get('email', '<EMAIL>')
    send_test_email.delay(email)
    return JsonResponse({'status': 'Email task initiated', 'email': email})

def minha_conta(request):
    if not request.user.is_authenticated:
        return redirect('accounts:login')
    return render(request, 'accounts/minha-conta.html')

def delete_my_account(request):
    if not request.user.is_authenticated:
        return redirect('accounts:login')
    user = request.user
    if request.method == 'POST':
        form = ExcluirContaForm(request.POST or None)
        if form.is_valid():
            data = form.cleaned_data
            delete_account = data.get('apagar_minha_conta')
            if delete_account:
                user.delete()
                messages.add_message(request, messages.SUCCESS, "Conta excluída com sucesso.")
                logout(request)
                return redirect('accounts:login')
    else:
        form = ExcluirContaForm(initial=request.POST)
    context = {
        'form': form,
    }
    return render(request, 'accounts/excluir-minha-conta.html', context)


def user_must_accept_terms_of_use(request):
    user = request.user
    if user and user.aceite_termos_uso_em:
        return redirect('accounts:minha-conta')
    next_url = request.GET.get('next')
    user_ip = get_client_ip(request)
    if request.method == 'POST':
        form = AprovaTermosDeUsoForm(request.POST or None)
        if form.is_valid():
            data = form.cleaned_data
            _now = timezone.now()
            user.aceite_termos_uso_em = _now
            user.aceite_termos_uso_ip = user_ip
            user.save()
            messages.add_message(request, messages.SUCCESS, "Termo de Uso aceitado com sucesso.")
            return redirect('accounts:minha-conta')
    else:
        form = AprovaTermosDeUsoForm(initial=request.POST)
    context = {
        'form': form,
        'next_url': next_url,
        'user_ip': user_ip,
    }
    return render(request, 'accounts/aprova-termos-de-uso.html', context)

def solicita_cadastro(request):
    if request.user.is_authenticated:
        return redirect('home')
    if request.method == 'POST':
        form = CadastroForm(request.POST)
        if form.is_valid():
            ip = get_client_ip(request)
            data = form.cleaned_data
            nome = data.get('nome', '').strip().upper()
            nome_publico = nome.split(' ')[0] + nome.split(' ')[-1][0] + '.'
            email = data.get('email', '').strip().lower()
            motivacao = data.get('motivacao', '').strip().upper()
            recebe_avisos = data.get('recebe_avisos', '')
            # try:
            #enviar email
            data_sanitized = {
                'first_name': nome,
                'nome_publico': nome_publico,
                'email': email,
                'motivacao': motivacao,
                'aceite_termos_uso_em': timezone.now(),
                'aceite_termos_uso_ip': ip,
                'recebe_avisos': recebe_avisos,
            }
            User = get_user_model()
            user_exists = User.objects.filter(email=data_sanitized['email'])
            if user_exists:
                messages.add_message(request, messages.ERROR, f'E-mail já cadastrado no sistema')
                return redirect('solicitacao-cadastro')
            user, pwd = create_user(**data_sanitized)
            send_mail_cadastro_validado.delay(
                nome=user.nome_publico,
                email=user.email,
                senha=pwd
            )
            messages.add_message(request, messages.SUCCESS, f'Usuário criado com sucesso. Verifique seu e-mail.')
            return redirect('accounts:login')
            # except Exception as err:
            #     print(err)
            #     logger.error(err)
            #     messages.add_message(request, messages.ERROR, "Ocorreu um erro. Por favor, tente novamente.")

        else:
            # print('form invalid: ', form.cleaned_data)
            messages.add_message(request, messages.ERROR, "Por favor, verifique os erros abaixo.")
    else:
        form = CadastroForm(initial=request.POST)
    context = {
        'form': form,
    }
    return render(request, 'accounts/solicitacao-cadastro.html', context)


class LoginView(auth_views.LoginView):
    template_name = 'accounts/login.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        ip = get_client_ip(self.request)
        if not self.request.user.is_superuser:
            LoginAudit.objects.create(
                user=self.request.user, ip=ip, logged_in_at=timezone.now()
        )
        return response


class LogoutView(LoginRequiredMixin, auth_views.LogoutView):
    template_name = 'accounts/logout.html'


class PasswordChangeView(LoginRequiredMixin, auth_views.PasswordChangeView):
    template_name = 'accounts/change-password.html'

    def get_success_url(self):
        messages.add_message(self.request, messages.SUCCESS, 'Sua senha foi alterada com sucesso')
        return reverse('home')


class MyPasswordResetView(auth_views.PasswordResetView):
    template_name = 'accounts/password-reset.html'
    success_url = reverse_lazy('accounts:password_reset_done')
    email_template_name = 'accounts/email.html'
    subject_template_name = 'accounts/subject.txt'
    from_email = settings.EMAIL_ADDRESS


class MyPasswordResetDoneView(auth_views.PasswordResetDoneView):
    template_name = 'accounts/password-reset-done.html'


class MyPasswordResetConfirmView(auth_views.PasswordResetConfirmView):
    template_name = 'accounts/password-reset-confirm.html'
    success_url = reverse_lazy('accounts:password_reset_complete')


class MyPasswordResetCompleteView(auth_views.PasswordResetCompleteView):
    template_name = 'accounts/password-reset-complete.html'


class CookieTokenRefreshSerializer(TokenRefreshSerializer):
    refresh = None
    def validate(self, attrs):
        attrs['refresh'] = self.context['request'].COOKIES.get('refresh_token')
        if attrs['refresh']:
            return super().validate(attrs)
        else:
            raise InvalidToken('No valid token found in cookie \'refresh_token\'')

class CookieTokenObtainPairView(TokenObtainPairView):
  def finalize_response(self, request, response, *args, **kwargs):
    if response.data.get('refresh'):
        cookie_max_age = 3600 * 24 * 14 # 14 days
        response.set_cookie('refresh_token', response.data['refresh'], max_age=cookie_max_age, httponly=True )
        del response.data['refresh']
    return super().finalize_response(request, response, *args, **kwargs)

class CookieTokenRefreshView(TokenRefreshView):
    def finalize_response(self, request, response, *args, **kwargs):
        if response.data.get('refresh'):
            cookie_max_age = 3600 * 24 * 14 # 14 days
            response.set_cookie('refresh_token', response.data['refresh'], max_age=cookie_max_age, httponly=True )
            del response.data['refresh']
        return super().finalize_response(request, response, *args, **kwargs)
    serializer_class = CookieTokenRefreshSerializer


class BlacklistTokenView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = ()

    def post(self, request):
        try:
            refresh_token = request.COOKIES['refresh_token']
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response(status=status.HTTP_205_RESET_CONTENT)
        except Exception as e:
            return Response(status=status.HTTP_400_BAD_REQUEST)


def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

import secrets
def create_user(**data):
    try:
        User = get_user_model()
        publico_group = Group.objects.get(name='público')
        user = User.objects.create(
            email=data['email'],
            user_name=data['email'],
            first_name=data['first_name'],
            motivacao=data['motivacao'],
            is_active=True,
            aceite_termos_uso_em=data['aceite_termos_uso_em'],
            aceite_termos_uso_ip=data['aceite_termos_uso_ip'],
        )
        password_length = 13
        pwd = secrets.token_urlsafe(password_length)
        user.set_password(pwd)
        user.save()
        user.groups.add(publico_group)
        return user, pwd
    except Exception as err:
        raise err