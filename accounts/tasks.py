import os
import logging
import datetime
import zipfile
import tempfile
import pandas as pd
from io import BytesIO
from typing import Tuple

from dotenv import load_dotenv

load_dotenv("../../.env")

from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.core.management.base import BaseCommand
from django.http import HttpResponseServerError
from django.utils import timezone
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from accounts.models import PathAudit, LoginAudit, Profile

from celery import shared_task, Celery

app = Celery()

logger = logging.getLogger("django_file")
celery_logger = logging.getLogger("celery")

USE_PROXY = os.getenv("USE_PROXY") == "on"


@shared_task
def send_mail_cadastro_validado(nome, email, senha):
    try:
        html_content = render_to_string(
            "accounts/aprovacao-cadastro-email.html",
            {
                "nome": nome,
                "email": email,
                "senha": senha,
                "url_cci": "https://cci.artesp.sp.gov.br",
                "url_minha_conta": "https://cci.artesp.sp.gov.br/contas/minha-conta/",
            },
        )
        text_content = strip_tags(html_content)
        email = EmailMultiAlternatives(
            subject=f"[ portalcci ] Seu cadastro foi realizado",
            body=text_content,
            from_email="<EMAIL>",
            to=(email,),
            # bcc=('<EMAIL>',),
        )
        email.attach_alternative(html_content, "text/html")
        email.send(fail_silently=False)
        return "Email para {email} enviado com sucesso!"
    except Exception as err:
        logger.error(err)
        raise err
        return err


def get_excel_visitas():
    qs_visitas = PathAudit.objects.prefetch_related("user").values()
    df_visitas = pd.DataFrame.from_records(
        qs_visitas.values(
            "ip",
            "caminho",
            "acessado_em",
            "user__email",
            "user__first_name",
        )
    )

    df_visitas.columns = [
        "IP",
        "CAMINHO",
        "ACESSADO EM",
        "USUÁRIO",
        "NOME",
    ]

    with BytesIO() as buffer:
        writer = pd.ExcelWriter(buffer, engine="xlsxwriter")
        df_visitas.to_excel(writer, sheet_name="ESTATÍSTICAS", index=False)
        writer.close()
        buffer.seek(0)

        return (
            "visitas-paineis.xlsx",
            buffer.read(),
        )


def send_mail_rel_visitas(
    to, cc, bcc, subject, body_text, buffer, attachment_name
) -> Tuple[str, bool]:
    try:
        email = EmailMultiAlternatives(
            subject=subject,
            body=body_text,
            from_email=os.environ.get("MAIL_FROM"),
            to=to,
            cc=cc,
            bcc=bcc,
            # connection=smtp_backend
        )
        # zip file
        email.attach(
            f"{attachment_name}",
            buffer,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        email.send(fail_silently=False)
        return "E-mail enviado com sucesso!\n", False
    except Exception as err:
        return f"Ops! Não foi possível enviar o e-mail: \n'{err}'", True


@app.task
def envia_relatorio_visitas():
    try:
        _now = datetime.datetime.now()
        text_content = "Estatísticas de acesso aos painéis no últimos 60 dias"
        data_str = _now.strftime("%d/%m/%Y %H:%M")
        subject = f"[ portalcci ] Estatísticas de acesso aos painéis no últimos 60 dias | {data_str}"
        print("Enviando e-mail...")
        to = ["<EMAIL>"]
        cc = ["<EMAIL>"]
        bcc = []
        attachment_anme, buffer = get_excel_visitas()
        msg, error = send_mail_rel_visitas(
            to, cc, bcc, subject, text_content, buffer, attachment_anme
        )
        if error:
            raise HttpResponseServerError(msg)

        print(msg)
    except Exception as err:
        logger.error(err)
        print(f"Ocorreu um erro:\n {err}")


@app.task
def limpa_login():
    print("Apagando logs de login...")

    # FETCH DATA
    _now = datetime.datetime.now()
    _timedelta_31_dias = datetime.timedelta(days=30)
    trinta_dias_atras = _now - _timedelta_31_dias
    login_audit_selected = LoginAudit.objects.filter(logged_in_at__lt=trinta_dias_atras)
    total = len(login_audit_selected)
    texto = "Arquivos apagados com sucesso"
    print(f"{total} itens encontrados")
    login_audit_selected.delete()
    if total:
        print("Enviando e-mail de confirmação...")
        email = EmailMultiAlternatives(
            subject=f"LoginAutdi antigos ({total}) foram apagados com sucesso",
            body=texto,
            from_email=os.environ.get("MAIL_FROM"),
            to=[os.environ.get("MAIL_ADMIN")],
        )
        email.send(fail_silently=True)
        print("E-mail enviado com sucesso...")
    # END
    print("Fim...")


@app.task
def limpa_relatorio_visitas():
    # START...
    print("Apagando logs de visitas...")
    # FETCH DATA
    _now = datetime.datetime.now()
    N_DIAS = 61
    _timedelta_n_dias = datetime.timedelta(days=N_DIAS)
    n_dias_atras = _now - _timedelta_n_dias
    visitas_audit_selected = PathAudit.objects.filter(acessado_em__lt=n_dias_atras)
    total = len(visitas_audit_selected)
    texto = "Arquivos apagados com sucesso"
    print(f"{total} itens encontrados")
    visitas_audit_selected.delete()
    if total:
        print("Enviando e-mail de confirmação...")
        email = EmailMultiAlternatives(
            subject=f"PathAudit antigos ({total}) foram apagados com sucesso",
            body=texto,
            from_email=os.environ.get("MAIL_FROM"),
            to=[os.environ.get("MAIL_ADMIN")],
        )
        email.send(fail_silently=True)
        print("E-mail enviado com sucesso...")

    print("Fim...")


@shared_task(time_limit=600)
def send_warning_emails() -> str:
    logger.info("Sending warning emails...")
    print("Sending warning emails...")
    forty_five_days_ago = timezone.now() - datetime.timedelta(days=45)
    profiles_to_warn = Profile.objects.filter(
        last_login__lt=forty_five_days_ago, 
        user__is_active=True,
        to_delete__isnull=True
    )
    to_delete = 0
    for profile in profiles_to_warn:
        to_delete += 1
        try:
            # Send email to the user
            # send_mail(
            #     "Atenção: sua conta no PortalCCI será apagada em breve.",
            #     "Seu último acesso ao PortalCCI ocorreu há mais de 45 dias. Caso não entre no sistema, sua conta será apagada em 15 dias, contados a partir da data deste aviso. Este é um e-mail automático.",
            #     "<EMAIL>",
            #     [profile.user.email],
            #     fail_silently=False,
            # )
            profile.to_delete = timezone.now() + datetime.timedelta(days=15)
            profile.save()
        except Exception as e:
            logger.error(f"Failed to send warning email to {profile.user.email}: {e}")

    logger.info("Finished sending warning emails.")
    return f"Warned {to_delete} users."

@shared_task(time_limit=600)
def delete_old_users() -> str:
    profiles_to_delete = Profile.objects.filter(
        to_delete__isnull=False,
        to_delete__lt=timezone.now(),
        user__is_active=True,
    )

    deleted_count = 0
    for profile in profiles_to_delete:
        user_email = profile.user.email
        try:
            profile.user.delete()
            deleted_count += 1

            # Send email to the user about account deletion
            # send_mail(
            #     subject="Sua conta no PortalCCI foi apagada",
            #     message="Sua conta e todos os seus dados foram apagados do nosso servidor. Caso queira acessar o PortalCCI novamente, crie uma conta novamente. Este é um e-mail automático.",
            #     from_email="<EMAIL>",
            #     recipient_list=[user_email],
            #     fail_silently=False,
            # )
        except Exception as e:
            logger.error(f"Failed to delete user {profile.user.email} and send deletion email: {e}")

    if deleted_count > 0:
        try:
            # Send summary email notification
            send_mail(
                subject="User Deletion Report",
                message=f"Successfully deleted {deleted_count} users",
                from_email="<EMAIL>",
                recipient_list=["<EMAIL>"],
                fail_silently=False,
            )
        except Exception as e:
            logger.error(f"Failed to send user deletion summary email: {e}")

    logger.info(f"Deleted {deleted_count} users.")
    return f"Deleted {deleted_count} users."
    


@shared_task
def send_test_email(to_email):
    print("send_test_email")
    try:
        subject = "Test Email"
        message = "This is a test email sent from Celery."
        from_email = settings.DEFAULT_FROM_EMAIL
        send_mail(subject, message, from_email, [to_email])
        logger.info(f"Email sent to {to_email}")
        celery_logger.info(f"Email sent to {to_email}")
    except Exception as e:
        logger.error(f"Error sending email to {to_email}: {e}")
        celery_logger.error(e)
        print(e)
        raise e


import requests


@shared_task(soft_time_limit=10, time_limit=15)
def test_request_call(url):
    try:
        logger.debug(f"Attempting to access {url}")
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        logger.info(
            f"Request to {url} succeeded with status code {response.status_code}"
        )
        return response.json()  # or response.text if the content is not JSON
    except requests.exceptions.RequestException as e:
        logger.error(f"Request to {url} failed: {e}")
        raise e
