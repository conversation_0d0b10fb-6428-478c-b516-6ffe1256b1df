import datetime

from django.contrib.auth.signals import user_logged_in
from django.db.models.signals import post_save, pre_save
from django.contrib.auth import get_user_model
from django.dispatch import receiver
from . models import Profile

User = get_user_model()

def set_nome_publico(first_name):
    try:
        names = first_name.split(" ")
        if names and len(names) > 1:
            first = names[0]
            second = names[-1][0] + '.'
            name = f'{first} {second}'
        elif names and len(names) == 1:
            name = names[0]
        else:
            name = first_name
        return name
    except:
        return first_name


@receiver(pre_save)
def fill_nome_publico(sender, instance, *args, **kwargs):
    if not issubclass(sender, User):
        return
    if not instance.nome_publico:
        instance.nome_publico = set_nome_publico(instance.first_name)

@receiver(user_logged_in)
def user_logged(sender,request,user,**kwargs):
    _now = datetime.datetime.now()
    profile, _ = Profile.objects.get_or_create(user=user)
    profile.log_count += 1
    profile.last_login = _now
    profile.to_delete = None
    profile.save()
    user.save()


@receiver(post_save)
def gen_profile(sender, instance, created, **kwargs):
    if not issubclass(sender, User):
        return
    if created:
        instance.save()
        _now = datetime.datetime.now()
        profile = Profile(user=instance, last_login=_now)
        profile.save()