# Generated by Django 3.2 on 2023-05-11 07:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0004_auto_20230508_1509'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='end_web',
            field=models.URLField(blank=True, max_length=150, null=True, verbose_name='Endereço web'),
        ),
        migrations.AddField(
            model_name='account',
            name='is_artesp',
            field=models.BooleanField(default=False, verbose_name='ARTESP'),
        ),
        migrations.AddField(
            model_name='account',
            name='is_concessionaria',
            field=models.BooleanField(default=False, verbose_name='CONCESSIONÁRIA'),
        ),
        migrations.AddField(
            model_name='account',
            name='is_externo',
            field=models.BooleanField(default=False, verbose_name='EXTERNO'),
        ),
        migrations.AddField(
            model_name='account',
            name='is_governo',
            field=models.BooleanField(default=False, verbose_name='GOVERNO'),
        ),
        migrations.AddField(
            model_name='account',
            name='is_imprensa',
            field=models.BooleanField(default=False, verbose_name='IMPRENSA ARTESP'),
        ),
        migrations.AddField(
            model_name='account',
            name='motivacao',
            field=models.TextField(blank=True, max_length=500, verbose_name='Motivação para cadastro'),
        ),
    ]
