# Generated by Django 4.2.5 on 2024-04-27 19:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0014_profile_last_login'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='aceite_termos_uso_em',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Aceite dos termos de uso'),
        ),
        migrations.AddField(
            model_name='account',
            name='aceite_termos_uso_ip',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='solicitacaocadastro',
            name='ip',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
    ]
