# Generated by Django 3.2 on 2023-07-20 13:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0007_account_dt_nascimento'),
    ]

    operations = [
        migrations.AlterField(
            model_name='account',
            name='start_date',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='Data Cadastro'),
        ),
        migrations.CreateModel(
            name='PathAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('caminho', models.CharField(blank=True, max_length=255, null=True, verbose_name='caminho')),
                ('acessado_em', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='paths', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Visita',
                'verbose_name_plural': 'Visitas',
                'ordering': ('-acessado_em',),
            },
        ),
    ]
