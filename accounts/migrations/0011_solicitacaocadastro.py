# Generated by Django 4.2.5 on 2023-10-26 17:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0010_alter_account_concessionarias'),
    ]

    operations = [
        migrations.CreateModel(
            name='SolicitacaoCadastro',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, verbose_name='e-mail')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='nome completo')),
                ('cpf', models.CharField(blank=True, max_length=25, null=True, verbose_name='cpf')),
                ('dt_nascimento', models.DateField(blank=True, null=True, verbose_name='Data de nascimento')),
                ('cnpj', models.Char<PERSON>ield(blank=True, max_length=25, null=True, verbose_name='cnpj')),
                ('funcao', models.CharField(blank=True, max_length=150, null=True, verbose_name='área de atuação')),
                ('telefone', models.CharField(blank=True, max_length=25, null=True, verbose_name='telefone')),
                ('empresa', models.CharField(blank=True, max_length=150, null=True, verbose_name='empresa')),
                ('end_web', models.URLField(blank=True, max_length=150, null=True, verbose_name='Endereço web')),
                ('motivacao', models.TextField(blank=True, max_length=500, verbose_name='Motivação para cadastro')),
                ('criada_em', models.DateTimeField(auto_now_add=True)),
                ('eliminada_em', models.DateTimeField(blank=True, null=True)),
                ('aprovada_em', models.DateTimeField(blank=True, null=True)),
                ('aprovada_por', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='aprovador', to=settings.AUTH_USER_MODEL, verbose_name='Aprovada por')),
                ('eliminada_por', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='eliminador', to=settings.AUTH_USER_MODEL, verbose_name='Eliminada por')),
            ],
            options={
                'verbose_name': 'Solicitação de cadastro',
                'verbose_name_plural': 'Solicitações de cadastro',
                'ordering': ['first_name', 'email'],
            },
        ),
    ]
