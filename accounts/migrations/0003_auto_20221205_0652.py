# Generated by Django 3.2 on 2022-12-05 06:52

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_loginaudit_profile'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='account',
            options={'ordering': ['first_name', 'email'], 'verbose_name': 'Usuário', 'verbose_name_plural': 'Usuários'},
        ),
        migrations.AlterModelOptions(
            name='loginaudit',
            options={'ordering': ('-logged_in_at',), 'verbose_name': 'Auditoria', 'verbose_name_plural': 'Auditoria'},
        ),
        migrations.AlterModelOptions(
            name='profile',
            options={'ordering': ('-log_count',), 'verbose_name': 'Perfil', 'verbose_name_plural': 'Perfis'},
        ),
    ]
