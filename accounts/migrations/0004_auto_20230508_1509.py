# Generated by Django 3.2 on 2023-05-08 15:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_auto_20221205_0652'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='cnpj',
            field=models.CharField(blank=True, max_length=25, null=True, verbose_name='cnpj'),
        ),
        migrations.AddField(
            model_name='account',
            name='cpf',
            field=models.CharField(blank=True, max_length=25, null=True, verbose_name='cpf'),
        ),
        migrations.AddField(
            model_name='account',
            name='empresa',
            field=models.CharField(blank=True, max_length=150, null=True, verbose_name='empresa'),
        ),
        migrations.AddField(
            model_name='account',
            name='funcao',
            field=models.CharField(blank=True, max_length=150, null=True, verbose_name='área de atuação'),
        ),
        migrations.Add<PERSON>ield(
            model_name='account',
            name='telefone',
            field=models.CharField(blank=True, max_length=25, null=True, verbose_name='telefone'),
        ),
        migrations.AlterField(
            model_name='account',
            name='first_name',
            field=models.CharField(blank=True, max_length=150, verbose_name='nome completo'),
        ),
        migrations.AlterField(
            model_name='account',
            name='user_name',
            field=models.CharField(max_length=150, unique=True, verbose_name='nome de usuário'),
        ),
    ]
