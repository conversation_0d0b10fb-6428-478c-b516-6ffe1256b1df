from django.core.management.base import BaseCommand
from _core.celery import app

class Command(BaseCommand):
    help = 'Purge the default Celery queue'

    def handle(self, *args, **kwargs):
        try:
            queue_name = 'celery'  # Default queue name
            num_purged = app.control.purge()
            self.stdout.write(self.style.SUCCESS(f'Successfully purged {num_purged} messages from queue: {queue_name}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error purging queue: {str(e)}'))

