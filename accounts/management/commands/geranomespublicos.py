import datetime
import os
from django.contrib.auth import get_user_model
from dotenv import load_dotenv
load_dotenv('../../.env')

from django.core.mail import EmailMultiAlternatives
from django.conf import settings

from accounts.models import LoginAudit

from django.core.management.base import BaseCommand

User = get_user_model()

def set_nome_publico(first_name):
    try:
        names = first_name.split(" ")
        if names and len(names) > 1:
            first = names[0]
            second = names[-1][0] + '.'
            name = f'{first} {second}'
        elif names and len(names) == 1:
            name = names[0]
        else:
            name = first_name
        return name.title() if isinstance(name, str) else name
    except:
        return first_name

class Command(BaseCommand):
    help = 'Gera nomes públicos'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Gerando nomes públicos...'))

        # FETCH DATA
        users = User.objects.all()
        for user in users:
            set_nome_publico(user.first_name)
            user.first_name = user.first_name.upper() if isinstance(user.first_name, str) else user.first_name
            user.funcao = user.funcao.upper() if isinstance(user.funcao, str) else user.funcao
            user.empresa = user.empresa.upper() if isinstance(user.empresa, str) else user.empresa
            user.motivacao = user.motivacao.upper() if isinstance(user.motivacao, str) else user.motivacao
            user.end_web = user.end_web.lower() if isinstance(user.end_web, str) else user.end_web
            user.save()

        
        # END
        self.stdout.write(self.style.SUCCESS('Fim...'))

