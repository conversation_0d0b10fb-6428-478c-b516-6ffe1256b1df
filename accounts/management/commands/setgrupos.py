from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group
from paineis.models import Painel

class Command(BaseCommand):
    help = 'Set the grupos_acesso field to the "público" group for all Painel objects'

    def handle(self, *args, **kwargs):
        try:
            publico_group = Group.objects.get(name='público')
        except Group.DoesNotExist:
            raise CommandError('Group "público" does not exist')

        paineis = Painel.objects.all()
        for painel in paineis:
            painel.grupos_acesso.add(publico_group)
            self.stdout.write(self.style.SUCCESS(f'Successfully added "público" group to Painel ID {painel.id}'))

        self.stdout.write(self.style.SUCCESS('Successfully set grupos_acesso for all Painel objects'))
