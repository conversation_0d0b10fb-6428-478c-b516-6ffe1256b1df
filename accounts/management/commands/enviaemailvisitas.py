import os
import logging
import datetime
import zipfile
import tempfile
import pandas as pd
from io import BytesIO
from typing import Tuple

from dotenv import load_dotenv
load_dotenv('../../.env')

from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.core.management.base import BaseCommand
from django.http import HttpResponseServerError
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from accounts.models import PathAudit

logger = logging.getLogger("django_file")

from accounts.tasks import envia_relatorio_visitas

class Command(BaseCommand):
    help = 'Limpa os logs de visitas com mais de um mês'

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Enviando visitas painéis por e-mail...'))
        envia_relatorio_visitas()
        # END
        self.stdout.write(self.style.SUCCESS('Fim...'))


