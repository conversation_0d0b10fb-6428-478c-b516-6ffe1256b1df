import datetime
import os

from dotenv import load_dotenv
load_dotenv('../../.env')

from django.core.mail import EmailMultiAlternatives
from django.conf import settings

from accounts.models import PathAudit

from django.core.management.base import BaseCommand

from accounts.tasks import limpa_relatorio_visitas

class Command(BaseCommand):
    help = 'Limpa os logs de visitas com mais de um mês'

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        limpa_relatorio_visitas()

