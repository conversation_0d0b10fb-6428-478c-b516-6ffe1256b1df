from django.core.management.base import BaseCommand
from django.utils import timezone
from accounts.models import Profile
from datetime import timedelta

class Command(BaseCommand):
    help = 'Add 15 days to the "to_delete" field of Profile if the field is not null.'

    def handle(self, *args, **kwargs):
        # Fetch profiles with a non-null 'to_delete' field
        profiles = Profile.objects.filter(to_delete__isnull=False)
        updated_count = 0

        for profile in profiles:
            profile.to_delete += timedelta(days=15)
            profile.save()
            updated_count += 1

        self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated_count} profiles.'))
