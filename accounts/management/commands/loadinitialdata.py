from django.core import management
from django.core.management.commands import loaddata
from django.contrib.auth.models import Group, Permission
from concessionarias.models import Equipe

from django.core.management.base import BaseCommand, CommandError

class Command(BaseCommand):
    help = 'Carrega dados iniciais'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Loading data...'))

        # DADOS INICIAIS
        management.call_command('loaddata', 'municipios', verbosity=1)
        management.call_command('loaddata', 'regionais', verbosity=1)
        management.call_command('loaddata', 'concessionarias', verbosity=1)
        management.call_command('loaddata', 'rodovias', verbosity=1)
        management.call_command('loaddata', 'trechos', verbosity=1)

        # GRUPOS DE ACESSO
        Group.objects.get_or_create(name='cci')
        Group.objects.get_or_create(name='artesp')
        Group.objects.get_or_create(name='externo')

        # EQUIPES
        Equipe.objects.get_or_create(nome='Acessos')
        Equipe.objects.get_or_create(nome='Administrativo')
        Equipe.objects.get_or_create(nome='CCO')
        Equipe.objects.get_or_create(nome='Equipamentos')
        Equipe.objects.get_or_create(nome='Faixa de domínio')
        Equipe.objects.get_or_create(nome='Obras')
        Equipe.objects.get_or_create(nome='Operação')
        Equipe.objects.get_or_create(nome='Pedágio')
        Equipe.objects.get_or_create(nome='Segurança e Sinalização')
        # END
        self.stdout.write(self.style.SUCCESS('Data loaded'))

