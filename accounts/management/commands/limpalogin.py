import datetime
import os

from dotenv import load_dotenv
load_dotenv('../../.env')

from django.core.mail import EmailMultiAlternatives
from django.conf import settings

from accounts.models import LoginAudit

from django.core.management.base import BaseCommand

from accounts.tasks import limpa_login

class Command(BaseCommand):
    help = 'Limpa os logs de login com mais de um mês'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        limpa_login()


