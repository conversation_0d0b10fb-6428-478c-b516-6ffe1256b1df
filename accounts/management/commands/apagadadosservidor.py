from django.contrib.auth import get_user_model
from dotenv import load_dotenv
load_dotenv('../../.env')

from django.core.management.base import BaseCommand

User = get_user_model()


class Command(BaseCommand):
    help = 'Gera nomes públicos'

    def add_arguments(self, parser):
        pass
        #parser.add_argument('poll_ids', nargs='+', type=int)

    def handle(self, *args, **options):
        # START...
        self.stdout.write(self.style.WARNING('Apagando dados sensíveis dos usuários...'))

        # FETCH DATA
        users = User.objects.all()
        for user in users:
            user.cpf = None
            user.dt_nascimento = None
            user.cnpj = None
            user.telefone = None
            user.funcao = None
            user.empresa = None
            user.end_web = None
            user.save()

        
        # END
        self.stdout.write(self.style.SUCCESS('Fim...'))

