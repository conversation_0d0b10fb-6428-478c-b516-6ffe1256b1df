from ipaddress import ip_address
from datetime import datetime   
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin, BaseUserManager
from django.conf import settings


class CustomAccountManager(BaseUserManager):

    def create_superuser(self, email, user_name, first_name, password, **other_fields):

        other_fields.setdefault('is_staff', True)
        other_fields.setdefault('is_superuser', True)
        other_fields.setdefault('is_active', True)

        if other_fields.get('is_staff') is not True:
            raise ValueError(
                'Superuser must be assigned to is_staff=True.')
        if other_fields.get('is_superuser') is not True:
            raise ValueError(
                'Superuser must be assigned to is_superuser=True.')

        return self.create_user(email, user_name, first_name, password, **other_fields)

    def create_user(self, email, user_name, first_name, password, **other_fields):

        if not email:
            raise ValueError(_('You must provide an email address'))

        email = self.normalize_email(email)
        user = self.model(email=email, user_name=user_name,
                          first_name=first_name, **other_fields)
        user.set_password(password)
        user.save()
        profile = Profile(user=user)
        profile.save()
        return user


class Account(AbstractBaseUser, PermissionsMixin):
    class Meta:
        ordering = ['first_name', 'email',]
        verbose_name = 'Usuário'
        verbose_name_plural  = 'Usuários'

    email = models.EmailField(_('e-mail'), unique=True)
    user_name = models.CharField(verbose_name='nome de usuário', max_length=150, unique=True)
    first_name = models.CharField(verbose_name='nome completo', max_length=150, blank=True)
    nome_publico = models.CharField(verbose_name='nome público', max_length=150, blank=True)

    # novos campos
    cpf = models.CharField(verbose_name='cpf', max_length=25, blank=True, null=True)
    dt_nascimento = models.DateField(verbose_name='Data de nascimento', blank=True, null=True)
    cnpj = models.CharField(verbose_name='cnpj', max_length=25, blank=True, null=True)
    funcao = models.CharField(verbose_name='área de atuação', max_length=150, blank=True, null=True)
    telefone = models.CharField(verbose_name='telefone', max_length=25, blank=True, null=True)
    empresa = models.CharField(verbose_name='empresa', max_length=150, blank=True, null=True)
    end_web = models.URLField(verbose_name='Endereço web', max_length=150, blank=True, null=True)
    motivacao = models.TextField(_(
        'Motivação para cadastro'), max_length=500, blank=True)
    aceite_termos_uso_em = models.DateTimeField(null=True, blank=True, verbose_name='Aceite dos termos de uso')
    aceite_termos_uso_ip = models.GenericIPAddressField(null=True, blank=True,)
    recebe_avisos = models.BooleanField('Receber avisos', null=True, blank=True, default=True)

    # antigos
    start_date = models.DateTimeField(default=timezone.now, verbose_name='Data Cadastro')
    about = models.TextField(_(
        'Sobre'), max_length=500, blank=True)
    is_artesp = models.BooleanField(verbose_name='ARTESP', default=False)
    is_concessionaria = models.BooleanField(verbose_name='CONCESSIONÁRIA', default=False)
    is_imprensa = models.BooleanField(verbose_name='IMPRENSA ARTESP', default=False)
    is_governo = models.BooleanField(verbose_name='GOVERNO', default=False)
    is_externo = models.BooleanField(verbose_name='EXTERNO', default=False)
    is_staff = models.BooleanField(verbose_name='ADM', default=False)
    is_active = models.BooleanField(verbose_name='ATIVO',default=True) # TODO: turn false and activation through e-mail

    concessionarias = models.ManyToManyField(
        "concessionarias.Concessionaria", verbose_name=_("Concessionarias"), related_name='user_concs', blank=True)

    objects = CustomAccountManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['user_name', 'first_name']

    def get_display_menu_name(self):
        try:
            names = self.first_name.split(" ")
            if names and len(names) > 1:
                first = names[0]
                second = names[-1][0] + '.'
                name = f'{first} {second}'
            elif names and len(names) == 1:
                name = names[0]
            else:
                name = ''
            empresa = self.empresa
            if empresa:
                if len(empresa) > 12:
                    empresa = empresa[:12] + '...'
                name = name + f' ({empresa})'
            return name
        except:
            return self.first_name

    def __str__(self):
        return f'{self.nome_publico} <{self.email}>'


class Profile(models.Model):
    class Meta:
        ordering = ('-log_count',)
        verbose_name = 'Perfil'
        verbose_name_plural  = 'Perfis'
    user = models.ForeignKey(Account, on_delete=models.CASCADE, related_name="profile")
    log_count = models.IntegerField(default=0)
    last_login = models.DateTimeField(auto_now_add=False, default=datetime.now)
    to_delete = models.DateTimeField(null=True, blank=True) 

    def __str__(self):
        return f'{self.user.first_name} <{self.user.email}>'


class LoginAudit(models.Model):
    class Meta:
        ordering = ('-logged_in_at',)
        verbose_name = 'Auditoria'
        verbose_name_plural  = 'Auditoria'
    user = models.ForeignKey(Account, on_delete=models.CASCADE, related_name="logins")
    ip = models.GenericIPAddressField()
    logged_in_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f'{self.logged_in_at} <{self.user.email}>'


class PathAudit(models.Model):
    class Meta:
        ordering = ('-acessado_em',)
        verbose_name = 'Visita'
        verbose_name_plural  = 'Visitas'
    user = models.ForeignKey(Account, on_delete=models.CASCADE, related_name="paths", blank=True, null=True)
    caminho = models.CharField(verbose_name='caminho', max_length=255, blank=True, null=True)
    ip = models.GenericIPAddressField(null=True, blank=True)
    acessado_em = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f'{self.acessado_em} <{self.user.email}>'


class SolicitacaoCadastro(models.Model):
    class Meta:
        ordering = ['first_name', 'email',]
        verbose_name = 'Solicitação de cadastro'
        verbose_name_plural  = 'Solicitações de cadastro'

    email = models.EmailField(_('e-mail'),)
    first_name = models.CharField(verbose_name='nome completo', max_length=150, blank=True)

    # novos campos
    cpf = models.CharField(verbose_name='cpf', max_length=25, blank=True, null=True)
    dt_nascimento = models.DateField(verbose_name='Data de nascimento', blank=True, null=True)
    cnpj = models.CharField(verbose_name='cnpj', max_length=25, blank=True, null=True)
    funcao = models.CharField(verbose_name='área de atuação', max_length=150, blank=True, null=True)
    telefone = models.CharField(verbose_name='telefone', max_length=25, blank=True, null=True)
    empresa = models.CharField(verbose_name='empresa', max_length=150, blank=True, null=True)
    end_web = models.URLField(verbose_name='Endereço web', max_length=150, blank=True, null=True)
    motivacao = models.TextField(_(
        'Motivação para cadastro'), max_length=500, blank=True)
    criada_em = models.DateTimeField(auto_now_add=True)
    eliminada_em = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    eliminada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name='Eliminada por', related_name='eliminador'
    )
    aprovada_em = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    aprovada_por = models.ForeignKey(
        settings.AUTH_USER_MODEL, null=True, blank=True, on_delete=models.SET_NULL,
        verbose_name='Aprovada por', related_name='aprovador'
    )
    ip = models.GenericIPAddressField(null=True, blank=True)
    
    def __str__(self):
        return f'{self.first_name} <{self.email}> ({self.criada_em})'