from django.db import models
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class Municipio(models.Model):
    cod_ibge = models.Char<PERSON>ield(_("Cód. IBGE"), max_length=50)
    nome = models.CharField(_("Nome"), max_length=50)
    capital = models.BooleanField(_("Capital"))
    uf = models.CharField(_("UF"), max_length=2)
    lat = models.DecimalField(_("Latitude"), max_digits=12, decimal_places=7)
    lng = models.DecimalField(_("Longitude"), max_digits=12, decimal_places=7)

    class Meta:
        ordering = ['nome']


    def __str__(self):
        return f'{self.nome}-SP'