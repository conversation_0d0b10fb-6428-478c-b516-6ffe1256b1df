# Generated by Django 3.2 on 2021-08-29 13:13

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Municipio',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cod_ibge', models.Char<PERSON>ield(max_length=50, verbose_name='Cód. IBGE')),
                ('nome', models.CharField(max_length=50, verbose_name='Nome')),
                ('capital', models.BooleanField(verbose_name='Capital')),
                ('uf', models.CharField(max_length=2, verbose_name='UF')),
                ('lat', models.DecimalField(decimal_places=7, max_digits=12, verbose_name='Latitude')),
                ('lng', models.DecimalField(decimal_places=7, max_digits=12, verbose_name='Longitude')),
            ],
            options={
                'ordering': ['nome'],
            },
        ),
    ]
