# 0 9 15 * * /usr/bin/certbot renew >> /logs/certbot-cron.log 2>&1
0 3 1 * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py enviaboletim
0 4 1 * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py enviaemailvisitas 
0 3 2 * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py limpalogin
0 4 2 * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py limpacoletas 
0 5 2 * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py limpa_coletas 
0 6 2 * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py limpavisitas
0 7 * * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py dbbackup --clean
0 * * * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py tempoviagem 
*/30 * * * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py getwazetraffic 
*/35 * * * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py makewazecsv 
*/12 * * * * cd /root/cciapp/cciapp && ../venv/bin/python manage.py getwazealerts
