#!/bin/bash

# A script to fully refresh a PostgreSQL database in a Docker container.
# It drops all existing tables and then ingests a new SQL dump file,
# temporarily disabling foreign key constraints to bypass integrity errors.

# Exit immediately if a command fails
set -eu

##
# --- Configuration ---
##
CONTAINER_NAME="cciapp_postgres"
SQL_FILE="/home/<USER>/projects/python/cciapp/bkp/20250715_main.sql"
POSTGRES_USER="doretto"
POSTGRES_PASSWORD="doretto" # Used by psql via the PGPASSWORD env var
POSTGRES_DB="portalcci"

# Internal path for the SQL file inside the container
CONTAINER_SQL_PATH="/tmp/dump.sql"


##
# --- Pre-flight Checks ---
##
echo "▶️ Starting database refresh for container '$CONTAINER_NAME'..."

# Check if container is running
if ! sudo docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "❌ Error: Container '${CONTAINER_NAME}' is not running."
    exit 1
fi

# Ensure the SQL file exists
if [ ! -f "$SQL_FILE" ]; then
    echo "❌ Error: SQL file not found at '$SQL_FILE'"
    exit 1
fi


##
# --- Main Logic ---
##

# Step 1: Drop all tables, sequences, etc., by recreating the public schema.
echo "🗑️  Resetting database by recreating the 'public' schema..."
sudo docker exec "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" \
    -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
echo "✅ Schema reset successfully."

# Step 2: Copy the SQL dump into the container.
echo "🚚 Copying SQL file to container..."
sudo docker cp "$SQL_FILE" "${CONTAINER_NAME}:${CONTAINER_SQL_PATH}"

# Step 3: Ingest the SQL dump.
# We disable foreign key checks by setting session_replication_role to 'replica'.
# This allows loading data with integrity issues (like orphaned records).
echo "🚀 Ingesting SQL dump (with foreign key checks temporarily disabled)..."
sudo docker exec \
    -e PGPASSWORD="$POSTGRES_PASSWORD" \
    -i "$CONTAINER_NAME" psql \
    -U "$POSTGRES_USER" \
    -d "$POSTGRES_DB" \
    --quiet \
    --set ON_ERROR_STOP=1 <<-EOF
SET session_replication_role = 'replica';
\i $CONTAINER_SQL_PATH
SET session_replication_role = 'origin';
EOF
# Check the exit code from the pipeline
if [ $? -ne 0 ]; then
    echo "❌ Error: SQL ingestion failed."
    exit 1
fi
echo "✅ SQL ingestion completed."

# Step 4: Clean up by removing the SQL file from the container.
echo "🧹 Cleaning up..."
sudo docker exec "$CONTAINER_NAME" rm "$CONTAINER_SQL_PATH"

echo "🎉 Database refresh complete!"

exit 0