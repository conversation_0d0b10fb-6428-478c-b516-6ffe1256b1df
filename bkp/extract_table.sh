#!/bin/bash

# Check if table name is provided
if [ -z "$1" ]; then
    echo "Error: Table name not provided."
    echo "Usage: $0 <table_name> [output_file]"
    exit 1
fi

# Variables
CONTAINER_NAME="cciapp_postgres"
TABLE_NAME="$1"
OUTPUT_FILE="${2:-/home/<USER>/$(date +%Y%m%d)_${TABLE_NAME}.csv}"  # Default or provided output path
POSTGRES_USER="doretto"
POSTGRES_PASSWORD="doritos2023"
POSTGRES_DB="portalcci"

echo "Extracting table $TABLE_NAME to CSV..."

# Export table to CSV using docker exec to run the COPY command
docker exec -i $CONTAINER_NAME psql -U $POSTGRES_USER -d $POSTGRES_DB -c "\COPY $TABLE_NAME TO STDOUT WITH CSV HEADER" > "$OUTPUT_FILE"

# Check if the export was successful
if [ $? -eq 0 ]; then
    echo "Table exported successfully to $OUTPUT_FILE"
    echo "File size: $(du -h "$OUTPUT_FILE" | cut -f1)"
else
    echo "Error exporting table to CSV."
    exit 1
fi