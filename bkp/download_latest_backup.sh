#!/bin/bash
# download_latest_backup.sh
#
# Purpose: Connect to a remote server, find the latest modified .sql file containing "_main",
#          and download it to a local directory.
# Usage: ./download_latest_backup.sh
#
# Prerequisites:
# - SSH access configured to the remote server using the specified alias/hostname.
# - The specified remote directory exists and the user has read permissions.
# - The local directory exists or can be created, and the user has write permissions.
#
# Created by Gemini Code Assist

# Enable strict error handling
set -euo pipefail

# Setup logging to both console and file
# Using a timestamp in the log file name for uniqueness
LOGFILE="/home/<USER>/Projects/python/cciapp/bkp/download_latest_backup_$(date +%Y%m%d_%H%M%S).log"
mkdir -p "$(dirname "$LOGFILE")" 2>/dev/null || true
exec > >(tee "$LOGFILE") 2>&1

echo "============================================="
echo "Starting latest backup download process: $(date)"
echo "============================================="

# Environment Variables - Customize as needed
# -------------------

# Remote Server Details
REMOTE_SSH_ALIAS="portalcci"       # SSH alias from ~/.ssh/config or hostname
REMOTE_BACKUP_DIR="/home/<USER>/cciapp/cciapp/backups" # Directory on the remote server

# Local Directory
LOCAL_BKP_DIR="/home/<USER>/projects/python/cciapp/bkp" # Local directory to save the backup

# Function to handle errors
error_exit() {
    echo "ERROR: $1" >&2
    echo "Script failed at $(date)" >&2
    exit 1
}

# Function to check if a command is available
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error_exit "Required command '$1' not found. Please install it and try again."
    fi
}

# Check required commands
check_command ssh
check_command scp

# Create local directory if it doesn't exist
echo "Ensuring local backup directory exists: $LOCAL_BKP_DIR"
mkdir -p "$LOCAL_BKP_DIR" || error_exit "Failed to create local directory: $LOCAL_BKP_DIR"

# Find the latest modified file on the remote server
echo "Searching for the latest '*_main*.sql' file in $REMOTE_BACKUP_DIR on $REMOTE_SSH_ALIAS..."

# Use find to list files matching '*_main*.sql', sort by modification time, and get the full path
# -name '*_main*.sql': only consider files with "_main" in the name and a .sql extension
REMOTE_LATEST_FILE=$(ssh "$REMOTE_SSH_ALIAS" "find \"$REMOTE_BACKUP_DIR\" -maxdepth 1 -type f -name '*_main*.sql' -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -n 1 | cut -d' ' -f2-")

# Check if a file was found
if [ -z "$REMOTE_LATEST_FILE" ]; then
    echo "No '*_main*.sql' files found or unable to access directory '$REMOTE_BACKUP_DIR' on $REMOTE_SSH_ALIAS."
    echo "Please ensure the directory exists, contains matching .sql files, and you have read permissions."
    error_exit "Failed to find latest backup file on remote server."
fi

echo "Latest remote file identified: $REMOTE_LATEST_FILE"

# Extract just the filename from the full path
REMOTE_FILENAME=$(basename "$REMOTE_LATEST_FILE")
LOCAL_SAVE_PATH="$LOCAL_BKP_DIR/$REMOTE_FILENAME"

# Check if the file already exists locally (optional)
if [ -f "$LOCAL_SAVE_PATH" ]; then
    echo "Warning: Local file '$LOCAL_SAVE_PATH' already exists. Overwriting."
fi

# Copy the latest file from the remote server to the local directory
echo "Copying '$REMOTE_LATEST_FILE' from $REMOTE_SSH_ALIAS to '$LOCAL_SAVE_PATH'..."
scp "$REMOTE_SSH_ALIAS:$REMOTE_LATEST_FILE" "$LOCAL_SAVE_PATH" || error_exit "Failed to copy file from remote server."

echo "File copied successfully."
echo "Local file size: $(du -h "$LOCAL_SAVE_PATH" | cut -f1)"

echo "============================================="
echo "SUCCESS: Latest backup downloaded successfully: $(date)"
echo "============================================="

exit 0