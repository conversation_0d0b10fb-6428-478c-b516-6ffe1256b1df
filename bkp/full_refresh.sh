#!/bin/bash

# Variables (from your existing script)
CONTAINER_NAME="postgres16"
SQL_FILE="/home/<USER>/Projects/python/cciapp/cciapp/bkp/20250416_main.sql"
POSTGRES_USER="doretto"
POSTGRES_PASSWORD="doretto"
POSTGRES_DB="portalcci"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Check if container is running
echo -e "${YELLOW}Checking if container ${CONTAINER_NAME} is running...${NC}"
if ! sudo podman ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo -e "${RED}Error: Container ${CONTAINER_NAME} is not running.${NC}"
    exit 1
fi

# Ensure the SQL file exists
echo -e "${YELLOW}Checking if SQL file exists...${NC}"
if [ ! -f "$SQL_FILE" ]; then
    echo -e "${RED}Error: SQL file not found at $SQL_FILE${NC}"
    exit 1
fi

# Create a backup before dropping (optional but recommended)
BACKUP_DIR="/home/<USER>/Projects/python/cciapp/cciapp/bkp"
BACKUP_FILE="${BACKUP_DIR}/backup_$(date +%Y%m%d_%H%M%S).sql"
echo -e "${YELLOW}Creating backup in $BACKUP_FILE...${NC}"

# Make backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Create backup
sudo podman exec "$CONTAINER_NAME" pg_dump -U "$POSTGRES_USER" -d "$POSTGRES_DB" > "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Backup completed successfully.${NC}"
else
    echo -e "${RED}Backup failed. Do you want to continue anyway? (y/n)${NC}"
    read -r response
    if [[ "$response" != "y" ]]; then
        echo "Aborting operation."
        exit 1
    fi
fi

# Drop all tables in the database
echo -e "${YELLOW}Dropping all tables...${NC}"

# Generate a temporary SQL script to drop all tables
DROP_TABLES_SQL=$(cat <<EOF
DO \$\$
DECLARE
    r RECORD;
BEGIN
    -- Disable triggers
    SET session_replication_role = 'replica';
    
    -- Drop all tables
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
    
    -- Re-enable triggers
    SET session_replication_role = 'origin';
END \$\$;
EOF
)

# Execute the drop tables command
echo "$DROP_TABLES_SQL" | sudo podman exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB"

echo -e "${GREEN}All tables dropped successfully.${NC}"

# Recreate schema and import data
echo -e "${YELLOW}Recreating schema and importing data...${NC}"

# Execute the SQL file
sudo podman exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" < "$SQL_FILE"

# Check if the import succeeded
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Data imported successfully.${NC}"
else
    echo -e "${RED}Error importing data.${NC}"
    echo -e "${YELLOW}Restoring from backup...${NC}"
    
    # First drop all tables again
    echo "$DROP_TABLES_SQL" | sudo podman exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB"
    
    # Then restore from backup
    sudo podman exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" < "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        echo -e "${YELLOW}Restore from backup completed.${NC}"
    else
        echo -e "${RED}Failed to restore from backup. Database may be in an inconsistent state.${NC}"
    fi
    
    exit 1
fi

# Verify the import
echo -e "${YELLOW}Verifying database state...${NC}"
TABLE_COUNT=$(sudo podman exec "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")

echo -e "${GREEN}Database refresh completed with $TABLE_COUNT tables created.${NC}"
echo -e "${GREEN}Backup saved as: $BACKUP_FILE${NC}"