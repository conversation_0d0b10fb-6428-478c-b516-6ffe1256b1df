#!/bin/bash
# backup_and_update_table.sh
# 
# Purpose: Backup a PostgreSQL table from a Podman container and update it on a remote server
# Usage: ./backup_and_update_table.sh
#
# This script handles:
# - Creating a plain-text dump of a table from a local PostgreSQL in Podman container
# - Optional local test restore with renamed table to verify dump integrity
# - Secure copying to a remote server
# - Optional backup on remote server before update
# - Optional termination of existing connections to the table
# - Transaction-based restore on remote server
# - Comprehensive logging and error handling
#
# Authentication notes to avoid password prompts:
# 1. For PostgreSQL: Create .pgpass files with appropriate permissions (chmod 600):
#    - Local: ~/.pgpass with entry for localhost
#    - Remote: ~/.pgpass with entry for the server
#    Format: hostname:port:database:username:password
#
# 2. For SSH/SCP: Set up SSH keys:
#    ssh-copy-id doretto@*************
#
# Created by <PERSON> on March 20, 2025

# Enable strict error handling
set -euo pipefail

# Setup logging to both console and file
LOGFILE="/home/<USER>/Projects/python/cciapp/cciapp/bkp/backup_and_update_table.log"
mkdir -p "$(dirname "$LOGFILE")" 2>/dev/null || true
exec > >(tee "$LOGFILE") 2>&1

echo "============================================="
echo "Starting backup and update process: $(date)"
echo "============================================="

# Environment Variables - Customize as needed
# -------------------

# Local PostgreSQL (in Podman container)
LOCAL_CONTAINER="postgres"      # Make sure this matches your container's name or ID
LOCAL_DB="portalcci"
LOCAL_USER="doretto"
LOCAL_TABLE="rodovias_trecho"
LOCAL_SCHEMA="public"

# Remote PostgreSQL
REMOTE_SSH_ALIAS="portalcci"       # SSH alias from ~/.ssh/config
REMOTE_HOST="*************"        # Only used for PostgreSQL connection
REMOTE_PORT="5432"
REMOTE_DB="portalcci"
REMOTE_USER="doretto"

# File paths
LOCAL_DUMP_DIR="/home/<USER>/Projects/python/cciapp/cciapp/bkp"
LOCAL_DUMP_FILE="$LOCAL_DUMP_DIR/${LOCAL_TABLE}_dump.sql"
LOCAL_TEST_DUMP_FILE="$LOCAL_DUMP_DIR/test_${LOCAL_TABLE}_dump.sql"
REMOTE_DUMP_DIR="/home/<USER>/postgres/bkp"
REMOTE_DUMP_FILE="$REMOTE_DUMP_DIR/${LOCAL_TABLE}_dump.sql"
REMOTE_BACKUP_FILE="$REMOTE_DUMP_DIR/${LOCAL_TABLE}_backup_before_update.sql"

# Optional features (set to "true" to enable, "false" to disable)
DO_LOCAL_TEST="true"               # Test restore locally before sending to remote
DO_REMOTE_BACKUP="true"            # Backup remote table before updating
DO_TERMINATE_CONNECTIONS="true"    # Terminate remote connections before update

# PostgreSQL connection strings
LOCAL_PSQL_CONN="-U $LOCAL_USER -d $LOCAL_DB"
REMOTE_PSQL_CONN="-h localhost -p $REMOTE_PORT -U $REMOTE_USER -d $REMOTE_DB"

# Function to handle errors
error_exit() {
    echo "ERROR: $1" >&2
    echo "Script failed at $(date)" >&2
    exit 1
}

# Function to check if a command is available
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error_exit "Required command '$1' not found. Please install it and try again."
    fi
}

# Check required commands (including 'sudo' here, since we need it for podman)
check_command sudo
check_command podman
check_command scp
check_command ssh
check_command sed

# Create directory for local dumps if it doesn't exist
mkdir -p "$LOCAL_DUMP_DIR" || error_exit "Failed to create directory: $LOCAL_DUMP_DIR"

# Function: Dump the local table
dump_local_table() {
    echo "===== STEP 1: Dumping local table '$LOCAL_SCHEMA.$LOCAL_TABLE' ====="
    
    echo "Creating plain-text dump at: $LOCAL_DUMP_FILE"
    # Using plain text format (-F p) and excluding ownership info
    sudo podman exec -i "$LOCAL_CONTAINER" \
        pg_dump $LOCAL_PSQL_CONN \
        -F p \
        -t "$LOCAL_SCHEMA.$LOCAL_TABLE" \
        --data-only \
        --no-owner \
        --no-privileges \
        > "$LOCAL_DUMP_FILE" || error_exit "Failed to dump local table"
    
    echo "Dump completed successfully."
    echo "Dump file size: $(du -h "$LOCAL_DUMP_FILE" | cut -f1)"
}

# Function: Test restore the dump locally (optional) - Data only
test_local_restore() {
    if [ "$DO_LOCAL_TEST" != "true" ]; then
        echo "===== SKIPPING: Local test restore ====="
        return 0
    fi
    
    echo "===== STEP 2: Testing local data restore with test table ====="
    
    # Create a test table as a copy of the original structure
    TEST_TABLE="test_${LOCAL_TABLE}"
    
    # Drop the test table if it exists
    echo "Dropping test table if it exists..."
    echo "DROP TABLE IF EXISTS $LOCAL_SCHEMA.$TEST_TABLE CASCADE;" | \
        sudo podman exec -i "$LOCAL_CONTAINER" psql $LOCAL_PSQL_CONN
    
    # Create test table with same structure but no data
    echo "Creating test table with same structure as original..."
    echo "CREATE TABLE $LOCAL_SCHEMA.$TEST_TABLE (LIKE $LOCAL_SCHEMA.$LOCAL_TABLE INCLUDING ALL);" | \
        sudo podman exec -i "$LOCAL_CONTAINER" psql $LOCAL_PSQL_CONN
    
    # Create a modified dump file pointing to test table
    echo "Creating modified test dump file..."
    cp "$LOCAL_DUMP_FILE" "$LOCAL_TEST_DUMP_FILE"
    sed -i "s/$LOCAL_SCHEMA\.$LOCAL_TABLE/$LOCAL_SCHEMA\.$TEST_TABLE/g" "$LOCAL_TEST_DUMP_FILE"
    
    # Restore the data to the test table
    echo "Restoring data to test table..."
    cat "$LOCAL_TEST_DUMP_FILE" | \
        sudo podman exec -i "$LOCAL_CONTAINER" psql $LOCAL_PSQL_CONN \
        -v ON_ERROR_STOP=1 || error_exit "Test data restore failed"
    
    # Verify the test table has data
    echo "Verifying test table data..."
    ROW_COUNT=$(echo "SELECT COUNT(*) FROM $LOCAL_SCHEMA.$TEST_TABLE;" | \
        sudo podman exec -i "$LOCAL_CONTAINER" psql $LOCAL_PSQL_CONN -t | tr -d '[:space:]')
    
    echo "Test table contains $ROW_COUNT records."
    if [ -z "$ROW_COUNT" ] || [ "$ROW_COUNT" -eq 0 ]; then
        error_exit "Test table appears to be empty. Data restore test failed."
    fi
    
    # Clean up: Drop the test table
    echo "Cleaning up: Dropping test table..."
    echo "DROP TABLE $LOCAL_SCHEMA.$TEST_TABLE CASCADE;" | \
        sudo podman exec -i "$LOCAL_CONTAINER" psql $LOCAL_PSQL_CONN
    
    # Clean up: Remove the test dump file
    echo "Cleaning up: Removing test dump file..."
    rm -f "$LOCAL_TEST_DUMP_FILE"
    
    echo "Local test restore completed successfully."
}

# Function: Copy the dump to the remote server
copy_to_remote() {
    echo "===== STEP 3: Copying dump to remote server ====="
    
    echo "Creating remote directory if it doesn't exist..."
    ssh "$REMOTE_SSH_ALIAS" "mkdir -p $REMOTE_DUMP_DIR" || \
        error_exit "Failed to create remote directory"
    
    echo "Copying dump file to remote server..."
    scp "$LOCAL_DUMP_FILE" "$REMOTE_SSH_ALIAS:$REMOTE_DUMP_FILE" || \
        error_exit "Failed to copy dump file to remote server"
    
    echo "Dump file copied successfully to remote server."
}

# Function: Create backup on remote server (optional)
backup_remote_table() {
    if [ "$DO_REMOTE_BACKUP" != "true" ]; then
        echo "===== SKIPPING: Remote table backup ====="
        return 0
    fi
    
    echo "===== STEP 4: Creating backup of remote table ====="
    
    echo "Creating backup of remote table to: $REMOTE_BACKUP_FILE"
    ssh "$REMOTE_SSH_ALIAS" "pg_dump $REMOTE_PSQL_CONN \
        -F p -t $LOCAL_SCHEMA.$LOCAL_TABLE --data-only --no-owner --no-privileges \
        > $REMOTE_BACKUP_FILE" || error_exit "Failed to backup remote table"
    
    echo "Remote table backup completed successfully."
}

# Function: Terminate connections on remote server (optional)
terminate_remote_connections() {
    if [ "$DO_TERMINATE_CONNECTIONS" != "true" ]; then
        echo "===== SKIPPING: Terminating remote connections ====="
        return 0
    fi
    
    echo "===== STEP 5: Terminating remote connections to table ====="
    
    # SQL to find and terminate all connections to the target table
    TERMINATE_SQL="SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE 
        pid <> pg_backend_pid() AND 
        datname = '$REMOTE_DB' AND 
        query LIKE '%$LOCAL_TABLE%';"
    
    echo "Terminating active connections..."
    ssh "$REMOTE_SSH_ALIAS" "psql $REMOTE_PSQL_CONN -c \"$TERMINATE_SQL\"" || \
        echo "WARNING: Failed to terminate connections, but continuing anyway."
    
    echo "Connection termination completed."
}

# Function: Restore dump on remote server
restore_remote() {
    echo "===== STEP 6: Restoring dump on remote server ====="
    
    # Create a temporary SQL file for the transaction-based restore
    REMOTE_RESTORE_SQL="$REMOTE_DUMP_DIR/restore_${LOCAL_TABLE}.sql"
    
    echo "Creating transaction-based restore script (data only)..."
    cat <<EOF | ssh "$REMOTE_SSH_ALIAS" "cat > $REMOTE_RESTORE_SQL"
BEGIN;
-- Truncate the target table
DELETE FROM $LOCAL_SCHEMA.$LOCAL_TABLE;
-- Import the data dump
\i $REMOTE_DUMP_FILE
COMMIT;
EOF
    
    echo "Executing transaction-based restore..."
    ssh "$REMOTE_SSH_ALIAS" "psql $REMOTE_PSQL_CONN -v ON_ERROR_STOP=1 -f $REMOTE_RESTORE_SQL" || \
        error_exit "Failed to restore dump on remote server"
    
    echo "Cleaning up temporary restore script..."
    ssh "$REMOTE_SSH_ALIAS" "rm -f $REMOTE_RESTORE_SQL"
    
    echo "Remote restore completed successfully."
}

# Function: Verify remote restore
verify_remote() {
    echo "===== STEP 7: Verifying remote restore ====="
    
    echo "Counting records in remote table..."
    REMOTE_COUNT=$(ssh "$REMOTE_SSH_ALIAS" "psql $REMOTE_PSQL_CONN -t -c \"SELECT COUNT(*) FROM $LOCAL_SCHEMA.$LOCAL_TABLE;\"" | tr -d '[:space:]')
    
    echo "Remote table contains $REMOTE_COUNT records."
    
    # Error if table is empty
    if [ -z "$REMOTE_COUNT" ] || [ "$REMOTE_COUNT" -eq 0 ]; then
        error_exit "Remote table appears to be empty after restore. Verification failed."
    fi
    
    echo "Remote restore verification completed successfully."
}

# Main execution block
# -----------------
main() {
    echo "Starting main execution..."
    
    dump_local_table
    test_local_restore
    copy_to_remote
    backup_remote_table
    terminate_remote_connections
    restore_remote
    verify_remote
    
    echo "============================================="
    echo "SUCCESS: Backup and update process completed successfully: $(date)"
    echo "============================================="
}

# Run the main function
main
