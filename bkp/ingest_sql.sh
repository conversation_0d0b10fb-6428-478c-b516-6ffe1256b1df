#!/bin/bash

# Variables
CONTAINER_NAME="postgres"
SQL_FILE="/home/<USER>/Projects/python/cciapp/cciapp/bkp/20250317_main.sql"
POSTGRES_USER="doretto"
POSTGRES_PASSWORD="doretto"
POSTGRES_DB="portalcci"

# Ensure the SQL file exists
if [ ! -f "$SQL_FILE" ]; then
    echo "Error: SQL file not found at $SQL_FILE"
    exit 1
fi

# Copy the SQL file into the container
podman cp "$SQL_FILE" "$CONTAINER_NAME:/tmp/main.sql"

# Execute the SQL script inside the container
podman exec -it "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f "/tmp/main.sql"

# Cleanup: Remove the SQL file from the container after execution
podman exec -it "$CONTAINER_NAME" rm -f "/tmp/main.sql"

echo "SQL ingestion completed successfully."

