{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["_f = Path('./cadastro_rodovias_portalcci.csv')\n", "_f.is_file()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tipo</th>\n", "      <th>municipio__nome</th>\n", "      <th>regional__nome</th>\n", "      <th>concessionaria__nome_fantasia</th>\n", "      <th>rodovia__codigo</th>\n", "      <th>km_inicial</th>\n", "      <th>km_final</th>\n", "      <th>pista</th>\n", "      <th>nro_faixas</th>\n", "      <th>sentido</th>\n", "      <th>sentido_ordem</th>\n", "      <th>superfície</th>\n", "      <th>nome_primario</th>\n", "      <th>nome_segmento</th>\n", "      <th>vdm</th>\n", "      <th>ramo_dispositivo</th>\n", "      <th>observacao</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>EIXO</td>\n", "      <td>SOROCABA</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>0.0</td>\n", "      <td>11.000</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>NORTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>JOSÉ ERMIRIO DE MORAES</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>EIXO</td>\n", "      <td>SOROCABA</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>0.0</td>\n", "      <td>11.000</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>SUL</td>\n", "      <td>DECRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>JOSÉ ERMIRIO DE MORAES</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>EIXO</td>\n", "      <td>ITU</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>11.0</td>\n", "      <td>15.000</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>NORTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>JOSÉ ERMIRIO DE MORAES</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>EIXO</td>\n", "      <td>ITU</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>11.0</td>\n", "      <td>15.000</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>SUL</td>\n", "      <td>DECRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>JOSÉ ERMIRIO DE MORAES</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>EIXO</td>\n", "      <td>ITU</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>15.0</td>\n", "      <td>15.695</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>NORTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>DEP. ARCHIMEDES LAMMOGLIA</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   tipo municipio__nome  regional__nome concessionaria__nome_fantasia  \\\n", "0  EIXO        SOROCABA             NaN               ROTA SOROCABANA   \n", "1  EIXO        SOROCABA             NaN               ROTA SOROCABANA   \n", "2  EIXO             ITU             NaN               ROTA SOROCABANA   \n", "3  EIXO             ITU             NaN               ROTA SOROCABANA   \n", "4  EIXO             ITU             NaN               ROTA SOROCABANA   \n", "\n", "  rodovia__codigo  km_inicial  km_final  pista  nro_faixas sentido  \\\n", "0          SP 075         0.0    11.000  DUPLA         NaN   NORTE   \n", "1          SP 075         0.0    11.000  DUPLA         NaN     SUL   \n", "2          SP 075        11.0    15.000  DUPLA         NaN   NORTE   \n", "3          SP 075        11.0    15.000  DUPLA         NaN     SUL   \n", "4          SP 075        15.0    15.695  DUPLA         NaN   NORTE   \n", "\n", "  sentido_ordem  superfície              nome_primario  nome_segmento  vdm  \\\n", "0     CRESCENTE         NaN     JOSÉ ERMIRIO DE MORAES            NaN  NaN   \n", "1   DECRESCENTE         NaN     JOSÉ ERMIRIO DE MORAES            NaN  NaN   \n", "2     CRESCENTE         NaN     JOSÉ ERMIRIO DE MORAES            NaN  NaN   \n", "3   DECRESCENTE         NaN     JOSÉ ERMIRIO DE MORAES            NaN  NaN   \n", "4     CRESCENTE         NaN  DEP. ARCHIMEDES LAMMOGLIA            NaN  NaN   \n", "\n", "   ramo_dispositivo  observacao  \n", "0               NaN         NaN  \n", "1               NaN         NaN  \n", "2               NaN         NaN  \n", "3               NaN         NaN  \n", "4               NaN         NaN  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(_f, sep='|')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tipo</th>\n", "      <th>municipio__nome</th>\n", "      <th>regional__nome</th>\n", "      <th>concessionaria__nome_fantasia</th>\n", "      <th>rodovia__codigo</th>\n", "      <th>km_inicial</th>\n", "      <th>km_final</th>\n", "      <th>pista</th>\n", "      <th>nro_faixas</th>\n", "      <th>sentido</th>\n", "      <th>sentido_ordem</th>\n", "      <th>superfície</th>\n", "      <th>nome_primario</th>\n", "      <th>nome_segmento</th>\n", "      <th>vdm</th>\n", "      <th>ramo_dispositivo</th>\n", "      <th>observacao</th>\n", "      <th>km_medio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>EIXO</td>\n", "      <td>SOROCABA</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>0.00</td>\n", "      <td>11.000</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>NORTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>JOSÉ ERMIRIO DE MORAES</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.5000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>EIXO</td>\n", "      <td>SOROCABA</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>0.00</td>\n", "      <td>11.000</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>SUL</td>\n", "      <td>DECRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>JOSÉ ERMIRIO DE MORAES</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.5000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>EIXO</td>\n", "      <td>ITU</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>11.00</td>\n", "      <td>15.000</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>NORTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>JOSÉ ERMIRIO DE MORAES</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>13.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>EIXO</td>\n", "      <td>ITU</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>11.00</td>\n", "      <td>15.000</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>SUL</td>\n", "      <td>DECRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>JOSÉ ERMIRIO DE MORAES</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>13.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>EIXO</td>\n", "      <td>ITU</td>\n", "      <td>NaN</td>\n", "      <td>ROTA SOROCABANA</td>\n", "      <td>SP 075</td>\n", "      <td>15.00</td>\n", "      <td>15.695</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>NORTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>DEP. ARCHIMEDES LAMMOGLIA</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>15.3475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>MARGINAL</td>\n", "      <td>SÃO PAULO</td>\n", "      <td>NaN</td>\n", "      <td>NOVA RAPOSO</td>\n", "      <td>SPM 280 E</td>\n", "      <td>13.46</td>\n", "      <td>14.750</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>OESTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>CASTELLO BRANCO</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>14.1050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>MARGINAL</td>\n", "      <td>OSASCO</td>\n", "      <td>NaN</td>\n", "      <td>NOVA RAPOSO</td>\n", "      <td>SPM 280 E</td>\n", "      <td>14.75</td>\n", "      <td>19.550</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>LESTE</td>\n", "      <td>DECRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>CASTELLO BRANCO</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>17.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>MARGINAL</td>\n", "      <td>OSASCO</td>\n", "      <td>NaN</td>\n", "      <td>NOVA RAPOSO</td>\n", "      <td>SPM 280 E</td>\n", "      <td>14.75</td>\n", "      <td>19.550</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>OESTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>CASTELLO BRANCO</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>17.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>MARGINAL</td>\n", "      <td>BARUERI</td>\n", "      <td>NaN</td>\n", "      <td>NOVA RAPOSO</td>\n", "      <td>SPM 280 E</td>\n", "      <td>19.55</td>\n", "      <td>23.020</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>LESTE</td>\n", "      <td>DECRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>CASTELLO BRANCO</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>21.2850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>MARGINAL</td>\n", "      <td>BARUERI</td>\n", "      <td>NaN</td>\n", "      <td>NOVA RAPOSO</td>\n", "      <td>SPM 280 E</td>\n", "      <td>19.55</td>\n", "      <td>23.020</td>\n", "      <td>DUPLA</td>\n", "      <td>NaN</td>\n", "      <td>OESTE</td>\n", "      <td>CRESCENTE</td>\n", "      <td>NaN</td>\n", "      <td>CASTELLO BRANCO</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>21.2850</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>162 rows × 18 columns</p>\n", "</div>"], "text/plain": ["         tipo municipio__nome  regional__nome concessionaria__nome_fantasia  \\\n", "0        EIXO        SOROCABA             NaN               ROTA SOROCABANA   \n", "1        EIXO        SOROCABA             NaN               ROTA SOROCABANA   \n", "2        EIXO             ITU             NaN               ROTA SOROCABANA   \n", "3        EIXO             ITU             NaN               ROTA SOROCABANA   \n", "4        EIXO             ITU             NaN               ROTA SOROCABANA   \n", "..        ...             ...             ...                           ...   \n", "157  MARGINAL       SÃO PAULO             NaN                   NOVA RAPOSO   \n", "158  MARGINAL          OSASCO             NaN                   NOVA RAPOSO   \n", "159  MARGINAL          OSASCO             NaN                   NOVA RAPOSO   \n", "160  MARGINAL         BARUERI             NaN                   NOVA RAPOSO   \n", "161  MARGINAL         BARUERI             NaN                   NOVA RAPOSO   \n", "\n", "    rodovia__codigo  km_inicial  km_final  pista  nro_faixas sentido  \\\n", "0            SP 075        0.00    11.000  DUPLA         NaN   NORTE   \n", "1            SP 075        0.00    11.000  DUPLA         NaN     SUL   \n", "2            SP 075       11.00    15.000  DUPLA         NaN   NORTE   \n", "3            SP 075       11.00    15.000  DUPLA         NaN     SUL   \n", "4            SP 075       15.00    15.695  DUPLA         NaN   NORTE   \n", "..              ...         ...       ...    ...         ...     ...   \n", "157       SPM 280 E       13.46    14.750  DUPLA         NaN   OESTE   \n", "158       SPM 280 E       14.75    19.550  DUPLA         NaN   LESTE   \n", "159       SPM 280 E       14.75    19.550  DUPLA         NaN   OESTE   \n", "160       SPM 280 E       19.55    23.020  DUPLA         NaN   LESTE   \n", "161       SPM 280 E       19.55    23.020  DUPLA         NaN   OESTE   \n", "\n", "    sentido_ordem  superfície              nome_primario  nome_segmento  vdm  \\\n", "0       CRESCENTE         NaN     JOSÉ ERMIRIO DE MORAES            NaN  NaN   \n", "1     DECRESCENTE         NaN     JOSÉ ERMIRIO DE MORAES            NaN  NaN   \n", "2       CRESCENTE         NaN     JOSÉ ERMIRIO DE MORAES            NaN  NaN   \n", "3     DECRESCENTE         NaN     JOSÉ ERMIRIO DE MORAES            NaN  NaN   \n", "4       CRESCENTE         NaN  DEP. ARCHIMEDES LAMMOGLIA            NaN  NaN   \n", "..            ...         ...                        ...            ...  ...   \n", "157     CRESCENTE         NaN            CASTELLO BRANCO            NaN  NaN   \n", "158   DECRESCENTE         NaN            CASTELLO BRANCO            NaN  NaN   \n", "159     CRESCENTE         NaN            CASTELLO BRANCO            NaN  NaN   \n", "160   DECRESCENTE         NaN            CASTELLO BRANCO            NaN  NaN   \n", "161     CRESCENTE         NaN            CASTELLO BRANCO            NaN  NaN   \n", "\n", "     ramo_dispositivo  observacao  km_medio  \n", "0                 NaN         NaN    5.5000  \n", "1                 NaN         NaN    5.5000  \n", "2                 NaN         NaN   13.0000  \n", "3                 NaN         NaN   13.0000  \n", "4                 NaN         NaN   15.3475  \n", "..                ...         ...       ...  \n", "157               NaN         NaN   14.1050  \n", "158               NaN         NaN   17.1500  \n", "159               NaN         NaN   17.1500  \n", "160               NaN         NaN   21.2850  \n", "161               NaN         NaN   21.2850  \n", "\n", "[162 rows x 18 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df['km_medio'] = (df['km_final'] + df['km_inicial']) / 2\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}