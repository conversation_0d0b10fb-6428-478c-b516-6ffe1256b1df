#!/bin/bash

# Variables
CONTAINER_NAME="cciapp_postgres"
SQL_FILE="/home/<USER>/projects/python/cciapp/bkp/20250715_main.sql"
POSTGRES_USER="doretto"
POSTGRES_PASSWORD="doretto"
POSTGRES_DB="portalcci"

# Check if container is running
if ! sudo docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "Error: Container ${CONTAINER_NAME} is not running."
    exit 1
fi

# Ensure the SQL file exists
if [ ! -f "$SQL_FILE" ]; then
    echo "Error: SQL file not found at $SQL_FILE"
    exit 1
fi

echo "Executing SQL script directly..."
# Instead of copying the file into the container, execute it directly
# This avoids potential networking namespace issues
sudo docker exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" < "$SQL_FILE"

# Check if the command succeeded
if [ $? -eq 0 ]; then
    echo "SQL ingestion completed successfully."
else
    echo "Error: SQL ingestion failed."
    exit 1
fi