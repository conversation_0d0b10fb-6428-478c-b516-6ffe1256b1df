#!/bin/bash

# Variables
CONTAINER_NAME="postgres16"
SQL_FILE="/home/<USER>/Projects/python/cciapp/cciapp/bkp/20250416_main.sql"
POSTGRES_USER="doretto"
POSTGRES_PASSWORD="doretto"
POSTGRES_DB="portalcci"

# Output colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# --- Step 1: Check container running ---
echo -e "${YELLOW}Checking if container ${CONTAINER_NAME} is running...${NC}"
if ! podman ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo -e "${RED}Error: Container ${CONTAINER_NAME} is not running.${NC}"
    exit 1
fi

# --- Step 2: Ensure superuser exists ---
echo -e "${YELLOW}Ensuring PostgreSQL superuser '${POSTGRES_USER}' exists...${NC}"
USER_EXISTS=$(podman exec "$CONTAINER_NAME" psql -U postgres -tAc "SELECT 1 FROM pg_roles WHERE rolname='${POSTGRES_USER}';")

if [[ "$USER_EXISTS" != "1" ]]; then
    echo -e "${YELLOW}User '${POSTGRES_USER}' does not exist. Creating...${NC}"
    podman exec "$CONTAINER_NAME" psql -U postgres -c "CREATE ROLE ${POSTGRES_USER} WITH LOGIN SUPERUSER PASSWORD '${POSTGRES_PASSWORD}';"
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}User '${POSTGRES_USER}' created successfully.${NC}"
    else
        echo -e "${RED}Failed to create user '${POSTGRES_USER}'.${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}User '${POSTGRES_USER}' already exists.${NC}"
fi

# --- Step 3: Ensure database exists ---
echo -e "${YELLOW}Ensuring database '${POSTGRES_DB}' exists...${NC}"
DB_EXISTS=$(podman exec "$CONTAINER_NAME" psql -U postgres -tAc "SELECT 1 FROM pg_database WHERE datname='${POSTGRES_DB}';")

if [[ "$DB_EXISTS" != "1" ]]; then
    echo -e "${YELLOW}Database '${POSTGRES_DB}' does not exist. Creating...${NC}"
    podman exec "$CONTAINER_NAME" psql -U postgres -c "CREATE DATABASE ${POSTGRES_DB} OWNER ${POSTGRES_USER};"
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Database '${POSTGRES_DB}' created successfully.${NC}"
    else
        echo -e "${RED}Failed to create database '${POSTGRES_DB}'.${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}Database '${POSTGRES_DB}' already exists.${NC}"
fi

# --- Step 4: Ensure SQL file exists ---
echo -e "${YELLOW}Checking if SQL file exists...${NC}"
if [ ! -f "$SQL_FILE" ]; then
    echo -e "${RED}Error: SQL file not found at $SQL_FILE${NC}"
    exit 1
fi

# --- Step 5: Backup current database ---
BACKUP_DIR="/home/<USER>/Projects/python/cciapp/cciapp/bkp"
BACKUP_FILE="${BACKUP_DIR}/backup_$(date +%Y%m%d_%H%M%S).sql"
mkdir -p "$BACKUP_DIR"

echo -e "${YELLOW}Creating backup in $BACKUP_FILE...${NC}"
podman exec "$CONTAINER_NAME" pg_dump -U "$POSTGRES_USER" -d "$POSTGRES_DB" > "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Backup completed successfully.${NC}"
else
    echo -e "${RED}Backup failed. Do you want to continue anyway? (y/n)${NC}"
    read -r response
    if [[ "$response" != "y" ]]; then
        echo "Aborting operation."
        exit 1
    fi
fi

# --- Step 6: Drop all tables ---
echo -e "${YELLOW}Dropping all tables from ${POSTGRES_DB}...${NC}"
DROP_TABLES_SQL=$(cat <<EOF
DO \$\$
DECLARE
    r RECORD;
BEGIN
    SET session_replication_role = 'replica';
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
    SET session_replication_role = 'origin';
END \$\$;
EOF
)

echo "$DROP_TABLES_SQL" | podman exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB"
echo -e "${GREEN}All tables dropped successfully.${NC}"

# --- Step 7: Import SQL file ---
echo -e "${YELLOW}Recreating schema and importing data...${NC}"
podman exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" < "$SQL_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Data imported successfully.${NC}"
else
    echo -e "${RED}Error importing data.${NC}"
    echo -e "${YELLOW}Restoring from backup...${NC}"
    echo "$DROP_TABLES_SQL" | podman exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB"
    podman exec -i "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" < "$BACKUP_FILE"
    if [ $? -eq 0 ]; then
        echo -e "${YELLOW}Restore from backup completed.${NC}"
    else
        echo -e "${RED}Failed to restore from backup. Database may be in an inconsistent state.${NC}"
    fi
    exit 1
fi

# --- Step 8: Verify state ---
echo -e "${YELLOW}Verifying database state...${NC}"
TABLE_COUNT=$(podman exec "$CONTAINER_NAME" psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
echo -e "${GREEN}Database refresh completed with $TABLE_COUNT tables created.${NC}"
echo -e "${GREEN}Backup saved as: $BACKUP_FILE${NC}"
