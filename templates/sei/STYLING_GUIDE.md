# SEI Templates Styling Guide

## Overview
This document outlines the standardized styling approach implemented across all HTML templates in the `templates/sei/` directory. The styling follows a clean, minimalist, and elegant design philosophy with consistent visual patterns.

## Color Palette

### Brand Colors (Use Sparingly for Emphasis Only)
- **Primary**: `#FF161F` (Red - Pantone 485 C) - For errors and critical actions only
- **Secondary**: `#034EA2` (Blue - Pantone 2955 C) - For navigation and focus elements
- **Accent**: `#0B9247` (Green - Pantone 347 C) - For success and positive actions
- **Highlight**: `#FBB900` (Yellow - Pantone 123 C) - For highlights and warnings

### Neutral Colors (Primary Design Elements)
- **Gray Darkest**: `#000000` - Headers and primary text
- **Gray Darker**: `#333333` - Secondary text
- **Gray Dark**: `#4b5563` - Medium elements
- **Gray Medium**: `#6b7280` - Neutral elements
- **Gray Light**: `#9ca3af` - Light elements
- **Gray Lighter**: `#d1d5db` - Very light elements
- **Gray Lightest**: `#f3f4f6` - Backgrounds

### Supporting Colors
- **Primary Light**: `#ffeeee` - Light red for error backgrounds
- **Accent Dark**: `#047857` - Darker green for hover states

## Standardized Tailwind Configuration

All templates use the following Tailwind configuration:

```javascript
tailwind.config = {
    theme: {
        extend: {
            colors: {
                // Brand Colors (for minor elements & accents)
                'primary': '#FF161F',        // Vermelho (Pantone 485 C) - Errors & critical actions only
                'primary-light': '#ffeeee',  // Light red for error backgrounds
                'secondary': '#034EA2',      // Azul (Pantone 2955 C) - Navigation & focus
                'accent': '#0B9247',         // Verde (Pantone 347 C) - Success & positive actions
                'accent-dark': '#047857',    // Darker green
                'highlight': '#FBB900',      // Amarelo (Pantone 123 C) - Highlights

                // Neutrals (for major elements)
                'gray-darkest': '#000000',   // Headers & primary text
                'gray-darker': '#333333',    // Secondary text
                'gray-dark': '#4b5563',      // Medium elements
                'gray-medium': '#6b7280',    // Neutral elements
                'gray-light': '#9ca3af',     // Light elements
                'gray-lighter': '#d1d5db',   // Very light elements
                'gray-lightest': '#f3f4f6'   // Backgrounds
            }
        }
    }
}
```

## Layout Structure

### Header Pattern
All templates use a consistent header structure:
- Background: `bg-gray-darkest` with `shadow-lg` and `border-b border-gray-dark`
- Container: `max-w-7xl mx-auto px-4 sm:px-6 lg:px-8`
- Content: Flexbox layout with system title and navigation
- Typography: `text-2xl font-bold text-white` for main title, `text-gray-light` for subtitle

### Footer Pattern
All templates use a consistent footer structure:
- Background: `bg-gray-darkest text-white mt-12`
- Container: `max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6`
- Content: Centered copyright text with `text-sm text-gray-light`

### Main Content
- Container: `max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8` (or `max-w-7xl` for list views)
- Background: `bg-gray-50` for body
- Cards: `bg-white rounded-lg shadow-sm border border-gray-200`

## Component Styling

### Buttons
- **Primary Action**: `bg-secondary text-white hover:bg-blue-700 focus:ring-secondary`
- **Success Action**: `bg-accent text-white hover:bg-accent-dark focus:ring-accent`
- **Danger Action**: `bg-primary text-white hover:bg-primary-dark focus:ring-primary`
- **Secondary Action**: `border border-gray-300 text-gray-700 bg-white hover:bg-gray-50`

### Form Elements
- **Input Fields**: `border border-gray-300 rounded-md focus:ring-2 focus:ring-secondary focus:border-transparent`
- **Labels**: `text-sm font-medium text-gray-700`
- **Error States**: `border-primary` for inputs, `text-red-500` for error text

### Cards and Containers
- **Standard Card**: `bg-white rounded-lg shadow-sm border border-gray-200`
- **Section Headers**: `bg-gray-darker px-6 py-3` with `text-lg font-semibold text-white`
- **Content Padding**: `p-6` for card content

## Typography Hierarchy

### Headings
- **H1**: `text-3xl font-bold text-gray-900` (page titles)
- **H2**: `text-2xl font-bold text-gray-900` (section titles)
- **H3**: `text-lg font-semibold text-gray-900` (subsection titles)
- **H4**: `text-md font-semibold text-gray-900` (component titles)

### Body Text
- **Primary**: `text-gray-900` (main content)
- **Secondary**: `text-gray-700` (supporting content)
- **Muted**: `text-gray-500` (less important content)
- **Light**: `text-gray-400` (placeholder text)

## Responsive Design

### Breakpoints
- **Mobile First**: Base styles for mobile devices
- **SM**: `sm:` prefix for small screens (640px+)
- **MD**: `md:` prefix for medium screens (768px+)
- **LG**: `lg:` prefix for large screens (1024px+)

### Grid Patterns
- **Single Column**: `grid-cols-1` (mobile)
- **Two Columns**: `md:grid-cols-2` (medium screens+)
- **Three Columns**: `lg:grid-cols-3` (large screens+)

## Accessibility

### Focus States
- All interactive elements have `focus:outline-none focus:ring-2 focus:ring-offset-2`
- Focus ring colors match the element's primary color (secondary, accent, or primary)

### Color Contrast
- All text meets WCAG AA contrast requirements
- Primary text uses `text-gray-900` on white backgrounds
- Light text uses `text-white` on dark backgrounds

## Implementation Status

All templates have been standardized with the following updates:
- ✅ `list.html` - Reference template (already consistent)
- ✅ `form.html` - Updated with standardized header/footer and color palette
- ✅ `detail.html` - Updated with standardized color palette
- ✅ `edit.html` - Updated with standardized header/footer and color palette
- ✅ `success.html` - Updated with standardized header/footer and color palette
- ✅ `delete.html` - Updated with standardized header/footer and color palette

## Maintenance Guidelines

1. **Color Usage**: Always use the defined color variables instead of hardcoded hex values
2. **Component Consistency**: Reuse the established patterns for new components
3. **Responsive Design**: Test all changes across different screen sizes
4. **Accessibility**: Ensure all new elements maintain proper contrast and focus states
5. **Documentation**: Update this guide when making significant styling changes
