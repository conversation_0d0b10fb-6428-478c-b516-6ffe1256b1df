<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lista de Protocolos - Sistema GERPRO</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- jQuery (required for Select2) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Brand Colors (for minor elements & accents)
                        'primary': '#FF161F',        // Vermelho (Pantone 485 C) - Errors & critical actions only
                        'primary-light': '#ffeeee',  // Light red for error backgrounds
                        'secondary': '#034EA2',      // Azul (Pantone 2955 C) - Navigation & focus
                        'accent': '#0B9247',         // Verde (Pantone 347 C) - Success & positive actions
                        'highlight': '#FBB900',      // Amarelo (Pantone 123 C) - Highlights

                        // Neutrals (for major elements)
                        'gray-darkest': '#000000',   // Headers & primary text
                        'gray-darker': '#333333',    // Secondary text
                        'gray-dark': '#4b5563',      // Medium elements
                        'gray-medium': '#6b7280',    // Neutral elements
                        'gray-light': '#9ca3af',     // Light elements
                        'gray-lighter': '#d1d5db',   // Very light elements
                        'gray-lightest': '#f3f4f6'   // Backgrounds
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        /* Basic Select2 styling - minimal approach */
        .select2-container {
            width: 100% !important;
        }
        
        .select2-container--default .select2-selection--single {
            height: 42px !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem !important;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 26px !important;
            padding-left: 0 !important;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 40px !important;
            right: 8px !important;
        }
        
        .select2-container--default.select2-container--focus .select2-selection--single {
            border-color: #FF161F !important;
            box-shadow: 0 0 0 1px #FF161F !important;
        }
        
        /* Loading state */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-gray-darkest shadow-lg border-b border-gray-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-white">Sistema GERPRO</h1>
                    <span class="ml-4 text-gray-light">Lista de Protocolos</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'sei:sei_form' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-secondary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors shadow-md">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Novo Protocolo
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" x-data="protocoloList()">
        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Buscar e Filtrar</h2>
            
            <form method="GET" class="space-y-4">
                <!-- Search Input -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                        Buscar em todos os campos
                    </label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ search_term }}"
                           placeholder="Digite para buscar em código, nome, email, tipo de serviço..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>

                <!-- Filter Dropdowns -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label for="unidade_filter" class="block text-sm font-medium text-gray-700 mb-2">
                            Unidade
                        </label>
                        <select id="unidade_filter" name="unidade" class="w-full filter-select">
                            <option value="">Todas as unidades</option>
                        </select>
                    </div>

                    <div>
                        <label for="interessado_filter" class="block text-sm font-medium text-gray-700 mb-2">
                            Interessado
                        </label>
                        <select id="interessado_filter" name="interessado" class="w-full filter-select">
                            <option value="">Todos os interessados</option>
                        </select>
                    </div>

                    <div>
                        <label for="localizacao_filter" class="block text-sm font-medium text-gray-700 mb-2">
                            Localização
                        </label>
                        <select id="localizacao_filter" name="localizacao" class="w-full filter-select">
                            <option value="">Todas as localizações</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_filter" class="block text-sm font-medium text-gray-700 mb-2">
                            Assunto
                        </label>
                        <select id="assunto_filter" name="assunto" class="w-full filter-select">
                            <option value="">Todos os assuntos</option>
                        </select>
                    </div>

                    <div>
                        <label for="local_filter" class="block text-sm font-medium text-gray-700 mb-2">
                            Local
                        </label>
                        <select id="local_filter" name="local" class="w-full filter-select">
                            <option value="">Todos os locais</option>
                        </select>
                    </div>

                    <div>
                        <label for="disciplina_filter" class="block text-sm font-medium text-gray-700 mb-2">
                            Disciplina
                        </label>
                        <select id="disciplina_filter" name="disciplina" class="w-full filter-select">
                            <option value="">Todas as disciplinas</option>
                        </select>
                    </div>
                </div>

                <!-- Date Filters -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">
                            Data inicial
                        </label>
                        <input type="date" 
                               id="date_from" 
                               name="date_from" 
                               value="{{ filters.date_from }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>

                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">
                            Data final
                        </label>
                        <input type="date" 
                               id="date_to" 
                               name="date_to" 
                               value="{{ filters.date_to }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="submit" 
                            class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-secondary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Buscar
                    </button>
                    
                    <a href="{% url 'sei:protocolo_list' %}" 
                       class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Limpar Filtros
                    </a>
                </div>
            </form>
        </div>

        <!-- Results Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    Mostrando {{ protocolos.start_index }} a {{ protocolos.end_index }} de {{ total_count }} protocolos
                    {% if search_term %}
                        <span class="font-medium">para "{{ search_term }}"</span>
                    {% endif %}
                </div>
                <div class="text-sm text-gray-500">
                    Página {{ protocolos.number }} de {{ protocolos.paginator.num_pages }}
                </div>
            </div>
        </div>

        <!-- Protocols Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Código do Documento
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Requisitante
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                E-mail
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Data de Criação
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ações
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for protocolo in protocolos %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ protocolo.doc_cod }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 max-w-32 truncate"
                                     title="{{ protocolo.usuario.nome|default:'Não informado' }}">
                                    {{ protocolo.usuario.nome|default:"Não informado" }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 max-w-40 truncate"
                                     title="{{ protocolo.usuario.email|default:'Não informado' }}">
                                    {{ protocolo.usuario.email|default:"Não informado" }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ protocolo.created_at|date:"d/m/Y H:i" }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{% url 'sei:protocolo_detail' protocolo_id=protocolo.id %}"
                                       class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-secondary bg-blue-50 hover:bg-blue-100 border border-blue-200 transition-colors">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        <!-- Detalhes -->
                                    </a>
                                    <a href="{% url 'sei:protocolo_edit' protocolo_id=protocolo.id %}"
                                       class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-accent bg-green-50 hover:bg-green-100 border border-green-200 transition-colors">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        <!-- Editar -->
                                    </a>
                                    <a href="{% url 'sei:protocolo_delete' protocolo_id=protocolo.id %}"
                                       class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-primary bg-red-50 hover:bg-red-100 border border-red-200 transition-colors">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        <!-- Excluir -->
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhum protocolo encontrado</h3>
                                    <p class="mt-1 text-sm text-gray-500">
                                        {% if search_term or filters.unidade or filters.interessado %}
                                            Tente ajustar os filtros de busca.
                                        {% else %}
                                            Comece criando um novo protocolo.
                                        {% endif %}
                                    </p>
                                    {% if not search_term and not filters.unidade and not filters.interessado %}
                                    <div class="mt-6">
                                        <a href="{% url 'sei:sei_form' %}"
                                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-secondary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            Criar Primeiro Protocolo
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        {% if protocolos.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow-sm">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if protocolos.has_previous %}
                    <a href="?{% if search_term %}search={{ search_term }}&{% endif %}{% for key, value in filters.items %}{% if value %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ protocolos.previous_page_number }}"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Anterior
                    </a>
                {% endif %}
                {% if protocolos.has_next %}
                    <a href="?{% if search_term %}search={{ search_term }}&{% endif %}{% for key, value in filters.items %}{% if value %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ protocolos.next_page_number }}"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Próxima
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Mostrando
                        <span class="font-medium">{{ protocolos.start_index }}</span>
                        a
                        <span class="font-medium">{{ protocolos.end_index }}</span>
                        de
                        <span class="font-medium">{{ total_count }}</span>
                        resultados
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if protocolos.has_previous %}
                            <a href="?{% if search_term %}search={{ search_term }}&{% endif %}{% for key, value in filters.items %}{% if value %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ protocolos.previous_page_number }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Anterior</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}

                        {% for num in protocolos.paginator.page_range %}
                            {% if protocolos.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-primary bg-primary text-sm font-medium text-white">
                                    {{ num }}
                                </span>
                            {% elif num > protocolos.number|add:'-3' and num < protocolos.number|add:'3' %}
                                <a href="?{% if search_term %}search={{ search_term }}&{% endif %}{% for key, value in filters.items %}{% if value %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}

                        {% if protocolos.has_next %}
                            <a href="?{% if search_term %}search={{ search_term }}&{% endif %}{% for key, value in filters.items %}{% if value %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ protocolos.next_page_number }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Próxima</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-darkest text-white mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-light">
                    © 2025 Sistema GERPRO - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        function protocoloList() {
            return {
                loading: false,

                init() {
                    this.initializeSelect2Filters();
                },

                initializeSelect2Filters() {
                    const commonConfig = {
                        width: '100%',
                        allowClear: true
                    };

                    // Initialize filter dropdowns
                    $('#unidade_filter').select2({
                        ...commonConfig,
                        placeholder: 'Todas as unidades',
                        ajax: {
                            url: '/sei/api/unidade/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    });

                    $('#interessado_filter').select2({
                        ...commonConfig,
                        placeholder: 'Todos os interessados',
                        ajax: {
                            url: '/sei/api/interessado/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    });

                    $('#localizacao_filter').select2({
                        ...commonConfig,
                        placeholder: 'Todas as localizações',
                        ajax: {
                            url: '/sei/api/localizacao/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    });

                    $('#assunto_filter').select2({
                        ...commonConfig,
                        placeholder: 'Todos os assuntos',
                        ajax: {
                            url: '/sei/api/assunto/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    });

                    $('#local_filter').select2({
                        ...commonConfig,
                        placeholder: 'Todos os locais',
                        ajax: {
                            url: '/sei/api/local/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    });

                    $('#disciplina_filter').select2({
                        ...commonConfig,
                        placeholder: 'Todas as disciplinas',
                        ajax: {
                            url: '/sei/api/disciplina/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    });

                    // Set current filter values
                    this.setCurrentFilterValues();
                },

                setCurrentFilterValues() {
                    // Set current filter values from URL parameters
                    const urlParams = new URLSearchParams(window.location.search);

                    const filters = {
                        'unidade_filter': urlParams.get('unidade'),
                        'interessado_filter': urlParams.get('interessado'),
                        'localizacao_filter': urlParams.get('localizacao'),
                        'assunto_filter': urlParams.get('assunto'),
                        'local_filter': urlParams.get('local'),
                        'disciplina_filter': urlParams.get('disciplina')
                    };

                    // Set values for each filter
                    Object.entries(filters).forEach(([filterId, value]) => {
                        if (value) {
                            // Create option and set as selected
                            const $select = $(`#${filterId}`);
                            const option = new Option('Loading...', value, true, true);
                            $select.append(option).trigger('change');

                            // Load the actual text for the option
                            $select.trigger('select2:select');
                        }
                    });
                }
            }
        }
    </script>
</body>
</html>
